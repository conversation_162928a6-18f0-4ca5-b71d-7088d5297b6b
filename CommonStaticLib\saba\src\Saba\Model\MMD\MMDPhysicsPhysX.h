﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//
#pragma once

#include "MMDPhysics.h"
#include "PxPhysicsAPI.h"

namespace saba {
	extern physx::PxPhysics* gPxPhysics ;
	extern physx::PxScene* gPxScene ;
	class PhysXRigidBody : public MMDRigidBody
	{
		friend class PhysXJoint;
	public:
		PhysXRigidBody(MMDPhysics * phy);
		virtual ~PhysXRigidBody();
		// Inherited via MMDRigidBody
		bool CreatePMX(const PMXRigidbody& pmxRigidBody, MMDModel* model, MMDNode* node) override;
		void Destroy() override;
		const glm::mat4 GetTransform() override;
		const glm::vec3 getPosition() override;
		void ResetMovement(MMDPhysics* physics) override;
		void SetActivation(bool activation,bool force=false) override;
		void setScale(glm::vec3 scv) override;
		void updateMassOnDensity() override;
		void addToWorld() override;
		void removeFromWorld() override;

		void setGravityMul(float gm) override;
		void setDamping(float d, int what) override;
		glm::vec3 getLinearVel() override;
		glm::vec3 getAngularVel() override;
		void setLinearVel(const glm::vec3& vel) override;

		glm::vec3 addLinearVel(const glm::vec3& vel) override;
		void addLinearVelThenLimit(const glm::vec3& vel, float maxSpeed);

		void addForce(const glm::vec3& f, bool isVelChange = true) override;
		void addTorque(const glm::vec3& torque, bool isVelChange =true) override;
		void addTorqueOnMatRtt(const glm::mat4& m, const glm::vec3& torque, bool isVelChange) override;
		void addRotationToMatOnNode(glm::mat3 rttMat, float angleMul, float chgVel = false) override;


		void setAngularVel(const glm::vec3& vel) override;
		void setAngVelToRotateOnNode(glm::mat4 rttMat, float velMul) override;
		void SetCoMTranslate(const glm::vec3& v, float dummy) override;
		void SetCoMTransform(const glm::mat4& m, float mul) override;
		void scaleVel(PhyReal s, int what) override;
		void scaleVel3(glm::vec3 s, int what) override;
		float getMass() override;
		void setGravityOn(bool on) override;
		virtual void setMassMul(float massMul) override;
		void setGlmMat(glm::mat4 m) override;
		void setContactTest(bool con) override;
		virtual int connectJointCount() override;
		// Get the constraints (joints) connected to this rigid body
		int getConnectedConstraints(physx::PxConstraint** userBuffer, int bufferSize, int startIndex = 0);
		void onAllRbCreated(MMDModel *model) override;
		void setCollideFilterMask(int setMode, uint32_t ft, int flag = 0) override;  //setmode 1:set 0:restore

		void setFilterInternal(physx::PxShape* shape);

		virtual glm::vec3 predictPosition(float afterTimeS) override;

#if SABA_USE_PHYSX
		physx::PxRigidBody* GetRigidBody() const {	return mRb;	}
#else
		btRigidBody* GetRigidBody() constconst {return mRb;
		// 通过 MMDRigidBody 继承


		// 通过 MMDRigidBody 继承


	}
#endif
		virtual void pdbResetPos() override;
	private:
		int rbInWhat = 0;  //1 world, 2 aggregate
		
#if SABA_USE_PHYSX

		physx::PxRigidDynamic* mRb{};
		physx::PxArticulationLink* arb{};

		physx::PxAggregate* aggregate{};
#else
		std::unique_ptr<btCollisionShape>	m_shape;
		std::unique_ptr<MMDMotionState>		m_activeMotionState;
		std::unique_ptr<MMDMotionState>		m_kinematicMotionState;
		std::unique_ptr<btRigidBody>		mRb;
#endif
		bool isSDF = false;
		bool canUpdateMass = true;
	};

	class PhysXJoint : public MMDJoint
	{
	public:
		PhysXJoint();
		virtual ~PhysXJoint();
		// Inherited via MMDJoint
		virtual bool CreateJointPmx(PMXJoint pmxJoint, MMDRigidBody* rigidBodyA, MMDRigidBody* rigidBodyB, float scale) override;
		virtual bool CreateJoint2pt(const PMXJoint& pmxJoint, MMDRigidBody* rigidBodyA, MMDRigidBody* rigidBodyB, int lockRtt, int lockPos);

		virtual void setDrive(float spring, float damping) override;
		void setDriveMul(float springMultiplier, float dampingMultiplier=1.f) override;
		void updateDrive(physx::PxD6Joint* j);
		virtual void destroy() override;  // delete if connectRb, dot not call directly

		virtual void setBreakThreshold(float s) override;
		virtual void scaleBreakThreshold(float s) override;
		virtual bool isConnected() override;
		virtual void setScale() override;
		virtual void setDrivePose(glm::mat4 m, uint32_t flag,float driveMul) override;
		virtual void setDriveVel(glm::vec3 lv, glm::vec3 av, uint32_t flag) override;

		virtual glm::vec3 getPosition() override;
#if SABA_USE_PHYSX
	private:
		int jointType = 0;
		physx::PxJoint* pJoint{};
		physx::PxTransform localPoseA, localPoseB;
		void createJointInternal();

		PhysXRigidBody* rbA{}, * rbB{};
#else

	private:
		std::unique_ptr<btTypedConstraint>	m_constraint;
#endif


		// 通过 MMDJoint 继承
		void getLocalFrame(int id, glm::vec3& t, glm::quat& r) override;

		void setLocalFrame(int id, const glm::vec3& t, const glm::quat& r) override;
		void lockRtt(bool lk, glm::vec3 angLimit=glm::vec3{0}) override;

	};



	class PhysXMan : public MMDPhysics
	{

	public:
		PhysXMan();
		virtual ~PhysXMan();
		// Inherited via MMDPhysics
		virtual EPhysicsEngine getType() override 	{return EPhysicsEngine::PhysX;	}
		bool Create() override;

		virtual MMDRigidBody* newRigidBody() override;
		virtual MMDJoint* newJoint() override;
		virtual void senPhyDbgVisual(bool show) override;
		virtual void Destroy() override;
		void updateStart(float time);
		void updateWait();
		virtual void clearForces();
		virtual void getLines(DebugLine*& ptr, int& num) override;
		virtual void setGravity(glm::vec3 g) override;

		void* createInflate(PhyMeshData& pd);
		void  updateInflate(const PhyMeshData& pd, std::vector< InflateBufBind>* bindArray);


#if TEST_VEHICLE2
		// New method to create a vehicle
		PhysXVehicle* createVehicle(const glm::vec3& startPos = glm::vec3(0.0f, 0.5f, 0.0f));
		virtual void test1() override;
		PhysXVehicle* testVC{};
#endif

#if USE_DIRECT_GPU
		void initDirectGPU();
		void cleanupDirectGPU();
		void addRigidBodyToGPU(PxRigidDynamic* rb, const PxGeometry& geometry);
#endif
	private:
		bool subStepStarted = false;



#if USE_DIRECT_GPU
		// GPU buffers and indices
		std::vector<PxU32> m_rbIndices;
		std::vector<PxGeometryType::Enum> m_rbGeometries;
		std::vector<PxTransform> m_rbPoses;

		// GPU device pointers
		uint64_t m_rbIndicesD{ 0 };
		uint64_t m_rbPosesD{ 0 };
		uint64_t m_rbInitPosesD{ 0 };
		uint64_t m_rbInitLinVelsD{ 0 };
		uint64_t m_rbInitAngVelsD{ 0 };

		size_t m_currentGPUBufferSize{ 0 };

		void reallocateGPUBuffers(size_t newSize);
#endif

	};

}