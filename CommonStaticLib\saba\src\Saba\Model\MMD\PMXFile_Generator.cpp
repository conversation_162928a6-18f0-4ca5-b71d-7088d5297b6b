#include "PMXFile_Generator.h"
#include <Saba/Base/Log.h>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtx/quaternion.hpp>
#include <glm/gtx/euler_angles.hpp>

#define PI (glm::pi<float>())
namespace saba {
    PMXFile_Generator_Param gPmxGenPm;
PMXFile_Generator::PMXFile_Generator()
    : m_currentMaterialFaceCount(0)
{
    initializeHeader();
}

PMXFile_Generator::~PMXFile_Generator()
{
}

void PMXFile_Generator::initialize(const std::string& modelName, const std::string& comment)
{
    // Clear any existing data
    m_pmxFile.m_vertices.clear();
    m_pmxFile.m_faces.clear();
    m_pmxFile.m_textures.clear();
    m_pmxFile.m_materials.clear();
    m_pmxFile.m_bones.clear();
    m_pmxFile.m_morphs.clear();
    m_pmxFile.m_displayFrames.clear();
    m_pmxFile.m_rigidbodies.clear();
    m_pmxFile.m_joints.clear();
    m_pmxFile.m_softbodies.clear();

    // Initialize header
    initializeHeader();

    // Set model info
    m_pmxFile.m_info.m_modelName = modelName;
    m_pmxFile.m_info.m_englishModelName = modelName;
    m_pmxFile.m_info.m_comment = comment;
    m_pmxFile.m_info.m_englishComment = comment;

    // Reset material face count
    m_currentMaterialFaceCount = 0;

    //// Add a root bone
    //addBone("Root", glm::vec3(0.0f), -1,
    //    (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | PMXBoneFlags::Visible | PMXBoneFlags::AllowControl),
    //    false, -1);
}

void PMXFile_Generator::initializeHeader()
{
    // Set PMX header
	ReadPMXFile(&m_pmxFile, "data/mmd/Model/basePmxModel.pmx");

    m_pmxFile.m_header.m_magic.Set("PMX ");
    m_pmxFile.m_header.m_version = 2.0f;
    m_pmxFile.m_header.m_dataSize = 8;
    m_pmxFile.m_header.m_encode = 0; //
    m_pmxFile.m_header.m_addUVNum = 0;
    m_pmxFile.m_header.m_vertexIndexSize = 1; // 32-bit indices
    m_pmxFile.m_header.m_textureIndexSize =1;
    m_pmxFile.m_header.m_materialIndexSize = 1;
    m_pmxFile.m_header.m_boneIndexSize = 1;
    m_pmxFile.m_header.m_morphIndexSize = 1;
    m_pmxFile.m_header.m_rigidbodyIndexSize = 1;


    m_pmxFile.m_bones.clear();

}

int32_t PMXFile_Generator::addVertex(const glm::vec3& position, const glm::vec3& normal, const glm::vec2& uv)
{
    PMXVertex vertex;
    vertex.m_position = position;
    vertex.m_normal = normal;
    vertex.m_uv = uv;

    // Set default bone weights (all to bone 0)
    vertex.m_weightType = PMXVertexWeight::BDEF1;
    vertex.m_boneIndices[0] = 0;
    vertex.m_boneIndices[1] = 0;
    vertex.m_boneIndices[2] = 0;
    vertex.m_boneIndices[3] = 0;
    vertex.m_boneWeights[0] = 1.0f;
    vertex.m_boneWeights[1] = 0.0f;
    vertex.m_boneWeights[2] = 0.0f;
    vertex.m_boneWeights[3] = 0.0f;

    // Set edge flag
    vertex.m_edgeMag = 1.0f;

    m_pmxFile.m_vertices.push_back(vertex);
    return (int32_t)(m_pmxFile.m_vertices.size() - 1);
}

void PMXFile_Generator::addFace(uint32_t v1, uint32_t v2, uint32_t v3)
{
    PMXFace face;
    face.vtxId[0] = v1;
    face.vtxId[1] = v2;
    face.vtxId[2] = v3;

    m_pmxFile.m_faces.push_back(face);
    m_currentMaterialFaceCount++;
}

int32_t PMXFile_Generator::addMaterial(const std::string& name, const glm::vec4& diffuse, int32_t faceCount)
{
    PMXMaterial material;
    material.m_name = name;
    material.m_englishName = name;

    // Set material properties
    material.m_diffuse = diffuse;
    material.m_specular = glm::vec3(0.5f, 0.5f, 0.5f);
    material.m_specularPower = 16.0f;
    material.m_ambient = glm::vec3(0.5f, 0.5f, 0.5f);

    // Set draw mode
    material.m_drawMode = PMXDrawModeFlags(PMXDrawModeFlags::GroundShadow | PMXDrawModeFlags::CastSelfShadow | PMXDrawModeFlags::RecieveSelfShadow);

    // Set edge properties
    material.m_edgeColor = glm::vec4(0.0f, 0.0f, 0.0f, 1.0f);
    material.m_edgeSize = 1.0f;

    // Set texture indices
    material.m_textureIndex = -1;
    material.m_sphereTextureIndex = -1;
    material.m_sphereMode = PMXSphereMode::None;

    // Set toon properties
    material.m_toonMode = PMXToonMode::Common;
    material.m_toonTextureIndex = 0;
 
    // Set face count
    material.m_numFaceVertices = faceCount * 3;
	// material.m_memo = R"~({"rainbow":1 })~";
    m_pmxFile.m_materials.push_back(material);
    return (int32_t)(m_pmxFile.m_materials.size() - 1);
}

int32_t PMXFile_Generator::createDefaultMaterial(int32_t faceCount)
{
    return addMaterial("Material", glm::vec4(0.8f, 0.8f, 0.8f, 1.0f), faceCount);
}

int32_t PMXFile_Generator::addBone(const std::string& name, const glm::vec3& position, int32_t parentBoneIndex, uint16_t boneFlags, bool isChainBone, int32_t nextBoneIndex)
{
    PMXBone bone;
    bone.m_name = name;
    bone.m_englishName = name;
    bone.m_nameU = ToWString(name);
    bone.m_position = position;
    bone.m_parentBoneIndex = parentBoneIndex;
    bone.m_deformDepth = 0;
    bone.m_boneFlag = (PMXBoneFlags)boneFlags;
	bone.m_positionOffset = glm::vec3(0.0f);

    // If this is a chain bone, link it to the next bone
    if (isChainBone && nextBoneIndex >= 0) {
		bone.m_boneFlag = (PMXBoneFlags)((uint16_t)bone.m_boneFlag & ~(uint16_t)PMXBoneFlags::TargetShowMode);
        bone.m_linkBoneIndex = nextBoneIndex;
    } else {
        bone.m_boneFlag = (PMXBoneFlags)((uint16_t)bone.m_boneFlag | (uint16_t)PMXBoneFlags::TargetShowMode);

        bone.m_linkBoneIndex = -1;
    }

    // If no parent, set as root bone
    if (parentBoneIndex < 0) {
        bone.m_boneFlag = (PMXBoneFlags)(
            (uint16_t)PMXBoneFlags::AllowRotate |
            (uint16_t)PMXBoneFlags::AllowTranslate |
            (uint16_t)PMXBoneFlags::Visible |
            (uint16_t)PMXBoneFlags::AllowControl
        );
    }

    m_pmxFile.m_bones.push_back(bone);
    return (int32_t)(m_pmxFile.m_bones.size() - 1);
}

int32_t PMXFile_Generator::addRigidBody(const RigidBodyDef& rbDef)
{
    PMXRigidbody rb;
    rb.m_name = rbDef.name;
    rb.m_englishName = rbDef.engName;
    rb.m_boneIndex = rbDef.boneIndex;
    rb.m_group = rbDef.group;
    rb.m_collideMask32 = rbDef.collisionMask;
    rb.m_shape = rbDef.shape;
    rb.m_shapeSize = rbDef.size *0.5f ;
    rb.m_translate = rbDef.position;
    rb.m_rotate = rbDef.rotation;
    rb.m_mass = rbDef.mass;
    rb.m_translateDimmer = rbDef.linearDamping;
    rb.m_rotateDimmer = rbDef.angularDamping;
    rb.m_repulsion = rbDef.restitution;
    rb.m_friction = rbDef.friction;
    rb.m_op = rbDef.operation;

    m_pmxFile.m_rigidbodies.push_back(rb);
    return (int32_t)(m_pmxFile.m_rigidbodies.size() - 1);
}

int32_t PMXFile_Generator::addJoint(const JointDef& jointDef)
{
    PMXJoint joint;
    joint.m_name = jointDef.name;
    joint.m_englishName = jointDef.name;
    joint.m_type = jointDef.type;
    joint.m_rigidbodyAIndex = jointDef.rigidBodyAIndex;
    joint.m_rigidbodyBIndex = jointDef.rigidBodyBIndex;
    joint.translate = jointDef.position;
    joint.rotate = jointDef.rotation;
    joint.limitMinT = jointDef.linearLimitMin;
    joint.limitMaxT = jointDef.linearLimitMax;
    joint.limitMinR = jointDef.angularLimitMin;
    joint.limitMaxR = jointDef.angularLimitMax;
    joint.springT = jointDef.springLinear;
    joint.springR = jointDef.springAngular;

    m_pmxFile.m_joints.push_back(joint);
    return (int32_t)(m_pmxFile.m_joints.size() - 1);
}

void PMXFile_Generator::createBoxMesh(const glm::vec3& size, const glm::vec3& position, const glm::vec3& rotation)
{
    // Create transformation matrix
    glm::mat4 transform = glm::translate(glm::mat4(1.0f), position);
    transform = transform * glm::eulerAngleXYZ(rotation.x, rotation.y, rotation.z);

    // Half sizes
    float w = size.x * 0.5f;
    float h = size.y * 0.5f;
    float d = size.z * 0.5f;

    // Vertices for a box (8 corners)
    glm::vec3 v[8] = {
        glm::vec3(-w, -h, -d), // 0: left bottom back
        glm::vec3( w, -h, -d), // 1: right bottom back
        glm::vec3( w,  h, -d), // 2: right top back
        glm::vec3(-w,  h, -d), // 3: left top back
        glm::vec3(-w, -h,  d), // 4: left bottom front
        glm::vec3( w, -h,  d), // 5: right bottom front
        glm::vec3( w,  h,  d), // 6: right top front
        glm::vec3(-w,  h,  d)  // 7: left top front
    };

    // Normals for each face
    glm::vec3 normals[6] = {
        glm::vec3( 0.0f,  0.0f, -1.0f), // back
        glm::vec3( 0.0f,  0.0f,  1.0f), // front
        glm::vec3( 1.0f,  0.0f,  0.0f), // right
        glm::vec3(-1.0f,  0.0f,  0.0f), // left
        glm::vec3( 0.0f,  1.0f,  0.0f), // top
        glm::vec3( 0.0f, -1.0f,  0.0f)  // bottom
    };

    // UVs for each vertex of each face
    glm::vec2 uvs[4] = {
        glm::vec2(0.0f, 1.0f), // bottom left
        glm::vec2(1.0f, 1.0f), // bottom right
        glm::vec2(1.0f, 0.0f), // top right
        glm::vec2(0.0f, 0.0f)  // top left
    };

    // Transform vertices
    for (int i = 0; i < 8; i++) {
        glm::vec4 transformedVertex = transform * glm::vec4(v[i], 1.0f);
        v[i] = glm::vec3(transformedVertex);
    }

    // Transform normals (only rotation)
    glm::mat3 normalTransform = glm::mat3(transform);
    for (int i = 0; i < 6; i++) {
        normals[i] = normalTransform * normals[i];
    }

    // Store starting vertex index
    uint32_t baseIndex = (uint32_t)m_pmxFile.m_vertices.size();

    // Add vertices for each face (4 vertices per face, 6 faces)
    // Back face (0,1,2,3)
    addVertex(v[0], normals[0], uvs[0]);
    addVertex(v[1], normals[0], uvs[1]);
    addVertex(v[2], normals[0], uvs[2]);
    addVertex(v[3], normals[0], uvs[3]);

    // Front face (4,5,6,7)
    addVertex(v[4], normals[1], uvs[0]);
    addVertex(v[5], normals[1], uvs[1]);
    addVertex(v[6], normals[1], uvs[2]);
    addVertex(v[7], normals[1], uvs[3]);

    // Right face (1,5,6,2)
    addVertex(v[1], normals[2], uvs[0]);
    addVertex(v[5], normals[2], uvs[1]);
    addVertex(v[6], normals[2], uvs[2]);
    addVertex(v[2], normals[2], uvs[3]);

    // Left face (4,0,3,7)
    addVertex(v[4], normals[3], uvs[0]);
    addVertex(v[0], normals[3], uvs[1]);
    addVertex(v[3], normals[3], uvs[2]);
    addVertex(v[7], normals[3], uvs[3]);

    // Top face (3,2,6,7)
    addVertex(v[3], normals[4], uvs[0]);
    addVertex(v[2], normals[4], uvs[1]);
    addVertex(v[6], normals[4], uvs[2]);
    addVertex(v[7], normals[4], uvs[3]);

    // Bottom face (0,4,5,1)
    addVertex(v[0], normals[5], uvs[0]);
    addVertex(v[4], normals[5], uvs[1]);
    addVertex(v[5], normals[5], uvs[2]);
    addVertex(v[1], normals[5], uvs[3]);

    // Add triangles for each face (2 triangles per face, 6 faces)
    // Back face
    addFace(baseIndex + 0, baseIndex + 2, baseIndex + 1);
    addFace(baseIndex + 0, baseIndex + 3, baseIndex + 2);

    // Front face (fix winding order)
    addFace(baseIndex + 4, baseIndex + 5, baseIndex + 6);
    addFace(baseIndex + 4, baseIndex + 6, baseIndex + 7);

    // Right face
    addFace(baseIndex + 8, baseIndex + 10, baseIndex + 9);
    addFace(baseIndex + 8, baseIndex + 11, baseIndex + 10);

    // Left face
    addFace(baseIndex + 12, baseIndex + 14, baseIndex + 13);
    addFace(baseIndex + 12, baseIndex + 15, baseIndex + 14);

    // Top face
    addFace(baseIndex + 16, baseIndex + 18, baseIndex + 17);
    addFace(baseIndex + 16, baseIndex + 19, baseIndex + 18);

    // Bottom face
    addFace(baseIndex + 20, baseIndex + 22, baseIndex + 21);
    addFace(baseIndex + 20, baseIndex + 23, baseIndex + 22);
}

void PMXFile_Generator::createChainLinkMesh(float radius, float length, const glm::vec3& position, const glm::vec3& rotation, int32_t boneIndex)
{
    // Create transformation matrix
    glm::mat4 transform = glm::translate(glm::mat4(1.0f), position);
    // Apply rotation before any other transformations
    transform = transform * glm::eulerAngleXYZ(rotation.x, rotation.y, rotation.z);

    // Parameters for the chain link
    const int ringSegments = 18; // Number of segments around the main ring
    const int tubeSegments = 8; // Number of segments around the tube cross-section
    const float tubeRadius = radius * 0.2f; // Radius of the tube
    const float halfLength = length * 0.5f;

    // Generate vertices for the chain link (a torus-like shape)
    std::vector<glm::vec3> positions;
    std::vector<glm::vec3> normals;
    std::vector<glm::vec2> uvs;

    // First create all vertices in local space
    for (int i = 0; i < ringSegments; i++) {  // Note: <= to ensure proper closed loop
		bool upperHalf = (i  < ringSegments/4.f-0.00001f || i>ringSegments * 0.75f+0.00001f);
        float angle = (float)i / ringSegments * glm::two_pi<float>();

        // Calculate positions on the main circle in YZ plane (so it points along X axis)
        float y = radius * cos(angle)+(upperHalf?length/4:-length/4);
        float z = radius * sin(angle);

        // Create a circle around the current point on the main circle
        for (int j = 0; j <= tubeSegments; j++) {  // Note: <= to ensure proper texture wrapping
            float tubeAngle = (float)j / tubeSegments * glm::two_pi<float>();

            // Calculate directions for the tube cross-section
            glm::vec3 mainTangent(0.0f, -sin(angle), cos(angle));
            glm::vec3 normal(1.0f, 0.0f, 0.0f);
            glm::vec3 binormal = glm::cross(mainTangent, normal);
            normal = glm::normalize(glm::cross(binormal, mainTangent));
            binormal = glm::normalize(binormal);

            // Calculate positions on the tube
            glm::vec3 tubeOffset = normal * tubeRadius * cos(tubeAngle) +
                                  binormal * tubeRadius * sin(tubeAngle);

            // Final vertex position
            glm::vec3 pos(0.0f, y, z);
            pos += tubeOffset;

            // Normal points from tube center outward
            glm::vec3 vertexNormal = glm::normalize(tubeOffset);

            // Add to local arrays
            positions.push_back(pos);
            normals.push_back(vertexNormal);
            uvs.push_back(glm::vec2((float)i/ringSegments, (float)j/tubeSegments));
        }
    }

    // Now create all faces with proper indexing
    uint32_t numVerticesPerRing = tubeSegments + 1;
    uint32_t baseIndex = (uint32_t)m_pmxFile.m_vertices.size();

    // First, add all vertices to the PMX file
    for (size_t i = 0; i < positions.size(); i++) {
        // Transform vertex and normal to world space
        glm::vec4 transformedPos = transform * glm::vec4(positions[i], 1.0f);
        glm::mat3 normalMatrix = glm::mat3(transform);
        glm::vec3 transformedNormal = normalMatrix * normals[i];

        // Create the vertex with proper bone binding
        PMXVertex vertex;
        vertex.m_position = glm::vec3(transformedPos);
        vertex.m_normal = glm::normalize(transformedNormal);
        vertex.m_uv = uvs[i];

        // Set bone weights to the specific bone for this chain link
        vertex.m_weightType = PMXVertexWeight::BDEF1;
        vertex.m_boneIndices[0] = boneIndex;
        vertex.m_boneIndices[1] = 0;
        vertex.m_boneIndices[2] = 0;
        vertex.m_boneIndices[3] = 0;
        vertex.m_boneWeights[0] = 1.0f;
        vertex.m_boneWeights[1] = 0.0f;
        vertex.m_boneWeights[2] = 0.0f;
        vertex.m_boneWeights[3] = 0.0f;
        vertex.m_edgeMag = 1.0f;

        m_pmxFile.m_vertices.push_back(vertex);
    }

    // Then create faces connecting the vertices
    for (int i = 0; i < ringSegments; i++) {  // Note: < ringSegments because we're connecting to i+1
        for (int j = 0; j < tubeSegments; j++) {  // Note: < tubeSegments because we're connecting to j+1
            // Calculate the four corners of each quad
            uint32_t v0 = baseIndex + i * numVerticesPerRing + j;
            uint32_t v1 = baseIndex + i * numVerticesPerRing + (j + 1);
            uint32_t v2 = baseIndex + ((i + 1) % ringSegments) * numVerticesPerRing + (j + 1);
            uint32_t v3 = baseIndex + ((i + 1) % ringSegments) * numVerticesPerRing + j;

            // Add two triangles for each quad
            addFace(v0, v1, v2);
            addFace(v0, v2, v3);
        }
    }
}

void PMXFile_Generator::createBox(const Gpm_BoxShape& boxDef)
{
    // Initialize with default settings
    initialize("Box", "Generated box model");

    // Create box mesh
    createBoxMesh(boxDef.size, boxDef.position, boxDef.rotation);

    // Create material
    createDefaultMaterial(m_currentMaterialFaceCount);
    m_currentMaterialFaceCount = 0;

    // Create bone at the center of the box
    int32_t boneIndex = addBone("Center", boxDef.position, 0,
        (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | PMXBoneFlags::Visible),
        false, -1);

    // Create rigid body
    RigidBodyDef rbDef;
    rbDef.name = "BoxBody";
    rbDef.shape = PMXRigidbody::Shape::Box;
    rbDef.size = boxDef.size;
    rbDef.position = boxDef.position;
    rbDef.rotation = boxDef.rotation;
    rbDef.boneIndex = boneIndex;
    rbDef.operation = PMXRigidbody::Operation::Static; // Static by default

    addRigidBody(rbDef);
}

void PMXFile_Generator::createChain(const std::vector<Gpm_ChainLink>& links,bool selfCollision)
{
    // Initialize with default settings
    initialize("Chain", "Generated chain model");


    // Create chain links
    for (size_t i = 0; i < links.size(); i++) {
        const Gpm_ChainLink& link = links[i];
        createChainLinkMesh(link.radius/2, link.length, link.position, link.rotation, i);
    }

    // Create material
    createDefaultMaterial(m_currentMaterialFaceCount);
    m_currentMaterialFaceCount = 0;

    // Create bones and rigid bodies for each link
    std::vector<int32_t> boneIndices;
    std::vector<int32_t> rbIndices;

    for (size_t i = 0; i < links.size(); i++) {
        const Gpm_ChainLink& link = links[i];

        // Create bone
        int32_t parentIndex = (i == 0) ? 0 : boneIndices[i - 1];
        int32_t nextBoneIndex = (i < links.size() - 1) ? -1 : -1; // Will be set after all bones are created
        int32_t boneIndex = addBone("Link_" + std::to_string(i), link.position, parentIndex,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | PMXBoneFlags::Visible),
            true, nextBoneIndex); // Mark as chain bone, but nextBoneIndex will be updated later
        boneIndices.push_back(boneIndex);

        // Create rigid body
        RigidBodyDef rbDef;
        rbDef.name = "LinkBody_" + std::to_string(i);
        rbDef.shape = PMXRigidbody::Shape::Capsule;
        rbDef.size = glm::vec3(link.radius  , link.length *(selfCollision?0.8f:1.f)* 2.0f-link.radius*2.f,1); // 200% of mesh size
        rbDef.position = link.position;
        // Add 90 degrees rotation around Z axis to fix the orientation
        glm::vec3 fixedRotation = link.rotation + glm::vec3(0, 0.0f, 0);
        rbDef.rotation = fixedRotation;
        rbDef.boneIndex = boneIndex;
        rbDef.operation =// (i == 0) ? PMXRigidbody::Operation::Static :
            PMXRigidbody::Operation::Dynamic;
        rbDef.engName = selfCollision?"$`{\"noIgGrp\":1}": rbDef.name;
        int32_t rbIndex = addRigidBody(rbDef);
        rbIndices.push_back(rbIndex);
    }

    // Update the nextBoneIndex for chain bones
    for (size_t i = 0; i < links.size() - 1; i++) {
        // Get the current bone and update its linkBoneIndex to point to the next bone
        PMXBone& bone = m_pmxFile.m_bones[boneIndices[i]];
        bone.m_linkBoneIndex = boneIndices[i + 1];
    }

    // Create joints between links
    for (size_t i = 1; i < links.size(); i++) {
        const Gpm_ChainLink& link = links[i];
        const Gpm_ChainLink& prevLink = links[i - 1];

        // Calculate joint position (between links)
        glm::vec3 jointPos = (prevLink.position + link.position) * 0.5f;

        // Create joint
        JointDef jointDef;
        jointDef.name = "Joint_" + std::to_string(i - 1) + "_" + std::to_string(i);
        jointDef.type = PMXJoint::JointType::SpringDOF6;
        jointDef.rigidBodyAIndex = rbIndices[i - 1];
        jointDef.rigidBodyBIndex = rbIndices[i];
        jointDef.position = jointPos;
        jointDef.linearLimitMin = glm::vec3(-0.1f);
        jointDef.linearLimitMax = glm::vec3(0.1f);
        jointDef.angularLimitMin = glm::vec3(-glm::pi<float>() / 4);
        jointDef.angularLimitMax = glm::vec3(glm::pi<float>() / 4);
        jointDef.springLinear = glm::vec3(100.0f);
        jointDef.springAngular = glm::vec3(100.0f);

        addJoint(jointDef);
    }
}

void PMXFile_Generator::createCylinderMesh(float radius, float length, const glm::vec3& position, const glm::vec3& rotation, int32_t boneIndex,
    int32_t prevBoneIndex, int32_t nextBoneIndex,   int boneMeshSegments)
{
    // Create transformation matrix
    glm::mat4 transform = glm::translate(glm::mat4(1.0f), position);
    // Apply rotation before any other transformations
    transform = transform * glm::eulerAngleXYZ(rotation.x, rotation.y, rotation.z);

    // Parameters for the cylinder
    const int circleSegments = 8; // Number of segments around the circle
    const float halfLength = length * 0.5f;

    // Ensure boneMeshSegments is at least 1
    boneMeshSegments = std::max(1, boneMeshSegments);

    // Generate vertices for the cylinder
    std::vector<glm::vec3> positions;
    std::vector<glm::vec3> normals;
    std::vector<glm::vec2> uvs;
    std::vector<float> boneWeights; // To store bone weights for each vertex

    // Create cap vertices if needed
    uint32_t baseIndex = (uint32_t)m_pmxFile.m_vertices.size();
    uint32_t topCapStartIndex = baseIndex;
    uint32_t bottomCapStartIndex = 0;
    uint32_t sideStartIndex = baseIndex;
    int capsVertexCount = 1 + circleSegments;

    if (prevBoneIndex<0) {
        // Bottom cap (center + circle vertices)
        positions.push_back(glm::vec3(0.0f, -halfLength, 0.0f)); // Center vertex
        normals.push_back(glm::vec3(0.0f, -1.0f, 0.0f));
        uvs.push_back(glm::vec2(0.5f, 0.5f));
        boneWeights.push_back(0.0f); // Bottom cap = 0.0 weight (50% prev bone, 50% current bone)

        for (int i = 0; i < circleSegments; i++) {
            float angle = (float)i / circleSegments * glm::two_pi<float>();
            float x = radius * cos(angle);
            float z = radius * sin(angle);

            positions.push_back(glm::vec3(x, -halfLength, z));
            normals.push_back(glm::vec3(0.0f, -1.0f, 0.0f));
            uvs.push_back(glm::vec2(0.5f + 0.5f * cos(angle), 0.5f + 0.5f * sin(angle)));
            boneWeights.push_back(0.0f); // Bottom cap = 0.0 weight (50% prev bone, 50% current bone)
        }
        bottomCapStartIndex = sideStartIndex; 
        sideStartIndex += capsVertexCount;
    }

	if (nextBoneIndex<0) {
        // Top cap (center + circle vertices)
        positions.push_back(glm::vec3(0.0f, halfLength, 0.0f)); // Center vertex
        normals.push_back(glm::vec3(0.0f, 1.0f, 0.0f));
        uvs.push_back(glm::vec2(0.5f, 0.5f));
        boneWeights.push_back(1.0f); // Top cap = 1.0 weight (50% current bone, 50% next bone)

        for (int i = 0; i < circleSegments; i++) {
            float angle = (float)i / circleSegments * glm::two_pi<float>();
            float x = radius * cos(angle);
            float z = radius * sin(angle);

            positions.push_back(glm::vec3(x, halfLength, z));
            normals.push_back(glm::vec3(0.0f, 1.0f, 0.0f));
            uvs.push_back(glm::vec2(0.5f + 0.5f * cos(angle), 0.5f + 0.5f * sin(angle)));
            boneWeights.push_back(1.0f); // Top cap = 1.0 weight (50% current bone, 50% next bone)
        }

        
        topCapStartIndex = sideStartIndex; 
        sideStartIndex += capsVertexCount;
    } 

    // Create side vertices with proper segmentation
    for (int segment = 0; segment <= boneMeshSegments; segment++) {
        // Calculate Y position for this segment
        float t = (float)segment / boneMeshSegments;
        float y = -halfLength + t * (2 * halfLength);

        // Calculate weight for this segment (0 at bottom, 1 at top)
        float weight = t;
        
		auto rmulFunc = [=](float x) { 
           // float piCount = 2, sinRat = 0.9f; return (sin((fmod(x, boneMeshSegments) / boneMeshSegments) * PI * piCount)*0.5f+0.5f) * sinRat + (1 - sinRat);
            float piCount = 3, sinRat = 0.9f; return abs(sin((fmod(x, boneMeshSegments) / boneMeshSegments) * PI * piCount)) * sinRat + (1 - sinRat);
            };
        float rmul = rmulFunc(segment);

        // Create vertices for this circle
        for (int i = 0; i < circleSegments; i++) {
            float angle = (float)i / circleSegments * glm::two_pi<float>();
            float x = radius * cos(angle) * rmul;
            float z = radius * sin(angle) * rmul;

            // Add vertex position
            positions.push_back(glm::vec3(x, y, z));
#if 0
            // Normal points outward from center
            glm::vec3 normal = glm::normalize(glm::vec3(x, 0.0f, z));
#else

            // Normal points outward from center, adjusted for the radius modifier
            glm::vec3 normal = glm::normalize(glm::vec3(
                cos(angle) * rmul,  // X component 
                0.0f,               // Y component (will be adjusted below)
                sin(angle) * rmul   // Z component
            ));
            
            // If this is not the first or last segment, calculate Y component based on radius change
            if (segment > 0 && segment < boneMeshSegments) {
                // Calculate radius change between adjacent segments for slope
                float prevRmul = rmulFunc(segment - 1);
				float nextRmul = rmulFunc(segment + 1);
                
                // Calculate Y component based on change in radius (determines surface slope)
                float yComponent = (prevRmul - nextRmul) * 0.5f;
                normal.y = yComponent;
                normal = glm::normalize(normal);
            }
            else normal = glm::normalize(glm::vec3(x, 0.0f, z));
    #endif
            normals.push_back(normal);

            // UV coordinates
            uvs.push_back(glm::vec2((float)i / circleSegments, t));

            // Bone weight based on position along cylinder
            boneWeights.push_back(weight);
        }
    }

    // Add all vertices to the PMX file
    for (size_t i = 0; i < positions.size(); i++) {
        // Transform vertex and normal to world space
        glm::vec4 transformedPos = transform * glm::vec4(positions[i], 1.0f);
        glm::mat3 normalMatrix = glm::mat3(transform);
        glm::vec3 transformedNormal = normalMatrix * normals[i];

        // Create the vertex with proper bone binding
        PMXVertex vertex;
        vertex.m_position = glm::vec3(transformedPos);
        vertex.m_normal = glm::normalize(transformedNormal);
        vertex.m_uv = uvs[i];

        // Set bone weights
        vertex.m_weightType = PMXVertexWeight::BDEF2;

        // Set bone indices and weights to create a smooth transition from (prev+cur)/2 to (cur+next)/2
        if (prevBoneIndex >= 0 && nextBoneIndex >= 0) {
            // We have both previous and next bones
            if (boneWeights[i] < 0.5f) {
                // First half: blend between previous and current bone
                vertex.m_boneIndices[0] = prevBoneIndex;
                vertex.m_boneIndices[1] = boneIndex;

                // At boneWeights[i] = 0.0, we want 50% prev, 50% current
                // At boneWeights[i] = 0.5, we want 0% prev, 100% current
                // So we remap [0.0, 0.5] to [0.5, 0.0] for prev bone weight
                float prevWeight = 0.5f - boneWeights[i];
                vertex.m_boneWeights[0] = prevWeight;        // Weight for prev bone
                vertex.m_boneWeights[1] = 1.0f - prevWeight; // Weight for current bone
            } else {
                // Second half: blend between current and next bone
                vertex.m_boneIndices[0] = boneIndex;
                vertex.m_boneIndices[1] = nextBoneIndex;

                // At boneWeights[i] = 0.5, we want 100% current, 0% next
                // At boneWeights[i] = 1.0, we want 50% current, 50% next
                // So we remap [0.5, 1.0] to [0.0, 0.5] for next bone weight
                float nextWeight = boneWeights[i] - 0.5f;
                vertex.m_boneWeights[0] = 1.0f - nextWeight; // Weight for current bone
                vertex.m_boneWeights[1] = nextWeight;        // Weight for next bone
            }
        } else if (prevBoneIndex >= 0) {
            // We only have previous bone, no next bone
            vertex.m_boneIndices[0] = prevBoneIndex;
            vertex.m_boneIndices[1] = boneIndex;

            // Remap [0.0, 1.0] to [0.5, 0.0] for prev bone weight
            float prevWeight = 0.5f * (1.0f - boneWeights[i]);
            vertex.m_boneWeights[0] = prevWeight;        // Weight for prev bone
            vertex.m_boneWeights[1] = 1.0f - prevWeight; // Weight for current bone
        } else if (nextBoneIndex >= 0) {
            // We only have next bone, no previous bone
            vertex.m_boneIndices[0] = boneIndex;
            vertex.m_boneIndices[1] = nextBoneIndex;

            // Remap [0.0, 1.0] to [1.0, 0.5] for current bone weight
            float nextWeight = 0.5f * boneWeights[i];
            vertex.m_boneWeights[0] = 1.0f - nextWeight; // Weight for current bone
            vertex.m_boneWeights[1] = nextWeight;        // Weight for next bone
        } else {
            // No previous or next bone, just use current bone twice
            vertex.m_boneIndices[0] = boneIndex;
            vertex.m_boneIndices[1] = boneIndex;
            vertex.m_boneWeights[0] = 1.0f;
            vertex.m_boneWeights[1] = 0.0f;
        }

        vertex.m_edgeMag = 1.0f;
        m_pmxFile.m_vertices.push_back(vertex);
    }

    // Add faces for caps if needed
    
        // Bottom cap faces
        if (prevBoneIndex < 0) 
            for (int i = 0; i < circleSegments; i++) {
            int current = bottomCapStartIndex + 1 + i;
            int next = bottomCapStartIndex + 1 + ((i + 1) % circleSegments);
            addFace(bottomCapStartIndex, current, next); // Note: reversed winding for bottom face
        }

        // Top cap faces
            if (nextBoneIndex < 0)  for (int i = 0; i < circleSegments; i++) {
            int current = topCapStartIndex + 1 + i;
            int next = topCapStartIndex + 1 + ((i + 1) % circleSegments);
            addFace(topCapStartIndex, next, current);
        }
    

    // Add faces for the sides
    for (int segment = 0; segment < boneMeshSegments; segment++) {
        int bottomRowStart = sideStartIndex + segment * circleSegments;
        int topRowStart = sideStartIndex + (segment + 1) * circleSegments;

        for (int i = 0; i < circleSegments; i++) {
            int nextI = (i + 1) % circleSegments;

            // Bottom-left vertex
            int bl = bottomRowStart + i;
            // Bottom-right vertex
            int br = bottomRowStart + nextI;
            // Top-left vertex
            int tl = topRowStart + i;
            // Top-right vertex
            int tr = topRowStart + nextI;

            // Add two triangles to form a quad
            addFace(bl, tr, br);
            addFace(bl, tl, tr);
        }
    }
}

void PMXFile_Generator::createRope(const std::vector<Gpm_ChainLink>& links, bool selfCollision, int boneMeshSegments, bool connectBoneMesh)
{
    // Initialize with default settings
    initialize("Rope", "Generated rope model");

    if (!connectBoneMesh)
        // Create separate rope segments
        for (size_t i = 0; i < links.size(); i++) {
            const Gpm_ChainLink& link = links[i];
            int32_t nextBoneIndex = (i < links.size() - 1) ? i + 1 : -1;
            int32_t prevBoneIndex = (i > 0) ? i - 1 : -1;
            createCylinderMesh(link.radius *pow(gPmxGenPm.chainMassScale, i), link.length, link.position, link.rotation, i, prevBoneIndex, nextBoneIndex, boneMeshSegments);
        }
    else //connectBoneMesh
    {
        // Create connected rope segments
        const int circleSegments = 8; // Must match the value in createCylinderMesh
        std::vector<uint32_t> lastCircleIndices;
        
        // Process first link normally to establish the base
        if (!links.empty()) {
            const Gpm_ChainLink& firstLink = links[0];
            // Create first segment with caps as needed
            int32_t nextBoneIndex = (links.size() > 1) ? 1 : -1;
            createCylinderMesh(
                firstLink.radius  ,
                firstLink.length, 
                firstLink.position, 
                firstLink.rotation, 
                0, -1, nextBoneIndex, 
                boneMeshSegments
            );
            
            // Store indices of the last circle vertices (top circle)
            uint32_t vertexCount = m_pmxFile.m_vertices.size();
            for (int i = 0; i < circleSegments; i++) {
                // Calculate index of top circle vertices (last ring of vertices)
                // Top ring is at the end of the vertex list, before any cap vertices
                lastCircleIndices.push_back(vertexCount - circleSegments + i);
            }
            
            // Create subsequent links with connected meshes
            for (size_t i = 1; i < links.size(); i++) {
                const Gpm_ChainLink& link = links[i];
                float radius = link.radius * pow(gPmxGenPm.chainMassScale,i);
                int32_t prevBoneIndex = i - 1;
                int32_t nextBoneIndex = (i < links.size() - 1) ? i + 1 : -1;
                
                // Create transformation matrix for this segment
                glm::mat4 transform = glm::translate(glm::mat4(1.0f), link.position);
                transform = transform * glm::eulerAngleXYZ(link.rotation.x, link.rotation.y, link.rotation.z);
                
                // Generate new vertices for the sides (excluding bottom circle)
                uint32_t baseIndex = (uint32_t)m_pmxFile.m_vertices.size();
                
                // Skip bottom cap and first circle - we'll reuse lastCircleIndices
                
                // Create side vertices with proper segmentation (starting from 1 to skip bottom circle)
                for (int segment = 1; segment <= boneMeshSegments; segment++) {
                    float t = (float)segment / boneMeshSegments;
                    float y = -link.length * 0.5f + t * link.length;
                    float weight = t;
                    
                    // Create vertices for this circle
                    for (int j = 0; j < circleSegments; j++) {
                        float angle = (float)j / circleSegments * glm::two_pi<float>();
                        float x = radius * cos(angle);
                        float z = radius * sin(angle);
                        
                        // Add transformed vertex
                        glm::vec4 pos = transform * glm::vec4(x, y, z, 1.0f);
                        glm::vec3 normal = glm::normalize(glm::vec3(x, 0.0f, z));
                        glm::mat3 normalMatrix = glm::mat3(transform);
                        glm::vec3 transformedNormal = normalMatrix * normal;
                        
                        // Create the vertex with proper bone binding
                        PMXVertex vertex;
                        vertex.m_position = glm::vec3(pos);
                        vertex.m_normal = glm::normalize(transformedNormal);
                        vertex.m_uv = glm::vec2((float)j / circleSegments, t);
                        
                        // Set bone weights
                        vertex.m_weightType = PMXVertexWeight::BDEF2;
                        
                        // Weight between previous and current bones
                        vertex.m_boneIndices[0] = prevBoneIndex;
                        vertex.m_boneIndices[1] = i;
                        vertex.m_boneWeights[0] = 1.0f - weight;
                        vertex.m_boneWeights[1] = weight;
                        
                        vertex.m_edgeMag = 1.0f;
                        m_pmxFile.m_vertices.push_back(vertex);
                    }
                }
                
                // Add top cap if this is the last segment
                if (nextBoneIndex < 0) {
                    // Top center vertex
                    glm::vec4 centerPos = transform * glm::vec4(0.0f, link.length * 0.5f, 0.0f, 1.0f);
                    PMXVertex centerVertex;
                    centerVertex.m_position = glm::vec3(centerPos);
                    centerVertex.m_normal = glm::normalize(glm::mat3(transform) * glm::vec3(0.0f, 1.0f, 0.0f));
                    centerVertex.m_uv = glm::vec2(0.5f, 0.5f);
                    centerVertex.m_weightType = PMXVertexWeight::BDEF1;
                    centerVertex.m_boneIndices[0] = i;
                    centerVertex.m_boneWeights[0] = 1.0f;
                    centerVertex.m_edgeMag = 1.0f;
                    
                    uint32_t topCenterIndex = m_pmxFile.m_vertices.size();
                    m_pmxFile.m_vertices.push_back(centerVertex);
                    
                    // Add top cap faces
                    uint32_t lastRingStart = baseIndex + (boneMeshSegments - 1) * circleSegments;
                    for (int j = 0; j < circleSegments; j++) {
                        uint32_t current = lastRingStart + j;
                        uint32_t next = lastRingStart + ((j + 1) % circleSegments);
                        addFace(topCenterIndex, next, current);
                    }
                }
                
                // Add side faces connecting circles
                // First connect the reused bottom circle (lastCircleIndices) to the first new circle
                for (int j = 0; j < circleSegments; j++) {
                    uint32_t bl = lastCircleIndices[j];
                    uint32_t br = lastCircleIndices[(j + 1) % circleSegments];
                    uint32_t tl = baseIndex + j;
                    uint32_t tr = baseIndex + ((j + 1) % circleSegments);
                    
                    // Add two triangles to form a quad
                    addFace(bl, tr, br);
                    addFace(bl, tl, tr);
                }
                
                // Then connect the rest of the circles
                for (int segment = 1; segment < boneMeshSegments; segment++) {
                    uint32_t bottomRowStart = baseIndex + (segment - 1) * circleSegments;
                    uint32_t topRowStart = baseIndex + segment * circleSegments;
                    
                    for (int j = 0; j < circleSegments; j++) {
                        uint32_t bl = bottomRowStart + j;
                        uint32_t br = bottomRowStart + ((j + 1) % circleSegments);
                        uint32_t tl = topRowStart + j;
                        uint32_t tr = topRowStart + ((j + 1) % circleSegments);
                        
                        // Add two triangles to form a quad
                        addFace(bl, tr, br);
                        addFace(bl, tl, tr);
                    }
                }
                
                // Update lastCircleIndices for the next segment
                lastCircleIndices.clear();
                uint32_t lastRingStart = baseIndex + (boneMeshSegments - 1) * circleSegments;
                for (int j = 0; j < circleSegments; j++) {
                    lastCircleIndices.push_back(lastRingStart + j);
                }
            }
        }
    }

    // Create material
    createDefaultMaterial(m_currentMaterialFaceCount);
    m_currentMaterialFaceCount = 0;

    // Create bones and rigid bodies for each segment
    std::vector<int32_t> boneIndices;
    std::vector<int32_t> rbIndices;

    for (size_t i = 0; i < links.size(); i++) {
        const Gpm_ChainLink& link = links[i];

        // Create bone
        int32_t parentIndex = (i == 0) ? 0 : boneIndices[i - 1];
        int32_t nextBoneIndex = (i < links.size() - 1) ? -1 : -1; // Will be set after all bones are created
        int32_t boneIndex = addBone("Rope_" + std::to_string(i), link.position, parentIndex,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | PMXBoneFlags::Visible),
            true, nextBoneIndex); // Mark as chain bone, but nextBoneIndex will be updated later
        boneIndices.push_back(boneIndex);

        // Create rigid body
        RigidBodyDef rbDef;
        rbDef.name = "RopeBody_" + std::to_string(i);
        rbDef.shape = PMXRigidbody::Shape::Capsule;
        rbDef.size = glm::vec3(link.radius * 2.0f, link.length * 2.0f, 1.0f); // 200% of mesh size
        rbDef.position = link.position;
        rbDef.rotation = link.rotation;
        rbDef.friction = 0.9f;
        rbDef.boneIndex = boneIndex;
        rbDef.operation = PMXRigidbody::Operation::Dynamic;
        rbDef.engName = selfCollision ? "$`{\"noIgGrp\":1}" : rbDef.name;
         
        rbDef.mass = 1.f * pow(gPmxGenPm.chainMassScale, i);
        int32_t rbIndex = addRigidBody(rbDef);
        rbIndices.push_back(rbIndex);
    }

    // Update the nextBoneIndex for chain bones
    for (size_t i = 0; i < links.size() - 1; i++) {
        // Get the current bone and update its linkBoneIndex to point to the next bone
        PMXBone& bone = m_pmxFile.m_bones[boneIndices[i]];
        bone.m_linkBoneIndex = boneIndices[i + 1];
    }

    // Create joints between segments
    for (size_t i = 1; i < links.size(); i++) {
        const Gpm_ChainLink& link = links[i];
        const Gpm_ChainLink& prevLink = links[i - 1];

        // Calculate joint position (between links)
        glm::vec3 jointPos = (prevLink.position + link.position) * 0.5f;

        // Create joint
        JointDef jointDef;
        jointDef.name = "Joint_" + std::to_string(i - 1) + "_" + std::to_string(i);
        jointDef.type = PMXJoint::JointType::SpringDOF6;
        jointDef.rigidBodyAIndex = rbIndices[i - 1];
        jointDef.rigidBodyBIndex = rbIndices[i];
        jointDef.position = jointPos;
        jointDef.linearLimitMin = glm::vec3(-0.1f);
        jointDef.linearLimitMax = glm::vec3(0.1f);
        jointDef.angularLimitMin = glm::radians(glm::vec3(-18, -6, -18));
        jointDef.angularLimitMax = glm::radians(glm::vec3(18, 6, 18));
        jointDef.springLinear = glm::vec3(300.0f);
        jointDef.springAngular = glm::vec3(300.0f);

        addJoint(jointDef);
    }
}

void PMXFile_Generator::createMultiBox(const std::vector<Gpm_BoxShape>& boxes)
{
    // Initialize with default settings
    initialize("MultiBox", "Generated multi-box model");

    // Create boxes
    for (size_t i = 0; i < boxes.size(); i++) {
        const Gpm_BoxShape& box = boxes[i];
        createBoxMesh(box.size, box.position, box.rotation);
    }

    // Create material
    createDefaultMaterial(m_currentMaterialFaceCount);
    m_currentMaterialFaceCount = 0;

    // Create bones and rigid bodies for each box
    std::vector<int32_t> boneIndices;
    std::vector<int32_t> rbIndices;

    for (size_t i = 0; i < boxes.size(); i++) {
        const Gpm_BoxShape& box = boxes[i];

        // Create bone
        int32_t boneIndex = addBone("Box_" + std::to_string(i), box.position, 0,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | PMXBoneFlags::Visible),
            false, -1);
        boneIndices.push_back(boneIndex);

        // Create rigid body
        RigidBodyDef rbDef;
        rbDef.name = "BoxBody_" + std::to_string(i);
        rbDef.shape = PMXRigidbody::Shape::Box;
        rbDef.size = box.size;
        rbDef.position = box.position;
        rbDef.rotation = box.rotation;
        rbDef.boneIndex = boneIndex;
        rbDef.operation = PMXRigidbody::Operation::Dynamic;

        int32_t rbIndex = addRigidBody(rbDef);
        rbIndices.push_back(rbIndex);
    }

    // Create joints between adjacent boxes
    for (size_t i = 1; i < boxes.size(); i++) {
        // Create joint to the previous box
        JointDef jointDef;
        jointDef.name = "Joint_" + std::to_string(i - 1) + "_" + std::to_string(i);
        jointDef.type = PMXJoint::JointType::SpringDOF6;
        jointDef.rigidBodyAIndex = rbIndices[i - 1];
        jointDef.rigidBodyBIndex = rbIndices[i];
        jointDef.position = (boxes[i - 1].position + boxes[i].position) * 0.5f;
        jointDef.linearLimitMin = glm::vec3(-0.1f);
        jointDef.linearLimitMax = glm::vec3(0.1f);
        jointDef.angularLimitMin = glm::vec3(-glm::pi<float>() / 4);
        jointDef.angularLimitMax = glm::vec3(glm::pi<float>() / 4);
        jointDef.springLinear = glm::vec3(100.0f);
        jointDef.springAngular = glm::vec3(100.0f);

        addJoint(jointDef);
    }
}

void PMXFile_Generator::createBrickWall(
    int rows,
    int columns,
    float brickWidth,
    float brickHeight,
    float brickDepth,
    float mortar,
    const glm::vec3& origin
)
{
    initialize("BrickWall", "Generated brick wall model");

    float xStep = brickWidth + mortar;
    float yStep = brickHeight + mortar;
    float z = origin.z;

    // Compute total wall size for centering
    float wallWidth = columns * xStep - mortar;
    float wallHeight = rows * yStep - mortar;

    // Offset so that the first brick is centered at the origin
    float xCenterOffset = wallWidth * 0.5f - xStep * 0.5f;
    float yCenterOffset = wallHeight * 0.5f - yStep * 0.5f;

    // Add a root bone for the wall
    int32_t rootBoneIndex = addBone("WallRoot", origin, -1,
        (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | PMXBoneFlags::Visible | PMXBoneFlags::AllowControl),
        false, -1);

    std::vector<int32_t> boneIndices;
    std::vector<int32_t> rbIndices;

    int brickIdx = 0;
    // Create all bricks as boxes
    for (int row = 0; row < rows; ++row) {
        float y = origin.y + row * yStep - yCenterOffset;
        // Stagger every other row by half a brick
        float xOffset = (row % 2) * (xStep * 0.5f);
        for (int col = 0; col < columns; ++col) {
            float x = origin.x + col * xStep + xOffset - xCenterOffset;
            glm::vec3 pos(x, y, z);

            // Store vertex count before mesh
            size_t vtxStart = m_pmxFile.m_vertices.size();
            createBoxMesh(glm::vec3(brickWidth, brickHeight, brickDepth), pos, glm::vec3(0.0f));
            size_t vtxEnd = m_pmxFile.m_vertices.size();

            // Add a bone for this brick, parented to root
            std::string boneName = "Brick_" + std::to_string(brickIdx);
            int32_t boneIndex = addBone(boneName, pos, rootBoneIndex,
                (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | PMXBoneFlags::Visible),
                false, -1);
            boneIndices.push_back(boneIndex);

            // Set bone weight for this brick's mesh vertices
            for (size_t vi = vtxStart; vi < vtxEnd; ++vi) {
                PMXVertex& v = m_pmxFile.m_vertices[vi];
                v.m_weightType = PMXVertexWeight::BDEF1;
                v.m_boneIndices[0] = boneIndex;
                v.m_boneIndices[1] = 0;
                v.m_boneIndices[2] = 0;
                v.m_boneIndices[3] = 0;
                v.m_boneWeights[0] = 1.0f;
                v.m_boneWeights[1] = 0.0f;
                v.m_boneWeights[2] = 0.0f;
                v.m_boneWeights[3] = 0.0f;
            }

            // Add a rigid body for this brick
            RigidBodyDef rbDef;
            rbDef.name = "BrickBody_" + std::to_string(brickIdx);
            rbDef.engName = rbDef.name;
            rbDef.shape = PMXRigidbody::Shape::Box;
            rbDef.size = glm::vec3(brickWidth, brickHeight, brickDepth);
            rbDef.position = pos;
            rbDef.rotation = glm::vec3(0.0f);
            rbDef.boneIndex = boneIndex;
            rbDef.operation = PMXRigidbody::Operation::Dynamic;
            int32_t rbIndex = addRigidBody(rbDef);
            rbIndices.push_back(rbIndex);

            ++brickIdx;
        }
    }

    // Create a single material for all bricks
    createDefaultMaterial(m_currentMaterialFaceCount);
    m_currentMaterialFaceCount = 0;
}
void PMXFile_Generator::createCone(float radius, float height1, const glm::vec3& position, const glm::vec3& rotation, const std::string& boneName, const std::string& rbName)
{
    float hfHeight = height1 / 2;
    initialize("Cone", "Generated cone model");
    // Create bone at the center of the cone
    int32_t boneIndex = addBone(boneName, position, -1,
        (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | PMXBoneFlags::Visible),
        false, -1);
    // Create cone mesh
    // Parameters
    const int circleSegments = 16;
    glm::mat4 transform = glm::translate(glm::mat4(1.0f), position);
    transform = transform * glm::eulerAngleXYZ(rotation.x, rotation.y, rotation.z);
    float halfHeight = hfHeight * 0.5f;
    uint32_t baseIndex = (uint32_t)m_pmxFile.m_vertices.size();
    // Apex vertex (top)
    glm::vec3 apex = glm::vec3(0.0f, halfHeight, 0.0f);
    glm::vec4 apexT = transform  * glm::vec4(apex, 1.0f);
    int32_t apexIdx = addVertex(glm::vec3(apexT), glm::normalize(glm::vec3(0.0f, 1.0f, 0.0f)), glm::vec2(0.5f, 0.0f));
    // Base circle vertices
    std::vector<int32_t> baseIndices;
    for (int i = 0; i < circleSegments; ++i) {
        float angle = (float)i / circleSegments * glm::two_pi<float>();
        float x = radius * cos(angle);
        float z = radius * sin(angle);
        glm::vec3 pos = glm::vec3(x, -halfHeight, z);
        glm::vec4 posT = transform * glm::vec4(pos, 1.0f);
        glm::vec3 normal = glm::normalize(glm::vec3(x, radius / hfHeight, z));
        glm::vec2 uv = glm::vec2((cos(angle) + 1.0f) * 0.5f, (sin(angle) + 1.0f) * 0.5f);
        int32_t idx = addVertex(glm::vec3(posT), normal, uv);
        baseIndices.push_back(idx);
    }
    // Center of base
    glm::vec3 baseCenter = glm::vec3(0.0f, -halfHeight, 0.0f);
    glm::vec4 baseCenterT = transform * glm::vec4(baseCenter, 1.0f);
    int32_t baseCenterIdx = addVertex(glm::vec3(baseCenterT), glm::normalize(glm::vec3(0.0f, -1.0f, 0.0f)), glm::vec2(0.5f, 1.0f));
    // Side faces
    for (int i = 0; i < circleSegments; ++i) {
        int32_t next = (i + 1) % circleSegments;
        addFace(apexIdx, baseIndices[next], baseIndices[i]);
    }
    // Base faces
    for (int i = 0; i < circleSegments; ++i) {
        int32_t next = (i + 1) % circleSegments;
        addFace(baseCenterIdx, baseIndices[i], baseIndices[next]);
    }
    // Set bone weights for all new vertices
    for (uint32_t i = baseIndex; i < m_pmxFile.m_vertices.size(); ++i) {
        PMXVertex& v = m_pmxFile.m_vertices[i];
        v.m_weightType = PMXVertexWeight::BDEF1;
        v.m_boneIndices[0] = boneIndex;
        v.m_boneWeights[0] = 1.0f;
    }
    // Create material
    createDefaultMaterial(m_currentMaterialFaceCount);
    m_currentMaterialFaceCount = 0;
    // Create rigid body
    RigidBodyDef rbDef;
    rbDef.name = rbName;
    rbDef.engName = rbName;
    rbDef.shape = PMXRigidbody::Shape::Cone;
    rbDef.size = glm::vec3(radius * 2.0f, height1, 1.0f);
    rbDef.position = position;
    rbDef.rotation = rotation;
    rbDef.boneIndex = boneIndex;
    rbDef.operation = PMXRigidbody::Operation::Dynamic;
    addRigidBody(rbDef);
}
bool PMXFile_Generator::saveToFile(const std::string& filename)
{

    auto finalVertexCount = m_pmxFile.m_vertices.size();
    if (  finalVertexCount > 65535) {
         m_pmxFile.m_header.m_vertexIndexSize = 4;
    }
    else if ( finalVertexCount > 255) {
        if (finalVertexCount <= 65535) {
            SABA_INFO("Upgrading vertex index size from 1 byte to 2 bytes to accommodate {} vertices", finalVertexCount);
            m_pmxFile.m_header.m_vertexIndexSize = 2;
        }
    }

    //check m_boneIndexSize ,m_rigidbodyIndexSize

    if (m_pmxFile.m_bones.size() > 255) {
        m_pmxFile.m_header.m_boneIndexSize = 2;
    }
    if ( m_pmxFile.m_rigidbodies.size() > 255) {
        m_pmxFile.m_header.m_rigidbodyIndexSize = 2;
    }
    PMXWriter writer;
    return writer.writeFile(&m_pmxFile, filename.c_str());
}

} // namespace saba
