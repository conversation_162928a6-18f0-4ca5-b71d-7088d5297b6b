#pragma once
#include <UaLib.h>
#include <memory>
#include <dsp/EQMan.h>
#include "FT/FT2Man.h"
#include "IrrFw/SnGuQin.h"
#include "FFHelper/UaFfmpeg.h"
#include "VideoHelpers.h"
#include "FL_ArEditor.h"
#include "SnArRoot.h"
#include "LeapMan.h" 
#include "audio/CXAudioManager.h"
#include "irrmmd/ccubegridscenenode.h"
#include "irrmmd/CMidiPlateSceneNode.h"

#if USE_DML_AI
#include "irrmmd/SnTestAI.h"
#endif
#define HAS_FW 1
extern intptr_t(*my_callback_blocking_fp_)(intptr_t);

class NetMan;
class LeapMan;
namespace EQVisual { 
	class SnCsParticle;
	class EQV;	
	class SnPiano;
	class SnWater;
}

namespace irr {
	namespace video {
		class VkDriver;
	}
	namespace scene {
		class IrrSaba;
		class IrrMMD;
		class PhyObjManager;
		class CVoxelMeshSceneNode;
	}
}
namespace ualib {
	class FileChangeDetectorEx;
}
namespace AppNameSpace
{


	class AppMainAMP : public ualib::ILibStage
	{
		friend class irr::scene::SnArRoot;
	public:
		AppMainAMP(ualib::IUaLibBase* lib);
		virtual ~AppMainAMP();

		virtual void StageBegin();
		void initObjectsInBeginScene();
		virtual void StageEnd();
		virtual void StageUpdate(float stepTime);

		void eqvRecreate();

		void initsBeforePPT();

		void lockCamUpdate();

		void onKeyUpdate();

		void onFrameN(int fr);

		void processVideo();
		virtual void StageRender() override;

		void renderSubview();

		bool updateFpvCamOnSaba();

		virtual void StageOnBackBufferResized(bool first) override;
		void drawBgVideo(irr::video::IVideoDriver* driver, irr::video::ITexture* tex = nullptr, irr::video::SColor sc = 0xFFFFFFFF);
		virtual bool StageOnPointerEvent(const irr::SEvent::SPointInput& pe);
		virtual void sendMsgTolib(int msg, int64_t pm1, int64_t pm2, int64_t pm3) override;
		virtual bool StageOnKeyEvent(bool pressed, const irr::SEvent::SKeyInput& ke) override;
		bool shotR(const irr::SEvent::SKeyInput& ke, bool& retFlag);
		void newCombineDance(irr::scene::IrrSaba *sb);
		void loadVmd(int inc, bool cyc=true);
		void attachObjToMmd(irr::scene::IrrSaba* sb, int lcount);

		void updateLightPos(int inc);

		void addMmdObj(ualib::addMmdObjParam apm);
		virtual bool StageOnJoyStickEvent(const irr::SEvent::SJoystickEvent& je) override;



		virtual void StageAfterPresent() override;
		virtual bool StageGetVar(StgVar sv, void* var) override
		{
#define StageGetVar_COPY(VAR) *((decltype(VAR)*) var) = VAR;
			switch (sv)
			{
			case ILibStage::varMediaTime: StageGetVar_COPY(vp.mediaTime); return true;
			}
			return false;
		}
 
		irr::scene::SnArItem* loadSabaModel(saba::PMXFileCreateParam fcp  ,bool reset=false,uint32_t aiFlag=0x11);
		void createCubes(irr::scene::BrickWallParams bwp);
		irr::scene::IrrSaba* loadPmxItem(std::string pmxfile,irr::float3 pos, irr::float3 rtt,bool massDec=false, float scale=1);
		//  all:0x1  arChar:0x10  item:0x100
		irr::scene::SnArItem* loadSabaModelScale( saba::PMXFileCreateParam fcp,float sc ,bool reset=false, uint32_t flag=0x11);

		virtual void StageStringMsgFromUIThread(std::string msg, std::string pm1) override;
 
		virtual int64_t StageOnCmdEvent(const irr::SEvent::SCmdInput& ce) override;

		void genStkText(bool all=false,int id=0,float spdmul=1.f);
		bool startDecVideo(int edMode, float startFromS);
		void saveMidiWithLyric(double bpm,int ofs);
		void stopRecord();
		void startVideoProcessing();
		void nextVmd();

		void nextModel();
		void setPhyCallBack();


		void InitFwPPT();

		void InitPPT(int kld);

		void UpdateEqvData(const EQVisual::PlayerEQVParam& evd);

		void startRecord(int recMode);
		void updateArPath();
		void LaunchOneFW(float hueadd, int mid, const irr::core::vector3df* pos, irr::core::vector3df* vel, int fwIdOfs);
		void setCamSb(irr::scene::IrrSaba* sb,bool forceSet);

	public: //P2
		void initData();
		void copyEqvdef();
		void oxrBegin();
		void djiUpdate();
		void oxrUpdate();
		void drawSsaoRT();
		void drawBloom();
		void morphToFace(float dur);
		void scaleMmdNode(irr::scene::IrrSaba* sb, saba::MMDNode* node, float sc, int resetRB = 1);
		void mediaSeekToPercent(double st);
		void saveVFrame(irr::video::ITexture* tex, int idx);
		void changeSpeed(double spd);
		void writeTimelineToFile(const std::string& filename);
		void onDamakuCmd(const std::string& pm1, bool resetPtc);
		;
	private:

		void saveCam(irr::scene::ICameraSceneNode* cam);
		void saveCameraVmd();
		void translateInActiveCamView(irr::core::vector3df vec);
		void camTimelineStart(std::string file, int tlNum, bool xxx = false);
		float frameTimeS = 0.f;
		ualib::VideoProcessor vp{};
		//test

		irr::scene::ISceneNode* mSn{}, * mSnBall{}, * snB[32]{};
		irr::scene::ILightSceneNode* snLight;
		irr::video::IVideoDriver* mDriver;
		EQVisual::SnCsParticle* IFppt = nullptr;
		UP_LOCK_DECLVAR_TYPE lockGenText;
		EQVisual::EQV* Eqv{};

		struct EQVData
		{
			bool needRecreate = true;
			bool mEqvLoadingFailed = false;
			bool useFW = true;
			int maxParticles = 100000;
		} mEqvDat;
		struct globalData
		{
			bool supportFW = false;
			bool FwInited = false;
		} g;

		float curPtR = 1.f;

		irr::IrrlichtDevice* IrrDevice;

		irr::scene::IrrMMD* mmd{};
		int mmdMax = 0;
		bool bRecording = false;
		bool bMmdSHow = false, bTgtShow = true, bDelayFrame = false;
		bool inited = false;
		irr::scene::ISceneNode* snPtBall{}, * snBox{}, * snBoxSub{}, * snGrid{}, * snShadowRcv{}, * snGrid1{}, * snSkyDome{}, * snSkyDomeM{};


		EQVisual::MatrixRecorder::DrDataStruct curDs, lastDs, lastDs1;
		int dsFrame = -1;
		bool drawPD = false;
		float txtBaseHue = 0.f;  //0~1

		int editMode = 1;  //1:playback   2:convert
		uint8_t* inVFilePtr{};
		uint32_t inVFileSize{};
		irr::io::path curInVideo, curInSubtitle;

		std::string curArText = "LOVE";
		irr::scene::ICameraSceneNode* camSave{};

		irr::video::ITexture* texWaterMark{};
		int drawWaterMarkCC = -1;
		int toShowTextCD = -1;
		float camDepFar = 1000;
		//Load MMD
		irr::io::IFileArchive* faPMX{};
		

		bool setAROriginPoint = true;
		irr::core::vector3df arOriginPoint,arOriginRtt;

		int toSaveHLBase = 0, toSaveAHB = 0;;

        irr::video::ITexture* texAHB{};

		EQVisual::SnPiano* Piano{};
		//Flutter

		irr::scene::SnArRoot* arRoot{};

		void ArSnToCs(irr::scene::SnArItem* sn, CsEditorItem* p);
		irr::scene::IrrSaba* curChar() { return arRoot->curChar()?arRoot->curChar()->sb:nullptr; }
		irr::scene::IrrSaba* curAiChar() { return arRoot->curAiSb(); }
		irr::scene::IrrSaba* curSaba() { return arRoot->curArSn->sb; }
		irr::scene::SnArItem* curArSn() { return arRoot->curArSn; }

		bool mediaStartHandled = false;
		bool toSaveCamVmd = false;

        std::thread arThread;
		bool arStarted=false, arStop= false,arDatUpdated=false;
        void arUdpate();
		void phyFrameUpdate();
		void phyModifyUpdate();


		void testTextFw(bool show, std::wstring txt,irr::u32 flag=0);
		bool toSaveScrShot = false;
		bool drawDepth = false, depthRenderFw=false, drawBG=true;
		bool drawMirror=false;

		int copySScd = 0;
		PITex opRT{}, bgImg{};
		float depthOutMul = 1.f;
		int drawOpenPoseFrames = 0;
		int mFwBlendId = 0, mMrNeedRefresh=0;
		irr::scene::SnArItem* it0{}, *it1{}, * it2{};
		std::vector< irr::scene::IrrSaba*> midiSabas;
		NetMan* netMan{};
		int arTimeOfsAutoIncUs = 0;

		int lockIt = 0, lockItViewId = 2;
		irr::scene::IrrSaba* lockOnSb{}, * lockOnSbLast{}, * lockOnSbMorphSrc{}; irr::scene::PhyObj* lockOnPo{}, * lockOnPoLast{};
		int camDisLock = 0;
		float lockRatio = 2;
		EQVisual::SnWater* snWater{};
		saba::MMDRigidBody* camRb{}; 
		irr::scene::PhyObj* camPhyObj{};


		ualib::OxmUpdateData curOud{}, origOud{}, ctrOud{};
		
		irr::scene::SnArItem* itScene{},  * itSns[32]{};;
		irr::scene::IrrSaba* sb0{}, * sb1{}, * sb2{}, * mic{}, * sbScene{};
		std::wstring reloadScene();
		ualib::FileChangeDetectorEx* fcdScene{};


		std::vector < irr::scene::IrrSaba* > tmpSns;
		irr::scene::SabaCloth* cloth{};
		int SphereCamShot = -1;
#if HAS_MIDI
		//midi
		
		std::string curMidiFile = "data/midi/yoasobi.mid";//qinglian.mid";// lemon.mid";
		bool dropInMidiFile = false;
#endif
		bool RT2Drawed = false;
		bool mirrorLR = false;
		LeapMan* leap{};

		int scMmdFrame = -1;
		std::vector<irr::video::S3DVertex> lineBuf;
		saba::MMDRigidBody* wallRb[4]{};

		bool objCenterForce = false; float objCenterForceMul = 1.f;
		float springMul = 1;
		int ModelCreateCD = 0;
		int ballFwId = 0;
		bool customAudioLoaded = false;
		int frameMmd = 0;

		int frameAddObj = 0; float frameAddObjSpdMul = 1.f;
		irr::scene::ISceneNode *snCam{};
		aubio::BeatMan beatMan;
		irr::video::VkDriver* VkDrv;
		irr::scene::PhyObjManager* Pom{};
		irr::scene::ICameraSceneNode* baseCamera{}; glm::mat4 baseCamMat;
		std::string camTcAnimRecStr;  int camTcAnimuid = 0; float bulletTimeSpd = 0.1f;


		int poleDance = 0;

		int   curMdPlate=-1, maxMdPlate=9;

		std::vector<irr::scene::CMidiPlateSceneNode*> Plates;
		std::vector<X3dUpdateParam> g_timeline[8];

		int numKeyMode = 0;
		saba::PMXFileCreateParam defaultFcp{   };
		int frameSleep = 0;

		irr::scene::IrrSaba* lastCntSb = 0;
		irr::scene::CVoxelMeshSceneNode* vmsn[8]{}; int vmsnId = 0;
		glm::vec3 initpsBasePos = glm::vec3(0); glm::vec3 initpsBaseRtt = glm::vec3(0);
		glm::quat lastFollowCamQR = glm::quat(1, 0, 0, 0);

		float switchItemTime = 0;
		bool isRenderingFpv;

#if USE_DML_AI
		//AI
		irr::scene::SnTestAI* Ai{};
#endif
    };


}


