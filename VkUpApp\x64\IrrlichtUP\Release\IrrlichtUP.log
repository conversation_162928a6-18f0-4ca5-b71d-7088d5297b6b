﻿  VkDriver.cpp
  VkMr2D.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkMr2D.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkDriver.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file 'VulkanRenderer/VkDriver.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(192,77): warning C4267: 'argument': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(1135,51): warning C4838: conversion from 'int' to 'float' requires a narrowing conversion
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(1135,51): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(1236,11): warning C4553: '==': result of expression not used; did you intend '='?
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(355,44): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(377,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(388,22): warning C4244: '+=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(411,32): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(779,44): warning C4267: 'return': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(3705,20): warning C4018: '<': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(4482,84): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(4482,56): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(4694,17): warning C4101: 'dstImageMemory': unreferenced local variable
  IrrlichtUP.vcxproj -> D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib
