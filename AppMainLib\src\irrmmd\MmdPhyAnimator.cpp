#include "AppGlobal.h"
#include "MmdPhyAnimator.h"
#include "irrsaba.h"
#include <unordered_set>
#include "irrmmd.h"
#include <cppIncDefine.h>
#include <VulkanRenderer/VkDriver.h>
#include "stlUtils.h"
#include "../../app/armmplayer/snarRoot.h"
#if USE_IMGUI
#include "VulkanRenderer/base/VulkanUIOverlay.h"
#endif
#include "CSceneNodeAnimatorCameraTouchControl.h"

#include "external/imgui/imgui_internal.h"
#include "external/imgui/misc/cpp/imgui_stdlib.h"
#include "ImGuiMmdHelper.h"
#include "irrmmd/ccubegridscenenode.h"
#include "CInstancedMeshSceneNode.h"

using namespace irr::scene;
using namespace ualib;
using namespace glm;



namespace {
	static PhyObj** ppNdB[256] = {};  //dynBase , 1 on 1 saba model
	static const std::string KeyPoseCmdTypeNames[] = {
	#define MACRO_X(name) #name,
			KEYPOSE_CMD_TYPES(MACRO_X)
	#undef MACRO_X
	};
}

// Usage in MmdRigKeys
void MmdPhyAnimator::uiSequencer() {
	auto& io = ImGui::GetIO();
 
	showCmdEditor();
 
	uiMenu();

	ImGui::Begin("BottomToolWindow", 0, ImGuiWindowFlags_NoMove|ImGuiWindowFlags_NoTitleBar);
	static int firstShow = 1;
	if (firstShow) {
		firstShow = 0;
		ImGui::SetWindowPos(ImVec2(0, Ctx->gd.scrHeight - 350));
		ImGui::SetWindowSize(ImVec2(Ctx->gd.scrWidth, 350));
	}
	if (ImGui::BeginTabBar("SequencerTabs")) {
		if (ImGui::BeginTabItem(ualib::strFmt("[%s]", mpaName.c_str()).c_str(), nullptr, setUiSequencerSelected?ImGuiTabItemFlags_SetSelected:0)) {
			setUiSequencerSelected = false;
			static int firstFrame = 0;
			static bool expanded = true;			 
			int& currentFrame = curFrame;
			int oldFrame = currentFrame;
			// Create a child window with fixed height
			ImGui::BeginChild("SequencerContainer",
				ImVec2(0, -100), //ImVec2(ImGui::GetContentRegionAvail().x, ImGui::GetContentRegionAvail().y-110),
				0,  // Border
				ImGuiChildFlags_AlwaysUseWindowPadding
			);
			//ImSequencer::SELECT_BACK_COLOR = 0x208080FF;  //IMGUI COLOR u32 ABGR
			ImSequencer::LABEL_WIDTH = 128;
			rigSequencer->itemHeight = std::clamp((int)(ImGui::GetContentRegionAvail().y - 28.f) / std::min(21, rigSequencer->GetItemCount() + 1), 20, 100);
			// Render the sequencer
			bool sequencerChanged = ImSequencer::Sequencer(
				rigSequencer,        // Sequencer interface
				&currentFrame,       // Current frame/time
				&expanded,           // Expanded state
				&seqSelectedIdx,      // Selected entry
				&firstFrame,         // First visible frame
				ImSequencer::SEQUENCER_EDIT_STARTEND | ImSequencer::SEQUENCER_CHANGE_FRAME |
				(playState != 0 || io.MouseDown[0] && io.KeyAlt ? ImSequencer::SEQUENCER_KEEP_CURRENT_FRAME_IN_VIEW : 0)
			);
			ImGui::EndChild();
			auto it = nodeMap.end();
			if (seqSelectedIdx >= 0) {
				it = std::next(nodeMap.begin(), seqSelectedIdx);
				// Handle sequencer changes
				if (oldFrame != currentFrame)
					rigSequencer->syncPlaybackFromFrame(currentFrame);

				if (sequencerChanged)
				{
					if (it != nodeMap.end() && it->node != curMmdNode) {
						seqClearSelection();
						switchToNode(it->node);
					}
				}
			}


			ImGui::BeginGroup();
			{
				std::string s; for (auto& sb : sabas) s += std::to_string(sb->getItemIdx());
				s += " C"; s += Ctx->getViewCamera()->getName();
				ImGui::Text(s.c_str());
				if (it != nodeMap.end()) {
					ImGui::SetNextItemWidth(120);
					if (ImGui::InputText("##Alias", &it->alias)) {
						curRigNode->isCameraNode = (it->alias == "Camera");
						if (curRigNode->isCameraNode) 
						{ 
							curCameraRigNode = curRigNode->node;
						}
					}
				}
 
				ImGui::Text("Time  %.2f\nFrame %d/%d\nT*%g P*%g ", curTime, oldFrame, currentFrame, MMDPhysics::gameTimeMul, MMDPhysics::phyTimeMul);

			}
			ImGui::EndGroup();
			ImGui::SameLine(0, 10);
			ImGui::BeginGroup();
			{
				if (ImGui::Button("|<")) {
					currentFrame = rigSequencer->GetFrameMin();
					rigSequencer->syncPlaybackFromFrame(currentFrame);
				}
				ImGui::SameLine();
				if (ImGui::Button(">|")) {
					currentFrame = rigSequencer->GetFrameMax();
					rigSequencer->syncPlaybackFromFrame(currentFrame);
				}ImGui::SameLine();
				if (ImGui::Button(">")) {
					playStart(1, 1);
				}
				ImGui::SameLine();
				if (ImGui::Button("||")) {
					playStop();
				}
				if (ImGui::Button("Zm-")) {
					rigSequencer->framePixelWidthTarget /= 2;
				}
				if (ImGui::IsItemHovered()) {
					ImGui::BeginTooltip();
					ImGui::Text("Zoom: %f", rigSequencer->framePixelWidthTarget);
					ImGui::EndTooltip();
				}
				ImGui::SameLine();
				if (ImGui::Button("Zm+")) {

					rigSequencer->framePixelWidthTarget *= 2;
				} 
			}
			ImGui::EndGroup();
			ImGui::SameLine(0, 10);
			ImGui::BeginGroup();
			{
				ImGui::SetNextItemWidth(100);
				if (ImGui::InputFloat("Start Time", &customStartTime, 1.0f, 10.0f, "%.2f")) {
					customStartTime = std::max(0.0f, customStartTime);
					if (customEndTime < customStartTime) {
						customEndTime = customStartTime;
					}
				}
				ImGui::SameLine();
				if (ImGui::Button("Set Start")) {
					customStartTime = curTime;
					if (customEndTime < customStartTime) {
						customEndTime = customStartTime;
					}
					saveWorld();
				}

				ImGui::SetNextItemWidth(100);
				if (ImGui::InputFloat("End Time", &customEndTime, 1.0f, 10.0f, "%.2f")) {
					customEndTime = std::max(customStartTime, customEndTime);
				}
				ImGui::SameLine();
				if (ImGui::Button("Set End")) {
					customEndTime = curTime;
					if (customEndTime < customStartTime) {
						customStartTime = customEndTime;
					}
				}

				ImGui::Checkbox("Use Range", &useRangePlayback);
				ImGui::SameLine();
				if (ImGui::Button("Play Range")) {

					playStart(1, 3); // New mode 2 for range playback
				}


			}
			ImGui::EndGroup(); ImGui::SameLine();

			// Additional controls
			ImGui::BeginGroup();
			{
				auto pickNode = arRoot->curPickNode.sbNode;
				auto pickSb = pickNode ? pickNode->model->saba : nullptr;
				if (pickSb) {
					if (ImGui::Button("Chg Nd")) {
						
						auto oldNode = lastEditingNode; 
						//also change node in nodeMap
						auto it = nodeMap.find(oldNode);
						if (it != nodeMap.end()) {
							saveState();
							createNode(arRoot->curPickNode.sbNode);
							auto& rigNode = nodeMap[arRoot->curPickNode.sbNode];
							int sbt = rigNode.sbT;
							rigNode = nodeMap[oldNode];
							rigNode.sbT = sbt;
							rigNode.node = arRoot->curPickNode.sbNode;

							removeNode(oldNode);
							switchToNode(arRoot->curPickNode.sbNode);
						}
					}ImGui::SameLine();
					if (ImGui::Button("Cpy Nd")) {
						auto oldNode = lastEditingNode;
						//also change node in nodeMap
						auto it = nodeMap.find(oldNode);
						if (it != nodeMap.end()) {
							saveState();
							createNode(arRoot->curPickNode.sbNode);
							nodeMap[arRoot->curPickNode.sbNode] = nodeMap[oldNode];
							nodeMap[arRoot->curPickNode.sbNode].node = arRoot->curPickNode.sbNode;
							//removeNode(oldNode);
							switchToNode(arRoot->curPickNode.sbNode);
						}
					}ImGui::SameLine();
					if (ImGui::Button("Add Nd"))
						addExtNode();
					if (ImGui::Button("New Rt") && arRoot->curPickNode.sbNode)
					{
						setNode(arRoot->curPickNode.sbNode->model->saba->Pmx->GetNodeManager()->GetMMDNode(0));
						newKeyIf(0, 1), setPose();
					}ImGui::SameLine();
					if (ImGui::Button("RtAddNd")) {
						auto nd = addExtNode(pickSb->ndRoot);		setNode(nd);

					}ImGui::SameLine();
					if (ImGui::Button("AddCamera")) {
						auto nd = addExtNode(pickSb->ndRoot); setNode(nd);
						nodeMap[nd].alias = "Camera"; nodeMap[nd].isCameraNode = true; 
						curCameraRigNode = nodeMap[nd].node;
					}
				}
			}
			ImGui::EndGroup();
			ImGui::EndTabItem();
		}
		uiOtherTabs();

		ImGui::EndTabBar();

	}
 
	ImGui::End();

	if ((io.MouseWheel < -FLT_EPSILON || io.MouseWheel > FLT_EPSILON))
	{
		DP(("SW"));
		if (io.KeyAlt) {
			DP(("SW+"));
			switchNodes(io.MouseWheel > 0 ? -1 : 1);
		}
		else {
			// Only process MouseWheel if not accepted by any other control
			if (!io.WantCaptureMouse && io.MouseWheel != 0.0f)
			{
				if (io.MouseWheel > 0) toNextKey(); else toPrevKey();

			}
		}
	}
}



void MmdPhyAnimator::uiMenu()
{
	// Set menu color to green
	//if (camPreview) {
	//	ImGui::PushStyleColor(ImGuiCol_MenuBarBg, ImVec4(0.0f, 0.5f, 0.0f, 1.0f));        // Dark green background
	//	ImGui::PushStyleColor(ImGuiCol_Header, ImVec4(0.0f, 0.6f, 0.0f, 1.0f));           // Slightly lighter for hover
	//	ImGui::PushStyleColor(ImGuiCol_HeaderHovered, ImVec4(0.0f, 0.7f, 0.0f, 1.0f));    // Even lighter for active
	//} // Don't forget to pop!
	if (ImGui::BeginMainMenuBar())
	{

		// Additional controls
		if (ImGui::BeginMenu("File"))
		{
			if (ImGui::MenuItem("Load")) {
				std::string filePath;
				if (vks::showSysPickFileDialog(filePath, false, "Timeline Files\0*.json;*.json5\0")) {			
					loadFromFile({ filePath,false });
					if (this == mmd->mainMPA && filePath.find("\\tmp\\") == std::string::npos)
						Ctx->jss["mpa"]["lastSavedFile"] = mmd->mainMPA->lastSavedFile = filePath;
				}
			}
			 
			if (ImGui::MenuItem((std::string("Load ") + lastSavedFile).c_str())) { 
				loadFromFile({ lastSavedFile,false });
			}
			if (ImGui::MenuItem("SaveAs")) {

				std::string filePath;
				if (vks::showSysPickFileDialog(filePath, true, "Timeline Files\0*.json;*.json5\0")) {
					if (filePath.find(".json") == std::string::npos)
						filePath += ".json";
					if (this == mmd->mainMPA)
						Ctx->jss["mpa"]["lastSavedFile"] = mmd->mainMPA->lastSavedFile = curFilePath = filePath;
					saveToFile(filePath);
				}
					
			}
			if (ImGui::MenuItem("SaveAsS0")) {
				for (auto& n : nodeMap) {
					n.sbT = 0;
				}
				std::string filePath;
				if (vks::showSysPickFileDialog(filePath, true, "Timeline Files\0*.json;*.json5\0"))
					saveToFile(filePath);
			}
			if (ImGui::MenuItem((std::string("Save ")+curFilePath).c_str())) {
				 
				 
					saveToFile(curFilePath);
			}
			ImGui::EndMenu();
		}
		if (ImGui::BeginMenu("Edit"))
		{
			if (ImGui::MenuItem("Undo", "CTRL+Z")) { undo(); }
			if (ImGui::MenuItem("Redo", "CTRL+SHIFT+Z", false, false)) { redo(); }  // Disabled item
			ImGui::Separator();
			if (ImGui::BeginMenu("Key")) {
				if (ImGui::MenuItem("Add key")) { newKeyIf(1, 0); }
				if (ImGui::MenuItem("Add key with copy")) { newKeyIf(1, 1); }
				if (ImGui::MenuItem("Insert key")) { newKeyIf(0, 0); }
				if (ImGui::MenuItem("Insert key with copy", "CTRL+I")) { newKeyIf(0, 1); }
				if (ImGui::MenuItem("Selected key frame * 2")) {
					if (rigSequencer->mSelectedKeys.empty()) return;
					saveState();
					std::vector<std::pair<saba::MMDNode*, RigKey>> duplicatedKeys;
					for (const auto& keyPair : rigSequencer->mSelectedKeys) {
						auto& rigNode = nodeMap[keyPair.first];
						auto& key = rigNode.keys[keyPair.second];
						key.frame *= 2;
					} 
					updateCumulativeTimes();
				}
				ImGui::EndMenu();
			}
			ImGui::Separator();
			if (ImGui::BeginMenu("Frame"))
			{
				if (ImGui::MenuItem("Del frame")) { delCurFrame(); }
				if (ImGui::MenuItem("Del frame until key")) { while (delCurFrame()); }
				if (ImGui::MenuItem("Frame Scale x2")) { 
					saveState();
					for (auto& rigNode : nodeMap) {
						for (auto& key : rigNode.keys) {
							key.frame *= 2;
							key.frameIdx *= 2;
						}
					}

				}
				ImGui::EndMenu();
			}
			ImGui::Separator();
			if (ImGui::BeginMenu("Node"))
			{
				if (ImGui::MenuItem("Delete Node")) {
					removeNode(nodeIt->node);
				}
				ImGui::EndMenu();
			} 
			ImGui::Separator();

			if (ImGui::MenuItem("Cut", "CTRL+X")) {}
			if (ImGui::MenuItem("Copy", "CTRL+C")) {}
			if (ImGui::MenuItem("Paste", "CTRL+V")) {}


			
			ImGui::EndMenu();
		}
		if (ImGui::BeginMenu("Scene"))
		{
			if (ImGui::MenuItem("Set Frame Break")) {
				frameBreak = curFrame;
			}
			if (ImGui::MenuItem("Reset Camera")) {
				auto anm = Ctx->getCamTCAnimator();
				anm->tld.rtt = vec3(0, 0, 0);
			}
			ImGui::EndMenu();
		}
 
		ImGui::EndMainMenuBar();
		
	}
	//if (camPreview) ImGui::PopStyleColor(3);
}

saba::MMDNode* MmdPhyAnimator::addExtNode(saba::MMDNode* parentNode,std::string idstr)
{
	if (!parentNode) parentNode = arRoot->curPickNode.sbNode;
	PMXBone bone = parentNode->model->saba->Pmx->pmx.m_bones[parentNode->GetIndex()];
	bone.m_parentBoneIndex = parentNode->GetIndex();
	bone.m_boneFlag |= PMXBoneFlags::AllowTranslate;
	auto psb = parentNode->model->saba;
	if (idstr.empty()) {
		int tryId = 0; ;
		do {
			idstr = std::string("$x_") + std::to_string(tryId++) + "_" + parentNode->GetName();
		} while (psb->Pmx->GetNodeManager()->FindNode(idstr) != nullptr);
	}
	bone.m_name = idstr ;
	bone.m_nameU = ualib::Utf8toWcs(bone.m_name);
	return parentNode->model->saba->Pmx->addExtNode(bone,true);
}

bool irr::scene::MmdPhyAnimator::toggleCamera() {
	if (curCameraRigNode) {
		nodeMap[curCameraRigNode].disabled = !nodeMap[curCameraRigNode].disabled;
		//irrCamToRttCam(curCameraRigNode->model->saba);
		//auto rtt = Ctx->getCamTCAnimator()->tld.rtt;
		//Ctx->getCamTCAnimator()->tld.rtt = vec3(rtt.x, rtt.y, 0);
		return !nodeMap[curCameraRigNode].disabled;
	}
}


MmdPhyAnimator::MmdPhyAnimator(MmdPhyAnimatorParam pm)
{
	Pm = pm;
	mmd = pm.mmd;	
	Sb0 = mmd->sabas[0];	
	resetSabas();
	
	SceneManager = Sb0->getSceneManager();

	Ctx = mmd->Ctx; 
	Driver = Ctx->getDriver();
	arRoot = mmd->arRoot;

	if (Pm.isEditor) createUI();
}

void irr::scene::MmdPhyAnimator::resetSabas( )
{
	sabas.clear();
	if (Pm.setSb0) sabas.push_back(Pm.setSb0);
	if (Pm.setSb1) sabas.push_back(Pm.setSb1);
	for (auto sbi : Pm.sbs) if (sbi<mmd->sabas.size()) {

		sabas.push_back(sbi>=0?mmd->sabas[sbi]:Pm.setSb0);
	}
}

void MmdPhyAnimator::createUI()
{
	if (!rigSequencer)
	{
		lastSavedFile = Ctx->jss["mpa"]["lastSavedFile"].asString();

		rigSequencer = new MmdRigKeysSequencer(this);
		rigSequencer->canMoveItem = false;


		OnUpdateUIOverlay = [this](vks::UIOverlay* ol) {
			bool br = false;
			using namespace ImGui;
			auto& ui = *ol;
			auto cam = SceneManager->getActiveCamera();
			glm::mat4 viewMatrix = cam->getViewMatrix(), projMatrix = cam->getProjectionMatrix();

			// Set width to 500
			auto kShift = mmd->Ctx->getEvtRcv()->IsKeyDown(irr::KEY_LSHIFT);
			ImGui::PushItemWidth(200);

			// Optional: Setup view manipulator context
			ui.setupViewManipulator();

			//ui.drawViewManipulator((viewMatrix), projMatrix);
			////cam->setRotation(core::matrix4(viewMatrix) .getRotationDegrees());
			//auto anm = ((CSceneNodeAnimatorCameraTouchControl*)*mmd->Ctx->gd.CamRtt->getAnimators().begin());
			//auto rtt = glm::eulerAngles(quat((viewMatrix)));
			//// Handle view manipulator interactions
			//if (ui.handleViewManipulatorInput()) {			anm->tld.rtt = vec3(rtt.y, rtt.x, rtt.z) * core::RADTODEG;		}

			if (ui.header("MRK")) {
				int nodeId = (nodeMap.size() ? std::distance(nodeMap.begin(), nodeIt) : -1);
				char saved = nodeId >= 0 && nodeIt->curPose.saved ? 'S' : ' ';
				ui.checkBox("P", &showNodePath);  SameLine();
				ui.text("N:%d \nK:%d", nodeId, curEditKeyIndex());

				if (curMmdNode)
				{
					auto kp = editKeyPose();
					SameLine(); ui.text("S%d T %s\nE%d B %s",
						curRigNode->sbT,
						ualib::WcstoUtf8(curMmdNode->GetNameU()).c_str(),
						kp && kp->getB() ? kp->sbB : 0,
						ualib::WcstoUtf8((kp && kp->getB() ? kp->getB()->GetNameU() : L"")).c_str());
				}
				if (ui.button("+"))     newKeyIf(1, 0); SameLine();
				if (ui.button("+C"))   newKeyIf(1, 1); SameLine();
				if (ui.button("i"))     newKeyIf(0, 0); SameLine();
				if (ui.button("iC"))   newKeyIf(0, 1); SameLine();
				if (ui.button("xb"))    setBase(nullptr); SameLine();
				if (Button(rigBaseNd ? (rigBaseNd->GetName() + "##baseNd").c_str() : "##baseNd", ImVec2(100, 0))) {
					setBase(rigBaseNd);
				};

				if (ui.button("-F"))     delCurFrame(); SameLine();

				SameLine(0, 30);
				PushItemFlag(ImGuiItemFlags_ButtonRepeat, true);

				PopItemFlag();
				ImGui::Dummy(ImVec2(0, 10));
				//defTime = curKey() ? curKey()->frame*timePerFrame : 0.f;
				//if (curRigKeyIdx >= 0 && ui.sliderFloat("Time (ms) ", &defTime, 0.f, 1.f)) {
				//    curKey()->frame = defTime/timePerFrame+0.5f;
				//}
				SetNextItemWidth(100); SameLine();
				if (ImGui::DragFloat("Time mul ", &mmd->rigPlaybackTimeMul, 0.1f, 0.1f, 3.f)) {
					//setPose();
				}
				Separator();
				auto kp = &curEditKey()->pose;
				if (curEditKey()) {

					ui.pushW(100);
					br = false;
					br = ui.inputFloat("Fm", &kp->fmul, 1.f, 0.1f);
					SameLine(); if (Button("/2")) { br = true; kp->fmul *= 0.5f; }
					SameLine(); if (Button("*2")) { br = true; kp->fmul *= 2.f; }
					kp->fmul = std::clamp(kp->fmul, 0.f, 100.f);
					if (br) sOtSl([](auto& kp, auto& s) {kp.fmul = s.fmul; });

					if (ui.inputFloat("Sc", &kp->fsc, 1.f, 0.1f))
						sOtSl([](auto& kp, auto& s) {kp.fsc = s.fsc; });

					if (br = ui.checkBox("MT", &kp->ndTmir)) { kp->ndBmir = kp->ndTmir; }
					SameLine(); br |= ui.checkBox("MB", &kp->ndBmir);   SameLine(0, 10);
					if (br) sOtSl([](auto& kp, auto& s) {kp.ndTmir = s.ndTmir; kp.ndBmir = s.ndBmir; });
					if (Button("X=0")) {
						kp->pos.x = 0;
					}
					br = ui.checkBox("Rd", &kp->setRd);
					if (br) sOtSl([](auto& kp, auto& s) {kp.setRd = s.setRd; });
					if (kp->setRd) {
						SameLine();
						bool br = ui.inputFloat("Rm", &kp->rdRatio, 1.f, 0.1f);   kp->rdRatio = std::clamp(kp->rdRatio, 0.f, 1.f);
						if (br) 
							sOtSl([](auto& kp, auto& s) {kp.rdRatio = s.rdRatio; });
					}
					ImGui::SetNextItemWidth(80);
					if (ImGui::InputInt("BMode", &kp->baseMode))
						sOtSl([](auto& kp, auto& s) {kp.baseMode = s.baseMode; });
					SameLine(0, 10);
					ImGui::SetNextItemWidth(80);
					if (ImGui::InputInt("PoB", &kp->dndB))
						sOtSl([](auto& kp, auto& s) {kp.dndB = s.dndB; });
					ImGui::SetNextItemWidth(80);
					if (ImGui::InputInt("RttMode", &kp->rttMode))
						sOtSl([](auto& kp, auto& s) {kp.rttMode = s.rttMode; });					

					if (kp->rttMode) {
						if (ImGui::InputFloat("AMul", &kp->amul, 1, 10, "%g"))
							sOtSl([](auto& kp, auto& s) {kp.amul = s.amul; });;
						if (kp->rttMode == 2) {
							if (ImGui::InputFloat3("Front", &kp->front.x))
								sOtSl([](auto& kp, auto& s) {kp.front = s.front; });
						}
					}
					ui.popW();
				}
				SameLine();
				if (ui.button("sabas")) {
					static int sbcc = 0; sbcc++;
					sabas[1] = Sb0->mmd->sabas[1 + sbcc % (Sb0->mmd->sabas.size() - 1)];
					//loadFromFile("data/rigsave.json");
				}

			}

			// 在渲染循环中

			if (showGizmo && curMmdNode && !mmd->ikVisable)
			{
				glm::mat4 m;

				auto kp = editKeyPose();
				auto pose = kp ? *kp : curNodePose();
				m = glm::translate(mat4(1), pose.pos) * glm::mat4(pose.rtt);
				auto rigBase = pose.getB();


				auto basemat = rigBase ? rigBase->getMatT((pose.baseMode)) : mat4(1);
				mat4 irm = Sb0->mmd2irr(basemat * m);
				// 绘制Gizmo
				ui.separator();
				ui.drawGizmoWithEditor("Object Transform", viewMatrix, projMatrix, irm,false);
				//else ui.drawGizmo(viewMatrix, projMatrix, irm);
				m = Sb0->irr2mmd(irm);
				// 检查Gizmo是否被使用
				if (ui.isUsingGizmo())
				{
					// matrix已被更新，应用到你的对象上   
					auto lm = glm::inverse(basemat) * m;
					curNodePose().pos = lm[3];
					curNodePose().rtt = glm::quat_cast(lm);
					if (kp) {
						kp->pos = curNodePose().pos;
						kp->rtt = curNodePose().rtt;
						if (!camPreview && kp->cmds.size()) {
							auto& cmd = kp->cmds[0];
							if (cmd.type == KeyPoseCmdType::ectCameraKey)
							{
								auto& kpm = std::get<KeyPoseCmd::KpcCameraKey>(cmd.cmdPm);
								kpm.vc.m_interest = kp->pos;
							}
						}
						// setPose();
					}
					//  rigBase->setAnimationMatrix(glm::inverse(rigBase->GetParent()->GetGlobalTransform()) * m);
				}

			}
			if (nodeIt != nodeMap.end())  ui.text("CP  %3.3f %3.3f %3.3f ", curNodePose().pos.x, curNodePose().pos.y, curNodePose().pos.z, &curNodePose());
			if (auto k = editKeyPose()) {
				if (vks::DragFloatXYZExt("Pos", &k->pos.x, 0.1f, 0, 0, "%.1f")) {
					sOtSl([](auto& kp, auto& s) {kp.pos = s.pos; });
				} 
				vec3 rtt = glm::eulerAngles(k->rtt);
				if (vks::DragFloat3DegToRad("Rot", &rtt.x )) {
					k->rtt = glm::quat(rtt);
					sOtSl([=](auto& kp, auto& s) { kp.rtt = rtt; });
				}				 

				if (ui.checkBox("disabled", &curEditKey()->disabled)) {
					//setPose();
				}
			}
			ImGui::PopItemWidth();

			uiSequencer();
			};
	}
	//setUiSequencerSelected = true;
	setTabAddObjSelected = true;
	rigSequencer->framePixelWidthTarget = framePixelWidthTarget;
}

MmdPhyAnimator::~MmdPhyAnimator()
{
	//auto save
	if (this == mmd->mainMPA) {
		saveToFile("data/mpa/__autosave.json");
	}

	if (rigSequencer) {
		delete rigSequencer;
		rigSequencer = nullptr;
	}

}

// Helper functions for NEW_DATA_STRUCT

void MmdPhyAnimator::createNode(saba::MMDNode* node)
{
	if (nodeMap.contains(node)) return  ;

	RigNode rigNode;
	int sbT = 0;
	for (size_t i = 0; i < sabas.size(); i++)
		if (node->model->saba == sabas[i]) { sbT = i;  break; }

	curMmdNode = rigNode.node = node;
	rigNode.sb = node->model->saba;
	rigNode.sbT = sbT;
	rigNode.curPose.setB(nullptr);
	auto [newIt, _] = nodeMap.emplace(  rigNode);	
	nodeIt = newIt;
	switchToNode(node);

	if (node->GetName()[0] == '$' && node->rb0 && node->rb0->dynRbType || node->GetIndex() == 0) curRigNode->moveNodeAnim = 1;
	
	 
}

void irr::scene::MmdPhyAnimator::updateCumulativeTimes() {
	// Update cumulative frames for each node's keys
	maxCumFrame = 1;
	for (auto& rigNode : nodeMap) {
		int cumFrame = 0;
		for (auto& key : rigNode.keys) {
			cumFrame += key.frame;
			key.frameIdx = cumFrame;
			if (cumFrame > maxCumFrame) maxCumFrame = cumFrame;
		}
	}

}

void MmdPhyAnimator::update()
{
	if (Pm.isEditor && !nodeMap.empty() && curRigNode) {
		curMmdNode = curRigNode->node;
		// Update current node visualization
		int index = 0;
		for (const auto& pair : nodeMap) {
			if (pair.node == curMmdNode) break;
			index++;
		}

		video::SColorHSL hsl(0, 100, 80);
		hsl.Hue = index * 60;
		video::SColor sc = hsl.toSColor();
		sc.setAlpha(0xCC);


		bool saved = curRigNode->keys.size() > 0 && curRigNode->keys[curRigNode->curKeyIdx].pose.saved; 

		// Visualize key positions for current node
		if (showNodePath && !camPreview &&  Ctx->getDriver()->renderUI && !curRigNode->keys.empty()) {
			if (rigBaseNd ) sbFw2(saved ? "sw2b" : "sw2", rigBaseNd->getGlobalPos(), vec3(0), sc);
			if (curMmdNode) sbFw2(saved ? "swb" : "sw", curMmdNode->getGlobalPos(), vec3(0), sc);

			sc.setAlpha(0xC0);
			auto ndB = curKey()->pose.getB();
			if (ndB && ndB->GetIndex() > 1 && curMmdNode) {
				sbFw2Line("pt", ndB->getGlobalPos(), curMmdNode->getGlobalPos(), sc, saved ? 100.f : 200.f);
			}
			for (size_t i = 0; i < curRigNode->keys.size(); i++) {
				const auto& key = curRigNode->keys[i];
				if (key.pose.saved) {
					auto m = (key.pose.getB()?key.pose.getB()->mGlobalAnim:mat4(1)) * key.pose.relMat();
					sc.setAlpha(0xFF);
					sbFw2("sw", vec3(m[3]), vec3(0), sc);

					if (i > 0 && curRigNode->keys[i - 1].pose.saved) {
						auto prevM = (curRigNode->keys[i - 1].pose.getB() ? curRigNode->keys[i - 1].pose.getB()->mGlobalAnim:mat4(1)) * curRigNode->keys[i - 1].pose.relMat();
						vec3 pos0 = prevM[3], pos1 = m[3];
						float ratfr = std::max(3.f, float(key.frame));
						float step = glm::length(pos1 - pos0) * MMD_SABA_SCALE / 10.f;
						if (step > 100.f) ratfr /= step / 100.f, step = 100.f;
						sbFw2Line("pt", pos0, pos1, sc, step, fmod(Ctx->gd.frameCount, ratfr) / float(ratfr));
						//(Ctx->gd.frameCount % 60 / 60.f));
					}
				}
			}
		}
	}

	if (nextFrameTo >= 0)
	{
		curFrame = nextFrameTo;
		nextFrameTo = -1;
		playTime =  (curFrame)*timePerFrame;
		for (auto& rn : nodeMap) rn.lastPlayIdx = -100;
	}
	if (playState) playTime += Ctx->gd.deltaTime * mmd->rigPlaybackTimeMul * playSpeed;
	updatePlayback(playState == 0 || playState == 2 ? curTime : playTime );
}






void MmdPhyAnimator::newKeyIf(int mode, int copyLast, int frame)
{

	if (!curRigNode) return;
	saveState();
	RigKey key;
	key.frame = 0;

	auto& nodeKeys = curRigNode->keys;
	int curKeyIdx = mode == 0 ? curRigNode->getKeyIdAtFrame(curFrame) : curRigNode->curKeyIdx;
	int copyIdx = -1;
	if (nodeKeys.empty()) {

		key.frame = curFrame;

		nodeKeys.push_back(key);
		curKeyIdx = 0;

	}
	else {
		if (mode > 0) // insert at end
		{

			key.frame = curFrame - nodeKeys[curKeyIdx].frameIdx;
			if (key.frame < 1) key.frame = frame;

			nodeKeys.insert(nodeKeys.begin() + curKeyIdx + 1, key);
			copyIdx = curKeyIdx; curKeyIdx++;
			updateCumulativeTimes(); toKey(curKeyIdx);
		}
		else if (mode < 0) //insert at beginning, and others +1s
		{

			nodeKeys[0].frame = framePerSecond; // 1 second = 30 frames
			key.frame = 0;

			nodeKeys.insert(nodeKeys.begin(), key);
			copyIdx = 1; curKeyIdx = 0;
		}
		else //insert at current time
		{
			if (curKey()->frameIdx == curFrame) // if key already exists
				return;



			if (curKeyIdx < 0) {
				assert(nodeKeys[0].frameIdx > curFrame); //before node                
				key.frame = curFrame;
				nodeKeys[0].frame -= key.frame;
				nodeKeys.insert(nodeKeys.begin(), key);
				copyIdx = -1; curKeyIdx = 0;
			}
			else {
				key.frame = curFrame - nodeKeys[curKeyIdx].frameIdx;
				if (curKeyIdx < nodeKeys.size() - 1)
					nodeKeys[curKeyIdx + 1].frame -= key.frame;

				nodeKeys.insert(nodeKeys.begin() + curKeyIdx + 1, key);
				copyIdx = curKeyIdx; curKeyIdx++;
			}
		}

		if (copyLast && copyIdx >= 0) {
			if (copyIdx >= 0 && copyIdx < nodeKeys.size()) {
				curNodePose() = nodeKeys[curKeyIdx].pose = nodeKeys[copyIdx].pose;
				 
			}
		}

	}
	curRigNode->editKeyIdx = curRigNode->curKeyIdx = curKeyIdx;
	


	updateCumulativeTimes();
}


 

void MmdPhyAnimator::toNextKey()
{
	if (!curRigNode || curRigNode->keys.empty()) return;

	int toidx = curRigNode->editKeyIdx + 1;
	if (toidx >= curRigNode->keys.size()) {
		//toKey(0);
	}
	else toKey(toidx);
	if (mmd->Ctx->getEvtRcv()->IsKeyDown(irr::KEY_MENU)) {
		// Switch to next node if available
		auto it = nodeMap.find(curRigNode->node);
		if (it != nodeMap.end() && std::next(it) != nodeMap.end()) {
			switchToNode(std::next(it)->node);
		}
	}

	loadRigNode();
}

void MmdPhyAnimator::toPrevKey()
{
	if (!curRigNode || curRigNode->keys.empty()) return;

	int toidx = curRigNode->editKeyIdx - 1;
	if (toidx < 0) {
		//toKey(curRigNode->keys.size() - 1);
	}
	else toKey(toidx);

	if (mmd->Ctx->getEvtRcv()->IsKeyDown(irr::KEY_MENU)) {
		// Switch to previous node if available
		auto it = nodeMap.find(curRigNode->node);
		if (it != nodeMap.begin()) {
			switchToNode(std::prev(it)->node);
		}
	}

	loadRigNode();
}

void MmdPhyAnimator::toKey(int id)
{
	if (!curRigNode) 
		return;

	curRigNode->editKeyIdx = curRigNode->curKeyIdx = std::clamp(id, 0, static_cast<int>(curRigNode->keys.size()) - 1);
	curFrame = curKey()->frameIdx;
	curTime = curFrame * timePerFrame;
	rigBaseNd = curNodePose().ndB;
	loadRigNode();
}

void MmdPhyAnimator::setNode(saba::MMDNode* nd)
{
	auto oldrigNode = curMmdNode;
	curMmdNode = nd;

	createNode(curMmdNode);
}
void MmdPhyAnimator::setBase(saba::MMDNode* baseNd)
{
	if (!curMmdNode) return;
	assert(nodeMap.find(curMmdNode) != nodeMap.end());
	//assert(curRigNode == &nodeMap[curMmdNode]);
	nodeMap[curMmdNode].curPose.setB(baseNd);
	 
	rigBaseNd = baseNd;
	setPose(0);

}

void MmdPhyAnimator::copySelectedKeys() {
	copiedKeys.clear();
	for (auto keyPair : rigSequencer->mSelectedKeys) {		
		copiedKeys.push_back({ keyPair.first, nodeMap[keyPair.first].keys[keyPair.second] });
	}

	int minFrame = std::numeric_limits<int>::max();
	for (const auto& key : copiedKeys) {
		if (key.second.frameIdx < minFrame) {
			minFrame = key.second.frameIdx;
		}
	}
	for (auto& key : copiedKeys) {
		key.second.frameIdx -= minFrame;
	}
}

void MmdPhyAnimator::pasteKeysToCurrentFrame(bool insertMode)
{
	if (copiedKeys.empty()) return;
	saveState();

	// Calculate duration of copied keys
	int maxCopiedFrame = 0;
	for (const auto& key : copiedKeys) {
		maxCopiedFrame = std::max(maxCopiedFrame, key.second.frameIdx);
	}
	int copyDuration = maxCopiedFrame + 1;  // +1 because frame is 0-based

	if (insertMode) {
		// First pass: Find all nodes that need adjustment and shift their first key after curFrame
		std::unordered_map<saba::MMDNode*, RigKey*> shiftedKeys;  // Store shifted key frames
		for (auto& rigNode : nodeMap) {
			auto it = std::lower_bound(rigNode.keys.begin(), rigNode.keys.end(), curFrame,
				[](const RigKey& key, int frame) { return key.frameIdx < frame; });
			if (it != rigNode.keys.end()) {
				int oldFrameIdx = it->frameIdx;
				it->frameIdx += copyDuration;
				shiftedKeys[rigNode.node] = &*it;  // Remember original position

				// Only update frame for the first key after curFrame
				{
					it->frame += copyDuration;
				}
			}
		}
	}

	 
	
	// Insert copied keys at curFrame
	for (const auto& [node, key] : copiedKeys) {
		auto& rigNode = nodeMap[node];
		RigKey newKey = key;
		newKey.frameIdx = curFrame + key.frameIdx;  // Adjust frameIdx relative to curFrame

		// Find insertion position
		auto insertPos = std::lower_bound(rigNode.keys.begin(), rigNode.keys.end(), newKey.frameIdx,
			[](const RigKey& k, int frame) { return k.frameIdx < frame; });

		if (insertPos == rigNode.keys.end()) {
			newKey.frame = newKey.frameIdx - rigNode.keys.back().frameIdx;
		}
		else {
			// Calculate frame duration for the new key
			if (insertPos != rigNode.keys.begin()) {
				auto prevKey = std::prev(insertPos);
				newKey.frame = newKey.frameIdx - prevKey->frameIdx;

			}
			else {
				newKey.frame = newKey.frameIdx;  // First key, frame is same as frameIdx
			}
			insertPos->frame = insertPos->frameIdx - newKey.frameIdx;
		}
		// Insert the new key
		rigNode.keys.insert(insertPos, newKey);
		
	}
	toClosestKeyFrame(curFrame);
	updateCumulativeTimes();
}

void MmdPhyAnimator::deleteSelectedKeys()
{
	if (playState != 0   ) return;
	bool isKey  = seqSelectionHasCurKey(); 
	if (!isKey)
	{
		seqClearSelection();
		if (curKey() && curKey()->frameIdx == curFrame)
		rigSequencer->mSelectedKeys.push_back({ curRigNode->node, curRigNode->curKeyIdx});
	}
	saveState();

	// Track nodes that need to be removed
	std::unordered_set<saba::MMDNode*> emptyNodes;
	std::vector<std::pair<saba::MMDNode*, size_t>> sortedKeys = rigSequencer->mSelectedKeys; 
	// Del order from back to front, or the index will be wrong
	std::sort(sortedKeys.begin(), sortedKeys.end(),
		[](const auto& a, const auto& b) {
			return (a.first == b.first) ? (a.second > b.second) : (a.first > b.first);
		});
	// Process each selected key
	for (const auto& [node, keyIdx] : sortedKeys) {
		auto it = nodeMap.find(node);
		if (it == nodeMap.end()) continue;

		auto& rigNode = *it;
		if (keyIdx >= rigNode.keys.size()) continue;

		// Calculate frame adjustment for next key if needed
		int addNext = 0;
		if (keyIdx < rigNode.keys.size() - 1) {
			addNext = rigNode.keys[keyIdx].frame;
		}

		// Delete the key
		rigNode.keys.erase(rigNode.keys.begin() + keyIdx);

		// If no keys left, mark node for removal
		if (rigNode.keys.empty()) {
			emptyNodes.insert(node);
		}
		else {
			// Adjust next key's frame if it exists
			if (keyIdx < rigNode.keys.size()) {
				rigNode.keys[keyIdx].frame += addNext;
			}
		}
	}

	// Remove empty nodes
	for (auto* node : emptyNodes) {
		removeNode(node);
	}

	toKey(0);

	// Clear selection
	rigSequencer->mSelectedKeys.clear();

	// Update state
	loadRigNode();
	updateCumulativeTimes();
}

void MmdPhyAnimator::loadRigNode()
{
	auto kp = keyPose();
	if (kp) {
		
		curNodePose().sbB = kp->sbB;
		curMmdNode = curRigNode->node; 
	}
}

void MmdPhyAnimator::setPose(int mode)
{
	if (!curRigNode) return;
	saveState();
	if (!curKey())
		newKeyIf(1, 1);
	auto& key = *curKey();
	assert(nodeMap.find(curMmdNode) != nodeMap.end());
	auto rigBase = curNodePose().getB();
 
	auto ndB = rigBase;
	auto ndT = curMmdNode;
	auto m = ndB?glm::inverse(ndB->mGlobalAnim) * ndT->GetGlobalTransform() : ndT->GetGlobalTransform();
	auto mt = ndB ? ndB->GetGlobalTransform() * m:m;
	Sb0->mmdFw(2, "sw21s", mt[3], glh::matRotateVec(mt, vec3(0, 0, 0)), 0xFFFFFFFF);
	vec3 trs = m[3];
	quat qtr = glm::quat_cast(m);
	vec3 rtt = glm::degrees(glm::eulerAngles(qtr));

	auto kp = keyPose();
	if (kp) key.pose = *kp;

	if (rigBase!=ndT) key.pose.setB( rigBase);

	if (rigBase && rigBase->model)
	for (size_t i = 0; i < sabas.size(); i++)
		if (rigBase->model->saba == sabas[i]) { key.pose.sbB = i; break; }
	if (key.pose.sbB >= sabas.size()) key.pose.sbB = sabas.size() - 1;
	key.pose.pos = trs;
	key.pose.rtt = qtr;
	key.pose.saved = 1;

	loadRigNode();
	updateCumulativeTimes();

	if (curRigNode->isCameraNode)
	{
		if (key.pose.cmds.size() == 0)
		{
			key.pose.cmds.push_back(KeyPoseCmd());
			selectedCmdIdx[0] = key.pose.cmds.size() - 1;
		}
		auto& cmd = key.pose.cmds[0];
		KeyPoseCmd::KpcCameraKey c; c.vc.init();
		if (cmd.type != KeyPoseCmdType::ectCameraKey)
		{
			cmd.type = KeyPoseCmdType::ectCameraKey;
			cmd.cmdPm = c;
		}
		
		auto& vc = c.vc;
		{
			auto sb = curRigNode->node->model->saba;
			auto cam = Ctx->gd.CamRtt;// Ctx->getViewCamera(editViewId);
			assert(cam == Ctx->gd.CamRtt);
			auto anm = Ctx->getCamTCAnimator();
			auto pos = cam->getAbsolutePosition(), tgt = anm->tld.trs;
			sb->mmdBaseInv.transformVect(pos);
			sb->mmdBaseInv.transformVect(tgt);
			vc.m_fov = cam->getFOV();
			//k.isPerspective = 0;
			vc.m_distance = -(pos - tgt).getLength();
			if (key.pose.getB()) {
				tgt = glh::invMatTransformVec(key.pose.getB()->getIfRbMat(), tgt);
				rtt = glm::eulerAngles(glm::quat(glm::inverse(glm::mat3(key.pose.getB()->getIfRbMat())) * glm::mat3(glm::quat(rtt))));
			}
			glm::mat4 transformation = (sb->mmdBaseInv * cam->getRelativeTransformation()); // your transformation matrix.
			
			glm::vec3 rtt;
			glm::extractEulerAngleYXZ(transformation, rtt.y, rtt.x, rtt.z);
			vc.m_rotate.x = -rtt.x;
			vc.m_rotate.y = -rtt.y;
			vc.m_rotate.z = -rtt.z;

			vc.m_interest = tgt;
			key.pose.pos = tgt;
			key.pose.rtt = quat(rtt);
			
		}
		auto& ccmd = std::get<KeyPoseCmd::KpcCameraKey>(key.pose.cmds[0].cmdPm); 
		ccmd.vc = c.vc;
 
	}

	
}



void MmdPhyAnimator::removeNode(saba::MMDNode* nd)
{
	if (playState != 0) return;
	if (!nd) nd = curMmdNode;

	// Remove node from nodeMap
	auto it = nodeMap.find(nd);
	if (it != nodeMap.end()) {
		bool delcur = (nd == curMmdNode);
		nodeIt = nodeMap.erase(it);

		if (delcur) {
			// Find next available node
			if (!nodeMap.empty()) {
				curMmdNode = nodeMap.begin()->node;
				curRigNode = &*nodeMap.begin();
				nodeIt = nodeMap.begin();
			}
			else {
				curMmdNode = nullptr;
				curRigNode = nullptr;
				nodeIt = nodeMap.end();
			}
		}
	}
}

void MmdPhyAnimator::switchNodes(int ofs)
{
	if (nodeMap.empty()) return;

	// Get current time if we have a current node and key
	float currentTime = 0.0f;
	if (curRigNode && curRigNode->editKeyIdx >= 0 &&
		curRigNode->editKeyIdx < curRigNode->keys.size()) {
		currentTime = curRigNode->keys[curRigNode->editKeyIdx].frameIdx * timePerFrame;
	}

	auto it = nodeMap.find(curMmdNode);
	if (it == nodeMap.end()) {
		it = nodeMap.begin();
	}
	else {
		if (ofs > 0) {
			++it;
			if (it == nodeMap.end()) it = nodeMap.begin();
		}
		else {
			if (it == nodeMap.begin()) it = nodeMap.end();
			--it;
		}
	}

	curMmdNode = it->node;
	curRigNode = &*it ;
	nodeIt = it;

	toClosestKeyTime(currentTime);

	loadRigNode();
}

void MmdPhyAnimator::switchToNode(saba::MMDNode* node)
{
	auto it = nodeMap.find(node);
	if (it == nodeMap.end()) return;

	// Get current frame if we have a current node and key
	int currentFrame = 0;
	if (curRigNode && curRigNode->editKeyIdx >= 0 &&
		curRigNode->editKeyIdx < curRigNode->keys.size()) {
		currentFrame = curRigNode->keys[curRigNode->editKeyIdx].frameIdx;
	}
	curMmdNode = it->node;
	curRigNode = &*it;
	nodeIt = it;
	//toClosestKeyFrame(currentFrame);
	seqSelectedIdx = std::distance(nodeMap.begin(), it);


	loadRigNode();
}

void MmdPhyAnimator::toClosestKeyTime(float currentTime)
{
	int frame = currentTime / timePerFrame;
	// Find closest key by frame
	if (!curRigNode->keys.empty()) {
		size_t closestIdx = 0;
		int minFrameDiff = std::numeric_limits<int>::max();

		for (size_t i = 0; i < curRigNode->keys.size(); ++i) {
			int frameDiff = std::abs(curRigNode->keys[i].frameIdx - frame);
			if (frameDiff < minFrameDiff) {
				minFrameDiff = frameDiff;
				closestIdx = i;
			}
		}
		toKey(closestIdx);
	}
}

inline void MmdPhyAnimator::toClosestKeyFrame(int currentFrame) {
	// Find closest key by frame
	if (!curRigNode->keys.empty()) {
		size_t closestIdx = 0;
		int minFrameDiff = std::numeric_limits<int>::max();

		for (size_t i = 0; i < curRigNode->keys.size(); ++i) {
			int frameDiff = std::abs(curRigNode->keys[i].frameIdx - currentFrame);
			if (frameDiff < minFrameDiff) {
				minFrameDiff = frameDiff;
				closestIdx = i;
			}
		}
		toKey(closestIdx);
	}
}

void MmdPhyAnimator::clearPoses()
{
	nodeMap.clear();

	curMmdNode = nullptr;
	curRigNode = nullptr;
	nodeIt = nodeMap.end();
}

bool MmdPhyAnimator::delCurFrame() {
	// Early return if no nodes
	if (nodeMap.empty()) return false;
	saveState();
	bool frameDeleted = false;
	bool hasKey = false;

	// Iterate through all nodes to check if curFrame has a key
	for (auto& rigNode : nodeMap) {
		if (rigNode.keys.empty()) continue;

		// Find the key that contains or is closest to curFrame
		for (size_t i = 0; i < rigNode.keys.size(); ++i) {
			auto& key = rigNode.keys[i];

			if (key.frameIdx == curFrame) {
				// If this is the exact frame, mark that it has a key
				hasKey = true;
				break;
			}
		}
		if (hasKey) {
			break;
		}
	}
	// If the current frame has a key, just return
	if (hasKey) {
		return false;
	}
	// If no key is found, proceed with frame deletion
	for (auto& rigNode : nodeMap) {
		if (rigNode.keys.empty()) continue;

		// Find the key that contains or is closest to curFrame
		for (size_t i = 0; i < rigNode.keys.size(); ++i) {
			auto& key = rigNode.keys[i];

			if (key.frameIdx > curFrame) {
				// If this frame is between two keys, adjust the previous key's duration
				if (i > 0) {
					auto& prevKey = rigNode.keys[i - 1];
					int frameDiff = key.frameIdx - prevKey.frameIdx;
					if (frameDiff > 1) {
						// Reduce the frame duration by 1
						key.frame--;
						frameDeleted = true;
					}
				}
				else {
					// If this is the first key and the current frame is before it, decrement the frame
					key.frame--;
					frameDeleted = true;
				}
				break;
			}
		}
	}
	if (frameDeleted)
		updateCumulativeTimes();
	return frameDeleted;
}

 
bool MmdPhyAnimator::playStart(int mode, int fromWhat)
{
 
		playState = mode;

		if (fromWhat == 1)  //from current frame
		{
			playTime = curFrame * timePerFrame;
		}
		else if (fromWhat == 2) //from last key frame
		{
			int lastKeyId = curRigNode->getKeyIdAtFrame(curFrame);
			if (lastKeyId < 0) curFrame = 0;
			else curFrame = curRigNode->keys[lastKeyId].frameIdx;
			// Calculate the start time based on the current key frame
			playTime = curFrame * timePerFrame;
		}
		else if (fromWhat == 3) //custom start time
		{
			curFrame = static_cast<int>(customStartTime / timePerFrame);
			// Calculate the start time based on the current key frame
			playTime = curFrame * timePerFrame;
			restoreWorld();
		}
		else if (fromWhat == 16) //sub MPA custom start time
		{
			curFrame = static_cast<int>(customStartTime / timePerFrame);
			// Calculate the start time based on the current key frame
			playTime = curFrame * timePerFrame;
		}
		else { //from beginning
			//Ctx->changeSpeed(1);
			playTime = 0;
		}
		
		MMDPhysics::phyTimeMul = 1;
		for (auto& rigNode : nodeMap) {
			if (!rigNode.keys.empty()) { 
				rigNode.lastPlayIdx = -1;				
			}

			 
#if MULTI_DIST_LISTENER			
			rigNode.curDistListeners.clear(); 			
#else			
			rigNode.distListenerOn = false;			
#endif
		}
 
		Ctx->changeSpeed(1);
		return true;
	 
 
}

bool MmdPhyAnimator::playStartFromFrame(int frame, float speed)
{
	customStartTime = frame * timePerFrame;
	playSpeed = speed;
	return playStart(1, 16);
}

void MmdPhyAnimator::playStop()
{
	if (playState != 0) {
		playState = 0;
		// Reset all nodes to their initial state
		for (auto& rigNode : nodeMap) {
			if (!rigNode.keys.empty()) {
				rigNode.curKeyIdx = 0;
			}
		}
	}

}
void MmdPhyAnimator::onPlayKeyChanged(RigNode& rigNode, std::vector<KeyPoseCmd> &cmds, bool editChange)
{
	if (rigNode.disabled) return;

	for (auto& cmd :  cmds)
	{
		switch (cmd.type)
		{
		case KeyPoseCmdType::ectDistance:
		{
			auto dist = std::get<KeyPoseCmd::KpcDistance>(cmd.cmdPm);
#if MULTI_DIST_LISTENER
			if (dist.TypeFlag == 0) {
				// Remove all listeners
				rigNode.curDistListeners.clear();
			}
			else if (dist.TypeFlag == 1) {
				// Add new listener
				RigNode::DistListener listener;
				listener.distListener = dist;
				listener.distListenerOn = true;
				rigNode.curDistListeners.push_back(listener);
			}
#else
			// Original single listener code
			if (dist.TypeFlag == 0) {
				rigNode.curDistListener = dist;
				rigNode.distListenerOn = false;
			}
			else if (dist.TypeFlag == 1) {
				rigNode.curDistListener = dist;
				rigNode.distListenerOn = true;
			}
#endif
		}
		break;
		case KeyPoseCmdType::ectFw:
		{
			auto fw = std::get<KeyPoseCmd::KpcFw>(cmd.cmdPm);

			if (fw.TypeFlag & 1) {
				rigNode.curFw = fw;

				Sb0->mmdFw(fw.fwType, fw.name, rigNode.node->rb0->getPosition(), rigNode.node->exd.irrSpeed / MMD_SABA_SCALE * fw.vMul, 0xFFFFFFFF);
			}
			if (fw.TypeFlag & 2) {
				rigNode.curFw = fw;
				rigNode.fwOn = 1;
			}
			else if (fw.TypeFlag & 4)
				rigNode.fwOn = 0;
		}
		break;
		case KeyPoseCmdType::ectTextFw:
		{
			auto tfw = std::get<KeyPoseCmd::KpcTextFw>(cmd.cmdPm);
			

			if (tfw.TypeFlag & 1)
			{
				rigNode.curTFW = tfw;
				genTFW(rigNode);
			}
			if (tfw.TypeFlag & 2) {
				rigNode.curTFW = tfw;
				rigNode.tfwOn = 1;
				rigNode.tfwGenCount = 0;
				rigNode.lastTimeTFW = gSceneTime;
			}
			else if (tfw.TypeFlag & 4)
				rigNode.tfwOn = 0;
			
		}
		break;
		case KeyPoseCmdType::ectConnectRb:
		{
			auto crb = std::get<KeyPoseCmd::KpcConnectRb>(cmd.cmdPm);
			if (crb.TypeFlag & 4) {
				rigNode.breakAllJoints();
			}
			if (crb.TypeFlag & 1) {
				PMXJoint jt{};
				auto& currKey = rigNode.keys[rigNode.curKeyIdx];
				auto rbNdA = crb.nodeNameA.size()>0?sabas[crb.sbA]->Pmx->GetNodeManager()->FindNode(crb.nodeNameA): currKey.pose.ndB;
				auto rbNdB = rigNode.node;
				if (!rbNdA || !rbNdA->rb0) break;
				jt.setLocalPos = crb.localPos;
				jt.translate = crb.translate;
				jt.t2B = crb.t2B;
				jt.rotate = glm::radians(crb.rotate);
				jt.r2B = glm::radians(crb.r2B);
				jt.springT = glm::vec3(crb.springT);
				jt.dampingT = glm::vec3(crb.dampingT);
				jt.springR = glm::vec3(crb.springR);
				jt.dampingR = glm::vec3(crb.dampingR);
				jt.limitMinT = crb.limMinT;
				jt.limitMaxT = crb.limMaxT;
				jt.limitMinR = crb.limMinR;
				jt.limitMaxR = crb.limMaxR;
				rigNode.connectedJoints.push_back(Sb0->Pmx->connectRbM(rbNdA->rb0, rbNdB->rb0, crb.lockT, crb.lockR, jt));
				
				if (rbNdB && currKey.pose.ndTmir 
					//|| nextKey.pose.ndTmir
					) {
					rbNdA = rbNdA->getMirrorNode();
					if (currKey.pose.ndBmir) rbNdB =  rbNdB->getMirrorNode();
					rigNode.connectedJoints.push_back(Sb0->Pmx->connectRbM(rbNdA->rb0, rbNdB->rb0, crb.lockT, crb.lockR, jt));
				}
			}
			if (crb.TypeFlag & 8) {
				auto& currKey = rigNode.keys[rigNode.curKeyIdx];
				auto rbNdA = crb.nodeNameA.size() > 0 ? sabas[crb.sbA]->Pmx->GetNodeManager()->FindNode(crb.nodeNameA) : currKey.pose.ndB;
				auto rbNdB = rigNode.node;
				auto sbB = rbNdB->model->saba;
				glm::mat4 m = rbNdA->model->saba->Rb0()->GetTransform();

				sbB->Pmx->rootRt = glm::eulerAngles(quat(m))*vec3(0,1,0);
				sbB->Pmx->rootTr = vec3(m[3])*vec3(1,0,1); 
			}
 

		}
		break;
		case KeyPoseCmdType::ectAddPhyObj:
		{
			auto cpm = std::get<KeyPoseCmd::KpcAddPhyObj>(cmd.cmdPm);
			
			if (cpm.TypeFlag & 4) {
				rigNode.apoOn = 0;
			}
			if (cpm.TypeFlag & 2) {
				rigNode.curAPO = cpm;
				rigNode.apoOn = 1; 
				rigNode.apoGenCount = 0;
				rigNode.lastTimeAPO = gSceneTime;
			}
			if ((cpm.TypeFlag & 1) || editChange) {
				rigNode.curAPO = cpm;
				for (int i = 0; i < rigNode.curAPO.objNum; i++) {
					genPhyObj(rigNode, i, false);
					if (rigNode.keys[rigNode.curKeyIdx].pose.ndTmir)
					{
						genPhyObj(rigNode, i, true);
					}
				}
			}
		}
		break;
		case KeyPoseCmdType::ectCubeGrid:
		{
			KeyPoseCmd::KpcCubeGrid& cpm = std::get<KeyPoseCmd::KpcCubeGrid>(cmd.cmdPm);
			if (cpm.TypeFlag & 4) {
				 
			}
			if (cpm.TypeFlag & 2) {
 
			}
			if ((cpm.TypeFlag & 1)) {
				BrickWallParams bwp{}; 
				const auto& pose = rigNode.curKey().pose; 
				bwp.pos =  (pose.pos) + 
					cpm.pos;
				bwp.rtt =  glm::degrees(glm::eulerAngles(pose.rtt)) +
					cpm.rtt;
				bwp.grid = cpm.grid;
				bwp.brickSize = cpm.brickSize;
				bwp.brickSpace = cpm.brickSpace;
				bwp.density = cpm.density;
				bwp.restitution = cpm.restitution;
				bwp.friction = cpm.friction;
				bwp.color = cpm.color;
				bwp.connect = cpm.connect;
				bwp.detectSbClosing = 1;
				bwp.centerOrigin = cpm.centerOrigin;
				if (rigNode.curTexId < cpm.imgPaths.size()) {
					bwp.texPath = ualib::Utf8toWcs(cpm.imgPaths[rigNode.curTexId]); 
					rigNode.curTexId = (rigNode.curTexId + 1) % cpm.imgPaths.size();
				}
				else bwp.texPath = L"";
				if (cpm.srcNd.locate > 0) {
					auto& pm = cpm;
					saba::MMDNode* node = nullptr;
					if (cpm.srcNd.nodeName.size() == 0)
						node = rigNode.node;
					else node = cpm.srcNd.node ? cpm.srcNd.node :
						cpm.srcNd.node = sabas[cpm.srcNd.sbIdx]->Pmx->GetNodeManager()->FindNode(cpm.srcNd.nodeName);
					bwp.baseNd =  node;
					 
				}
				if (rigNode.curCubeGrid < 0)
				{
					rigNode.curCubeGrid = CCubeGridSceneNode::createCubes(mmd, arRoot->snMmdSpaceRoot, bwp);
				}
				else {
					if (playState==0)		 
						CCubeGridSceneNode::Walls[rigNode.curCubeGrid]->recreateCubes(bwp);
					if (rigNode.curCubeGrid< CCubeGridSceneNode::Walls.size())
					CCubeGridSceneNode::Walls[rigNode.curCubeGrid]->resetCubes(cpm.reset==2?1:0, &bwp);
					
				}
			}
		}
		break;
		case KeyPoseCmdType::ectModel:
		{
			KeyPoseCmd::KpcModel& cpm = std::get<KeyPoseCmd::KpcModel>(cmd.cmdPm);
			std::string fileType = ualib::GetFileExtToLow(cpm.filePath);
			if (fileType == ".pmx")
			{
				IrrSaba* sb=nullptr;
				if (!rigNode.curModelSb)
				{
					PMXFileCreateParam cp;
					cp.filePath = cpm.filePath;
					cp.massMul = cpm.massMul;
					cp.frictionMul = cpm.frictionMul;
					cp.modelScale = abs(cpm.scale - 1.f) > 0.001f;
					cp.modelScaleVec3 = glm::vec3(cpm.scale);
					arRoot->loadMMD(cp, cpm.isCharacter ? 0x1101 : 0x101);
					auto it = arRoot->curArSn;
					sb = it->sb;
					rigNode.curModelSb = sb;
				}
				else sb = rigNode.curModelSb;
				sb->Pmx->rt1Tr = rigNode.curPose.pos;
				sb->Pmx->rt1Rt = glm::eulerAngles(rigNode.curPose.rtt);
				sb->ndRoot->SetAnimationTranslate(cpm.pos);
				sb->ndRoot->SetAnimationRotate(cpm.rtt);
				sb->ndRoot->SetScale(vec3(cpm.scale));
			}

		}
		break;
		case KeyPoseCmdType::ectMotion:
		{
			KeyPoseCmd::KpcMotion& cpm = std::get<KeyPoseCmd::KpcMotion>(cmd.cmdPm);
			std::string fileType = ualib::GetFileExtToLow(cpm.filePath);
			if (cpm.filePath.size() == 0) {
				auto sb = sabas[rigNode.sbT];
				sb->resetAnimation();
			}
			else if (fileType == ".vmd")
			{
				auto sb = sabas[rigNode.sbT];
				sb->loadAnimation(ualib::Utf8toWcs(cpm.filePath).c_str());
				//sb->setAnimationSpeed(cpm.speedMul);
				rigNode.vmdStartTime = curTime;
				if (cpm.startTime < -0.99f)  rigNode.vmdStartTime = 0;
				else if (cpm.startTime > 0.01f) rigNode.vmdStartTime = curTime - cpm.startTime;
				rigNode.vmdSpeedMul = cpm.speedMul;
				
			}
			else if (fileType == ".vpd")
			{
				auto sb = sabas[rigNode.sbT];
				sb->loadPose(ualib::Utf8toWcs(cpm.filePath).c_str(),0);
				rigNode.vmdStartTime = curTime;
			}
			else if (fileType == ".json")
			{
				auto sb = sabas[rigNode.sbT];
				bool swap = cpm.swapLR;
				if (cpm.swapLR == 2) swap = (rigNode.swapLRCC++) % 2;
				MpaLoadJsonParam lf;
				lf.filepath = cpm.filePath;
				lf.swapLR = swap;
				lf.playSpeed = cpm.speedMul;
				sb->startSbMPA(lf);
			}
		}
		break;
		case KeyPoseCmdType::ectCustom:
		{
			auto sb = sabas[rigNode.sbT];
			KeyPoseCmd::KpcCustom& cpm = std::get<KeyPoseCmd::KpcCustom>(cmd.cmdPm);
			switch (cpm.cmdType)
			{
			case ECustomCmd::eccPhyAnim:
				sb->setPhyAnim(0, cpm.iv[0], true);
				sb->localPhyAnim = cpm.iv[1];
				break;
			case ECustomCmd::eccPhyAnimMul:
			{
				auto nd = rigNode.node;
				for (int i = 0; i < cpm.iv[2]; i++) if (auto pn=nd->GetParent()) nd = pn;
				nd->forEachSubNodes([=](saba::MMDNode* nd) {
				if (nd->rb0 && nd->rb0->dynRbType) {
					if (cpm.iv[0]) nd->phyAnimRatT = cpm.fv[0];
					if (cpm.iv[1]) nd->phyAnimRatR = cpm.fv[1];
				}
					}, true);
			} break;
			case ECustomCmd::eccPhyToAnim:
			{
				auto m = sb->Rb0()->getNodeTransform();
				auto pos = m[3]; pos.y = 0;
				auto rtt = glm::eulerAngles(glm::quat(m)); rtt.x = rtt.z = 0;
				sb->Pmx->rt1Tr = pos;
				sb->Pmx->rt1Rt = rtt;
			}
				break;
			case ECustomCmd::eccKinematic:
				sb->setAllDynRbActive(!cpm.iv[0]);
				break;

			case ECustomCmd::eccTimeMul:
				Ctx->changeSpeed(std::clamp(cpm.fv[0], 0.f, 8.f));
				break;

			case ECustomCmd::eccPhyTimeMul:
				MMDPhysics::phyTimeMul = std::clamp(cpm.fv[0], 0.f, 8.f);
				break;
			case ECustomCmd::eccCtrForce:
				sb->CenterForce = cpm.iv[0];
				sb->centerForceMul = cpm.fv[0];
				break;
			case ECustomCmd::eccKeyEvent: {
				auto old = inputingKey;
				inputingKey = true;
				Ctx->getLib()->CurStage()->sendKeyEvent((irr::EKEY_CODE)cpm.iv[0], 1, cpm.iv[1], cpm.iv[2], cpm.iv[3]);
				inputingKey = old;
			}
				break;
			case ECustomCmd::eccGoToFrame:	
				if (playState > 0) if (this == mmd->mainMPA) {
					//setCurFrame(cpm.iv[0]-1);	
							
					nextFrameTo = cpm.iv[0];

					 
				}
				break;
			default:
				break; // Handle unknown command types
			}
		}
		break;
		}

	}
}
void MmdPhyAnimator::genTFW( RigNode& rigNode)
{
	auto& tfw = rigNode.curTFW;
 
	ualib::VtxFwGrid vfg=tfw.opm.vfg;
	vfg.fwSpecular = tfw.fwSpecular;
	vfg.baseMass = tfw.baseMass;
	vfg.hitCvt = tfw.hitCvt; vfg.hitGroundCvt = tfw.hitGroundCvt;
	vfg.hitVelChgLimit = tfw.hitVelChgLimit;
	vfg.antiCvtLimit = 1; vfg.movHitLimit = 1;
	vfg.srcPosMode = tfw.srcPosMode;
	vfg.srcVelMode = tfw.srcVelMode;
 
	vfg.velOfs = tfw.velOfs;
	vfg.useCamMat = tfw.srcPosMode == 2 ? 1 : 3;
	if (tfw.srcNd.locate == 1) {
		vfg.useCamMat = 2;
		vfg.nodeLocalMat = glm::rotate(glm::mat4(1),  glm::radians(180.f), glm::vec3(0, 1, 0));
	}
	vfg.connectMode = tfw.connectMode;
	vfg.baseNode = rigNode.node;
	ualib::FT2TextParam& tp = tfw.tp;


	rigNode.curAPO = tfw;
	rigNode.popmt = genPhyObj(rigNode, 0, false,-1,0,1);
	auto& pm = rigNode.popmt;
	tp.srcPos = Sb0->mmd2irr(pm.pos);	tp.srcVel = Sb0->mmd2irr(pm.vel);
	tp.tgtPos = Sb0->mmd2irr(pm.tgt);
	pm.poType = 0;
	vfg.pmxrb = &pm.pmxrb;
	vfg.oTgtSb = rigNode.oTgtSb;
 
 	Sb0->Eqv->genTextFw(tfw.text, tfw.name.c_str(), tfw.allInOne, &vfg,&tp);
	Sb0->actVoiceFx(Sb0->getItemIdx(), 0, 60, tfw.text);
 
}
PhyObjParam MmdPhyAnimator::genPhyObj(RigNode& rigNode, int  idx, bool mirror,int frameCurKeyIdx,float keyRatio, int mode)
{
	KeyPoseCmd::KpcAddPhyObj& cpm = rigNode.curAPO;
	PhyObjParam pm=cpm.opm;
	PhyObj* epo{};
	static int shapeType[] = { 0, 1, 32, 11, 4, 5, 6, 7, 8, 9 };
	pm.poType = shapeType[cpm.poType];
	pm.mass = cpm.mass;
	pm.size = cpm.size;
#if 1
	float angle = static_cast<float>(rand()) / RAND_MAX * 2.0f * glm::pi<float>();
	float radius = std::sqrt(static_cast<float>(rand()) / RAND_MAX) * cpm.emitR;
	pm.pos = cpm.src + glm::vec3(radius * std::cos(angle), 0.0f, radius * std::sin(angle));
#else
	pm.pos = cpm.src +   ualib::UaRandVec3()*cpm.emitR;
#endif
	pm.rtt = cpm.rtt;
	pm.vel = cpm.vel;
	pm.autoKill = cpm.autoRemove;
	pm.timer = cpm.removeTimer;
	pm.tgt = cpm.tgt;

	
	if (cpm.TypeFlag & 16)
	{
		epo = mmd->Pom->objRec[rigNode.lastPoUid[mirror?1:0]].ifObj;
	}

	float sc = cpm.sizeScale;
	pm.meshScale = float3(sc);
	if (frameCurKeyIdx >= 0 && keyRatio > 0.00001f)
	{
		assert(frameCurKeyIdx < rigNode.keys.size() - 1);
		if (epo && rigNode.keys[frameCurKeyIdx + 1].pose.cmds.size())
		{
			auto cmd = rigNode.keys[frameCurKeyIdx + 1].pose.cmds[0];//first Must be AddPhyObj
			if (cmd.type == KeyPoseCmdType::ectAddPhyObj) {
				auto cpm1 = std::get<KeyPoseCmd::KpcAddPhyObj>(cmd.cmdPm);
				if (cpm.modFlag & 0x04) pm.pos = glm::vec3(glm::mix(cpm.src, cpm1.src, keyRatio));
				if (cpm.modFlag & 0x10)	pm.meshScale = glm::vec3(glm::mix(cpm.sizeScale, cpm1.sizeScale, keyRatio));

			}
			else { assert(0); }
		}
	}

	if (epo &&  !(cpm.modFlag&4) ) {
		pm.pos = epo->rb0->getPosition();
	}
	else if (cpm.srcNd.locate > 0) {
		saba::MMDNode *node = nullptr;
		if (cpm.srcNd.nodeName.size() == 0)
			node = rigNode.node;
		else node= cpm.srcNd.node? cpm.srcNd.node : 
			cpm.srcNd.node= sabas[cpm.srcNd.sbIdx]->Pmx->GetNodeManager()->FindNode(cpm.srcNd.nodeName);
		if (mirror) node = node->getMirrorNode();
		if (node)
		{
			pm.atkSbIgnore[sabas[cpm.srcNd.sbIdx]->getItemIdx()] = 1;
			switch (cpm.srcNd.locate)
			{
			case 1:	pm.pos = glh::matTransformVec(node->GetGlobalTransform(), pm.pos); break; //cpm.src
			case 2:	if (node->rb0) pm.pos = glh::matTransformVec(node->rb0->GetTransform(), pm.pos); break; //cpm.src
			case 3:	if (node->rb0) pm.pos = glh::matTransformVec(node->rb0->getNodeTransform(), pm.pos); break; //cpm.src
			default:
				break;
			}
		}
	}
	else 
	{
		pm.pos += rigNode.curPose.pos;
	}
	auto tNode = cpm.tgtNd.node ? cpm.tgtNd.node : cpm.tgtNd.node = sabas[cpm.tgtNd.sbIdx]->Pmx->GetNodeManager()->FindNode(cpm.tgtNd.nodeName);
	if (!tNode) tNode = curNodePose().getB() ? curNodePose().getB() : rigNode.sb->ndRbRoot;
	if (mirror) tNode = tNode->getMirrorNode();
	IrrSaba* oTgtSb = nullptr;
	if (cpm.tgtNd.locate > 0) {

		if (tNode)
		{
			oTgtSb = tNode->model->saba;
			switch (cpm.tgtNd.locate)
			{
			case 1:	pm.tgt = glh::matTransformVec(tNode->GetGlobalTransform(), cpm.tgt); break; //cpm.tgt
			case 2:	if (tNode->rb0) pm.tgt = glh::matTransformVec(tNode->rb0->GetTransform(), cpm.tgt); break; //cpm.tgt
			case 3:	if (tNode->rb0) pm.tgt = glh::matTransformVec(tNode->rb0->getNodeTransform(), cpm.tgt); break; //cpm.tgt
			case 4: {
				auto sb = tNode->model->saba;
				auto vmd = sb->Vmd.get();
				sb->Pmx->saveNodesTransform();
				
				vmd->Evaluate((curTime + cpm.flyTime + gFrameTime*float(idx)/cpm.objNum)*30.f, 1, 2);
				
				sb->Pmx->UpdateNodeAnimation(false);

				pm.tgt = tNode->getRbAnimGlobalPos();
				sb->Pmx->loadNodesTransform();
				
				sbFw2D("sw21s", pm.tgt, vec3(0, 0, 0), 0x3FFF0000);

			}break;
			default:
				break;
			}
			
		}
		
	}

	switch (cpm.srcPosMode)
	{
	case 1:
	{
		vec3 dir = cpm.vel;
		if (tNode) dir = -pm.tgt + tNode->model->saba->ndRoot->getGlobalPos(), dir.y = 0;
		pm.pos = glh::calculateInitialPosition(dir,
			std::max(1.f, glm::length(cpm.vel)), cpm.vel, pm.tgt, GRAVITY_VEC, cpm.flyTime, 1, 60 * SABA_PHYSICS_FRAMESTEP);
	}	break;
	case 2:
		pm.pos = Sb0->mtCam.getTranslation();
		break;
	}

	switch (cpm.srcVelMode)
	{
	case 1:pm.vel = // glh::calcVelocityP2PinT(pm.pos, pm.tgt, cpm.flyTime, GRAVITY_VEC);
		glh::calcVelocityP2PinTimeGuess(pm.pos, pm.tgt, GRAVITY_VEC,cpm.flyTime * MMDPhysics::phyTimeMul, pm.pmxrb.m_translateDimmer, 60 * SABA_PHYSICS_FRAMESTEP);
		break;
	case 2: pm.vel = glh::matRotateVec(Sb0->mtCam, glm::vec3(0, 0, 100));
		break;
	}

	pm.atkFlag =
 
		0 //| (1 << ENdId::eoL) | (1 << ENdId::eoR)  
		| (1 << ENdId::efL)
		| (1 << ENdId::efR)
		| (1 << ENdId::ehL)
		| (1 << ENdId::ehR) // |
		| (1 << ENdId::eHd)//
		//| (1 << ENdId::eYa) 
		//|(1 << ENdId::elL) | (1 << ENdId::elR)	
 		;
	
	pm.disableHitForTime = cpm.disableHitTime;
	pm.atkFlag = cpm.atkFlag;
	 

	switch (pm.poType)
	{
	case 1: {
		pm.instancedMesh = L"data/mesh/ball.obj";

#if EGGBALL
		pm.pmxrb.m_friction =  0.f;	 //	pm.ownerAdd = -1;
		pm.pmxrb.m_repulsion = 0.0f;
#endif
		break;
	}
	case 32:
	{
		static auto sms = SceneManager->getMesh("data/mesh/coin.obj");
		pm.meshScale = float3(cpm.size * sc);
		pm.size = { pm.size.x * 1.414,  pm.size.y / 3, pm.size.z * 1.414 };
		pm.size = pm.size * sc  ; 	
		pm.mesh = sms;
		
		static int mcc = 0;
		static const std::wstring mfn[] = { L"data/mesh/coin.obj",L"data/mesh/kd.obj",L"data/mesh/bowl.obj" };
		//pm.size = vec3(2, 1, 2);
		pm.instancedMesh = mfn[mcc++ % 1]; // "data/mesh/coin.obj";
		
	}
		break;
	}
	rigNode.oTgtSb = oTgtSb;
	if (mode == 1)
		return pm;
	if (cpm.TypeFlag & 16)
	{ 
		if (epo) {
			mmd->Pom->modObj(epo,pm,cpm.modFlag);
		}
	}
	else {
		//======================================== ADD OBJECT ==========================================
		auto po = mmd->Pom->addObj(pm);
		//==============================================================================================
		po->oTgtSb = rigNode.sb;
		Ctx->setSharedRbCb(po->rb);
		int ndbid = 0;
		int uid = po->uid;
		rigNode.lastPoUid[mirror?1:0] = uid;
		setPPNdB(ndbid, uid);
		if (pm.snIm) {
			auto& mtr = pm.snIm->getMaterial(0);
			switch (pm.poType)
			{
			case 1: {
				mtr.SpecularColor = 0x80FFFFFF;
				//mtr.DiffuseColor = 0x10C0E0FF;

			}break;
			case 32:
			{
				mtr.SpecularColor = 0x10808080;
				mtr.DiffuseColor = 0xFFDAA520;
			}
			break;
			}
		}
	}



	return pm;
}


void MmdPhyAnimator::updatePlayback(float time)
{
	if (playState != 0)
	{
		float playTime = time;
		if (playTime >= 1200) {
			playStop();
			toKey(0);
			return;
		}
		// Handle range playback
		if (playState == 2 && useRangePlayback) {
			if (playTime >= customEndTime) {
				playStop();
				curFrame = customStartTime / timePerFrame;
				playStart(2, 1);
				return;
			}

		}

		curTime = playTime;
		curFrame = static_cast<int>(curTime / timePerFrame + 0.5f);
	}

	static float lastTime = 0.f;
	float deltaTime = time - lastTime;
	lastTime = time;
	
	int currentFrame = static_cast<int>(time *  MMD_PHA_FPS);
	if (currentFrame == lastPlayFrame + 1 && playState != 0 )
	{
		if (frameBreak == currentFrame)
		{
			frameBreak = -1;
			playStop();
			Ctx->scenePaused = true;
			return;
		}
	};
	if (playState && currentFrame > maxCumFrame) {
		playStop();
	}
	//DP(("deltaTime %3.3f  fr %3d", deltaTime,currentFrame));
	lastPlayFrame = currentFrame;
	setCurFrame(currentFrame);
	for (auto& rigNode : nodeMap) {
		if (rigNode.keys.size() <= 0 || rigNode.disabled) continue;
		if (currentFrame < rigNode.keys[0].frame) continue;
		// Find current and next key frames
		size_t currIdx = 0;
		for (size_t i = 0; i < rigNode.keys.size(); i++) {
			if (currentFrame >= rigNode.keys[i].frameIdx) {
				currIdx = i;
			}
		}
		auto& currKey = rigNode.keys[currIdx];
		if (currKey.disabled)
			continue;
		if (currIdx > 1) {
		DP(("curkey %d %d", currIdx, rigNode.keys.size()));
	}
		auto sb = rigNode.node->model->saba;

		rigNode.curKeyIdx = currIdx;
		if (rigNode.lastPlayIdx != currIdx) {
			rigNode.lastPlayIdx = currIdx;
			onPlayKeyChanged(rigNode, currKey.pose.cmds);
			
		}
		size_t nextIdx = std::min(currIdx + 1, rigNode.keys.size() - 1);

		// Calculate interpolation ratio based on frames
		float ratio = 0.0f;
		if (currIdx != nextIdx) {
			int frameStart = currKey.frameIdx;
			int frameEnd = rigNode.keys[nextIdx].frameIdx;
			ratio = //static_cast<float>(currentFrame - frameStart) / (frameEnd - frameStart);
				(time-frameStart*timePerFrame)/(frameEnd-frameStart)/timePerFrame;
			ratio = std::clamp(ratio, 0.0f, 1.0f);
		}
		if (rigNode.curKeyIdx != rigNode.editKeyIdx && camPreview) {
			rigNode.editKeyIdx = rigNode.curKeyIdx  ;
			loadRigNode();
		}

		auto& nextKey = rigNode.keys[nextIdx];

		float timeStart = currKey.frameIdx * timePerFrame;
		float timeEnd = nextKey.frameIdx * timePerFrame;

		if (rigNode.node->isRoot()) {
			
			sb->setCurTime((time- rigNode.vmdStartTime) * rigNode.vmdSpeedMul);
		}
		if (currKey.pose.setRd)
			rigNode.node->model->saba->rd.phyAniMul = glm::clamp(glm::mix(currKey.pose.rdRatio, nextKey.pose.rdRatio, 0.f), 0.f, 1.f);
		if (currIdx >= rigNode.keys.size() - 1 && currentFrame > rigNode.keys.back().frameIdx) continue;
		

		// =========== Real Time Force START ==========	 


		// Get global transforms
		KeyPoseData& p0 = currKey.pose;
		KeyPoseData& p1 = nextKey.pose;

		int mtt =  (p0.baseMode);
 
		bool isMir = false;
		auto nd = rigNode.node, ndB = p0.getB();
		bool genApo = false;
		float fmul=1;
		{
			doUpdateNode(nd,  ndB, p0,  mtt, isMir, p1,   ratio, fmul, currKey, nextKey, rigNode, genApo);
		}
		// Handle mirroring
		if (currKey.pose.ndTmir 
			//|| nextKey.pose.ndTmir
			) {
			auto nd = rigNode.node->getMirrorNode();
			isMir = true;
			if (currKey.pose.ndBmir ) ndB = p0.getB() ? p0.getB()->getMirrorNode() : nullptr;
			doUpdateNode( nd,ndB, p0,   mtt, isMir, p1,   ratio, fmul, currKey, nextKey, rigNode, genApo);			 
		}
	}

}

void MmdPhyAnimator::doUpdateNode(saba::MMDNode*& nd, saba::MMDNode* ndB, KeyPoseData& p0,   int mtt, bool isMir, KeyPoseData& p1,   float ratio, float& fmul, RigKey& currKey, RigKey& nextKey, RigNode& rigNode, bool& genApo)
{
	mat4 m0;
	mat4 m1;
	auto baseMode1 = [=](saba::MMDNode* const& node, KeyPoseData& p0, glm::mat4& m0)
		{
			auto sbT = node->model->saba ;
			vec3 ypT = sbT->ndYao->rb0->getPosition(), ypB = p0.getB()->model? p0.getB()->model->saba->ndYao->rb0->getPosition():p0.getB()->getGlobalPos();
			core::vector3df dir = ypB - ypT;
			glm::vec3 rtt;
			if (p0.baseMode == 1) {
				node->baseLastRtt = dir.getHorizontalAngleRad();
				node->baseLastRtt.x = node->baseLastRtt.z = 0;
			}
			glm::quat qr = node->baseLastRtt;
			m0 = glm::translate(mat4(1), ypB) * glm::mat4_cast(qr);
		};

	if (ndB && (p0.baseMode == 1 || p0.baseMode == 2)) {
		baseMode1(nd, p0, m0);
	}
	else m0 = (ndB ? ndB->getMatT(mtt) : mat4(1));
	m0 *= isMir ? glh::mirrorMatOnX(p0.relMat()) : p0.relMat();

	if (p1.getB() && (p1.baseMode == 1 || p1.baseMode == 2)) {
		baseMode1(nd, p1, m1);
	}
	else m1 = (p1.getB() ? p1.getB()->getMatT(mtt) : mat4(1));
	m1 *= isMir ? glh::mirrorMatOnX(p1.relMat()) : p1.relMat();

	// Extract and interpolate position and rotation
	vec3 posNd0 = m0[3];
	vec3 posNd1 = m1[3];
	quat rotNd0 = glm::quat_cast(m0);
	quat rotNd1 = glm::quat_cast(m1);

	vec3 interpPos = glm::mix(posNd0, posNd1, ratio);
	quat interpRot = glm::slerp(rotNd0, rotNd1, ratio);
	rigNode.curPose.pos = interpPos;
	rigNode.curPose.rtt = interpRot;
	// Calculate force multiplier
	fmul = glm::mix(currKey.pose.fmul, nextKey.pose.fmul, ratio);

	bool genTfw = false;
	if (!isMir) {
		if (rigNode.tfwOn) {
			if (gSceneTime - rigNode.lastTimeTFW > rigNode.curTFW.genIntv - 0.001f && rigNode.tfwGenCount < rigNode.curTFW.maxGen) {
				genTfw = true;
				rigNode.lastTimeTFW = gSceneTime;
				rigNode.tfwGenCount++;
				genTFW(rigNode);
			}
		}
		if (Pm.isEditor && rigNode.isCameraNode && currKey.pose.cmds.size() && nextKey.pose.cmds.size() && currKey.pose.cmds[0].type == KeyPoseCmdType::ectCameraKey)
		{
			auto sb = nd->model->saba;
			auto& ccmd = std::get<KeyPoseCmd::KpcCameraKey>(currKey.pose.cmds[0].cmdPm);
			auto& ncmd = std::get<KeyPoseCmd::KpcCameraKey>(nextKey.pose.cmds[0].cmdPm);
			auto vc0 = ccmd.vc;
			auto vc1 = ncmd.vc;
			if (p0.getB()) {
				auto base_mat = p0.getB()->getMatT( (p0.baseMode));// p0.getB()->getIfRbMat();
				if (ccmd.local) {
					vc0.m_interest = base_mat * vec4(vc0.m_interest, 1);
					glm::mat4 rotMat = glm::eulerAngleYXZ(-vc0.m_rotate.y, -vc0.m_rotate.x, -vc0.m_rotate.z);
					glm::mat4 transformation = glm::mat4(glm::quat(glm::mat3(base_mat)) * glm::quat(rotMat));
					glm::extractEulerAngleYXZ(transformation, vc0.m_rotate.y, vc0.m_rotate.x, vc0.m_rotate.z);
					vc0.m_rotate = -vc0.m_rotate;
				}
				else {
					// World space - only transform interest point
					vc0.m_interest = base_mat * vec4(vc0.m_interest, 1);
				}
			}
			if (p1.getB()) {
				auto base_mat = p1.getB()->getMatT( (p1.baseMode)); //p1.getB()->getIfRbMat();
				if (ncmd.local) {
					vc1.m_interest = base_mat * vec4(vc1.m_interest, 1);
					glm::mat4 rotMat = glm::eulerAngleYXZ(-vc1.m_rotate.y, -vc1.m_rotate.x, -vc1.m_rotate.z);
					glm::mat4 transformation = glm::mat4(glm::quat(glm::mat3(base_mat)) * glm::quat(rotMat));
					glm::extractEulerAngleYXZ(transformation, vc1.m_rotate.y, vc1.m_rotate.x, vc1.m_rotate.z);
					vc1.m_rotate = -vc1.m_rotate;
				}
				else {
					// World space - only transform interest point
					vc1.m_interest = base_mat * vec4(vc1.m_interest, 1);
				}
			}
			saba::blendCamKey(vc0, vc1, ratio, mmdCamDat);
			
			sb->updateCameraOnMmdCamera(mmdCamDat, false);
			auto viewOnPause = playState == 0 && camPreview && curRigNode == &rigNode;
			if ((playState > 0 && ccmd.anim) || (viewOnPause))
			{
				if (Ctx->getViewCamera(0) != sb->irrCam)
				{
					Ctx->setViewCamera(sb->irrCam, 0);
					if (viewOnPause) {
						//irrcamToRttCam(sb);

					}
				}
			}
			else Ctx->setViewCamera(Ctx->gd.CamRtt);
		}
	}
	// Apply interpolated transform

	//float sc = 0.96f - 0.66f * std::min(1.f, fmul) * (curRigNode->sbT == 0 ? 1.f : 0.f);
	if (rigNode.moveNodeAnim) {
		if (nd->GetIndex() == 0) 
			nd->SetAnimationTranslate(interpPos);
		else nd->setAnimToGlobalPos(interpPos);
		nd->SetAnimationRotate(interpRot);
	}
	else if (nd->rb0) if (fmul != 0) {
		nd->rb0->scaleVel( p0.fsc);
		nd->rbAtk->addLinearVelToPosLimitDis(interpPos, mmd->rigForceMul * fmul, 0, 2.f);
	}
	if (p0.rttMode == 2 && ndB) {
		vec3 front = isMir ? p0.front * vec3(-1, 1, 1) : p0.front; if (front == vec3(0)) front = vec3(0, 0, -1);
		glm::vec3 dirTgt = glm::fastNormalize((ndB->rb0 ? ndB->rb0->getPosition() : ndB->getGlobalPos()) - nd->rb0->getPosition());
		glm::vec3 dirSrc = glm::fastNormalize(glh::matRotateVec(nd->rb0->GetTransform(), front));
		sbFw2LineD("pt", nd->rb0->getPosition(), nd->rb0->getPosition() + dirTgt * 3.5f, 0xCC808000, 60);
		sbFw2LineD("pt", nd->rb0->getPosition(), nd->rb0->getPosition() + dirSrc * 3.5f, 0xCC008080, 60);
		nd->rb0->rotateFromDirToDir(dirSrc, dirTgt, p0.amul * 10000.f);
	}
	if ( Ctx->getDriver()->renderUI && &curEditKey()->pose==&p0)	sbFw2(camPreview?"pt": "swc", interpPos, vec3(0), video::SColor(0xFF, 255, 255, 255));
	if (rigNode.fwOn) {
		onNodeFw(rigNode, nd);
	}
	// if (rigNode.tfwOn)            onNodeTextFw(rigNode, node);

	if ((!isMir && rigNode.apoOn) || (isMir && genApo)) {
		if (gSceneTime - rigNode.lastTimeAPO > rigNode.curAPO.genIntv - 0.001f && rigNode.apoGenCount < rigNode.curAPO.maxGen) {
			genApo = true;
			rigNode.lastTimeAPO = gSceneTime;
			rigNode.apoGenCount++;
			for (int i = 0; i < rigNode.curAPO.objNum; i++) genPhyObj(rigNode, i, isMir,rigNode.curKeyIdx, ratio);
		}
	}
	
#if MULTI_DIST_LISTENER
	if (!rigNode.curDistListeners.empty()) {
		sbFw2LineD("pt1s", nd->rb0->getPosition(), nd->rb0->getPosition(), 0xCC008000, 60);
		auto ndb = rigNode.keys[rigNode.curKeyIdx].pose.getB();
		if (ndb && ndb->rb0) {
			vec3 bpos = ndb->rb0->getPosition();
			
			for (auto& listener : rigNode.curDistListeners) {
				if (!listener.distListenerOn) continue;
				
				float dist = nd->rb0->disTo(bpos);
				if (listener.distListener.disType == 1) 
					dist *= nd->GetScale().x;
					
				if (dist < listener.distListener.distance) {
					sbFw2D("sw21s", bpos, vec3(0, 0, 0), 0xFFFF0000);
					sbFw2LineD("pt1s", nd->rb0->getPosition(), bpos, 0xCCF08000, 60);
					
					switch (listener.distListener.countMode) {
					case 0: listener.distListenerOn = false; break;
					case 1: ndb->distListenerOn = false; break;
					}
					
					onPlayKeyChanged(rigNode, listener.distListener.onCmds);
				}
			}
		}
	}
#else
	if (rigNode.distListenerOn ) {
		sbFw2LineD("pt1s", nd->rb0->getPosition(), nd->rb0->getPosition(), 0xCC008000, 60);
		auto ndb = rigNode.keys[rigNode.curKeyIdx].pose.getB();
		if (ndb && ndb->distListenerOn && ndb->rb0) {
			vec3 bpos = ndb->rb0->getPosition();

			float dist = nd->rb0->disTo(bpos);
			if (rigNode.curDistListener.disType == 1) dist *= nd->GetScale().x;
			if (dist < rigNode.curDistListener.distance)
			{
				sbFw2D("sw21s", bpos, vec3(0, 0, 0), 0xFFFF0000); sbFw2LineD("pt1s", nd->rb0->getPosition(), bpos, 0xCCF08000, 60);
				switch (rigNode.curDistListener.countMode) {
				case 0:rigNode.distListenerOn = false; break;
				case 1:ndb->distListenerOn = false; break;
				}
				
				onPlayNewKey(rigNode, rigNode.curDistListener.onCmds);
			}
			 
		}
	}
#endif
}

void MmdPhyAnimator::irrCamToRttCam(IrrSaba* sb)
{
	auto anm = ((CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());
	anm->tld.trs = sb->irrCam->getTarget();

	glm::mat4 rotationMatrix = glm::yawPitchRoll(mmdCamDat.m_rotate.y, mmdCamDat.m_rotate.x, mmdCamDat.m_rotate.z);
	anm->tld.rtt = -glm::eulerAngles(quat(rotationMatrix)) * core::RADTODEG;
	anm->tld.zoomPow = logf(-mmdCamDat.m_distance * MMD_SABA_SCALE) / logf(anm->DefaultZoom);
	std::swap(anm->tld.rtt.x, anm->tld.rtt.y);
}

void MmdPhyAnimator::sOtSl(UpdateCallback callback)
{
	auto* currentKeyPose = editKeyPose();
	if (!currentKeyPose) return;
	if (rigSequencer->mSelectedKeys.size() < 2) return;
	//saveState();

	for (auto& selectedKey : rigSequencer->mSelectedKeys) {
		auto nodeIt = nodeMap.find(selectedKey.first);
		if (nodeIt != nodeMap.end() && selectedKey.second < nodeIt->keys.size()) {
			callback(nodeIt->keys[selectedKey.second].pose, *currentKeyPose);
		}
	}
}



void MmdPhyAnimator::onNodeFw(RigNode& rigNode, MMDNode* node)
{
	const auto& fw = rigNode.curFw;
	Sb0->mmdFw(fw.fwType, fw.name, fw.rbPos ? node->rb0->getPosition() : node->getGlobalPos(), node->rb0->vel * fw.vMul, ((irr::video::SColorf*)&fw.color.x)->toSColor());
}

void MmdPhyAnimator::onSbNodePicked(MMDNode* node)
{
	if (ImGui::GetIO().MouseDoubleClicked[0])
	for (auto&   rigNode : nodeMap) {
		if (rigNode.node->model == node->model) {
			switchToNode(rigNode.node);
			 
			break;
		}
	}
 
		
}

bool MmdPhyAnimator::seqSelectionHasCurKey() {
	if (rigSequencer->mSelectedKeys.size() == 0) return false;
	return std::find(rigSequencer->mSelectedKeys.begin(), rigSequencer->mSelectedKeys.end(),
		std::make_pair(curRigNode->node, curRigNode->editKeyIdx)) != rigSequencer->mSelectedKeys.end();
}

void MmdPhyAnimator::seqClearSelection()
{
	rigSequencer->mSelectedKeys.clear();
}


void MmdPhyAnimator::saveWorld()
{
	for (auto sb : sabas)
		sb->Pmx->saveState();
}

void MmdPhyAnimator::restoreWorld()
{
	for (auto sb : sabas)
		sb->Pmx->restoreState(0);
}

bool MmdPhyAnimator::saveState() {

	if (gGameTime - lastSaveTime < 0.1f)
		return false;
	lastSaveTime = gGameTime;

	KeyState state;
	state.nodeMap = nodeMap;
	state.currentNode = curMmdNode;
	state.defaultTime = defaultKeyDurationTime;

	undoStack.push_back(state);
	clearRedoStack();
	return true;
}

void MmdPhyAnimator::restoreState(const KeyState& state) {

	nodeMap = state.nodeMap;
	curMmdNode = state.currentNode;
	defaultKeyDurationTime = state.defaultTime;

	// Update currentRigNode pointer and nodeIt
	if (curMmdNode) {
		auto it = nodeMap.find(curMmdNode);
		if (it != nodeMap.end()) {
			curRigNode = &*it;
			nodeIt = it;
		}
		else {
			// If current node not found, select node available node
			if (!nodeMap.empty()) {
				curMmdNode = nodeMap.begin()->node;
				curRigNode = &*nodeMap.begin();
				nodeIt = nodeMap.begin();
			}
			else {
				curMmdNode = nullptr;
				curRigNode = nullptr;
				nodeIt = nodeMap.end();
			}
		}
	}
	else {
		curRigNode = nullptr;
		nodeIt = nodeMap.end();
	}

	// Update cumulative times
	updateCumulativeTimes();
}

void imguiModFlagUI(uint32_t& modFlag) {
    // Define flag names and values
    struct ModFlagDef {
        const char* name;
        uint32_t flag;
    };
    
    const ModFlagDef flags[] = {
        {"Velocity", 1},
        {"Angular Velocity", 2},
        {"Position", 4}, 
        {"Rotation", 8},
        {"Size Scale", 0x10},
        {"Density Multiplier", 0x20}
    };

    // Create checkboxes for each flag
    for (const auto& flag : flags) {
        bool isSet = (modFlag & flag.flag) != 0;
        if (ImGui::Checkbox(flag.name, &isSet)) {
            if (isSet)
                modFlag |= flag.flag;    // Set flag
            else
                modFlag &= ~flag.flag;   // Clear flag
        }
    }
}
bool MmdPhyAnimator::imguiCommonAddPhyObjProperties(KeyPoseCmd::KpcAddPhyObj& apo) {
	bool updateNow = false;
	using namespace ImGui;
	// Basic properties
	InputInt("Flag", (int*)&apo.TypeFlag);
	SameLine(); TextDisabled("(?)");
	if (IsItemHovered()) {
		BeginTooltip();
		Text("1 = Add one PO\n2 = Turn on PO gen\n4 = Turn off PO gen");
		EndTooltip();
	}
	if (apo.TypeFlag & 16) {
		imguiModFlagUI(apo.modFlag);
	}

	// Generation settings
	if (apo.TypeFlag & 2) { // If PO gen is enabled
		updateNow |= InputFloat("Interval", &apo.genIntv);
	}
	if (apo.TypeFlag & 3) {
		
		updateNow |= DragInt("objNum", (int*)&apo.objNum, 0.1f, 1, 100);
		updateNow |= DragFloat("emitR", &apo.emitR, 0.1f, 0, 100);
	}
	if (apo.TypeFlag & 2) updateNow |= InputInt("maxGen", (int*)&apo.maxGen);

	// Object type and properties
	const char* poTypes[] = { "Cube", "Sphere", "Saba Model", "Tex Ball" };
	int poTypeIdx = apo.poType >= 100 ? 2 : apo.poType;
	if (Combo("PO Type", &poTypeIdx, poTypes, IM_ARRAYSIZE(poTypes))) {
		apo.poType =   poTypeIdx;
		updateNow = true;
	}

	updateNow |= InputFloat("Mass", &apo.mass);
	SetNextItemWidth(60);
	updateNow |= DragFloat("##scale", &apo.sizeScale, 0.1f, 0.1f, 10, "Sc %.1f");
	SetNextItemWidth(150); SameLine();
	updateNow |= DragFloat3("Size", &apo.size.x, 0.1f, 0.1f, 10.f, "%.2f"); 

	updateNow |= Checkbox("AutoScale", (bool*)&apo.opm.autoScale);
 
	if (apo.opm.autoScale) {				
		PushItemWidth(80); SameLine();
		updateNow |= DragFloat("Dur", &apo.opm.ascDur, 0.1f, 0.1f, 10.f, "%.1f");
		ImGui::Spacing( ); BeginGroup();
		updateNow |= DragFloat("ScB", &apo.opm.ascVal[0], 0.1f, 0.1f, 10.f, "%.1f"); SameLine();
		updateNow |= DragFloat("ScE", &apo.opm.ascVal[1], 0.1f, 0.1f, 10.f, "%.1f");
		EndGroup();
		PopItemWidth();
	}

	// Auto-remove settings
	Checkbox("Auto X", &apo.autoRemove);
	if (apo.autoRemove) {
		SameLine();
		SetNextItemWidth(50);
		updateNow |= DragFloat("s", &apo.removeTimer,0.1f,0,100,"%.1f");
	}
	SameLine(0,10); SetNextItemWidth(70);
	updateNow |= DragFloat("NC", &apo.disableHitTime,1.0/60/10,0,10,"%.5f");
	updateNow |= InputInt("atk", (int*) & apo.atkFlag, 1, 1);
	// Position and velocity settings
	Separator();

	const char* positionModes[] = { "Direct", "Calculate from Target", "Camera Position" };
	updateNow |= Combo("Pos Mode", &apo.srcPosMode, positionModes, IM_ARRAYSIZE(positionModes));

	const char* velocityModes[] = { "Direct", "Calculate from Target", "Camera Direction" };
	updateNow |= Combo("Vel Mode", &apo.srcVelMode, velocityModes, IM_ARRAYSIZE(velocityModes));

	if (apo.srcPosMode == 1 || apo.srcVelMode == 1) {
		updateNow |= InputFloat("Fly Time", &apo.flyTime);
	}

	Text("Source Settings");
	updateNow |= InputMMDNodeInfo(apo.srcNd, "S");

	updateNow |= vks::DragFloatXYZExt("S.T", &apo.src.x,0.1f);
	updateNow |= vks::DragFloatXYZExt("S.R", &apo.rtt.x,1.f);
	updateNow |= vks::DragFloatXYZExt("S.V", &apo.vel.x,1.f);

	Separator();
	Text("Target Settings");

	updateNow |= InputMMDNodeInfo(apo.tgtNd, "T");

	updateNow |= InputFloat3("T Pos", &apo.tgt.x);

	Text("Catcher Settings");
	updateNow |= InputInt("mode", &apo.opm.vfg.sbAtkMode);
	//updateNow |= InputInt("Atk Locks", &apo.opm.maxSbLockNodes, 1, 10);
	//updateNow |= InputInt("Atk Sabas", &apo.opm.maxLockSbs, 1, 10);
	return updateNow;
}

void MmdPhyAnimator::showCmdEditor() 
{
	static int lastSelectedCmd = -1;  // Track last frame's selection

	// Get current key
	RigKey* currentKey = curEditKey();
	if (!currentKey) return;

	// Check if current editing context has changed
	bool contextChanged = curRigNode != lastEditingRigNode ||
		(currentKey && lastEditingKey && currentKey != lastEditingKey) ||
		(curRigNode && curRigNode->node != lastEditingNode);

	// Always show the window


	if (contextChanged) {
		// Store current selection
		lastSelectedCmd = selectedCmdIdx[0];

		ImGuiContext* g = ImGui::GetCurrentContext();
		if (g) {
			g->InputTextDeactivatedState.ID = 0;
		}
		ImGui::ClearActiveID();

		lastEditingNode = curRigNode ? curRigNode->node : nullptr;
		lastEditingRigNode = curRigNode;
		lastEditingKey = currentKey;

		// Compare commands at the selected indices
		if (currentKey && lastEditingKey &&
			lastSelectedCmd >= 0 && lastSelectedCmd < lastEditingKey->pose.cmds.size() &&
			lastSelectedCmd < currentKey->pose.cmds.size()) {
			// If commands are different types, reset to 0
			if (lastEditingKey->pose.cmds[lastSelectedCmd].type != currentKey->pose.cmds[lastSelectedCmd].type) {
				selectedCmdIdx[0] = 0;
			}
		}
	}
	ImGui::Begin("Command Editor", nullptr, ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoTitleBar);
	editKeyCmd(currentKey->pose.cmds,0);
	ImGui::End();
	//for (size_t i = 0; i < rigSequencer->mSelectedKeys.size(); ++i) {
	//	auto key = rigSequencer->mSelectedKeys[i];
	//	if (currentKey != &nodeMap[key.first].keys[key.second]) 
	//	{
	//		editKeyCmd(&nodeMap[key.first].keys[key.second],i);
	//	}
	//}
}


 
void MmdPhyAnimator::undo() {

	if (!canUndo()) return;
	Sb0->Eqv->genTextFw(L"Undo", "lrc01");

	// Save current state to redo stack
	KeyState currentState;
	currentState.nodeMap = nodeMap;
	currentState.currentNode = curMmdNode;
	currentState.defaultTime = defaultKeyDurationTime;
	redoStack.push_back(currentState);

	// Restore previous state
	restoreState(undoStack.back());
	undoStack.pop_back();
}

void MmdPhyAnimator::redo() {

	if (!canRedo()) return;
	Sb0->Eqv->genTextFw(L"Redo", "lrc01");

	// Save current state to undo stack
	KeyState currentState;
	currentState.nodeMap = nodeMap;
	currentState.currentNode = curMmdNode;
	currentState.defaultTime = defaultKeyDurationTime;
	undoStack.push_back(currentState);

	// Restore next state
	restoreState(redoStack.back());
	redoStack.pop_back();
}



#pragma region MmdRigKeysSequencer

// *******************************************************************************************************************
// *********************************************** MmdRigKeysSequencer ***********************************************
// *******************************************************************************************************************

void irr::scene::MmdRigKeysSequencer::syncPlaybackFromFrame(int frame) {
	if (!mAnimator) return;

	mAnimator->curFrame = frame;
	mAnimator->curTime = frame * mAnimator->timePerFrame;
	mCurrentFrame = frame;
}

const char* irr::scene::MmdRigKeysSequencer::GetItemLabel(int index) const {
	auto it = std::next(mAnimator->nodeMap.begin(), index);
	if (it != mAnimator->nodeMap.end()) {
		static std::string s;
		std::string name = it->alias.size() ? "["+it->alias+"]" : it->node->GetName();
		bool extNode = name.size() >= 5 && name[0] == '$' && name[1] == 'x' && name[2] == '_';
		if (extNode) {
			std::string parentName;
			size_t pos = name.find('_', 4); // find the second underscore
			if (pos != std::wstring::npos) {
				parentName = name.substr(pos + 1);
			}
			name = 'x' + name.substr(3);
		}
		s = strFmt("%d.%s", it->node->model->saba->getItemIdx(), name.c_str());



		return s.c_str(); // Assuming MMDNode has a GetName() method
	}
	return "Unknown Node";
}

void irr::scene::MmdRigKeysSequencer::Get(int index, int** start, int** end, int* type, unsigned int* color)
{

	// Find the node at the given index
	auto it = std::next(mAnimator->nodeMap.begin(), index);
	int curNodeId = std::distance(mAnimator->nodeMap.begin(), mAnimator->nodeIt);
	if (it != mAnimator->nodeMap.end()) {
		const auto& rigNode = *it;

		if (!rigNode.keys.empty()) {
			static int startFrame = 0;
			static int endFrame = 0;

			startFrame = rigNode.keys.front().frameIdx;
			endFrame = rigNode.keys.back().frameIdx;

			if (start) *start = &startFrame;
			if (end) *end = &endFrame;
			if (type) *type = 0; // Default type
			if (color) *color = rigNode.disabled ? 0x30404040 : index == curNodeId ? ImSequencer::SELECT_BACK_COLOR : ImSequencer::NORMAL_BACK_COLOR; // Default color
		}
		else {
			static int startFrame = 0;
			if (start) *start = &startFrame;
			if (end) *end = &startFrame;
			if (type) *type = 0;
		}
	}
}

void irr::scene::MmdRigKeysSequencer::processKeySelection(const ImRect& rc) {
	ImRect selectionRect(
		ImVec2(std::min(mSelectionStart.x, mSelectionEnd.x), std::min(mSelectionStart.y, mSelectionEnd.y)),
		ImVec2(std::max(mSelectionStart.x, mSelectionEnd.x), std::max(mSelectionStart.y, mSelectionEnd.y))
	);

	// Check if selection rectangle is valid (has some size)
	if (selectionRect.GetWidth() < 1.0f || selectionRect.GetHeight() < 1.0f) {
		return;
	}

	for (const auto& rigNode : mAnimator->nodeMap) {
		
		const ImRect& nodeRect = mNodeRects[rigNode.node];  // Use stored rectangle for this node
		float startFrame = GetFrameMin();
		float endFrame = GetFrameMax();

		// First check if selection rectangle overlaps with this node's row
		bool rowIntersected = (selectionRect.Min.y <= nodeRect.Max.y && selectionRect.Max.y >= nodeRect.Min.y);
		if (!rowIntersected) continue;

		for (size_t keyIdx = 0; keyIdx < rigNode.keys.size(); keyIdx++) {
			const auto& key = rigNode.keys[keyIdx];
			float keyPos = static_cast<float>(key.frameIdx);

			if (keyPos >= startFrame && keyPos <= endFrame) {
				float r = (keyPos - startFrame) / (endFrame - startFrame + 1);
				float x = ImLerp(nodeRect.Min.x, nodeRect.Max.x, r);
				float y = (nodeRect.Min.y + nodeRect.Max.y) * 0.5f;

				bool inSelectionX = (x >= selectionRect.Min.x && x <= selectionRect.Max.x);
				bool inSelectionY = (y >= selectionRect.Min.y && y <= selectionRect.Max.y);

				if (inSelectionX && inSelectionY) {

					mSelectedKeys.push_back({ rigNode.node, keyIdx });
					lastSelectNode = rigNode.node; lastKeyIdx = keyIdx;
					DP(("select %d  /  %d", keyIdx, mSelectedKeys.size()));
				}
			}
		}
	}
}

void irr::scene::MmdRigKeysSequencer::CustomDrawCompact(int index, ImDrawList* draw_list, const ImRect& rc, const ImRect& clippingRect)
{
	auto it = std::next(mAnimator->nodeMap.begin(), std::max(0,index));
	auto& io = ImGui::GetIO();
	// Get mouse position and input state
	ImVec2 mousePos = ImGui::GetMousePos();
	bool isMouseClicked = ImGui::IsMouseClicked(ImGuiMouseButton_Left);
	bool isMouseReleased = ImGui::IsMouseReleased(ImGuiMouseButton_Left);
	bool isMouseDragging = ImGui::IsMouseDragging(ImGuiMouseButton_Left);
	bool hover = !ImGui::IsAnyMouseDown();
	if (lastDrawFinished)
	{
		lastDrawFinished = false;
		clickedOnKey = false;
 
		mNodeRects.clear();
		if (isMouseClicked)
		{
			//if (!io.KeyShift)			clearSelection();
			lastSelectNode = nullptr;
		}
	}
	else if (index == -1)
	{
		lastDrawFinished = true;
		// In CustomDrawCompact:
		if (isMouseClicked) {

			if (!clickedOnKey
				&& mAnimator->playState == 0
				&& mChildFrameRect.Contains(mousePos)  //added in SequenceInterface
				) {
				mIsSelecting = true;
				mSelectionStart = mousePos;
				mSelectionEnd = mousePos;
			}
		}
		return;
	}
	if (it != mAnimator->nodeMap.end()) {
		mNodeRects[it->node] = rc;
		auto& rigNode = *it;
		int disabledDiv = rigNode.disabled ? 2 : 1;
		int rcW = rc.Max.x - rc.Min.x;
		int rcH = rc.Max.y - rc.Min.y;
		// Start selection on click (if not on a key)
		// Handle drag end
		if (isMouseReleased) {
			if (mDraggingKey && mDragKeyNode == it->node) {
				mDraggingKey = false;
				mDragKeyNode = nullptr;
				mDragKeyIndex = -1;
			}
			if (mIsSelecting) {
				if (lastSelectNode) {
					mAnimator->switchToNode(lastSelectNode);
					mAnimator->toKey(lastKeyIdx);
				}
				mIsSelecting = false;  // Just end selection state, don't process
			}
		}

		auto& io = ImGui::GetIO();
		if (mIsSelecting && isMouseDragging
			&& (io.MouseDelta.x != 0 || io.MouseDelta.y != 0)
			) {
			DP(("mouse drag position %f,%f", io.MousePos.x, io.MousePos.y));
			DP(("mouse drag delta    %f,%f", io.MouseDelta.x, io.MouseDelta.y));
			if (!ImGui::GetIO().KeyShift) clearSelection();  // Clear on initial click
			mSelectionEnd = mousePos;
			processKeySelection(rc);  // Process selection while dragging
		}
		if (mIsSelecting)
		{
			// Draw selection rectangle
			ImRect selectionRect(
				ImVec2(std::min(mSelectionStart.x, mSelectionEnd.x), std::min(mSelectionStart.y, mSelectionEnd.y)),
				ImVec2(std::max(mSelectionStart.x, mSelectionEnd.x), std::max(mSelectionStart.y, mSelectionEnd.y))
			);
			draw_list->AddRect(selectionRect.Min, selectionRect.Max, IM_COL32(255, 255, 0, 255));
		}

		draw_list->PushClipRect(clippingRect.Min, clippingRect.Max, true);
		float keyWidth =  	framePixelWidthTarget;//std::min(6.0f, (rc.Max.x - rc.Min.x) / (GetFrameMax() - GetFrameMin() + 1));
		float halfW = std::min(6.f, keyWidth/2), halfH= rc.GetHeight()/2;
		// Iterate through keys
		for (size_t keyIdx = 0; keyIdx < rigNode.keys.size(); keyIdx++) {
			auto& keys = rigNode.keys;
			auto& key = keys[keyIdx];
			float keyPos = static_cast<float>(key.frameIdx);
			float startFrame = GetFrameMin();
			float endFrame = GetFrameMax();

			if (keyPos >= startFrame && keyPos <= endFrame) {
				// Map key position to screen coordinates
				float r = (keyPos - startFrame) / (endFrame - startFrame + 1);
				float x = ImLerp(rc.Min.x, rc.Max.x, r);
				float y = (rc.Min.y + rc.Max.y) * 0.5f;
				if (!clippingRect.Contains({ x,y })) continue; 

				ImRect hitRect(
					x - ((keyIdx > 0 && key.frame <= 8) ? std::min(8.f,halfW * key.frame) : 8), 
					y - rcH * 0.5f,
					x + ((keyIdx < keys.size() - 1 && keys[keyIdx + 1].frame <= 8) ? std::min(8.f, halfW * keys[keyIdx + 1].frame) : 8),
					y + rcH * 0.5f
				);
				bool inRect = hitRect.Contains(mousePos);

				// Check if key is selected
				bool isSelected = false;
				for (const auto& sel : mSelectedKeys) {
					if (sel.first == it->node && sel.second == keyIdx) {
						isSelected = true;
						break;
					}
				}
				if (key.pose.baseMode == 1) {
				
					float drly = rc.GetBL().y - 1;
					draw_list->AddLine(ImVec2(x - halfW-1, drly), ImVec2(x + halfW+1, drly), IM_COL32(0, 255, 0, 255), 2);
				}

				//  Draw KEY DOT
				uint8_t hl = inRect ? 255 : 0;
				ImU32 dotColor = (mDraggingKey && mDragKeyNode == it->node && mDragKeyIndex == keyIdx)
					? IM_COL32(255, 0, 0, 255)  // Yellow outline for dragged key
					: IM_COL32(hl, hl, hl, 255);     // Black outline for normal keys

				draw_list->AddCircle(
					ImVec2(x, y),
					inRect ? 5.f : key.disabled ? 1.f : 3.6f,
					dotColor
				);
				draw_list->AddCircleFilled(
					ImVec2(x, y),
					key.disabled ? 1.f : 3.6f,
					IM_COL32(inRect ? 255 : key.pose.fmul > 1.f ? std::min(1.f, (key.pose.fmul - 1)) * 255 : 0,
						hl / 2, hl / 2, std::min(255, int(255.f * key.pose.fmul + 0.5f)) / disabledDiv)
				);

				float lt = halfH * .25f; //line thickness
				if (key.pose.setRd)
				{
					int c = key.pose.rdRatio * 128 ;
					draw_list->AddLine(ImVec2(x - halfW, y - halfH + lt / 2), ImVec2(x + halfW, y - halfH + lt / 2), IM_COL32(c, c, c, 255), lt);
				}
				if (key.pose.cmds.size() > 0) {
					if (inRect && hover)
					{
						ImGui::BeginTooltip();
						static std::string s;
						s = "";
						for (auto& cmd : key.pose.cmds) {
							if (cmd.type == ectCustom) {
								auto& cc = std::get<KeyPoseCmd::KpcCustom>(cmd.cmdPm);
								if (cc.cmdType == eccKeyEvent)
									s += IrrGetKeyName((irr::EKEY_CODE)cc.iv.x), s += ualib::strFmt(" %s %s %s", cc.iv[1] ? "+C" : "", cc.iv[2] ? "+S":"", cc.iv[3] ? "+A":"");
								else s += ualib::strFmt("C:%s=%d|%g\n", cc.customCmdName.c_str(), cc.iv[0], cc.fv[0]);
							}
							else {
								s += KeyPoseCmdTypeNames[cmd.type] + "\n";
							}
						}
						ImGui::Text("%s", s.c_str());
						ImGui::EndTooltip();
					}	
					for (int i = 0; i < key.pose.cmds.size(); i++)
					{
						ImU32 lineCol = IM_COL32(192, 192, 192, 255);
						auto cmdType = key.pose.cmds[i].type;
						switch (cmdType) {
						case ectCustom: {
							auto c0 = std::get<KeyPoseCmd::KpcCustom>(key.pose.cmds[i].cmdPm);
							switch (c0.cmdType) {
							case eccKinematic:	lineCol = IM_COL32(0, 128, 255, 255); break;
							case eccPhyAnim:    lineCol = IM_COL32(255, 128, 255, 255); break;
							case eccTimeMul:	lineCol = IM_COL32(255, 128, 0, 255); break;
							case eccPhyTimeMul: lineCol = IM_COL32(222, 222, 0, 255); break;
							case eccKeyEvent: {
								auto name = IrrGetKeyName((irr::EKEY_CODE)c0.iv.x);
								if (name && name[0]) {
									ImVec2 textPos(x - 4, y - std::min(itemHeight/2+2,22));// Position at the center of the key marker
									bool isLetter = name[1] ==0;
									draw_list->AddText(textPos, IM_COL32(255, isLetter ? 160 : 255, isLetter?60: 255, isLetter?192: 128), name, name + 1);
								}
							}
							}break;

							
						}
						case ectMotion:
							lineCol = IM_COL32(128, 255, 0, 255); break;
						}
						float drly = y + halfH - i * lt - lt / 2- 2;
 						draw_list->AddLine(ImVec2(x - halfW, drly), ImVec2(x + halfW, drly), lineCol, lt);
					}


				}

				// Draw key with different color if selected
				if (isSelected)
					draw_list->AddCircle(ImVec2(x, y), 6.0f, IM_COL32(255, 255, 255, 128), 12, 1.f);

				if (keyIdx == mAnimator->curEditKeyIndex() && it == mAnimator->nodeIt) {
					// Draw a rectangle around the current key
					ImRect rect(
						ImVec2(x - halfW, rc.GetTL().y  ),
						ImVec2(x + halfW, rc.GetBL().y)
					);
					draw_list->AddRect(rect.Min, rect.Max, IM_COL32(255, 255, 255, 128 + 127 * (sin(gEditorTime * piFloat * 2) > 0 ? 1 : -1)), 1, 0, 1.f);
				}
				// In the mouse click handler:
				if (isMouseClicked && inRect) {
					clickedOnKey = true;
					mIsSelecting = false;

					// Drag multiple keys
					if (!ImGui::GetIO().KeyShift) {
						//clearSelection();
					}

					// Add to selection if not already selected
					bool alreadySelected = false;
					for (const auto& sel : mSelectedKeys) {
						if (sel.first == it->node && sel.second == keyIdx) {
							alreadySelected = true;
							break;
						}
					}
					if (!alreadySelected) {
						if (!ImGui::GetIO().KeyShift) clearSelection();

						mSelectedKeys.push_back({ it->node, keyIdx });
					}

					// Start dragging and store initial positions
					mDraggingKey = true;
					mDragKeyNode = it->node;
					mDragKeyIndex = keyIdx;
					mDragStartX = x;
					mDragStartPos = keyPos;

					// Store initial positions for all selected keys
					for (const auto& sel : mSelectedKeys) {
						auto& selRigNode = mAnimator->nodeMap[sel.first];
						selRigNode.keys[sel.second].dragStartPos =
							static_cast<float>(selRigNode.keys[sel.second].frameIdx);
					}

					mAnimator->switchToNode(it->node);
					mAnimator->toKey(keyIdx);
				}

				// In the drag handler:
				else if (mDraggingKey && isMouseDragging) {
					float dragDeltaX = mousePos.x - mDragStartX;
					float dragDeltaPos = dragDeltaX * (endFrame - startFrame + 1) / (rc.Max.x - rc.Min.x);
					bool ctrlPressed = ImGui::GetIO().KeyCtrl;
					bool anyKeyMoved = false;
					
					// Move all selected keys
					for (const auto& sel : mSelectedKeys) {
						auto& selRigNode = mAnimator->nodeMap[sel.first];
						auto& selKey = selRigNode.keys[sel.second];
						float newPos = selKey.dragStartPos + dragDeltaPos;

						// Clamp to valid range
						newPos = std::max(startFrame, std::min(endFrame, newPos));

						// Get frame constraints
						auto lastKeyFrame = (sel.second > 0 ? selRigNode.keys[sel.second - 1].frameIdx : 0);
						auto nextKeyFrame = (sel.second < selRigNode.keys.size() - 1 ?
							selRigNode.keys[sel.second + 1].frameIdx : 999999);
						auto cuframe = static_cast<int>(newPos + 0.5f);

						// Apply constraints
						if (cuframe <= lastKeyFrame) cuframe = sel.second ? lastKeyFrame + 1 : 0;
						if (cuframe >= nextKeyFrame) cuframe = sel.second < selRigNode.keys.size() - 1 ?
							nextKeyFrame - 1 : 999999;

						int frameDelta = cuframe - selKey.frameIdx;
						if (frameDelta != 0) {
							selKey.frameIdx = cuframe;
							selKey.frame = cuframe - lastKeyFrame;
							
							// If dragging without Ctrl and there's a next key
							if (!ctrlPressed && sel.second + 1 < selRigNode.keys.size()) {
								// Only adjust next key if it's not selected
								bool nextKeySelected = false;
								for (const auto& otherSel : mSelectedKeys) {
									if (otherSel.first == sel.first && otherSel.second == sel.second + 1) {
										nextKeySelected = true;
										break;
									}
								}
								//if (!nextKeySelected) 
									selRigNode.keys[sel.second + 1].frame -= frameDelta;
								
							}
							anyKeyMoved = true;
						}
						 
					}

					if (anyKeyMoved) {
						mAnimator->updateCumulativeTimes();
						mAnimator->toKey(mDragKeyIndex);
					}
				}
			}
		}

		draw_list->PopClipRect();
	}


}
#pragma endregion

void MmdPhyAnimator::setPPNdB(int ndbid, int uid)
{
	ppNdB[ndbid] = &mmd->Pom->objRec[uid].ifObj;
}

saba::MMDNode* irr::scene::KeyPoseData::getB() const {
	if (ndB)
		return ndB;
	else if (dndB>=0 && ppNdB[dndB]) return *ppNdB[dndB];
	else return nullptr;
}
glm::vec3 irr::scene::KeyPoseData::getBPos() const {
	if (ndB)
		return ndB->rb0->getPosition();
	else if (dndB >= 0 && ppNdB[dndB]) return (*ppNdB[dndB])->getGlobalPos();
	else return vec3(0);
}
void MpaManager::playMPA(MpaLoadJsonParam lf, std::vector<int> sbs,bool forceReload) {
	auto it = mpaMap.find(std::string(lf.swapLR ? "s" : "_") + lf.filepath);
	if (it == mpaMap.end() || forceReload) {
		MpaInfo info;
		info.filepath = lf.filepath.substr(1);
 
		info.mpa = std::make_shared<MmdPhyAnimator>(MmdPhyAnimator::MmdPhyAnimatorParam{ mmd, sbs, false,lf.setSb0, lf.setSb1 });
		info.mpa->loadFromFile(lf,false);
		info.mpa->playStartFromFrame(60, lf.playSpeed);
		info.isPlaying = true;
		mpaMap[std::string(lf.swapLR?"s":"_") + lf.filepath] = info;

	}
	else {
		it->second.isPlaying = true;
		it->second.mpa->playStartFromFrame(60, lf.playSpeed);
	}
}

void MpaManager::stopMPA(MpaLoadJsonParam lf) {
	auto it = mpaMap.find(std::string(lf.swapLR ? "s" : "_") + lf.filepath);
	if (it != mpaMap.end()) {
		it->second.isPlaying = false;
		it->second.mpa->playStop();
	}
}

void MpaManager::update() {
	for (auto& [file, info] : mpaMap) {
		
		if (info.mpa->playState!=0) {

				info.mpa->update();
			
		}
		else {
			info.isPlaying = false;
		}
	}
}
