#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;

layout (binding = 0) uniform UBO 
{


				float blurStep;
				float blurWeightMul;
				float sigma, sigma2;
				int blurSize, addPattern, i02, i03;
				float apLenMul, apHeightMul, apHeightAdd, f13;
				float apf1, apf2, _22, _23;
				vec4 matRtt;
				vec4 pad[31 - 5];

	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w
} ubo;

layout (binding = 2)  uniform sampler2D g_tex0_sampler;




	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;




//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;



#if  1
float blurknl(in float x) {
	return 0.39894*exp(-0.5*x*x/(ubo.sigma2))/ubo.sigma;
}


void main()
{


	int ic= int(ubo.blurSize+1.5);
	//vec2 tex_add = 0.5 / vec2(textureSize(g_tex0_sampler, 0));
	vec2 tex_offset = 1.0 / vec2(textureSize(g_tex0_sampler, 0)) ; // gets size of single texel
	vec3 result = texture(g_tex0_sampler, i_tex0).rgb  * blurknl(0); // current fragment's contribution

	for(int i = 1; i < ic; ++i)
	{
		float w=blurknl(i);
#if  1
		// H
			result += texture(g_tex0_sampler, i_tex0 + vec2(tex_offset.x * i, 0.0)).rgb * w ;// * ubo.blurWeightMul;
			result += texture(g_tex0_sampler, i_tex0 - vec2(tex_offset.x * i, 0.0)).rgb * w;// * ubo.blurWeightMul;
#else
			// V
		result += texture(g_tex0_sampler, i_tex0 +  vec2(0.0, tex_offset.y * i)).rgb * w;// * ubo.blurWeightMul;
		result += texture(g_tex0_sampler, i_tex0 - vec2(0.0, tex_offset.y * i)).rgb * w;// * ubo.blurWeightMul;
#endif
		
	}
	outFragColor = vec4(result.rgb* ubo.blurWeightMul, 1.0);  //V  -> H 
}
#else
void main()
{
  	float weight[5];
	weight[0] = 0.227027;
	weight[1] = 0.1945946;
	weight[2] = 0.1216216;
	weight[3] = 0.053999;
	weight[4] = 0.016216;

	vec2 tex_offset = 1.0 / vec2(textureSize(g_tex0_sampler, 0)) * ubo.blurLengthScale; // gets size of single texel

	vec3 result = texture(g_tex0_sampler, i_tex0).rgb * weight[0]; // current fragment's contribution
	for(int i = 1; i < 5; ++i)
	{
#if  1
		// H
		result += texture(g_tex0_sampler, i_tex0 + vec2(tex_offset.x * i, 0.0)).rgb * weight[i];// * ubo.blurWeightMul;
		result += texture(g_tex0_sampler, i_tex0 - vec2(tex_offset.x * i, 0.0)).rgb * weight[i];// * ubo.blurWeightMul;
#else		
			// V
		result += texture(g_tex0_sampler, i_tex0 + vec2(0.0, tex_offset.y * i)).rgb * weight[i];// * ubo.blurWeightMul;
		result += texture(g_tex0_sampler, i_tex0 - vec2(0.0, tex_offset.y * i)).rgb * weight[i];// * ubo.blurWeightMul;
#endif	
	}
	outFragColor = vec4(result.rgb*ubo.blurWeightMul, 1.0);
}
#endif