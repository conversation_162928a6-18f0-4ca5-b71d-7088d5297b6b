﻿#include "AppGlobal.h"
#include "CMidiPlateSceneNode.h"
#include "IVideoDriver.h"
#include "ISceneManager.h"
#include "S3DVertex.h"
#include "SMeshBuffer.h"
#include "irrMMD.h"
#include "IHardwareBuffer.h"
#include "../../AppMainLib/app/ArMmPLayer/SnArItem.h"
using namespace glm;
using namespace irr;
using namespace irr::scene;
using namespace irr::video;
using namespace ualib;



static int	dropMode = 0;
static float	rttDegree =180 - (180 / 7.f);// 360 / 2.f;//
static const float	plateYStepMul = 1.f;//180 - (180 / 12.f);//
static const float	incDegAdd = 0.f;//180 - (180 / 12.f);//
static const float	PLATE_MOV_OFSY = 0.2f;
static const bool	PSB_DynRbActive = 1; //MMD_ACTION_ANIM
static const float	viewScale = 0; //1.f; // 
static const float	hitVelMul = 1.0f;  //1;  0=not keep velLen	

static glm::vec3 initBallVel(0, 30, 20);

#define MEC_THICKNESS 1
#define MEC_SIDELEN   1
#define VOICE_NOTE    1
#define EVEN_SPLIT_NOTE		1	//平均分配Note到Sabas
#define PLATE_MOV_Y  0.f
#define HAND_MOV_Y  10.f

#define NOTE_MAX_DELTA 3.f
 
#define UP_FORCE_ON_NOTE -10	//按音符弹跳
 
 
static io::path CUBE_IMG_PATH = "data/img/zzzboss1.jpg";// kirara.jfif";//lycaon.jpg";//  jd1.png"; //mln.png";// 

namespace {
	const float prepareTimeMul = 1.f;		 const float noPhy_ball_GMul = 1.f;
	const float startOfs = -1;
	midilib::MidiMan midi;
	float playTime = 0;

}

#define PHYSICS 1

#define MP_MODE				111

CMidiPlateSceneNode::CMidiPlateSceneNode(ISceneNode* parent, ISceneManager* mgr,
	IrrMMD* _mmd, const core::vector3df& position,
	const core::vector3df& rotation, const core::vector3df& scale)
	: ISceneNode(parent, mgr, -1, position, rotation, scale),
	mmd(_mmd), hbVertices(nullptr), hbIndexs(nullptr)
{
	Ctx = mmd->Ctx;
	
	meshType = 0;
	actAnim = !PSB_DynRbActive;
	//shadow

	switch (MP_MODE)
	{
	default: {
		dropMode = 0; bHeadFront = 1; podOfs = { 9,6,0 }; podPreStep = 30; podRtt = 1;
		plateHasRb = false;
	}break;
	case 1: {
		dropMode = 1; bHeadFront = 0; podOfs = vec3(20, 16, 0); podPreStep = 30; podRtt = 0;
	}break;
	case 2: {
		dropMode = 1; bHeadFront = 0; podOfs = vec3(20, 16, 0); podPreStep = 30; podRtt = 0;
		plateHasRb = false;					plate_fix_yofs = 21.f; keepVelLen = false;
	}break;
	case 3: {
		dropMode = 1; bHeadFront = 0; podOfs = vec3(20, 16, 0); podPreStep = 30; podRtt = 0;
		plateHasRb = false;					plate_fix_yofs = 18.f;
	}break;
	case 11: {
		dropMode = 1; bHeadFront = 0; podOfs = vec3(20, 16, 0); podPreStep = 30; podRtt = 0;
		plateHasRb = false;					plate_fix_yofs = 18.f;  phyAfterHit = 1;
	}break;
	case 12: {
		dropMode = 2; bHeadFront = 0; podOfs = vec3(20, 16, 0); podPreStep = 30; podRtt = 0;
		plateHasRb = false;					plate_fix_yofs = 25.f;  phyAfterHit = 1;
	}break;
	case 110: { // 1 sb
		dropMode = 0; bHeadFront = 0; podOfs = vec3(20, 16, 0); podPreStep = 30; podRtt = 0;
		plateHasRb = 0;					   phyAfterHit = 1; ballDamping = 0.1f;
	}break;
	case 111: { // 1 sb
		dropMode = 1; bHeadFront = 0; podOfs = vec3(20, 16, 0); podPreStep = 30; podRtt = 0;
		plateHasRb = 0;					plate_fix_yofs = 2.f;  phyAfterHit = 1;
	}break;
 
	}


}

void CMidiPlateSceneNode::recreateCubes(CMidiPlateSceneNode::Param *pm, bool keepTime)
{

	this->keepTime = keepTime;
	if (pm) {
		Pm = *pm;
		is0 = pm->is0;
		if (pm->sb0) playerIdx = pm->sb0->getItemIdx()- PLATE_PLAYER_SKIP;
		//ballMmdOfs = vec3(0, -2, -Pm.sb0->Pmx->yaoPos.y * 0.1f);
	}
	baseRb = Pm.sb0->Pom->groundRb;
	density = Pm.mass;
	rootMat.setTranslation(Pm.pos);
	//rootMat.setRotationDegrees(pm.rtt);
	if (Pm.actMode == 0) {
		passTypeFlags |= IrrPassType_ShadowMap;            Material.RcvShadow = true;
		Material.SpecularColor = 0x20707070;
	}
	else if (Pm.actMode == 1) {
		Material.EmissiveColor = 0x00202020;
		Material.SpecularColor = 0x10FFFFFF;
		Material.DiffuseColor = 0x80FFFFFF;
	}

	// createItems(pm.grid.x, pm.grid.y, pm.grid.z, pm.brickSize.x);

	//
	
	loadNotes();
	if (notes.size() < 1) return;
	create_midi_plate(true);

	memset(sbLastActBallId, 0, sizeof(sbLastActBallId));

	createBall();
}

void CMidiPlateSceneNode::relocateCubes()
{
	create_midi_plate(false);
}

void CMidiPlateSceneNode::loadNotes()
{
	if (is0 && !keepTime) {
		midi.stopPlay();
		if (Pm.extenalMidi) {
			midi.loadFile(ualib::WcharToAnsi(Pm.path));
		}
		else midi.startPlay(int(Ctx->gd.time * 1000), 1, ualib::WcharToAnsi(Pm.path), nullptr);
	}
	
	Ctx->MidiFw.mute = true;
	//Ctx->Midi.setFwNoteInst(15, 0);///112
	
	auto evs = midi.getAllEvents(); //midi.getChannelEvents(0);
	if (evs.size() < 1) evs = midi.getAllEvents();
	if (evs.size() < 1) throw;
	int sbTrack = midi.noteTracks[0];
	trackCount = midi.noteTracks.size();
	if (Pm.track>=0) sbTrack = Pm.track;
	else if (Pm.subAct) {
		if (playerIdx < midi.noteTracks.size()) 
			sbTrack = midi.noteTracks[midi.noteTracks.size()-1-Pm.trackInv];
	}
	else  sbTrack = midi.noteTracks[playerIdx % midi.noteTracks.size()];
 
	nid = -1;
	notes.clear();
	float lastNoteTime = -1.f;
	float firstTime = evs[0].starttime;
	if (Pm.beats.size())
	{
		for (auto& bt : Pm.beats)
		{
			midilib::MidiEventStruct e;
			e.starttime = bt; e.duration = 0.2f;
			notes.push_back({ e.starttime,{ e },1 });
		}
	}
	else
		for (auto& e : evs) if (e.midevt.isNoteOn()) if (!Pm.plateOnSb ||  
#if EVEN_SPLIT_NOTE
			e.nid % mmd->players.size() == playerIdx 
#else
			e.track == sbTrack
#endif

			) {
			if (e.starttime - lastNoteTime < 1 / 60.f) {
				auto& l = notes.back();
				if (l.mec < 5) {
					l.me[l.mec++] = e;
				}
				continue;
			}
			float dt = (e.starttime - lastNoteTime);
			if (lastNoteTime > 0 && dt > NOTE_MAX_DELTA)
			{
				int step = dt/ NOTE_MAX_DELTA;
				for (int s=1;s<=step;s++)
				notes.push_back({ lastNoteTime +dt*s/(step+1)- firstTime,{e},1,true});
			}
			lastNoteTime = e.starttime;
			e.starttime -= firstTime;
			if (Pm.extenalMidi) e.starttime += Pm.midiDelay - 1;
			//DP(("+Note sb:%d tr:%d",sbIdx,e.track));

			notes.push_back({ e.starttime,{ e },1,false });
		}
}

void CMidiPlateSceneNode::resetFile(std::wstring path)
{
	Pm.path = path;
	clearBalls();
	 
	recreateCubes(&Pm);
}

void CMidiPlateSceneNode::createBall()
{

	int N = 1;
	for (int i = -N / 2; i <= N / 2; i++)
		//int i = 0;
	{ 
		float ofs = i * 0.1f;
		if (is0 && !keepTime) playTime = startOfs + ofs; playVoiceId = -1;

		PhyObjParam pm{ 11 ,1.f,vec3(Pm.ballRadiusX2 * (1 - fabs(i) * 1.5f / N)),{ ballStartPos },{},{ 0,0,0 },true,false,false };
		pm.operation = 1;   pm.autoKill = false;

		pm.visible =   Pm.actMode == 2 ? false : i == 0 && Pm.ballVisible;
		//pm.hitSbTest = false;
		pm.mass = Pm.mass;
		pm.vel = initBallVel;
		pm.pmxrb.m_repulsion = 1.f;
		pm.pmxrb.m_friction = 0.0f;
		pm.pmxrb.m_group = 15;
		pm.pmxrb.m_collideMask32 = pm.visible ? (actAnim ? 0xFFFF0000 :-1) : Pm.noteBall?0:0xFFFF4000;
		pm.pmxrb.m_translateDimmer = ballDamping;
		pm.pmxrb.m_rotateDimmer = 1;
		pm.pmxrb.flag = PHRBF_ContactOffset;
		pm.pmxrb.forceNotify = true; //enable USE_FORCE_NOTIFY
		//pm.pmxrb.modContactThreshold = true;
		pm.ownerAdd = -1; pm.pmxrb.pOwner = (void*)Pm.sb0->modelCollisionId;

		pm.tag = 'mdb';

		BallData bd; bd.ballId = balls.size(); bd.ptid = -1; bd.noteBallPlayId = -1;
		if (i == 0) curVisBallId = bd.ballId;
		bd.timeOfs = ofs;
		if (i != 0) {
			pm.alphaMtr = true; pm.color = 0x00FFFFFF;
		}
		else {
			//pm.alphaMtr = true; pm.color = 0x00FFFFFF;
		}
		pm.dataT = bd;
		auto o = mmd->Pom->addObj(pm);
		
		balls.push_back(o);
		auto brb = o->rb;
		if (i != 0) continue;
		brb->cbMod = [=](saba::PhysicsEngineObjectUserData* ud) {
			auto vel = brb->getLinearVel();
			brb->needModVel = true;  brb->modVelValue = vec3(0, 100, 0);// cbs[nid].ballVel;
			DP(("hit vel %.2f,%.2f,%.2f", vel.x, vel.y, vel.z));
			};

		if (cbs[0].rb) cbs[0].rb->setCollideFilterMask(1, -1);
		saba::PMXJoint jp{}; jp.setLocalPos = true;
		jp.springT = glm::vec3(1);
		jp.springR = glm::vec3(10000);
		jp.dampingR = glm::vec3(0);
		jp.translate = {};
		jp.limitMinT = glm::vec3(0);
		jp.limitMaxT = glm::vec3(0);
		jp.limitMinR = glm::vec3(-10);
		jp.limitMaxR = glm::vec3(10);
		jp.rotate = glm::radians(vec3(180, 0, 0));
		for (auto& j : sbjts) delete j; sbjts.clear();
		if (Pm.sbPod) {
			//jp.translate = { 0,0,0 };     sbjts.push_back(Pm.sb->Pmx->connectRb(brb, Pm.sb->Rb0(), 0, 0, jp));

			jp.springT = glm::vec3(0);
			jp.springR = glm::vec3(0);
			//jp.translate = {-1,1,-3};    sbjts.push_back(Pm.sb0->Pmx->connectRb(Pm.sb->Rb0(), Pm.sb0->ndHandR->rb0,0, 0, jp));
			//jp.translate = { 1,1,-3};   sbjts.push_back(Pm.sb0->Pmx->connectRb(Pm.sb->Rb0(), Pm.sb0->ndHandL->rb0, 0, 0, jp));
			//jp.translate = { 0,2.3,0 };    sbjts.push_back(Pm.sb0->Pmx->connectRb(Pm.sb->Rb0(), Pm.sb0->ndYao->rb0, 0, 0, jp));
			//jp.translate = { 0,2.3,-3 };    sbjts.push_back(Pm.sb0->Pmx->connectRb(Pm.sb->Rb0(), Pm.sb0->ndHead->rb0, 0, 0, jp));

			//jp.translate = { -1,1,2 };  sbjts.push_back(Pm.sb0->Pmx->connectRb(brb, Pm.sb0->ndArm1R->rb0, 0, 0, jp));
			//jp.translate = { 1,1,2 };   sbjts.push_back(Pm.sb0->Pmx->connectRb(brb, Pm.sb0->ndArm1L->rb0, 0, 0, jp));

			// jp.translate = { -0.6,-0.3,0 }; sbjts.push_back(Pm.sb0->Pmx->connectRb(brb, Pm.sb0->ndLeg1R->rb0, 0, 0, jp));
			 //jp.translate = { 0.6,-0.3,0 };  sbjts.push_back(Pm.sb0->Pmx->connectRb(brb, Pm.sb0->ndLeg1L->rb0, 0, 0, jp));
		}

		brb->cbPhyStep = [=](int step, int stepCount) {
			if (balls.size() == 0) return;
			ballFrameFw();

			phyStepMMD(playTime+gFrameTime*step/3.f);


			if (!phyAfterHit)
				if (step == 0 && nid < notes.size() - 1 && nid >= 0 && playTime >= notes[nid].time && playTime < notes[nid + 1].time)
				{
					BallData& pm = std::any_cast<BallData&>(o->pm.dataT); auto& pp = cbs[nid + 1].pp;
					if (pm.timeOfs != 0) return;
					float ratio = (playTime - notes[nid].time) / (notes[nid + 1].time - notes[nid].time);
					double ptidd = (pp.ptE - pp.ptB) * ratio;
					ptidd = pp.ptB - 1 + ptidd;
					//double rat = (playTime-startOfs) / (totalTime+2);
					//double ptidd= pathpts.size() * rat;
					int ptid = int(ptidd);
					//DP(("ptid %d, t %f",ptid, playTime));
					double dc = ptidd - ptid;
					vec3 pos = glm::mix(pathpts[ptid], pathpts[ptid + 1], dc);
					brb->SetCoMTranslate(glh::matTransformVec(mmdMat,pos));
					o->pm.sn->hasFollowCam = true;
					int idcam = std::max(0, ptid - 60);
					o->pm.sn->followCamPos = glm::mix(pathpts[idcam], pathpts[idcam + 1], dc) * MMD_SABA_SCALE;
					o->pm.sn->followCamPos += vec3(0, MMD_SABA_SCALE / 2, 0);

					// Get the ball's velocity
					vec3 velocity = pathpts[ptid + 1] - pathpts[ptid + 0]; velocity.y = 0;
					core::vector3df velirr = velocity;

					glm::mat4 rtm = velocity == vec3(0) ? glm::mat4(1) : glm::mat4(glh::directionToRotation(velocity, vec3(0, 1, 0)));
					brb->setAngVelToRotateOnNode(rtm, 20);

					mat4 mpod = glm::translate(mat4(1), pathpts[std::min((int)pathpts.size() - 1, ptid + podPreStep)]) *
						(podRtt ? rtm : mat4(1)) *
						glm::translate(mat4(1), podOfs);// vec3(9,2,12));
					if (Pm.sbPod) {
						if (nid < 3) Pm.sbPod->Rb0()->SetCoMTransform(mpod);
						else {
							Pm.sbPod->Rb0()->setLinearVelToPos(mpod[3], 3);
							Pm.sbPod->Rb0()->setAngVelToRotateOnNode(rtm, 6);
						}
						mat4 rty180 = rtm * glm::rotate(mat4(1), radians(180.f), vec3(0, 1, 0));
						if (bHeadFront) {
							Pm.sb0->ndHead->rb0->setAngVelToRotateOnNode(rty180, 10);
							Pm.sb0->ndUpper2->rb0->setAngVelToRotateOnNode(rty180, 300);
						}
						vec3 vn = glm::fastNormalize(velocity);
						if (bHeadFront) Pm.sb0->ndHandR->rb0->addLinearVel((vec3(0, .3f, 0) + vn * 0.6f + glm::cross(vec3(0, 1, 0), vn)) * 6.f * sin(ratio * core::PI));
						else if (cbs[nid + 1].rb) Pm.sb0->ndHandR->rb0->addLinearVelToPos(cbs[nid + 1].rb->getPosition(), 10);

						if (auto bp = o && 0)
						{
							int pid = nid;   if (pid > 0 && pid < notes.size() - 1) {
								auto rbf1 = Pm.sb0->ndFootL->rb0;// pid % 2 ? Pm.sb0->ndFootR->rb0 : Pm.sb0->ndFootL->rb0, rbf2 = (pid + 1) % 2 ? Pm.sb0->ndFootR->rb0 : Pm.sb0->ndFootL->rb0;
								//rbf1->setLinearVelToPos(bp->rb->getPosition(), 39);
								rbf1->setLinearVelToPos(cbs[pid + 1].rb->getPosition() + vec3(0, 0.9f, 0) + vec3(0, 0.6f, 0)
									* std::min(6.f, rbf1->disTo(cbs[pid + 1].rb->getPosition())), 3); //rbf1->setAngVelToRotateOnNode(rty180, 300);
								//rbf2->setLinearVelToPos(cbs[pid ].rb->getPosition() + vec3(0, 0.9f, 0)+vec3(0, 0.6f, 0) 
								//	* std::min(6.f, rbf2->disTo(cbs[pid ].rb->getPosition())), 3); rbf2->setAngVelToRotateOnNode(rty180, 300);
							}
						}
					}

				}
			//rtt = mat4(quat(vec3(-2, 0, 0))) * rtt;
			//Pm.sb0->ndFootL->rb0->setAngVelToRotateOnNode(rtt, 11200);
			//Pm.sb0->ndFootR->rb0->setAngVelToRotateOnNode(rtt, 11200);

							  // brb->setRotateTo(glm::mat4(glm::rotation(glm::fastNormalize(vec3(0, -1, 0.1)), glm::fastNormalize(cbs[i].ballVel)))
			};
	}
}

CMidiPlateSceneNode::~CMidiPlateSceneNode()
{
	if (hbVertices)			hbVertices->drop();
	if (hbIndexs)			hbIndexs->drop();
}

void CMidiPlateSceneNode::OnRegisterSceneNode()
{
	if (IsVisible)
		SceneManager->registerNodeForRendering(this);
	ISceneNode::OnRegisterSceneNode();
}

void CMidiPlateSceneNode::OnAnimate(u32 timeMs)
{
	ISceneNode::OnAnimate(timeMs);
	if (curAngleN != nbp.angleN) {
		curAngleN = nbp.angleN;
		relocateCubes();
	}
	sbb   =  mmd->nextSaba(Pm.sb0->getItemIdx(),nbp.sbMmdOfs);
	if (Pm.plateOnSb) Pm.sb0->ndYao->GetParent()->SetAnimationTranslate(powerMorph * Pm.movPowMul + Pm.movPowAdd);//PHYSX translate nan ERROR?

	mmdMat = (Pm.sb0->mmdBaseInv * AbsoluteTransformation);
	if (Pm.plateOnSb) {
		if (sbb->rbActCD > 0) return;
		auto mb = sbb->ndRbRoot->mGlobalAnim;// 
			//sbb->Rb0()->GetTransform();
		if (__is_nan(mb[3][0])) { 
			 sbb->Rb0()->SetCoMTransform(mb = sbb->ndRbRoot->mGlobalAnim);
			
			sbb->setAllDynRbActive(false,2);
			 
		}
		mmdMat = mb; 
	
	}
	mmdMat = mmdMat *glm::translate(mat4(1), nbp.ballMmdOfs);
	updateFrame();
	for (int i = 0; i < notes.size(); i++) {
		auto& n = notes[i];
		auto& cb = cbs[i];

	}


}


void CMidiPlateSceneNode::clearBalls()
{
	for (const auto& o : balls)
	{
		if (o) mmd->Pom->removeObj(o);
	}
	balls.clear();
}

void CMidiPlateSceneNode::recordBegin()
{
	assert(is0);
	midi.recordBegin(120,true,0.f);
}

void CMidiPlateSceneNode::recordEnd()
{
	assert(is0);
	if (midi.getAllEvents().size() <30) return;
	midi.recordEnd("r:/plate.mid");
}

void CMidiPlateSceneNode::recordUpdate(float time)
{
	assert(is0);
	midi.setRecTime(time);//recording real time
	if (Pm.extenalMidi) return;
	midi.updateTime(int64_t(gSceneTime * 1000), 1000, [this](const midilib::MidiEventStruct& evt, bool isCurPlaying) {});

	
}



void CMidiPlateSceneNode::render()
{
	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	driver->setTransform(video::ETS_WORLD, AbsoluteTransformation);

	driver->setMaterial(Material);

	if (hbVertices && hbIndexs)
	{
		driver->drawHardwareBuffer(hbVertices, hbIndexs, EVT_STANDARD,
			scene::EPT_TRIANGLES,
			EIT_32BIT, 1, indexCount, 0);
	}
}

const core::aabbox3d<f32>& CMidiPlateSceneNode::getBoundingBox() const
{
	return Box;
}

video::SMaterial& CMidiPlateSceneNode::getMaterial(u32 i)
{
	return Material;
}

u32 CMidiPlateSceneNode::getMaterialCount() const
{
	return 1;
}




void CMidiPlateSceneNode::create_midi_plate(bool createMesh)
{
	// 清除现有的cubes
	for (const auto& cb : cbs)
	{

		if (cb.o) mmd->Pom->removeObj(cb.o);
	}
	cbs.clear();


	// 设置钢板参数
	float depth = Pm.brickSize.y;  // 钢板厚度
	float side_len = Pm.brickSize.x;  // 钢板边长
	vec3 initPos;
	// 计算钢板参数
	auto plate_params = calculate_plate_params(depth, side_len, Pm.ballRadiusX2 / 2, Pm.ballStartOffsetY, ballDamping,
		dropMode, initPos, gGravity * (noPhy_ball_GMul), Pm);
	ballStartPos = initPos;
	ballStartPos = rootMat.getTransformedVect(ballStartPos);
	auto totalCubes = plate_params.size() - 1;
	cbs.resize(plate_params.size());
	assert(cbs.size() - notes.size() <= 1);

	vec3 ofspt = rootMat.getTranslation() - vec3(0, minY, 0);
	for (auto& pt : pathpts) {

		pt += ofspt;
	}
	// Create mesh
	if (createMesh) {
		if (meshType == 1)
			createCylinderMesh(plate_params, side_len, depth);
		else
			createPlateMesh(plate_params, side_len, depth);
	}
	// 设置物理对象

	for (int i = 0; i < totalCubes; ++i)
	{
		const auto& param = plate_params[i];
		PhyObjParam pm{ meshType == 1 ? 2 : 0 };
		pm.tag = 'mplt';
		pm.autoKill = false;
		pm.dummySn = 1;
		pm.mass = 0;  // 钢板是静态的
		float sl = side_len * (MEC_SIDELEN ? sqrt(float(notes[i].mec)) : 1.f);
		pm.size = glm::vec3(sl, meshType == 1 ? depth * 2 - side_len * 2 : depth, sl);
		pm.pos = param.boxPos;
		pm.rtt = vec3(0);// transform.getRotationDegrees()* core::DEGTORAD;
		pm.operation = 0;
		pm.pmxrb.m_name = "midi_plate";
		pm.pmxrb.flag = PHRBF_ContactOffset;
		pm.pmxrb.noGravity = true;
		pm.pmxrb.m_group = 14;
		pm.pmxrb.m_collideMask32 = 0;//  0xFFFF7FFF;//-1 : 0;
		//pm.pmxrb.modContactThreshold = true;
		pm.ownerAdd = -1; pm.pmxrb.pOwner = (void*)Pm.sb0->modelCollisionId;//			pm.ownerAdd = 200;


		cbs[i].pp = plate_params[i];
		cbs[i].boxTfm = core::matrix4(rootMat * glm::translate(glm::mat4(1.0f), param.boxPos) * glm::mat4_cast(param.rotation));
		cbs[i].ballVel = param.ball_velocity;
		cbs[i].ballPos = rootMat.getTransformedVect(param.ball_position);
		cbs[i].ballRtt = /*cbs[i].ballVel == vec3(0) ? glm::mat4(1) :*/ glm::mat4(glh::directionToRotation(cbs[i].ballVel, vec3(0, 1, 0)));
		cbs[i].ballRttYPi = cbs[i].ballRtt * glm::angleAxis(glm::radians(180.0f), glm::vec3(0.0f, 1.0f, 0.0f));

		if (!plateHasRb) continue;
		PhyObj* o = mmd->Pom->addObj(pm);
		cbs[i].o = o;
		cbs[i].rb = o->rb;
		cbs[i].phy = pm.pmxrb.m_collideMask32 == -1;
		auto sb0 = Pm.sb0;
		o->rb->setRotateTo(cbs[i].boxTfm);

 
	}

	//final pt
	{
		mat4 trf = rootMat * glm::translate(glm::mat4(1.0f), plate_params[totalCubes].ball_position);
		vec3 finalPos = trf[3];
		//Pm.sb0->ndRoot->SetAnimationTranslate(finalPos);
	}

	// 设置材质
	Material.MaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL;

	Material.setTexture(0, SceneManager->getVideoDriver()->getTexture("path/to/your/texture.png"));  // 设置适当的纹理
}

void CMidiPlateSceneNode::onBallHit(PhyObj* bo, int id, unsigned long long totalCubes)
{
	if (bo && bo->pm.tag == 'mdb') {
		BallData& pm = std::any_cast<BallData&>(bo->pm.dataT);
		if (pm.timeOfs == 0) {
			//if (notes[id].me[0].channel==0)
			if (VOICE_NOTE)
			{
				//Pm.sb0->actVoiceFx(8 + playerIdx, 5, notes[id].me[0].key, L"nh", 1);
				mmd->mdplr.aouWords(playerIdx, { L"aha", L"br" });
				//mmd->mdplr.chd[playerIdx].ckey = notes[id].me[0].key;
			}
			else mmd->mdplr.key2 = notes[id].me[0].key-12;
				float maxPow = 0;
				for (int mi = 0; mi < notes[id].mec; ++mi) {
					float vr = notes[id].me[mi].velocity / 127.f;
					//Ctx->Midi
					if (Pm.extenalMidi) midi.playNote(0, 15, notes[id].me[mi].key, vr, notes[id].me[mi].duration);
					curPower = vr;
					if (maxPow<curPower) maxPow = curPower;
					if (ballFw) for (int j = 0; j < vr * 7.f; j++)
						Pm.sb0->mmdFw(1, "volleyBall", glh::matTransformVec(mmdMat * cbs[id].boxTfm, vec3(0)), mat3(mmdMat * cbs[id].boxTfm) * vec3(0, -vr * 30.f, 0), 0xFFFFFFFF);

				} 
				if ( UP_FORCE_ON_NOTE) {
					Pm.sb0->ndLower->rb0->addLinearVel({ 0,maxPow* maxPow * UP_FORCE_ON_NOTE,0 });
					Pm.sb0->ndYao->rb0->addLinearVel({ 0,maxPow * maxPow * UP_FORCE_ON_NOTE ,0 });

				}
				nid = id;

				if (is0 && nid >= notes.size() - 1) onFinish();
				cbs[id].onTime = gSceneTime;
				if (id < totalCubes - 1)
					cbs[id + 1].preTime = gSceneTime + notes[id + 1].time - notes[id].time;
				auto& pp = cbs[id].pp;
				if (IS_WIN_DBG)	for (int pi = pp.ptB; pi < pp.ptE; pi++)
				{
					auto pos = pathpts[pi];
					Pm.sb0->mmdFw(2, "pt1s", pos, {}, 0xFFFFFFFF);
				}


#define CLOSEST_SB 0
#if CLOSEST_SB
				if (id < totalCubes - 2) {
					int ad = 0;
					float closeDis = 100000.f;
					auto& sbs = mmd->players;
					for (int si = 1; si < sbs.size(); ++si) {
						if (sbs[si] == cbs[id + ad].sb) continue;
						float dis = sbs[si]->ndUpper2->disTo(cbs[id + ad + 1].ballPos);
						if (dis < closeDis) {
							closeDis = dis;
							cbs[id + ad + 1].sb = sbs[si];
						}
					}
				}
#endif

			DP(("FW"));
			mat4 m = mmdMat*cbs[nid].boxTfm;
			float ds = viewScale > 0.1 ? 6 : 12;
			for (int x = -3; x <= 3; x++) for (int y = -3; y <= 3; y++) {
				vec4 pos(x * Pm.brickSize.x / ds, 0, y * Pm.brickSize.z / ds, 1); vec3 vel(0, -1, 0);
				pos = m * pos; vel = mat3(m) * vel;
				if (viewScale < 0.1f) pos = vec4(glh::matTransformVec(mmdMat,cbs[nid].ballPos),1);
				Pm.sb0->mmdFw(1, Pm.actMode==0? "dropDust":"daTieHuaSkr", pos, vel, Pm.dustColor);
			}

		}
		auto vel = bo->rb->getLinearVel();
		//DP(("hit %d  nt %d, v %.2f dur=%f", i, notes[i].me.key, notes[i].me.velocity / 127.f, notes[i].me.duration));

		auto brb = bo->rb;

		auto fun = [=]() {
			if (Pm.fixPosOnHit) brb->SetCoMTranslate(cbs[id].ballPos);
			vec3 vel = cbs[id].ballVel;
			int pass = 0;
			if (id > 0 && id + 1 < totalCubes && !Pm.fixPosOnHit) {
				float dt = cbs[id + 1].preTime - gSceneTime;
				if (dt > 0)
					vel = glh::calcVelocityP2PinTimeGuess(brb->getPosition(), cbs[id + 1].ballPos, gGravity, dt, ballDamping, 60 * SABA_PHYSICS_FRAMESTEP);
				else pass = 1; //hit old ball
				DP(("%2d dt=%.2f vel %.2f %.2f %.2f    %.2f %.2f %.2f", id, dt, vel.x, vel.y, vel.z, cbs[id].ballVel.x, cbs[id].ballVel.y, cbs[id].ballVel.z));
			}
			if (!pass) {
				brb->setLinearVel(vel);
				//brb->setLinearVel(glm::fastNormalize(vel)*glm::length(brb->getLinearVel()));
			}

			//  brb->setRotateTo(glm::mat4( glm::rotation(glm::fastNormalize(vec3(0, -1, -1)), glm::fastNormalize(cbs[i].ballVel))));
			// glh::buildLookAtMatrixLH(cbs[i].ballPos, cbs[i].ballPos + cbs[i].ballVel, vec3(0, 1, 0))

			//sb0->Pmx->moveAllOffset(brb->getPosition() - Pm.sb0->Rb0()->getPosition());
			// sb0->Pmx->setBodyVel(cbs[i].ballVel);
			// sb0->ndFootL->rb0->setLinearVel(cbs[i].ballVel*100.f);
			// sb0->ndFootR->rb0->setLinearVel(cbs[i].ballVel*100.f);
			//prb->setGravityOn(1);
			if (id > 0) {
				cbs[id - 1].rb->setCollideFilterMask(1, 0xFFFF7FFF); //碰撞后不再碰撞
				cbs[id - 1].phy = 0;
			}
			brb->setContactTest(false);
			if (id + 1 < totalCubes) {
				cbs[id + 1].rb->setCollideFilterMask(1, -1); //下一块开启碰撞
				cbs[id + 1].phy = 1;						if (id + 2 < totalCubes) {
					cbs[id + 2].rb->setCollideFilterMask(1, -1); //下一块开启碰撞
					cbs[id + 2].phy = 1;
				}
			}
			else {
				bo->pm.atkFlag = (1 << ENdId::efL) | (1 << ENdId::efR);// (1 << ENdId::ehL) | (1 << ENdId::ehR); //
				bo->resetStatus();
			}
			};
		if (phyAfterHit && id < totalCubes - 1)
		{
			vec3 shoudPos = glh::matTransformVec(mmdMat, cbs[id ].ballPos);
			//if (glm::length2(shoudPos - cbs[id].ballPos) < 10.f * 10.f)
			cbs[id].ballPos = bo->rb->getPosition();
			//auto rb = mmd->players[nidToSbid(id + 1)]->ndUpper2->rb0;
			//vec3 pos = glh::matTransformVec(rb->GetTransform(), { 0,0,-10 });
			//cbs[id + 1].ballPos = pos;
			cbs[id].ballVel = glh::calcVelocityP2PinTimeGuess(bo->rb->getPosition(),glh::matTransformVec(mmdMat, cbs[id + 1].ballPos), gGravity, cbs[id + 1].preTime - gSceneTime, ballDamping, 60 * SABA_PHYSICS_FRAMESTEP);
		}  
		if (Pm.fixPosOnHit)
			brb->SetCoMTranslate(cbs[id].ballPos);
		brb->setLinearVel(cbs[id].ballVel); 

	};
}

void CMidiPlateSceneNode::createPlateMesh(const std::vector<CMidiPlateSceneNode::PlateParams>& plate_params, float side_len, float depth)
{
	auto totalCubes = plate_params.size() - 1;

	// Set up indices
	indexCount = totalCubes * 36;
	std::vector<u32> indices(indexCount);
	for (u32 i = 0; i < totalCubes; ++i)
	{
		u32 baseIndex = i * 24;
		u32 indexOffset = i * 36;
		for (u32 f = 0; f < 6; ++f) {
			indices[indexOffset++] = baseIndex + f * 4 + 0;
			indices[indexOffset++] = baseIndex + f * 4 + 2;
			indices[indexOffset++] = baseIndex + f * 4 + 1;
			indices[indexOffset++] = baseIndex + f * 4 + 3;
			indices[indexOffset++] = baseIndex + f * 4 + 2;
			indices[indexOffset++] = baseIndex + f * 4 + 0;
		}
	}

	// Set up vertices , only set uv, others will update in updateFrame()
	u32 totalVertices = totalCubes * 24;
	std::vector<video::S3DVertex> vertices(totalVertices);

	for (int i = 0; i < totalCubes; ++i)
	{
		const auto& param = plate_params[i];
		glm::vec3 position = param.boxPos;
		glm::quat rotation = param.rotation;
		auto transform = rootMat * glm::translate(glm::mat4(1.0f), position) * glm::mat4_cast(rotation);

		u32 baseIndex = i * 24;
		for (u32 f = 0; f < 6; ++f)
		{
			glm::vec2 texCoord1, texCoord2;
			switch (f)
			{
			case 0: // Front
				texCoord1 = glm::vec2(0, 0); texCoord2 = glm::vec2(1, 1);
				break;
			case 1: // Back
				texCoord1 = glm::vec2(1, 0); texCoord2 = glm::vec2(0, 1);
				break;
			case 2: // Left
				texCoord1 = glm::vec2(0, 0); texCoord2 = glm::vec2(1, 1);
				break;
			case 3: // Right
				texCoord1 = glm::vec2(1, 0); texCoord2 = glm::vec2(0, 1);
				break;
			case 4: // Top
				texCoord1 = glm::vec2(0, 1); texCoord2 = glm::vec2(1, 0);
				break;
			case 5: // Bottom
				texCoord1 = glm::vec2(0, 0); texCoord2 = glm::vec2(1, 1);
				break;
			}
			for (u32 vIdx = 0; vIdx < 4; ++vIdx)
			{
				glm::vec2 texCoord = (vIdx == 0 || vIdx == 3) ? texCoord1 : texCoord2;
				vertices[baseIndex + f * 4 + vIdx].TCoords = core::vector2df(texCoord.x, texCoord.y);
			}
		}
	}

	// Create hardware buffers
	video::IVideoDriver* driver = SceneManager->getVideoDriver();

	if (hbVertices)
		hbVertices->drop();
	hbVertices = driver->createHardwareBuffer(video::EHBT_VERTEX, video::EHBA_DEFAULT_RW, totalVertices * sizeof(video::S3DVertex), 0, vertices.data());

	if (hbIndexs)
		hbIndexs->drop();
	hbIndexs = driver->createHardwareBuffer(video::EHBT_INDEX, video::EHBA_DEFAULT, indexCount * sizeof(u32), 0, indices.data());

	// Update bounding box
	Box.reset(vertices[0].Pos);
	for (const auto& vertex : vertices)
		Box.addInternalPoint(vertex.Pos);
}

void CMidiPlateSceneNode::createCylinderMesh(const std::vector<CMidiPlateSceneNode::PlateParams>& plate_params, float radius, float height)
{
	auto totalCylinders = plate_params.size() - 1;
	const int segments = 36; // Number of segments to approximate the cylinder
	// Calculate total vertices and indices
	u32 verticesPerCylinder = segments * 4 + 2; // 2 center vertices + segments * 4 (top, bottom, 2 for side)
	u32 totalVertices = totalCylinders * verticesPerCylinder;
	u32 indicesPerCylinder = segments * 12; // 3 * segments for top, 3 * segments for bottom, 6 * segments for sides
	indexCount = totalCylinders * indicesPerCylinder;
	std::vector<u32> indices(indexCount);
	std::vector<video::S3DVertex> vertices(totalVertices);
	u32 vertexOffset = 0;
	u32 indexOffset = 0;
	for (u32 i = 0; i < totalCylinders; ++i)
	{
		const auto& param = plate_params[i];
		glm::vec3 position = param.boxPos;
		glm::quat rotation = param.rotation;
		mat4 transform = rootMat * glm::translate(glm::mat4(1.0f), position) * glm::mat4_cast(rotation);
		u32 baseIndex = vertexOffset;
		// Center vertices for top and bottom
		glm::vec4 topCenter = transform * glm::vec4(0, height / 2, 0, 1.0f);
		glm::vec4 bottomCenter = transform * glm::vec4(0, -height / 2, 0, 1.0f);
		vertices[vertexOffset].Pos = core::vector3df(topCenter.x, topCenter.y, topCenter.z);
		vertices[vertexOffset].Normal = core::vector3df(0, 1, 0);
		vertices[vertexOffset].TCoords = core::vector2df(0.5f, 0.5f);
		vertexOffset++;
		vertices[vertexOffset].Pos = core::vector3df(bottomCenter.x, bottomCenter.y, bottomCenter.z);
		vertices[vertexOffset].Normal = core::vector3df(0, -1, 0);
		vertices[vertexOffset].TCoords = core::vector2df(0.5f, 0.5f);
		vertexOffset++;
		for (int j = 0; j < segments; ++j)
		{
			float angle = glm::radians(360.0f / segments * j);
			float x = radius * cos(angle);
			float z = radius * sin(angle);
			glm::vec4 topVertex = transform * glm::vec4(x, height / 2, z, 1.0f);
			glm::vec4 bottomVertex = transform * glm::vec4(x, -height / 2, z, 1.0f);
			// Top rim vertex
			vertices[vertexOffset].Pos = core::vector3df(topVertex.x, topVertex.y, topVertex.z);
			vertices[vertexOffset].Normal = core::vector3df(0, 1, 0);
			vertices[vertexOffset].TCoords = core::vector2df(cos(angle) * 0.5f + 0.5f, sin(angle) * 0.5f + 0.5f);
			vertexOffset++;
			// Bottom rim vertex
			vertices[vertexOffset].Pos = core::vector3df(bottomVertex.x, bottomVertex.y, bottomVertex.z);
			vertices[vertexOffset].Normal = core::vector3df(0, -1, 0);
			vertices[vertexOffset].TCoords = core::vector2df(cos(angle) * 0.5f + 0.5f, sin(angle) * 0.5f + 0.5f);
			vertexOffset++;
			// Side top vertex
			vertices[vertexOffset].Pos = core::vector3df(topVertex.x, topVertex.y, topVertex.z);
			vertices[vertexOffset].Normal = core::vector3df(x, 0, z).normalize();
			vertices[vertexOffset].TCoords = core::vector2df(float(j) / segments, 0);
			vertexOffset++;
			// Side bottom vertex
			vertices[vertexOffset].Pos = core::vector3df(bottomVertex.x, bottomVertex.y, bottomVertex.z);
			vertices[vertexOffset].Normal = core::vector3df(x, 0, z).normalize();
			vertices[vertexOffset].TCoords = core::vector2df(float(j) / segments, 1);
			vertexOffset++;
			int next = (j + 1) % segments;
			// Top face
			indices[indexOffset++] = baseIndex;
			indices[indexOffset++] = baseIndex + 2 + next * 4;
			indices[indexOffset++] = baseIndex + 2 + j * 4;
			// Bottom face
			indices[indexOffset++] = baseIndex + 1;
			indices[indexOffset++] = baseIndex + 3 + j * 4;
			indices[indexOffset++] = baseIndex + 3 + next * 4;
			// Side face
			indices[indexOffset++] = baseIndex + 4 + j * 4;
			indices[indexOffset++] = baseIndex + 4 + next * 4;
			indices[indexOffset++] = baseIndex + 5 + j * 4;
			indices[indexOffset++] = baseIndex + 4 + next * 4;
			indices[indexOffset++] = baseIndex + 5 + next * 4;
			indices[indexOffset++] = baseIndex + 5 + j * 4;
		}
	}
	// Create hardware buffers
	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	if (hbVertices)
		hbVertices->drop();
	hbVertices = driver->createHardwareBuffer(video::EHBT_VERTEX, video::EHBA_DEFAULT_RW, totalVertices * sizeof(video::S3DVertex), 0, vertices.data());
	if (hbIndexs)
		hbIndexs->drop();
	hbIndexs = driver->createHardwareBuffer(video::EHBT_INDEX, video::EHBA_DEFAULT, indexCount * sizeof(u32), 0, indices.data());
	// Update bounding box
	Box.reset(vertices[0].Pos);
	for (const auto& vertex : vertices)
		Box.addInternalPoint(vertex.Pos);
}

void CMidiPlateSceneNode::updateFrame()
{
	if (!hbVertices)
		return;
	if (is0)
		playTime += gFrameTime;
	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	S3DVertex* vertices = static_cast<S3DVertex*>(hbVertices->lock(true));
	if (!vertices)
		return;

	if (balls.size()) for (int i = 0; i < balls.size(); ++i) 
	{
		auto& bpo = balls[i];
		//auto &bpo=balls.back();
		BallData& pm = std::any_cast<BallData&>(bpo->pm.dataT);
		bool hit=false;
		while (pm.playId + 1 < notes.size() && playTime > notes[pm.playId + 1].time + pm.timeOfs) {
			pm.playId = pm.playId + 1;
			hit = true;
		}
		if (hit)
		{
			if (pm.timeOfs == 0) playid = pm.playId;
			if (pm.playId >= 0)
				onBallHit(bpo, pm.playId, notes.size());	
			if (auto po = cbs[pm.playId].flyPo) {
				float dis = glm::length(po->rb->getPosition() - Pm.sb0->ndHead->rb0->getPosition()) - po->pm.size.x/2.f;
				DP(("dis %f y=%f", dis,po->rb->getPosition().y)); 
				if ((dis < 2.f  || dis<3.f && Pm.sb0->zyPast>2.f) &&Pm.sb0->zyTimer > MMD_ZY_DUR)	Pm.sb0->zyTimer = MMD_ZY_DUR;	
				//po->rb->scaleVel(0.5f, 3);
				cbs[pm.playId].flyPo = nullptr; 
			//	mmd->Pom->removeObj(cbs[pm.playId].flyPo);
			}
		}

		if (Pm.noteBall) {
			if (pm.noteBallPlayId < pm.playId) pm.noteBallPlayId = pm.playId;
			bool hit = false; float flyTime = nbp.extraFlyTime ;
			while (pm.noteBallPlayId + 1 < notes.size() && playTime+flyTime > notes[pm.noteBallPlayId + 1].time + pm.timeOfs) {
				pm.noteBallPlayId = pm.noteBallPlayId + 1;
				hit = true;
			}
			if (hit)
			{
				float r = nbp.radiusX2;// Pm.ballRadiusX2* notes[pm.extraPlayId].me->velocity / 127.f * 3.f;
				vec3 srcpos = nbp.srcPos;// 
				vec3 tgtpos = glh::matTransformVec(mmdMat, cbs[pm.noteBallPlayId].ballPos);
				
				if (nbp.srcMode == 1) {
					float dropY = -0.5f * gGravity.y * pow(  flyTime * MMDPhysics::phyTimeMul, 2.f);
					srcpos += tgtpos*vec3(0,1,0) + vec3(0, dropY, 0) + UaRandm1to1() * vec3(1, 0, 1) * 0.f;
				}
					//Pm.sb0->Rb0()->getPosition()+ vec3{0,0,-20} + ualib::UaRandVec3() * 0.f; //vec3{ 0,35,-30 } + ualib::UaRandVec3()*5.f;
				PhyObjParam opm{ 1 ,1.f,vec3(r),{srcpos},{},{0,0,0},true,false,false};
				opm.operation = 1;   opm.autoKill = true; opm.timer = nbp.ballTimer;
				opm.visible = 1;
				//pm.hitSbTest = false;
				opm.mass = nbp.initMass; //opm.size.x * opm.size.y * opm.size.z 
				opm.pmxrb.m_repulsion = 0.5f;
				opm.pmxrb.m_friction = 0.5f;
				opm.pmxrb.m_group = 16;
				opm.pmxrb.m_collideMask32 = -1;
				//pm.pmxrb.modContact = true;
				opm.pmxrb.m_translateDimmer = ballDamping;
				opm.pmxrb.m_rotateDimmer = 1;
				opm.pmxrb.flag = PHRBF_ContactOffset;
				opm.pmxrb.forceNotify = true; //enable USE_FORCE_NOTIFY
				static int cc = 0;
				opm.ownerAdd = 0x20000 + cc++;;
				if (nbp.srcMode == 0) opm.autoScale = 1; opm.ascDur = flyTime-0.033f;

				//pm.pmxrb.modContactThreshold = true;
				static float h = 0; h += 3.0f;
				SColorHSL hsl(h, 100, 70);
				opm.color = hsl.toSColor().color; //0x7FFFC080;//
				opm.color.setAlpha(180); opm.alphaMtr = true;
				static std::vector<std::pair<std::string, float>> file = { {"D:/MMD/ITEMS/Petal/sakura.pmx",1},
					{"D:/MMD/ITEMS/Petal/leafFlower.pmx",1}, {"D:/MMD/ITEMS/coins/coinWBG1.pmx",0.2f},
					{"D:/MMD/PMX/JQL/zhe/1.pmx",0.3f},
				};
				auto pa = file[nbp.nbIdx % file.size()];
				PMXFileCreateParam cp{ pa.first };
				 
				cp.modelScale = true; cp.modelScaleVec3 = vec3(Ctx->gd.apm.scale*pa.second);
				if (Pm.nbIsSb) {
					opm.sb = gLoadSabaModel(cp, 0x1)->sb;
					opm.poType = 0;
					opm.noRtt = 1;
					opm.vel = glh::calcVelocityP2PinTimeGuess(srcpos, tgtpos, gGravity, (flyTime - 0.067f) * MMDPhysics::phyTimeMul, opm.sb->Rb0()->Pm.m_translateDimmer, 60 * SABA_PHYSICS_FRAMESTEP); ;
					int rbid = 0;
					for (auto& rb : opm.sb->Pmx->getRigidBodys(0)) {
						if (rbid > 0)
							rb->setCollideFilterMask(1, 0);
						rbid++;
						//rb->setContactTest(false);
					}
					opm.sb->hitDisabled = true;
				}
				else {
					opm.disableHitForTime = flyTime - 0.1f; //Do not set if manual controled
					opm.vel = glh::calcVelocityP2PinTimeGuess(srcpos, tgtpos, gGravity, flyTime * MMDPhysics::phyTimeMul, ballDamping, 60 * SABA_PHYSICS_FRAMESTEP); ;

					{
						opm.poType = 32; opm.color.setAlpha(255); opm.alphaMtr = false;
						auto& pm = opm;
						switch (pm.poType)
						{
						case 32:
						{
							static auto sms = SceneManager->getMesh("data/mesh/coin.obj");
							pm.meshScale = float3(nbp.radiusX2);
							pm.size = { pm.size.x * 1.414,  pm.size.y / 3, pm.size.z * 1.414 };
							pm.mesh = sms;

							static int mcc = 0;
							static const std::wstring mfn[] = { L"data/mesh/coin.obj",L"data/mesh/kd.obj",L"data/mesh/bowl.obj" };
							//pm.size = vec3(2, 1, 2);
							if (nbp.snIM) pm.instancedMesh = mfn[mcc++ % 1]; // "data/mesh/coin.obj";
						}
						break;
						}
					}
				}
				auto po = cbs[pm.noteBallPlayId ].flyPo = mmd->Pom->addObj(opm);
				if (po->pm.sn) {
					auto& mtr = po->pm.sn->getMaterial(0);
					mtr.SpecularColor = 0x02505050; mtr.AmbientColor = 0x80FFFFFF;
				}
				//opm.tag = 'exb';
				// rb->onHitBySb= [=]() {						Pm.sb0->zyTimer = MMD_ZY_DUR;
				if (onExtBall) onExtBall();
			}
		}
		//else if (Pm.actMode == 1)
		//{
		//	auto bp = bpo->rb->getPosition();
		//	if (bp.y>50.f) bpo->rb->SetCoMTranslate(vec3(bp.x, 50.f, bp.z));
		//}
		
		if (i == 0 && actAnim)
		{
			{
				Pm.sb0->mmdLookAt = Pm.noteBall && cbs[curVisBallId].flyPo ?cbs[curVisBallId].flyPo->rb->getPosition():balls[curVisBallId]->rb->getPosition();
				Pm.sb0->lookAt_mmdLookAt = true;
			}
			for (int si = 1; si < mmd->players.size(); ++si)
			{
				auto sb = mmd->players[si];
				if (!sb->Pmx->curVA  || playTime <= -1 + 1/60.f) {
					sb->setAdAnim(0, 1);; // to set afterT in saba update
				}
				if (sb->Pmx->curVA->afterT) {
					int ni = nid +1;
					int cc = 0;
					while (nidToSbidNstar(mmd->players.size()-1, ni)+1 != si && cc<mmd->players.size()) ni++,cc++;
					float timeToNext=notes[ni].time - playTime;
					DP(("ttn %d,%d",ni, si-1));
					int vai = sb->Pmx->vaGetLessTimeRand(timeToNext);
					auto va= &sb->Pmx->vmdAddAnims[vai];
					sb->setAdAnim(vai, 1, timeToNext-va->vi.actT);

					va->vmd->Evaluate(va->vi.actT * 30 + (sb->Rb0()->GetActivation() ? -0.6f : 0.f), 1,2);

					sb->Pmx->UpdateNodeAnimation(false);

					auto tgtPos = va->ndAct->getRbAnimGlobalPos();
					cbs[ni].ballPos = tgtPos;
					Pm.fixPosOnHit = false;
				}

			}
		}
	}

	//set can hit 
	for (int i = 0; i < notes.size() - 1; ++i)
	{
		if (cbs[i].flyPo && !cbs[i].flyPoCanHit && playTime > notes[i].time  - 0.0167f) {
			//cbs[i].flyPo->rb->setCollideFilterMask(1, -1);
			//cbs[i].flyPo->rb->addForce(vec3(0, -1000, 0));
			cbs[i].flyPoCanHit = true;
			//cbs[i].flyPo->setScale(vec3(0.1f));			
		}
	}
#define DIV_A  pow(a, 0.75f) //  a //
	if (viewScale>0.f)
	for (int i = 0; i < notes.size() - 1; ++i)
		//for (int i = notes.size() - 1; i >=0; --i)
	{
		float time = playTime;
		mat4 tfm; float oft, spd, durT, movRat, a, acting, movDis;
		getMovParams(spd, i, oft, time, durT, movRat, a, acting, movDis, PLATE_MOV_Y, tfm);

		mat4 transform = tfm;
		 if (Pm.actMode == 1)transform = transform * glm::translate(mat4(1), vec3(0, -2, 0));
		if (cbs[i].rb) cbs[i].rb->SetCoMTransform(transform);

		float nv = 0;
		float t = gSceneTime - cbs[i].onTime;
		if (t > 0 && t < 1)
			nv = 1 - t;
		SColorHSL hsl;
		hsl.Hue = i % 360 * 1.f;
		hsl.Saturation = 50.f + (cbs[i].phy * 0) + (notes[i].mec > 1 ? 0 : 0);
		hsl.Luminance = 50.f +  nv * 30.f;
		auto sc = hsl.toSColor();
		hsl.Saturation *= 0.5f;
		hsl.Luminance *= 0.3f;
		 
		auto scb = hsl.toSColor(); // scb.setAlpha(127); sc.setAlpha(127);
		if (meshType == 1)
		{
			const int segments = 36; // Number of segments to approximate the cylinder
			float radius = Pm.brickSize.x / 2.0f;
			float height = Pm.brickSize.y;
			u32 verticesPerCylinder = segments * 4 + 2; // 2 center vertices + segments * 4 (top, bottom, 2 for side)
			u32 baseIndex = i * verticesPerCylinder;

			// Update center vertices for top and bottom
			glm::vec4 topCenter = transform * glm::vec4(0, height / 2, 0, 1.0f);
			glm::vec4 bottomCenter = transform * glm::vec4(0, -height / 2, 0, 1.0f);

			vertices[baseIndex].Pos = core::vector3df(topCenter.x, topCenter.y, topCenter.z);
			vertices[baseIndex].Normal = core::vector3df(0, 1, 0);
			vertices[baseIndex].TCoords = core::vector2df(0.5f, 0.5f);
			vertices[baseIndex].Color = sc;
			baseIndex++;

			vertices[baseIndex].Pos = core::vector3df(bottomCenter.x, bottomCenter.y, bottomCenter.z);
			vertices[baseIndex].Normal = core::vector3df(0, -1, 0);
			vertices[baseIndex].TCoords = core::vector2df(0.5f, 0.5f);
			vertices[baseIndex].Color = scb;
			baseIndex++;

			for (int j = 0; j < segments; ++j)
			{
				float angle = glm::radians(360.0f / segments * j);
				float x = radius * cos(angle);
				float z = radius * sin(angle);
				glm::vec4 topVertex = transform * glm::vec4(x, height / 2, z, 1.0f);
				glm::vec4 bottomVertex = transform * glm::vec4(x, -height / 2, z, 1.0f);

				// Top rim vertex
				vertices[baseIndex].Pos = core::vector3df(topVertex.x, topVertex.y, topVertex.z);
				vertices[baseIndex].Normal = core::vector3df(0, 1, 0);
				vertices[baseIndex].TCoords = core::vector2df(cos(angle) * 0.5f + 0.5f, sin(angle) * 0.5f + 0.5f);
				vertices[baseIndex].Color = sc;
				baseIndex++;

				// Bottom rim vertex
				vertices[baseIndex].Pos = core::vector3df(bottomVertex.x, bottomVertex.y, bottomVertex.z);
				vertices[baseIndex].Normal = core::vector3df(0, -1, 0);
				vertices[baseIndex].TCoords = core::vector2df(cos(angle) * 0.5f + 0.5f, sin(angle) * 0.5f + 0.5f);
				vertices[baseIndex].Color = scb;
				baseIndex++;

				// Side top vertex
				vertices[baseIndex].Pos = core::vector3df(topVertex.x, topVertex.y, topVertex.z);
				vertices[baseIndex].Normal = core::vector3df(x, 0, z).normalize();
				vertices[baseIndex].TCoords = core::vector2df(float(j) / segments, 0);
				vertices[baseIndex].Color = sc;
				baseIndex++;

				// Side bottom vertex
				vertices[baseIndex].Pos = core::vector3df(bottomVertex.x, bottomVertex.y, bottomVertex.z);
				vertices[baseIndex].Normal = core::vector3df(x, 0, z).normalize();
				vertices[baseIndex].TCoords = core::vector2df(float(j) / segments, 1);
				vertices[baseIndex].Color = sc;
				baseIndex++;
			}
		}
		else
		{
			int mc = notes[i].mec; float mcmul = (mc > 1 && MEC_SIDELEN ? sqrt(float(mc)) : 1.f);
			float halfSide = Pm.brickSize.x / 2.0f * viewScale * mcmul;
			float hfy = Pm.brickSize.y / 2.0f * viewScale * (MEC_THICKNESS ? float(mc) : 1.f);
			float hfz = halfSide * viewScale;
			core::vector3df cubeVertices[8] = {
				{-halfSide, -hfy,  hfz}, // 0: Front left bottom
				{-halfSide,  hfy,  hfz}, // 1: Front left top
				{ halfSide,  hfy,  hfz}, // 2: Front right top
				{ halfSide, -hfy,  hfz}, // 3: Front right bottom
				{-halfSide, -hfy, -hfz}, // 4: Back left bottom
				{-halfSide,  hfy, -hfz}, // 5: Back left top
				{ halfSide,  hfy, -hfz}, // 6: Back right top
				{ halfSide, -hfy, -hfz}  // 7: Back right bottom
			};
			// Original normals
			static const vec3 normals[6] = {
				{ 0,  0,  1}, // Front
				{ 0,  0, -1}, // Back
				{-1,  0,  0}, // Left
				{ 1,  0,  0}, // Right
				{ 0,  1,  0}, // Top
				{ 0, -1,  0}  // Bottom
			};
			static const u16 cubeIndices[24] = {
				0, 1, 2, 3, // Front (Z+)
				7, 6, 5, 4, // Back (Z-)
				4, 5, 1, 0, // Left (X-)
				3, 2, 6, 7, // Right (X+)
				1, 5, 6, 2, // Top (Y+)
				4, 0, 3, 7  // Bottom (Y-)
			};
			// Transform vertices and update normals
			u32 baseIndex = i * 24;

			mat3 m3 = glm::mat3(transform);
			for (u32 f = 0; f < 6; ++f)
			{
				for (u32 v = 0; v < 4; ++v)
				{
					u32 vertexIndex = baseIndex + f * 4 + v;
					u32 cubeVertexIndex = cubeIndices[f * 4 + v];
					// Transform vertex position
					auto pos = cubeVertices[cubeVertexIndex];
					pos = glh::matTransformVec(transform, pos);
					vertices[vertexIndex].Pos = pos;
					// Transform normal
					vec3 norm = m3 * normals[f];
					vertices[vertexIndex].Normal = glm::fastNormalize(norm);
					vertices[vertexIndex].Color =  f == 5 ? 0xFF3C3C3C : sc;
				}
			}
		}
	}

	hbVertices->unlock();
}
 

void CMidiPlateSceneNode::sbPhyAction(int i, glm::mat4& tfm, float movRat, float spd, float a, IrrSaba* sb, bool acting)
{
	static const glm::mat4 LHRTT = glm::rotate(glm::mat4(1), core::PI / 2, vec3(0, -1, 0)) * glm::rotate(glm::mat4(1), core::PI * 3 / 4, vec3(0, 0, 1));
	static const glm::mat4 RHRTT = glm::rotate(glm::mat4(1), core::PI / 2, vec3(0, 1, 0)) * glm::rotate(glm::mat4(1), -core::PI * 3 / 4, vec3(0, 0, 1));
	
	float uph = sb->ndUpper2->mGlobalInit[3].y;
	float sbScale = sb->ndYao->absScale.x;
	glm::mat4 brt = mat4_cast(cbs[i].ballRtt);
	mat4 rty180 = mat4_cast(cbs[i].ballRttYPi);
	glm::mat4 tm = glm::translate(mat4(1), vec3(tfm[3])+ vec3(0, Pm.noteBall&&Pm.nbIsSb ? 2.f * sb->ndRbRoot->absScale.x : 0, 0)) * mat4(mat3(brt));
	float mul = (movRat < 0.52f ? std::max(0.f, pow(0.5f - abs(movRat - 0.5f), 2.f)) * glm::clamp(spd / 100.f, 0.75f, 1.5f) : 0) ;
	mul *= nbp.fMul;
 
	if (Pm.actMode == 1 && sb->ndFgrs[0][0][3])
	{
		float growE = std::max(0.1f, 0.5f - 1 / a);
		float mul2 = (movRat < growE ? std::min(1.0,   uu::blendFun(uu::Ebf::easeInOutSine, movRat / growE)) : 1.f);
		int fid = i % 10;
		int ci = 0; float cdis = 99999; vec3 ndpC;
		static vec3 fp[10] = { 
			vec3(1, 0, 2), vec3(1, 0, 1), vec3(1, 0, 0), vec3(1, 0, 1), vec3(1, 0, 2),
			vec3(-1,0,2), vec3(-1,0,1), vec3(-1,0,0), vec3(-1,0,1), vec3(-1,0,2)			
		};
		for (int fi = 0; fi < 10; fi++) {
			auto ndi = sb->ndFgrs[fi / 5][fi % 5][2]; vec3 ndp = vec3(ndi->mGlobalAnim[3]);
			vec3 ballp = cbs[i].ballPos; ballp.y = 0;
			float dis = glm::length(
				//vec3(sb->ndFgrs[fi / 5][fi % 5][2]->mGlobalAnim[3])
				fp[fi] 
				- ballp);
			if (dis < cdis) { ci = fi; cdis = dis; ndpC = ndp; }
			//sb->ndFgrs[fi / 5][fi % 5][1]->rb0->scaleVel(0.25f, 2);
		}
		//sb->mmdFw(2, "pt", ndpC, vec3(0), 0xFFFF007F);
		
		if (Pm.subAct == 0 )
		{    
			 
			int isR = ci / 5;
			auto nd = nbp.footButt? (isR ? sb->ndFootR : sb->ndFootL) :sb->ndFgrs[ci / 5][ci % 5][2];

			vec3 tgt = vec3(tm[3]);
			//sb->mmdFw(2, "pt", tgt, vec3(0), 0xFF00007F);
			//DP(("DIS i=%d ci=%d mul=%.2f dis=%f ",i, ci,mul, glm::length(nd->rb0->getPosition()-tgt)));
			if (!nd->rb0)
				nd = isR ? sb->ndHandR : sb->ndHandL;
			if (mul > 0.00001f) {
				nd->rb0->scaleVel(0.9f - mul * 0.2f, 1);
				//nd->rb0->addLinearVelToPosLimitDis(tgt,  200 * mul * (mul2), 1.f,5.0f);

				nd->rb0->addLinearVelToPosLimitDis2(tgt, 100 * mul * mul2, 1.f, 2.0f);
				//nd->rb0->addLinearVelToPosSc(tgt,   (acting ? 100 : 100) * (5) * mul * (mul2)  , 0.3f);
				auto ndHand = nbp.footButt ? nd : isR ? sb->ndHandR : sb->ndHandL;
				float d = Pm.noteBall && Pm.nbIsSb ? 0.f:uph / 12.f;
				//sb->mmdFw(2, "pt", (glm::translate(tm, { 0, glm::max(-2.f,-d * 0.5f) , 0 }))[3], vec3(0), 0xFFFFFFFF);
				vec3 uppos = (glm::translate(tm, { 0, 0 , -d * 1 }))[3]; //uppos.y = std::max(uppos.y, sb->ndUpper2->mGlobalInit[3].y);
				//sb->mmdFw(2, "pt", uppos, vec3(0), 0xFF00FFFF);
				nd->rb0->scaleVel(0.9f - mul * 0.16f, 1);
				//ndHand->rb0->addLinearVelToPosLimitDis(uppos, 35 * (mul)*uph / 30.f * (1.f + mul2 * 2.f), 2.f, 6.f);
				ndHand->rb0->addLinearVelToPosLimitDis2(uppos, mul * uph * 0.957f * (1.f + mul2 * 2.f), 2.f, 3.f);
				//ndHand->rb0->addLinearVel({ 0,981 * gFrameTime/saba::MMDPhysics::stepTimeMulInv,0 });
				ndHand->rb0->addRotationToMatOnNode(brt * (isR ? RHRTT : LHRTT), 300 + 1700 * mul2);
			}
			sb->ndUpper2->rb0->setAngVelToPos(tgt,  10+90* nbp.rMul);
			sb->ndUpper2->rb0->addLinearVelToPos(tgt, 0.02f +0.3f * nbp.rMul);
		}
		else if (Pm.subAct == 1)
		{
			float growE = std::max(0.1f, 0.5f );
			mul2 = movRat < growE *0.5f ?0: (movRat < growE ? std::min(1.0, uu::blendFun(uu::Ebf::easeInSine, movRat / growE)) : 1.f);
			//sb->mmdFw(2, "pt", ndpC, vec3(0), 0xFFFF007F);		
			int isR = ci / 5;
			auto nd = Pm.subActR? sb->ndFootR : sb->ndFootL;
			vec3 tgt = tm[3];
			if (nd->rb0) {
				//nd->rb0->scaleVel(0.8f - mul * 0.3f, 1);
				nd->rb0->addLinearVelToPosLimitDis2(tgt, 100 * mul * mul2, 0.f, 2.0f);
			}
		}
	}
	else if (Pm.actMode == 2)
	{
		
		float angle0 = glh::angleDegToAxisY(sb->ndYao->rb0->GetTransform());
		if (movRat > 0.2f && movRat < 0.47f)
		{
			///sb->Pmx->addBodyVel({ 0,  -3,0 }, 1);
			
			sb->ndYao->rb0->addLinearVel({ 0, (angle0 < 60? -16:16)* notes[i].me->velocity/100.f, 0 });
			sb->ndLegL->rb0->addLinearVel({ 0, -3, 0 });            sb->ndLegL->rb0->addRotationToMatOnNode_MatRttResetXZ( 30, 2, 0);

			sb->ndLegR->rb0->addLinearVel({ 0, -3, 0 });            sb->ndLegR->rb0->addRotationToMatOnNode_MatRttResetXZ( 30, 2, 0);
			sb->ndHandL->rb0->addLinearVel({ 0, 19, 0 });  
			sb->ndHandR->rb0->addLinearVel({ 0, 19, 0 });
		}
		else if (movRat > 0.47f && movRat < 0.51f && sb->ndYao->rb0->pos.y< sb->Pmx->legLength*1.1f)
		{
			DP(("jump %f", movRat));
			mul = (movRat<0.49f? (movRat-0.47f)/0.02f:1.f)*0.2f* nbp.fMul * (sb->Pmx->groundAngleDeg < 60?1.f:0.1f);
			vec3 bodyAddV = { 0,  notes[i].me->velocity * mul,0 };
			sb->Pmx->addBodyVel(bodyAddV, 1);
			mat4 ofsM = glm::translate(mat4(1), { 0, glm::max(-4.f, (uph - 20.f) * 0.5f - 1) + (nbp.footButt ? 6 : 0) ,nbp.footButt ? -uph / 3.f : -uph / 6.f });
			vec3 uppos = //sb->mmdLookAt;//
				sb->ndUpper2->getRbAnimGlobalPos(); uppos.y = std::max(30.f, sb->ndUpper2->mGlobalInit[3].y);
			if (movRat>0.49f) sb->ndUpper2->rb0->addLinearVelToPos(uppos,10);
			if (movRat < 0.5f)
			{
				sb->ndFootL->rb0->setLinearVel({ 0, -1, 0 });
				sb->ndFootR->rb0->setLinearVel({ 0, -1, 0 });
				sb->ndLeg1L->rb0->addLinearVel(-bodyAddV); sb->ndLeg1L->rb0->addLinearVel(-bodyAddV);
				sb->ndHandL->rb0->addLinearVel(-bodyAddV*2.f); sb->ndHandR->rb0->addLinearVel(-bodyAddV*2.f);
			}
			 sb->mmdFw(1, "airDown", sb->ndYao->rbPos(), vec3{ 0,-10,0 }, SColor(0xFFFFFFFF));
		}
		else if (movRat > 0.52f)
		{
			bool rtting = false;
			if (//movRat > 0.6f && 
				movRat<0.9f)
				if (sb->ndRbRoot->rb0->vel.y > -1.f 
					 || angle0 > 90 || sb->ndYao->lastAngleY < angle0
					) {
					//sb->ndRbRoot->rb0->addTorqueLocal(  vec3(0, nbp.rMul, 0) * float(1000.f / Ctx->gd.timeMul), true);
					float legmul = 100.f;
					sb->ndLegR->rb0->addTorqueLocal( vec3(-nbp.rMul * 2.f,0,-nbp.rMul*0.f  )*float(legmul / Ctx->gd.timeMul), true);
					sb->ndLegL->rb0->addTorqueLocal( vec3(-nbp.rMul * 2.f,0,nbp.rMul*0.f  )* float(legmul / Ctx->gd.timeMul), true);
					sb->ndLeg1L->rb0->addTorqueLocal(vec3(nbp.rMul, 0, 0)* float(legmul / Ctx->gd.timeMul), true);
					sb->ndLeg1R->rb0->addTorqueLocal(vec3(nbp.rMul, 0, 0)*  float(legmul / Ctx->gd.timeMul), true);
					sbFw2LineD("pt1s", sb->ndYao->rbPos(), sb->ndYao->rb0->getNodeTransform()*vec4(0,20,0,1), 0xFF0080FF, 10);
					rtting = true;
				}
			if (//movRat < 0.999f  && 
				!rtting
				) {
				//sb->Pmx->scaleBodyVel(0.5f, 2);
				sb->ndRbRoot->rb0->addRotationToMatOnNode_MatRttResetXZ(1000, 0, 0);
			}
		}
		sb->ndYao->lastAngleY = angle0;
	}

	else {
		float growE = std::max(0.1f, 0.5f - 1 / a);
		float mul2 = (movRat < growE ? std::min(1.f, 0.1f + 0.9f * (movRat / growE)) : 1.f)*2;
		int headButt = nbp.headButt;// | ((notes[i].me->track + 1) % 2);
		int footButt = nbp.footButt;// | (notes[i].me->track % 2);
		 if (nbp.footButt) mul2 = pow(mul2, 1.f) * 2;
		bool isL = mmd->players.size()<2?true: (i / (mmd->players.size() - 1) % 2 == 0);
		bool isLong = Pm.actMode == 0 && (notes[i].me->duration > 0.167f) && footButt==0;
		bool jump = sb->lookAt_mmdLookAt && movRat < 0.52f && movRat > 0.38f;
		if (mul > 0.001f) {// movRat<0.52
			if (sb->ndHitBall)
				sb->ndHitBall->rb0->addLinearVelToPosSc(tm[3], (acting ? 160 : 25) * (isLong ? 1 : 2) * mul * mul2, 0.5f);
			else if (headButt)
				sb->ndHead->rb0->addLinearVelToPosSc(tm[3], (acting ? 160 : 25) * (isLong ? 1 : 2) * mul * mul2, 0.5f);
			else {
				auto ndL = footButt ? sb->ndFootL : sb->ndHandL, ndR = footButt ? sb->ndFootR : sb->ndHandR; float fL, fR;
				if (movRat > 0.479f) {
					vec3 v1 = ndL->rb0->addLinearVelToPosSc((tm * glm::translate(mat4(1), { isLong && isL ? -1.f : 0.f, 0, 0 }))[3], fL = ( 2000) * (isLong ? 1 : isL ? 2 : -0.02f) * mul * mul2, 0.5f);
					vec3 v2 = ndR->rb0->addLinearVelToPosSc((tm * glm::translate(mat4(1), { isLong && !isL ? 1.f : 0.f, 0, 0 }))[3], fR = ( 2000) * (isLong ? 1 : isL ? -0.02f : 2) * mul * mul2, 0.5f);
					sb->ndUpper2->rb0->addLinearVel(-(v1 + v2) * 0.2f);
				}
				else {
					vec3 v1 = ndL->rb0->addLinearVelToPosSc((tm * glm::translate(mat4(1), { isLong && isL ? -1.f : 0.f, 0, 0 }))[3], fL = (  acting ? 160 : jump ? 2 : 1) * (isLong ? 1 : isL ? 2 : -0.02f) * mul * mul2, 0.5f);
					vec3 v2 = ndR->rb0->addLinearVelToPosSc((tm * glm::translate(mat4(1), { isLong && !isL ? 1.f : 0.f, 0, 0 }))[3], fR = (  acting ? 160 : jump ? 2 : 1) * (isLong ? 1 : isL ? -0.02f : 2) * mul * mul2, 0.5f);

				}
				{
					vec3 downmf = footButt ? vec3(0, -0.f, 0) : vec3(0, -0.2f, 0);
					if (isL) ndR->rb0->addLinearVel(downmf * fL);
					else ndL->rb0->addLinearVel(downmf * fR);
				}
				if (footButt) {
					 {
						sb->ndLegL->rb0->addTorqueLocal(vec3((isL ? -1 : 1) * nbp.rMul * 100.f, 0, 0.f) * float(1 / Ctx->gd.timeMul), true);
						sb->ndLegR->rb0->addTorqueLocal(vec3((isL ? 1 : -1) * nbp.rMul * 100.f, 0, 0.f) * float(1 / Ctx->gd.timeMul), true);
					}

				}else {
					ndL->rb0->addRotationToMatOnNode(brt * LHRTT, (acting ? 15000 : 7500));
					ndR->rb0->addRotationToMatOnNode(brt * RHRTT, (acting ? 15000 : 7500));
				}
				//ndL->rb0->dbgBreak_addVel = 1;
			}
			sb->ndYao->rb0->setAngVelToPos(vec3(mmdMat[3]), 100);
 
			//if (sb->ndRoot->phyAnimRatio() < 0.01f) {
			//	vec3 rootPos = tm[3]; rootPos.y = 0;
			//	sb->ndRoot->SetAnimationTranslate(rootPos);
			//}
		}
		if (sb->Rb0()->dis2To(tm[3]) > 10000.f) {
			sb->Pmx->moveAllOffset(vec3(tm[3]) - sb->Rb0()->getPosition(), false);
		}
		mat4 ofsM = glm::translate(mat4(1), { 0, glm::max(-4.f, (uph - 20.f) * 0.5f - 1) + (footButt ? 16 : 0) ,footButt ? -uph / 3.f : -uph / 6.f });
		//sb->ndUpper2->rb0->setLinearVel(glh::vecLimitFast(sb->ndUpper2->rb0->getLinearVel(),0, 10.f));
		//if (movRat < 0.5f ) sb->Pmx->scaleDynBodyVel(0.96f,3);
		//sb->mmdFw(2, "sw2", vec3((tm * ofsM)[3]), vec3{ 0 }, SColor(0xFFFFFFFF));
		
		vec3 uppos = (tm * ofsM)[3]; uppos.y = std::max(uppos.y +  (footButt && movRat < 0.52f ? 20.f : 0.f) , sb->ndUpper2->mGlobalInit[3].y);
		sb->ndUpper2->rb0->addLinearVelToPosSc(uppos, (jump?nbp.upMul:nbp.downMul) * (0.1f + 0.9f * mul) * uph *0.1f * (footButt && movRat < 0.52f  ? 1.1f : 1.f), 0.7f);
		//sb->mmdFw(2, "sw2", uppos, vec3{ 0 }, SColor(0xFFFFFFFF));
		if (jump)sb->ndUpper2->rb0->addLinearVel({ 0,981 * gFrameTime,0 });
		//sb->ndHead->rb0->setAngVelToRotateOnNode(rty180, 10);
		sb->ndUpper2->rb0->addRotationToMatOnNode(rty180, nbp.footButt ? 1000 : 10000);
		{
			quat q = quat(rty180);
			float yaw = atan2(2.0f * (q.w * q.y + q.x * q.z), 1.0f - 2.0f * (q.y * q.y + q.z * q.z));
			rty180 = mat4(glm::angleAxis(yaw, glm::vec3(0, 1, 0)));
		}

		//sb->ndUpper->rb0->addRotationToMatOnNode(rty180, 16000);
		sb->ndYao->rb0->addRotationToMatOnNode(rty180, nbp.footButt ? 1.600 : 16000);
		//sb->ndLower->rb0->addRotationToMatOnNode(rty180, 16000);
	}
}

void CMidiPlateSceneNode::ballFrameFw()
{
	if (!Pm.ballVisible) return;
	if (!balFrameFw ){
		for (auto ball : balls) {
			auto rb = balls[0]->rb;
			if (Pm.actMode!=2)
			Pm.sb0->mmdFw(2, "sw_200ms", rb->getPosition() , vec3(0), 0x80FFFFFF);
		}
		return;
	}
	for (auto ball : balls)
		for (int i = 0; i < 2; i++) {
			float a = i * glm::radians(180.f);
			vec3 dir = vec3(sin(a), 0, cos(a));
			auto rb = ball->rb;
			dir = glh::matRotateVec(rb->GetTransform(), dir * 100.f);
			Pm.sb0->mmdFw(1, "nodeSpdFwS", rb->getPosition() + rb->getLinearVel() * gFrameTime, dir, 0xFFFFFFFF);
		}
}

void CMidiPlateSceneNode::phyStepMMD(float time)
{
	if (!PSB_DynRbActive) return;

	int flag[32] = {};

	//DP(("time %.3f",time));
	
	for (int i = 0; i < notes.size() - 1; ++i) 
		//for (int i = notes.size() - 1; i >=0; --i)
	{
		mat4 tfm; float oft,spd, durT, movRat, a, acting, movDis;
		getMovParams(spd, i, oft, time, durT, movRat, a, acting, movDis, HAND_MOV_Y, tfm);
 
		auto& sbs = mmd->players;
		if (mmd->players.size() > 1 || Pm.actMode == 1 ) {
			int sbid = Pm.actMode==1? playerIdx : nidToSbidNstar(sbs.size() - 1, i) + 1;//i % (sbs.size() - 1) + 1;
			auto sb = sbs[sbid];
			if (CLOSEST_SB) if (cbs[i].sb) sb = cbs[i].sb;
			
			if (//movRat-0.5f > -3/a && movRat - 0.5f< 3/a 
				oft > -durT / 2.f && oft < durT / 4.f  //动作时间内
				// oft>-0.5f
			   //oft > -durT / 2 * std::max(1.f, prepareTimeMul) && oft < durT / 2 //oft>-durT && oft< durT/2
				&& !flag[sbid] )
			{	

				if (movRat < 0.5f) flag[sbid] = 1;
				sbLastActBallId[sbid] = i;
				if (!notes[i].fake) sbPhyAction(i, tfm, movRat, spd, a, sb, acting);


				if (Pm.subAct==0 && curVisBallId < balls.size()) {
					auto flyPo = Pm.noteBall ? cbs[i].flyPo:0;
					sb->mmdLookAt = flyPo ?  flyPo->rb->getPosition() : balls[curVisBallId]->rb->getPosition();
					sb->lookAt_mmdLookAt = Pm.actMode !=2 && notes[i].time - playTime < 0.25f;//				:sb->mmdLookAt.y-sb->ndYao->getGlobalPos().y<20.f*sb->ndYao->absScale.y &&  !notes[i].fake ;
					if (Pm.actMode != 1) {
						Pm.sb0->mmdLookAt = balls[curVisBallId]->rb->getPosition();
						Pm.sb0->lookAt_mmdLookAt = true;  
					}
					if (oft>-0.034f && flyPo) {
						auto osb = flyPo->pm.sb;
						if (osb && osb->hitDisabled) {
							osb->hitDisabled = false;
							for (auto& rb : osb->Pmx->getRigidBodys(0)) {
								rb->setCollideFilterMask(0, 0,1);
							}
						}
					}
				}
				//if (Pm.actMode==1)
				//Pm.sb0->phyLookHeadMul = sb->lookAt_mmdLookAt && oft ? 0.5f : 0.25f;
				powerMorph = glm::mix(powerMorph, std::min(1.f,curPower+0.5f), sb->lookAt_mmdLookAt? 0.05f:0.01f);
			}
			else if (Pm.subAct == 0 && oft > durT * 2.0f) {
				sb->lookAt_mmdLookAt = false;	
				Pm.sb0->phyLookHeadMul = 1;
				powerMorph = glm::mix(powerMorph, 0.5f, 0.001f);
				if (sb->mpWa && sb->mpWa->GetWeight() < 0.01f && powerMorph<0.51f) sb->mpWa->appendMorph(1, 0.5f, 10.f);
			}
			
#if 0
			if (movRat < 0 && sb->getPlaying()) sb->resetAnimation(0, 0, 0);
			if (movRat >= 0.f && !sb->getPlaying() && (sb->getCurTime() > durT / 2 || !sb->getPlaying())) {		//	sb->setAllDynRbActive(0); 	
				sb->setPlaying(true); sb->animCycleStart = -1;
			}
	
			auto& pp = cbs[i].pp;
			int ptid = pp.ptE;// glm::mix(pp.ptB, pp.ptE, 0.5f);
			sb->Pmx->rootTr = tfm[3]; //pathpts[ptid];  /

			sb->Pmx->rootRt = //glh::directionToRotation(pathpts[ptid - 1] - pathpts[ptid ],glm::fastNormalize(glm::slerp(quat(cbs[i].boxTfm),quat(cbs[i-1].boxTfm),0.5f)*vec3(0,1,0)));
				quat(tfm);// glm::slerp(quat(tfm), quat(tfml), 0.f);
			sb->Pmx->rt1Tr = { 0,0 ? Pm.brickSize.y / 2 : 0,0 && movRat < 0.25 ? (1 - movRat * 4) * 6.f : 0 }; sb->Pmx->rt1Rt = { 0,0,0 };
			sb->ndRoot->SetAnimationTranslate({});
			sb->Pmx->UpdateNodeAnimation(false);
			sb->Pmx->UpdatePhysics(0);
			sb->Pmx->UpdateNodeAnimation(1);

			Pm.sb0->mmdFw(2, "pt", sb->Pmx->rootTr, {}, 0xFFC0C0FF);
#endif
		}
		auto trm = cbs[i].rb ? cbs[i].rb->GetTransform() : mat4(tfm);
 
	}
 
	if(Pm.actMode!=1)
	for (int si = 1; si < mmd->players.size(); si++) if (flag[si] == 0)
	{
		if (sbLastActBallId[si] == 0) continue;
		int ni = sbLastActBallId[si] + 1;
		int cc = 0;
		while (nidToSbidNstar(mmd->players.size() - 1, ni) + 1 != si && cc < mmd->players.size()) ni++, cc++;
		if (ni >= (int)notes.size()) continue;
		float spd = std::min(100.f, glm::length(cbs[ni].ballVel) * 1.f);
		float durT = 2.f;//glm::clamp(10.f/spd,0.1f,2.f); //
		auto oft = -durT / 2;
		float movRat = clamp(oft / durT + 0.5f, 0.f, 1.f);
		float a = glm::clamp(spd * 0.2f, 2.f, 6.f); //10 m/s
		float movDis = 1;
		float ofsy = -movDis * 30.f / DIV_A;
		mat4 ofsM = glm::translate(mat4(1), { 0, ofsy, 0 });
		mat4 tfm = mmdMat * cbs[ni].boxTfm * ofsM; //mat4 tfml = mmdMat * cbs[ni > 0 ? ni - 1 : 0].boxTfm * ofsM;
		sbPhyAction(ni, tfm, 1.f, spd, a, mmd->players[si], false);
	}
}

void CMidiPlateSceneNode::getMovParams(float& spd, int i, float& oft, float time, float& durT, float& movRat, float& a, float& acting, float& movDis, float movMul, glm::mat4& tfm)
{
	spd = std::min(100.f, glm::length(cbs[i].ballVel) * 1.f);
	oft = (time - notes[i].time);
	durT = 2.f;//glm::clamp(10.f/spd,0.1f,2.f); //

	movRat = clamp(oft / durT + 0.5f, 0.f, 1.f);
	a = glm::clamp(spd * 0.2f, 2.f, 6.f); //10 m/s

	acting = abs(movRat - 0.5f) < 1 / a;
	movDis = //1 + (moving ? (cos(oft * core::PIx2 / durT) - 1) : -2) / 2;
		acting ? abs(a * (movRat - 0.5f)) : 1;
	//PLATE_MOV_OFSY

	float ofsy = -movDis * movMul / DIV_A;
	;// *std::min(2.f, pow(notes[i + 1].time - notes[i].time, 1.0f)) * 1.f;
	mat4 ofsM = glm::translate(mat4(1), { 0, ofsy, 0 });
	tfm = mmdMat* cbs[i].boxTfm * ofsM; mat4 tfml = cbs[i > 0 ? i - 1 : 0].boxTfm * ofsM;
}

std::vector<CMidiPlateSceneNode::PlateParams> CMidiPlateSceneNode::calculate_plate_params(float depth, float side_len, 
	float ball_radius,float ballStartOffsetY, float linearDamping, int mode, glm::vec3& initPos, glm::vec3 g, 
	CMidiPlateSceneNode::Param pm) {
	std::vector<PlateParams> plates;
	pathpts.clear();

	const float minAngle = glm::radians(30.0f);
	const float maxAngle = glm::radians(80.0f);

	bool fixY = plate_fix_yofs > 0.1f;
	// 小球初始位置
	glm::vec3 ballPos(0, fixY ? plate_fix_yofs + -g.y / 2 : 0, 0);// +vec3(0.0f, ballStartOffsetY, 0.0f);
	initPos = ballPos;
	pathpts.push_back(ballPos);
	// 小球初始速度（向下）
	glm::vec3 ballVel(initBallVel);

	// 第一块钢板（固定角度）
	float first_angle = minAngle;  // 第一块向右倾斜
	glm::quat first_rotation = glm::angleAxis(first_angle, glm::vec3(0.0f, 0.0f, -1.0f));

	// 计算第一块钢板的位置
	glm::vec3 plateNormal = first_rotation * glm::vec3(0, 1, 0);
	
	int N = curAngleN = nbp.angleN;// Pm.actMode == 1 ? Pm.angleN : mmd->players.size() - 1;
	rttDegree = 360.f / N;//
	int stepsPerSecond = 60 * SABA_PHYSICS_FRAMESTEP;
	static int incDeg = 0; incDeg += incDegAdd;
	float angY = glm::radians(rttDegree + incDeg);
	minY = 10000;
	float cy = 0.f;

	for (int ni = 0; ni <= notes.size(); ++ni) {
#if YUNQIU
		int i = ni / 2;
#else
		int i = ni;
#endif
		DP(("Pl %d", i));
		float plateOffset = depth / 2 * (MEC_THICKNESS && i < notes.size() ? notes[i].mec : 1) + ball_radius;
		PlateParams pp;
		float fall_time = i == 0 ? 1.0f : i < notes.size() ? notes[i].time - notes[i - 1].time : 1.0f;

		int totalSteps = static_cast<int>(std::ceil(fall_time * stepsPerSecond));
		float dt = fall_time / totalSteps;
		glm::vec3 velocity = ballVel;
		glm::vec3 fall_vector(0.0f), outVel(0);
		pp.ptB = pathpts.size();
		for (int i = 0; i < totalSteps; ++i) {
			// 计算当前步的位移
			glm::vec3 stepDisplacement = velocity * dt;
			// 更新总位移
			fall_vector += stepDisplacement;
			pathpts.push_back(fall_vector);
			// 计算阻尼力
			glm::vec3 dampingForce = -linearDamping * velocity;
			// 更新速度
			velocity += (g + dampingForce) * dt;
		}
		pp.ptE = pathpts.size();

		glm::vec3 fall_vector2 = 0.5f * g * fall_time * fall_time + ballVel * fall_time;
		ballVel = velocity;
		vec3 curBallPos = ballPos + fall_vector;
		for (int pi = pp.ptB; pi < pp.ptE; ++pi) {
			pathpts[pi] += ballPos;
		}
		// mode =  (fall_time > 0.25);
		if (mode == 0)
		{
			float vlen = glm::length(ballVel);  //vlen *= 0.96f;
			//if (current_pos.y < 0)                    vlen *= 1.1f;
			vec3 velXZ = ballVel; velXZ.y = 0;
			if (glm::length(velXZ) < 0.0001f)
				outVel = vec3(vlen, 0, 0);
			else {
				vec3 dir = glm::fastNormalize(velXZ);

				dir = glm::rotate(glm::mat4(1.0f), glm::radians(rttDegree + incDeg) , glm::vec3(0, 1, 0)) * vec4(dir, 1);
#if 1
				float adY = glm::clamp(-fall_vector.y / 4.f - 1, 0.f, 1.f);
				outVel = glm::fastNormalize(dir + glm::vec3(0, 0.f + adY, 0)) * vlen;
#else
				float adY = glm::clamp(-fall_vector.y / 1.f, 0.f, 100.f);
				outVel = glm::fastNormalize(dir + glm::vec3(0, 0.f + adY, 0)) * vlen;
#endif
			}
		}
		else {

			float a = glm::radians(rttDegree) * (i + 1);
			float r = pm.circleR ;
			auto t = i < notes.size() - 1 ? notes[i + 1].time - notes[i].time : 0.5f;
			float ty = fixY ? plate_fix_yofs : ballPos.y + std::min(1.f, (t * t * 2 - 2)) * side_len * plateYStepMul;
			vec3 tgt;
			glh::calcSpeedDirP2PinTimeGuess_Param pm = {
				.startPosition = curBallPos,
				//.targetPosition = tgt,
				.velLen = keepVelLen && i > 0 ? glm::length(ballVel) * hitVelMul : 0.f,
				.ignoreVelLenLimit = 10.f,
				//.tgtMovDir = adjDir * 0.5f,
				//.tgtMoveLimit = adjLimit,
				.gravity = g,
				.time = t,
				.linearDamping = linearDamping,
				.stepsPerSecond = stepsPerSecond,
				.maxErrorMag = fixY ? 30.f : 1000.f,
				.maxGuess = 32
			};
			switch (mode)
			{
			default:
				a = nidToSbidNstar(N,i+1)*2*core::PI/(N);
				tgt = vec3(cos(a) * r, ty, sin(a) * r); 
#if YUNQIU
				if (ni % 2) tgt = { 0,0,0 };		else pm.startPosition = vec3(0);
#endif
				break;
			case 2:
				a = glm::radians(rttDegree) * (i + 1)+core::HALF_PI;
				tgt = vec3(cos(a) * r/2, ty + sin(a) * r/2, 0); 
				pm.velLen = 0.f; 
				break;
			}

			//CPU_COUNT_B(p2pspd);
#if 1
			auto adjDir = vec3(0, -1, 0); float adjLimit = 100.f;
			if (fixY) {
				tgt.y += i < notes.size() - 2 && notes[i+1].fake ? 20 : 0;
				vec3 dir = -tgt; dir.y = 0; adjDir = glm::fastNormalize(dir); adjLimit = glm::length(dir);
			}
			pm.targetPosition = tgt;
			pm.tgtMovDir = adjDir * 0.5f;
			pm.tgtMoveLimit = adjLimit;

			outVel = calcSpeedDirP2PinTimeGuess(pm);

#else
			outVel = glh::calcVelocityP2PinTimeGuess(ballPos + fall_vector, tgt,
				g, t, linearDamping, stepsPerSecond, 32);
#endif
			//CPU_COUNT_E(p2pspd); 
		}



		plateNormal = glm::fastNormalize(glm::fastNormalize(outVel) - glm::fastNormalize(ballVel));
		if (mode == 0) outVel *= hitVelMul;
		//glh::vecLimitLengthMinMax(outVel, 20,50);
		// Calculate rotation from the plateNormal
		glm::quat rotation = glm::rotation(glm::vec3(0, 1, 0), plateNormal);
		vec3 lastPos = ballPos;
		ballPos += fall_vector;

		ballVel = outVel;
		vec3 platePos = ballPos - plateNormal * plateOffset;


		// 添加钢板
		if (platePos.y < minY) minY = platePos.y;
		pp.boxPos = platePos;
		pp.rotation = rotation;
		pp.ball_position = ballPos;
		pp.ball_velocity = ballVel;
		plates.push_back(pp);


	}

	minY -= side_len;

	if (fixY) minY = 0;// (minY < 0) ? minY : 0;

	for (auto& p : plates) { p.boxPos.y -= minY; p.ball_position.y -= minY; };

	initPos.y -= minY;
	return plates;
}

int CMidiPlateSceneNode::nidToSbid(int nid) { return  nid % (mmd->players.size() - 1) + 1; }


std::vector<int> drawStar(int N, int skip = 2) {
	std::vector<int> order;
	int current = 0;
	std::vector<bool> visited(N, false);

	// Starting at vertex 0, we find the sequence of connections
	while (!visited[current]) {
		order.push_back(current);
		visited[current] = true;
		current = (current + skip) % N; // Move to the next vertex by skipping
	}

	return order;
}

int CMidiPlateSceneNode::nidToSbidNstar(int n , int id)
{
 
	int si = 0; int add = Pm.actMode==2?1: n / 2;
 
	for (int i = 0; i < id; i++)
		si += add;
	
	return si%n;
	
}

