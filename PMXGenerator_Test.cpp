//
// PMXGenerator_Test.cpp - Test program for PMXFile_Generator
//

#include <Saba/Model/MMD/PMXFile_Generator.h>
#include <iostream>
#include <vector>
#include <string>
#include <glm/glm.hpp>
#include <glm/gtc/constants.hpp>

using namespace saba;
using namespace glm;

// Create a simple box model
void createBoxModel()
{
    std::cout << "Creating box model..." << std::endl;

    PMXFile_Generator generator;

    // Define a box
    BoxShape box;
    box.size = vec3(2.0f, 1.0f, 3.0f);
    box.position = vec3(0.0f, 1.0f, 0.0f);
    box.rotation = vec3(0.0f, 0.0f, 0.0f);

    // Create the box model
    generator.createBox(box);

    // Save to file
    if (generator.saveToFile("box_model.pmx")) {
        std::cout << "Box model saved to box_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save box model" << std::endl;
    }
}

// Create a chain model
void createChainModel()
{
    std::cout << "Creating chain model..." << std::endl;

    PMXFile_Generator generator;

    // Define chain links
    std::vector<ChainLink> links;

    // Create 5 links in a vertical chain
    for (int i = 0; i < 5; i++) {
        ChainLink link;
        link.radius = 0.5f;
        link.length = 1.0f;
        // Increase spacing between links
        link.position = vec3(0.0f, 5.0f - i * 2.0f, 0.0f);
        // Alternate the orientation of links for a more realistic chain
        if (i % 2 == 0) {
            link.rotation = vec3(0.0f, 0.0f, 0.0f);
        } else {
            link.rotation = vec3(0.0f, glm::half_pi<float>(), 0.0f);
        }
        links.push_back(link);
    }

    // Create the chain model
    generator.createChain(links);

    // Save to file
    if (generator.saveToFile("chain_model.pmx")) {
        std::cout << "Chain model saved to chain_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save chain model" << std::endl;
    }
}

// Create a cube model with 6 box faces
void createCubeModel()
{
    std::cout << "Creating cube model..." << std::endl;

    PMXFile_Generator generator;

    // Define boxes for each face of the cube
    std::vector<BoxShape> boxes;
    float size = 5.0f;
    float thickness = 0.2f;

    // Front face
    BoxShape front;
    front.size = vec3(size, size, thickness);
    front.position = vec3(0.0f, 0.0f, size/2);
    boxes.push_back(front);

    // Back face
    BoxShape back;
    back.size = vec3(size, size, thickness);
    back.position = vec3(0.0f, 0.0f, -size/2);
    boxes.push_back(back);

    // Left face
    BoxShape left;
    left.size = vec3(thickness, size, size);
    left.position = vec3(-size/2, 0.0f, 0.0f);
    boxes.push_back(left);

    // Right face
    BoxShape right;
    right.size = vec3(thickness, size, size);
    right.position = vec3(size/2, 0.0f, 0.0f);
    boxes.push_back(right);

    // Top face
    BoxShape top;
    top.size = vec3(size, thickness, size);
    top.position = vec3(0.0f, size/2, 0.0f);
    boxes.push_back(top);

    // Bottom face
    BoxShape bottom;
    bottom.size = vec3(size, thickness, size);
    bottom.position = vec3(0.0f, -size/2, 0.0f);
    boxes.push_back(bottom);

    // Create the multi-box model
    generator.createMultiBox(boxes);

    // Save to file
    if (generator.saveToFile("cube_model.pmx")) {
        std::cout << "Cube model saved to cube_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save cube model" << std::endl;
    }
}

// Create a balance scale model with physics
void createBalanceModel()
{
    std::cout << "Creating balance model..." << std::endl;
    
    PMXFile_Generator generator;
    generator.initialize("BalanceModel", "A balance scale model with physics");
    
    // Parameters for the balance
    const float baseWidth = 5.0f;
    const float baseHeight = 1.0f;
    const float baseDepth = 5.0f;
    
    const float poleHeight = 12.0f;
    const float poleRadius = 0.3f;
    
    const float beamLength = 10.0f;
    const float beamHeight = 0.4f;
    const float beamWidth = 0.4f;
    
    const float panRadius = 2.0f;
    const float panDepth = 0.5f;
    const float chainLength = 3.0f;
    
    // Create individual components using the public methods available
    
    // Step 1: Create the base (static)
    {
        Gpm_BoxShape base;
        base.size = vec3(baseWidth, baseHeight, baseDepth);
        base.position = vec3(0.0f, baseHeight/2.0f, 0.0f);
        base.rotation = vec3(0.0f, 0.0f, 0.0f);
        
        // Add a bone for the base
        int32_t baseBoneIndex = generator.addBone("Base", base.position, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the base
        RigidBodyDef baseRbDef;
        baseRbDef.name = "BaseBody";
        baseRbDef.engName = "BaseBody";
        baseRbDef.shape = PMXRigidbody::Shape::Box;
        baseRbDef.size = base.size;
        baseRbDef.position = base.position;
        baseRbDef.boneIndex = baseBoneIndex;
        baseRbDef.mass = 0.0f; // Static (immovable)
        baseRbDef.operation = PMXRigidbody::Operation::Static;
        int32_t baseRbIndex = generator.addRigidBody(baseRbDef);
        
        // Create a box for the base (this will create the mesh)
        generator.createBox(base);
    }
    
    // Step 2: Create the vertical pole
    vec3 polePosition = vec3(0.0f, baseHeight + poleHeight/2.0f, 0.0f);
    int32_t poleRbIndex;
    {
        // Create a bone for the pole
        int32_t poleBoneIndex = generator.addBone("Pole", polePosition, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the pole
        RigidBodyDef poleRbDef;
        poleRbDef.name = "PoleBody";
        poleRbDef.engName = "PoleBody";
        poleRbDef.shape = PMXRigidbody::Shape::Capsule;
        poleRbDef.size = vec3(poleRadius, poleHeight/2.0f, 0.0f);
        poleRbDef.position = polePosition;
        poleRbDef.boneIndex = poleBoneIndex;
        poleRbDef.mass = 0.0f; // Static
        poleRbDef.operation = PMXRigidbody::Operation::Static;
        poleRbIndex = generator.addRigidBody(poleRbDef);
        
        // Create the mesh for the pole using a chain link
        Gpm_ChainLink pole;
        pole.radius = poleRadius;
        pole.length = poleHeight;
        pole.position = polePosition;
        pole.rotation = vec3(0.0f, 0.0f, 0.0f);
        
        std::vector<Gpm_ChainLink> poleLinks = {pole};
        generator.createChain(poleLinks, false);
    }
    
    // Step 3: Create the balance beam (dynamic, pivots at the top of the pole)
    vec3 beamPosition = vec3(0.0f, baseHeight + poleHeight, 0.0f);
    int32_t beamBoneIndex, beamRbIndex;
    {
        Gpm_BoxShape beam;
        beam.size = vec3(beamLength, beamHeight, beamWidth);
        beam.position = beamPosition;
        beam.rotation = vec3(0.0f, 0.0f, 0.0f);
        
        // Create a bone for the beam
        beamBoneIndex = generator.addBone("Beam", beamPosition, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the beam
        RigidBodyDef beamRbDef;
        beamRbDef.name = "BeamBody";
        beamRbDef.engName = "BeamBody";
        beamRbDef.shape = PMXRigidbody::Shape::Box;
        beamRbDef.size = beam.size;
        beamRbDef.position = beam.position;
        beamRbDef.boneIndex = beamBoneIndex;
        beamRbDef.mass = 2.0f; // Light but has mass
        beamRbDef.linearDamping = 0.1f; // Low damping to allow swinging
        beamRbDef.angularDamping = 0.1f;
        beamRbDef.operation = PMXRigidbody::Operation::Dynamic;
        beamRbIndex = generator.addRigidBody(beamRbDef);
        
        // Create a box for the beam (this will create the mesh)
        generator.createBox(beam);
    }
    
    // Create a hinge joint at the top of the pole to allow the beam to pivot
    {
        JointDef beamJointDef;
        beamJointDef.name = "BeamPivot";
        beamJointDef.type = PMXJoint::JointType::Hinge;
        beamJointDef.rigidBodyAIndex = beamRbIndex; // Beam
        beamJointDef.rigidBodyBIndex = poleRbIndex; // Pole rigid body
        beamJointDef.position = beamPosition;
        beamJointDef.rotation = vec3(0.0f, 0.0f, glm::half_pi<float>()); // Rotate to make pivot around X axis
        
        // Set rotation limits and spring properties
        beamJointDef.angularLimitMin = vec3(-glm::quarter_pi<float>(), 0.0f, 0.0f); // Limit tilt
        beamJointDef.angularLimitMax = vec3(glm::quarter_pi<float>(), 0.0f, 0.0f);
        beamJointDef.linearLimitMin = vec3(0.0f);
        beamJointDef.linearLimitMax = vec3(0.0f);
        beamJointDef.springAngular = vec3(5.0f, 0.0f, 0.0f); // Small restoring force
        
        generator.addJoint(beamJointDef);
    }
    
    // Step 4: Create the pans on either side
    // Left pan
    vec3 leftPanPosition = vec3(-beamLength/2.0f, baseHeight + poleHeight - chainLength - panDepth/2.0f, 0.0f);
    int32_t leftPanBoneIndex, leftPanRbIndex;
    {
        // Create a bone for the left pan
        leftPanBoneIndex = generator.addBone("LeftPan", leftPanPosition, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the left pan
        RigidBodyDef leftPanRbDef;
        leftPanRbDef.name = "LeftPanBody";
        leftPanRbDef.engName = "LeftPanBody";
        leftPanRbDef.shape = PMXRigidbody::Shape::Box; // Using box instead of cylinder for simplicity
        leftPanRbDef.size = vec3(panRadius, panDepth, panRadius);
        leftPanRbDef.position = leftPanPosition;
        leftPanRbDef.boneIndex = leftPanBoneIndex;
        leftPanRbDef.mass = 1.0f;
        leftPanRbDef.linearDamping = 0.3f;
        leftPanRbDef.angularDamping = 0.3f;
        leftPanRbDef.operation = PMXRigidbody::Operation::Dynamic;
        leftPanRbIndex = generator.addRigidBody(leftPanRbDef);
        
        // Create a box for the left pan
        Gpm_BoxShape leftPan;
        leftPan.size = vec3(panRadius*2.0f, panDepth, panRadius*2.0f);
        leftPan.position = leftPanPosition;
        leftPan.rotation = vec3(0.0f, 0.0f, 0.0f);
        generator.createBox(leftPan);
    }
    
    // Right pan
    vec3 rightPanPosition = vec3(beamLength/2.0f, baseHeight + poleHeight - chainLength - panDepth/2.0f, 0.0f);
    int32_t rightPanBoneIndex, rightPanRbIndex;
    {
        // Create a bone for the right pan
        rightPanBoneIndex = generator.addBone("RightPan", rightPanPosition, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the right pan
        RigidBodyDef rightPanRbDef;
        rightPanRbDef.name = "RightPanBody";
        rightPanRbDef.engName = "RightPanBody";
        rightPanRbDef.shape = PMXRigidbody::Shape::Box;
        rightPanRbDef.size = vec3(panRadius, panDepth, panRadius);
        rightPanRbDef.position = rightPanPosition;
        rightPanRbDef.boneIndex = rightPanBoneIndex;
        rightPanRbDef.mass = 1.0f;
        rightPanRbDef.linearDamping = 0.3f;
        rightPanRbDef.angularDamping = 0.3f;
        rightPanRbDef.operation = PMXRigidbody::Operation::Dynamic;
        rightPanRbIndex = generator.addRigidBody(rightPanRbDef);
        
        // Create a box for the right pan
        Gpm_BoxShape rightPan;
        rightPan.size = vec3(panRadius*2.0f, panDepth, panRadius*2.0f);
        rightPan.position = rightPanPosition;
        rightPan.rotation = vec3(0.0f, 0.0f, 0.0f);
        generator.createBox(rightPan);
    }
    
    // Step 5: Create chains that connect the pans to the beam
    // Left chain
    {
        vec3 leftChainTop = vec3(-beamLength/2.0f, baseHeight + poleHeight, 0.0f);
        
        // Create a joint to connect the left pan to the beam
        JointDef leftChainJointDef;
        leftChainJointDef.name = "LeftChainJoint";
        leftChainJointDef.type = PMXJoint::JointType::SpringDOF6;
        leftChainJointDef.rigidBodyAIndex = leftPanRbIndex;
        leftChainJointDef.rigidBodyBIndex = beamRbIndex;
        leftChainJointDef.position = leftPanPosition; // Attach at pan center
        
        // Allow limited movement in all directions
        leftChainJointDef.linearLimitMin = vec3(-0.5f, -0.1f, -0.5f);
        leftChainJointDef.linearLimitMax = vec3(0.5f, 0.1f, 0.5f);
        leftChainJointDef.angularLimitMin = vec3(-glm::quarter_pi<float>());
        leftChainJointDef.angularLimitMax = vec3(glm::quarter_pi<float>());
        
        // Spring forces to simulate chain tension
        leftChainJointDef.springLinear = vec3(50.0f, 100.0f, 50.0f);
        leftChainJointDef.springAngular = vec3(10.0f);
        
        generator.addJoint(leftChainJointDef);
        
        // Visualize the chain with links
        std::vector<Gpm_ChainLink> chainLinks;
        const int numLinks = 5;
        float linkLength = chainLength / numLinks;
        
        for (int i = 0; i < numLinks; i++) {
            Gpm_ChainLink link;
            link.radius = 0.1f;
            link.length = linkLength;
            float t = (float)(i + 0.5) / numLinks; // Position along chain (0-1)
            link.position = vec3(
                leftChainTop.x, 
                leftChainTop.y - t * chainLength,
                leftChainTop.z
            );
            link.rotation = vec3(glm::half_pi<float>(), 0.0f, 0.0f); // Orient vertically
            chainLinks.push_back(link);
        }
        
        // Create chain meshes using the createChain method
        generator.createChain(chainLinks, false);
    }
    
    // Right chain
    {
        vec3 rightChainTop = vec3(beamLength/2.0f, baseHeight + poleHeight, 0.0f);
        
        // Create a joint to connect the right pan to the beam
        JointDef rightChainJointDef;
        rightChainJointDef.name = "RightChainJoint";
        rightChainJointDef.type = PMXJoint::JointType::SpringDOF6;
        rightChainJointDef.rigidBodyAIndex = rightPanRbIndex;
        rightChainJointDef.rigidBodyBIndex = beamRbIndex;
        rightChainJointDef.position = rightPanPosition; // Attach at pan center
        
        // Allow limited movement in all directions
        rightChainJointDef.linearLimitMin = vec3(-0.5f, -0.1f, -0.5f);
        rightChainJointDef.linearLimitMax = vec3(0.5f, 0.1f, 0.5f);
        rightChainJointDef.angularLimitMin = vec3(-glm::quarter_pi<float>());
        rightChainJointDef.angularLimitMax = vec3(glm::quarter_pi<float>());
        
        // Spring forces to simulate chain tension
        rightChainJointDef.springLinear = vec3(50.0f, 100.0f, 50.0f);
        rightChainJointDef.springAngular = vec3(10.0f);
        
        generator.addJoint(rightChainJointDef);
        
        // Visualize the chain with links
        std::vector<Gpm_ChainLink> chainLinks;
        const int numLinks = 5;
        float linkLength = chainLength / numLinks;
        
        for (int i = 0; i < numLinks; i++) {
            Gpm_ChainLink link;
            link.radius = 0.1f;
            link.length = linkLength;
            float t = (float)(i + 0.5) / numLinks; // Position along chain (0-1)
            link.position = vec3(
                rightChainTop.x, 
                rightChainTop.y - t * chainLength,
                rightChainTop.z
            );
            link.rotation = vec3(glm::half_pi<float>(), 0.0f, 0.0f); // Orient vertically
            chainLinks.push_back(link);
        }
        
        // Create chain meshes using the createChain method
        generator.createChain(chainLinks, false);
    }
    
    // Save to file
    if (generator.saveToFile("balance_model.pmx")) {
        std::cout << "Balance model saved to balance_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save balance model" << std::endl;
    }
}

int main()
{
    try {
        // Create example models
        //createBoxModel();
        //createChainModel();
        //createCubeModel();
        createBalanceModel(); // Create the balance scale with physics

        std::cout << "All models created successfully!" << std::endl;
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
