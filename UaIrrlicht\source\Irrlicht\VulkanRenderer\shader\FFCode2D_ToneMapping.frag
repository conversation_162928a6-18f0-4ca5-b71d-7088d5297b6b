#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;
//precision mediump float;

#define ADD_LAST_FRAME 0

#if ADD_LAST_FRAME
layout (binding = 2)  uniform sampler2D g_tex0_sampler;
#endif
layout (binding = 0) uniform UBO 
{


    float gamma;
	float exposure;
	//float lastFrameRatio, pad3;
    int bit,pad3;
	vec4 pad[31 - 1];

	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w
} ubo;



layout (input_attachment_index = 0, binding = 1) uniform subpassInput samplerposition;


	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;




//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;

// adding pixel shader
void main()
{
#if ADD_LAST_FRAME
    
	vec4 col=//texture(g_tex0_sampler,i_tex0);
    subpassLoad(samplerposition);

    vec3 mapped = vec3(1.0) - exp(-col.rgb * ubo.exposure);

   vec4 oc = vec4(pow(mapped, vec3(1.0 / ubo.gamma)),col.a);



    vec4 col1= texture(g_tex0_sampler,i_tex0)*ubo.lastFrameRatio;

    outFragColor = col1*(1-oc.a)+ oc;

#else // NORMAL

	vec4 col=//texture(g_tex0_sampler,i_tex0);
    subpassLoad(samplerposition);
#if 0
	outFragColor =   vec4(col.rgb/(col.rgb+1),col.a); 
#elif 0

    const float a = 2.51f;
    const float b = 0.03f;
    const float c = 2.43f;
    const float d = 0.59f;
    const float e = 0.14f;
    outFragColor= vec4(clamp((col.rgb*(a*col.rgb+b))/(col.rgb*(c*col.rgb+d)+e),0,1),col.a);
#elif 0
    // 曝光色调映射
    vec3 mapped = vec3(1.0) - exp(-col.rgb );
    // Gamma校正 
    outFragColor = vec4(mapped,col.a);
#elif 1
    // 曝光色调映射
    vec3 mapped = vec3(1.0) - exp(-col.rgb * ubo.exposure);
    // Gamma校正 
    outFragColor = vec4(pow(mapped, vec3(1.0 / ubo.gamma)),col.a);
#elif 1
    // 曝光色调映射
    vec4 mapped = vec4(1.0) - exp(-col * ubo.exposure);
    // Gamma校正 
    outFragColor = pow(mapped, vec4(1.0 / ubo.gamma));
    //outFragColor.a = col.a;
    //float cmax= max(col.r,max(col.g,col.b));    float mul= 255.0/cmax;
    
    //outFragColor = (col.a>0)?vec4(outFragColor.rgb/col.a,col.a):vec4(0);
#else
	outFragColor = vec4(vec3(1.0)-exp(-col.rgb*0.5),col.a);

#endif

#endif


    
}