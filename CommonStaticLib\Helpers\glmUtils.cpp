#include "glmUtils.h"
#include "glmUtils.h"
#include "glmUtils.h"
#include "glmUtils.h"
#include "glmUtils.h"
#include <glm/vec3.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/mat4x4.hpp>
#include <glm/gtc/matrix_transform.hpp>

#include <glm/gtx/rotate_vector.hpp>
#include <glm/gtx/vector_angle.hpp>
 
namespace glh {

    glm::vec3 rotateTowards(glm::vec3 A, glm::vec3 B, float limitAngleDeg) {
        // Normalize vectors
        glm::vec3 normA = glm::normalize(A);
        glm::vec3 normB = glm::normalize(B);
        if (normA == normB)
            return normA;
        // Calculate the angle between vectors
        float angle = glm::acos(glm::dot(normA, normB));
        if (isnan(angle))
            return normA;
        // Check if the actual angle is greater than the limit
        float angleLimit = glm::radians(limitAngleDeg); // Ensure ANG is in radians
        if (angle > angleLimit) {
            angle = angleLimit;
        }

        // Calculate rotation axis
        glm::vec3 rotationAxis = glm::cross(normA, normB);
        if (glm::length(rotationAxis) > 0) // Check to avoid division by zero
            rotationAxis = glm::normalize(rotationAxis);
        else
            return A; // No rotation needed if vectors are parallel

        // Rotate vector A towards B by the limited angle
        glm::vec3 rotatedVector = glm::rotate(normA, angle, rotationAxis);
        assert(!_isnanf(rotatedVector.x));
        return rotatedVector * glm::length(A);
    }
    glm::vec3 rotateTowardsXZ(glm::vec3 A, glm::vec3 B, float limitAngleDeg) {
        // Take only X, Z components and create vec2 for A and B
        glm::vec2 A2d = glm::vec2(A.x, A.z);
        glm::vec2 B2d = glm::vec2(B.x, B.z);

        // Normalize vectors
        glm::vec2 normA2d = glm::normalize(A2d);
        glm::vec2 normB2d = glm::normalize(B2d);
        if (normA2d == normB2d)
            return A;
        // Calculate the angle between vectors
        float angle = glm::acos(glm::dot(normA2d, normB2d));
        if (isnan(angle))
            return A;
        // Check if the actual angle is greater than the limit
        float angleLimit = glm::radians(limitAngleDeg); // Ensure ANG is in radians
        if (angle > angleLimit) {
            angle = angleLimit;
        }

        // Calculate rotation axis in 2D
        float rotationDirection = glm::sign(glm::cross(glm::vec3(normA2d, 0), glm::vec3(normB2d, 0)).y);

        // Rotate vector A2d towards B2d by the limited angle
        glm::vec2 rotatedVector2d = glm::rotate(normA2d, rotationDirection * angle) * glm::length(A2d);

        // Merge back Y component to A and store the rotation result in X, Z
        glm::vec3 rotatedVector = glm::normalize(glm::vec3(rotatedVector2d.x, A.y, rotatedVector2d.y));

        return rotatedVector;
    }


    float angleRadBetweenVec(glm::vec3 A, glm::vec3 B) {
        A = glm::fastNormalize(A);
        B = glm::fastNormalize(B);
        return acos(glm::dot(A, B));
    }

    float angleDegBetweenVec(glm::vec3 A, glm::vec3 B)
    {
        return glm::degrees(angleRadBetweenVec(A, B));
    }

    float posInCameraViewAngleDeg(glm::mat4 camMat, glm::vec3 pos)
    {        
        glm::vec3 camPos = glm::vec3(camMat[3]);
        glm::vec3 camDir = glm::vec3(camMat[2]);  // LHS, if RHS then -camDir
        glm::vec3 dir = glm::fastNormalize(pos - camPos);
        float angle = acos(glm::dot(camDir, dir)); 
		angle = glm::degrees(angle);
        return angle;
    }

    glm::vec3 mirrorPoint(const glm::vec3& LinePt0, const glm::vec3& linePt1, const glm::vec3& point)
    {
        glm::vec3 dir = linePt1 - LinePt0;
        glm::vec3 l2p = point - LinePt0;
        // Projection of l2p onto dir
        float t = glm::dot(l2p, dir) / glm::dot(dir, dir);
        glm::vec3 projection = LinePt0 + t * dir;
        // Compute the mirror point
        glm::vec3 mirrorPoint = 2.0f * projection - point;
        return mirrorPoint;
    }
    glm::vec3 mirrorPointLimitDis(const glm::vec3& LinePt0, const glm::vec3& linePt1, const glm::vec3& point,float minDis,float maxDis)
    {
        glm::vec3 dir = linePt1 - LinePt0;
        glm::vec3 l2p = point - LinePt0;
        // Projection of l2p onto dir
        float t = glm::dot(l2p, dir) / glm::dot(dir, dir);
        glm::vec3 projection = LinePt0 + t * dir;
        // Compute the mirror point
        float dis0 = glm::length(projection - point);
        glm::vec3 mirrorPoint = projection + vecLimit(projection-point,minDis- dis0,maxDis- dis0);
        return mirrorPoint;
    }

    glm::mat4 buildLookAtMatrixLH(
        const glm::vec3& position,
        const glm::vec3& target,
        const glm::vec3& upVector)
    {
        glm::vec3 zaxis = glm::normalize(target - position);


        glm::vec3 xaxis = glm::normalize(glm::cross(upVector, zaxis));


        glm::vec3 yaxis = glm::cross(zaxis, xaxis);

        glm::mat4 M(1);
        M[0][0] = xaxis.x;
        M[0][1] = yaxis.x;
        M[0][2] = zaxis.x;
        M[0][3] = 0;

        M[1][0] = xaxis.y;
        M[1][1] = yaxis.y;
        M[1][2] = zaxis.y;
        M[1][3] = 0;

        M[2][0] = xaxis.z;
        M[2][1] = yaxis.z;
        M[2][2] = zaxis.z;
        M[2][3] = 0;

        M[3][0] = -glm::dot(xaxis, position);
        M[3][1] = -glm::dot(yaxis, position);
        M[3][2] = -glm::dot(zaxis, position);
        M[3][3] = 1;
#if defined ( USE_MATRIX_TEST )
        definitelyIdentityMatrix = false;
#endif
        return M;
    }



    glm::mat4 createViewMatrixPTR(const glm::vec3& position, const glm::vec3& target, const glm::vec3& right) {
        // Calculate the front vector
        glm::vec3 front = glm::normalize(target - position);

        // Calculate the up vector as the cross product of front and right vectors
        glm::vec3 up = glm::normalize(glm::cross(front, right));

        // Create the view matrix using glm::lookAt function
        return glm::lookAt(position, target, up);
    }

    glm::mat4 rttMatDeg(glm::vec3 rotation)
    {
        const double cx = cos(glm::radians(rotation.x));
        const double sx = sin(glm::radians(rotation.x));
        const double cy = cos(glm::radians(rotation.y));
        const double sy = sin(glm::radians(rotation.y));
        const double cz = cos(glm::radians(rotation.z));
        const double sz = sin(glm::radians(rotation.z));

        glm::mat4 M(1);
        M[0][0] = cy * cz + sx * sy * sz;
        M[0][1] = cx * sz;
        M[0][2] = -cz * sy + cy * sx * sz;

        M[1][0] = cz * sx * sy - cy * sz;
        M[1][1] = cx * cz;
        M[1][2] = cy * cz * sx + sy * sz;

        M[2][0] = cx * sy;
        M[2][1] = -sx;
        M[2][2] = cx * cy;
        return M;
    }
 
    float getYaw(const glm::quat& q) {
        return std::atan2(2.0f * (q.y * q.w + q.x * q.z), 1.0f - 2.0f * (q.y * q.y + q.x * q.x));
    }

    glm::quat keepRotationY(glm::quat q) {
        float yaw = std::atan2(2.0f * (q.y * q.w + q.x * q.z), 1.0f - 2.0f * (q.y * q.y + q.x * q.x));
        return glm::angleAxis(yaw, glm::vec3(0.0f, 1.0f, 0.0f));
    }
    glm::quat keepRotationYaddPI(glm::quat q) {
        float yaw = std::atan2(2.0f * (q.y * q.w + q.x * q.z), 1.0f - 2.0f * (q.y * q.y + q.x * q.x));
        return glm::angleAxis(yaw+pi(), glm::vec3(0.0f, 1.0f, 0.0f));
    }
    glm::quat fromEulerWithOrder(const glm::vec3& eulerAngles, const std::string& rotationOrder) {
        // Create individual rotation quaternions
        glm::quat rotX = glm::angleAxis(eulerAngles.x, glm::vec3(1, 0, 0));
        glm::quat rotY = glm::angleAxis(eulerAngles.y, glm::vec3(0, 1, 0));
        glm::quat rotZ = glm::angleAxis(eulerAngles.z, glm::vec3(0, 0, 1));

        // Combine rotations based on specified order
        if (rotationOrder == "XYZ") {
            return rotZ * rotY * rotX;
        }
        else if (rotationOrder == "XZY") {
            return rotY * rotZ * rotX;
        }
        else if (rotationOrder == "YXZ") {
            return rotZ * rotX * rotY;
        }
        else if (rotationOrder == "YZX") {
            return rotX * rotZ * rotY;
        }
        else if (rotationOrder == "ZXY") {
            return rotY * rotX * rotZ;
        }
        else if (rotationOrder == "ZYX") {
            return rotX * rotY * rotZ;
        }
        else {
            // Default to XYZ if invalid order specified
            return rotZ * rotY * rotX;
        }
    }
    glm::vec3 toEulerWithOrder(const glm::quat& q, const std::string& rotationOrder) {
        // Convert quaternion to rotation matrix
        glm::mat3 rotMatrix = glm::mat3_cast(q);

        glm::vec3 result;

        // Extract Euler angles based on rotation order
        if (rotationOrder == "XYZ") {
            result.y = asin(glm::clamp(rotMatrix[0][2], -1.0f, 1.0f));

            if (abs(rotMatrix[0][2]) < 0.99999f) {
                result.x = atan2(-rotMatrix[1][2], rotMatrix[2][2]);
                result.z = atan2(-rotMatrix[0][1], rotMatrix[0][0]);
            }
            else {
                // Gimbal lock case
                result.x = atan2(rotMatrix[2][1], rotMatrix[1][1]);
                result.z = 0;
            }
        }
        else if (rotationOrder == "ZYX") {
            // For ZYX order (common in aerospace)
            result.y = asin(-glm::clamp(rotMatrix[2][0], -1.0f, 1.0f));

            if (abs(rotMatrix[2][0]) < 0.99999f) {
                result.x = atan2(rotMatrix[2][1], rotMatrix[2][2]);
                result.z = atan2(rotMatrix[1][0], rotMatrix[0][0]);
            }
            else {
                // Gimbal lock case
                result.z = atan2(-rotMatrix[0][1], rotMatrix[1][1]);
                result.x = 0;
            }
        }
        else if (rotationOrder == "ZXY") {
            // For ZXY order
            result.x = asin(glm::clamp(rotMatrix[1][2], -1.0f, 1.0f));
            if (abs(rotMatrix[1][2]) < 0.99999f) {
                result.y = atan2(-rotMatrix[0][2], rotMatrix[2][2]);
                result.z = atan2(-rotMatrix[1][0], rotMatrix[1][1]);
            }
            else {
                // Gimbal lock case
                result.y = atan2(rotMatrix[0][1], rotMatrix[0][0]);
                result.z = 0;
            }
        }
        else if (rotationOrder == "XZY") {
            // For XZY order
            result.z = asin(-glm::clamp(rotMatrix[0][1], -1.0f, 1.0f));
            if (abs(rotMatrix[0][1]) < 0.99999f) {
                result.x = atan2(rotMatrix[2][1], rotMatrix[1][1]);
                result.y = atan2(rotMatrix[0][2], rotMatrix[0][0]);
            }
            else {
                // Gimbal lock case
                result.x = atan2(-rotMatrix[1][2], rotMatrix[2][2]);
                result.y = 0;
            }
        }
        else if (rotationOrder == "YXZ") {
            // For YXZ order
            result.x = asin(-glm::clamp(rotMatrix[2][1], -1.0f, 1.0f));
            if (abs(rotMatrix[2][1]) < 0.99999f) {
                result.y = atan2(rotMatrix[2][0], rotMatrix[2][2]);
                result.z = atan2(rotMatrix[0][1], rotMatrix[1][1]);
            }
            else {
                // Gimbal lock case
                result.y = atan2(-rotMatrix[0][2], rotMatrix[0][0]);
                result.z = 0;
            }
        }
        else if (rotationOrder == "YZX") {
            // For YZX order
            result.z = asin(glm::clamp(rotMatrix[1][0], -1.0f, 1.0f));
            if (abs(rotMatrix[1][0]) < 0.99999f) {
                result.x = atan2(-rotMatrix[1][2], rotMatrix[1][1]);
                result.y = atan2(-rotMatrix[2][0], rotMatrix[0][0]);
            }
            else {
                // Gimbal lock case
                result.x = 0;
                result.y = atan2(rotMatrix[2][1], rotMatrix[2][2]);
            }
        }
        else {
            // For other orders, fallback to GLM's default (which is typically XYZ)
            // Note: This doesn't respect the requested order and is just a fallback
            result = glm::eulerAngles(q);
        }

        return result;
    }
    // Function to interpolate between two transformation matrices
    glm::mat4 interpolateTransform(const glm::mat4& M1, const glm::mat4& M2, float ratio) {
        // Extract translations
        glm::vec3 translation1 = glm::vec3(M1[3]);
        glm::vec3 translation2 = glm::vec3(M2[3]);

        // Interpolate translations
        glm::vec3 translationInterpolated = glm::mix(translation1, translation2, ratio);

        // Extract rotations
        glm::quat rotation1 = glm::quat_cast(M1);
        glm::quat rotation2 = glm::quat_cast(M2);

        // Interpolate rotations using SLERP
        glm::quat rotationInterpolated = glm::slerp(rotation1, rotation2, ratio);

        // Recompose the final matrix
        glm::mat4 interpolatedMatrix = glm::mat4_cast(rotationInterpolated);
        interpolatedMatrix[3] = glm::vec4(translationInterpolated, 1.0f);

        return interpolatedMatrix;
    }
    void quaternionToAxisAngle(const glm::quat& q, glm::vec3& outAxis, float& outAngle) {
        // Ensure the quaternion is normalized
        glm::quat normalizedQ = glm::normalize(q);

        // The angle is derived from the quaternion's w component
        outAngle = 2.0f * acos(normalizedQ.w);

        // The axis is derived from the x, y, z components
        float sinHalfAngle = sqrt(1.0f - normalizedQ.w * normalizedQ.w);

        if (sinHalfAngle < glm::epsilon<float>()) {
            // If the angle is close to 0, any axis will work (e.g., (1, 0, 0))
            outAxis = glm::vec3(1.0f, 0.0f, 0.0f);
        }
        else {
            outAxis = glm::vec3(normalizedQ.x, normalizedQ.y, normalizedQ.z) / sinHalfAngle;
        }
    }
    void getTransformDeltaVel(const glm::mat4& M1, const glm::mat4& M2, float deltaTime, glm::vec3& outVel, glm::vec3& outAngVel) {
        // Extract translations
        glm::vec3 translation1 = glm::vec3(M1[3]);
        glm::vec3 translation2 = glm::vec3(M2[3]);

        // Calculate velocity delta
        outVel = (translation2 - translation1) / deltaTime;

        // Extract rotations
        glm::quat rotation1 = glm::quat_cast(M1);
        glm::quat rotation2 = glm::quat_cast(M2);

        // Calculate relative rotation
        glm::quat deltaRotation = rotation2 * glm::inverse(rotation1);

        // Convert relative rotation to axis-angle
        float angle;
        glm::vec3 axis;
        glm::axisAngle(glm::mat4_cast(deltaRotation), axis, angle);
#ifdef _DEBUGXX //which fast ?
        float angle1;
        glm::vec3 axis1;
        quaternionToAxisAngle(deltaRotation, axis1, angle1);
        assert(glm::all(glm::epsilonEqual(axis, axis1, glm::epsilon<float>())));
		assert(glm::epsilonEqual(angle, angle1, glm::epsilon<float>()));
 
#endif
        // Calculate angular velocity delta
        outAngVel = axis * (angle / deltaTime);
    }

#if 0
    bool calculateInitialVelocity(vec3& initialVelocity, const glm::vec3& startPos, const glm::vec3& targetPos, double theta, double G = -9.81 * GRAVITY_MUL) {
        // Calculate the direction vector from startPos to targetPos
        glm::vec3 direction = targetPos - startPos;

        // Calculate the horizontal distance
        double hDistance = std::sqrt(direction.x * direction.x + direction.z * direction.z);

        // Calculate the azimuth angle
        double azimuth = std::atan2(direction.z, direction.x);

        // Calculate the unit direction vector of the launch
        glm::vec3 launchDir(std::cos(azimuth) * std::cos(theta), std::sin(theta), std::sin(azimuth) * std::cos(theta));

        auto calculateInitialSpeed = [=](const glm::vec2& targetPos, double A, double G) {
            double tanA = std::tan(A);

            double t1 = tanA * targetPos.x;
            double t2 = 0.5 * G * (targetPos.x * targetPos.x);
            double t3 = targetPos.x * cos(A) * (targetPos.y - t1);

            double V0 = std::sqrt(t2 / t3);

            return V0;
            };
        float speed = calculateInitialSpeed(vec2(hDistance, direction.y), theta, G);
        if (isnan(speed))
            return false;
        initialVelocity = launchDir * speed * 10.f;

        return true;
    }


    class LSH_vec3 {
    public:
        LSH_vec3(int numHashTables, int hashSize)
            : numHashTables(numHashTables), hashSize(hashSize) {
            generateHashFunctions();
        }

        void addPoint(const glm::vec3& point, int index) {
            for (int i = 0; i < numHashTables; ++i) {
                std::string hash = hashPoint(i, point);
                hashTables[i][hash].emplace_back(point, index);
            }
        }

        int nearestNeighbor(const glm::vec3& point) const {
            int closestIndex = -1;
            float minDist = std::numeric_limits<float>::max();

            for (int i = 0; i < numHashTables; ++i) {
                std::string hash = hashPoint(i, point);
                auto it = hashTables[i].find(hash);
                if (it != hashTables[i].end()) {
                    for (const auto& candidate : it->second) {
                        float dist = glm::distance(point, candidate.first);
                        if (dist < minDist) {
                            minDist = dist;
                            closestIndex = candidate.second;
                        }
                    }
                }
            }
            return closestIndex;
        }

        void reset() {
            // Clear the hash tables and regenerate the hash functions
            for (auto& table : hashTables) {
                table.clear();
            }
            generateHashFunctions();
        }

        void resetPoints() {
            // Clear the hash tables without regenerating the hash functions
            for (auto& table : hashTables) {
                table.clear();
            }
        }

    private:
        int numHashTables;
        int hashSize;
        std::vector<std::vector<glm::vec3>> randomVectors;
        std::vector<std::unordered_map<std::string, std::vector<std::pair<glm::vec3, int>>>> hashTables;

        void generateHashFunctions() {
            srand(static_cast<unsigned>(time(0)));
            randomVectors.resize(numHashTables, std::vector<glm::vec3>(hashSize));

            for (int i = 0; i < numHashTables; ++i) {
                for (int j = 0; j < hashSize; ++j) {
                    randomVectors[i][j] = glm::vec3(static_cast<float>(rand()) / RAND_MAX,
                        static_cast<float>(rand()) / RAND_MAX,
                        static_cast<float>(rand()) / RAND_MAX);
                }
            }
            hashTables.resize(numHashTables);
        }

        std::string hashPoint(int tableIndex, const glm::vec3& point) const {
            std::string hash = "";
            for (const glm::vec3& randVec : randomVectors[tableIndex]) {
                hash += glm::dot(randVec, point) > 0 ? "1" : "0";
            }
            return hash;
        }
    };

#endif


    glm::vec3 calculateInitialPosition(const glm::vec3& direction, float initialSpeedLength,glm::vec3& initialVelocity, const glm::vec3& targetPosition,
        const glm::vec3& gravity, float time, float linearDamping,
        int stepsPerSecond) {
        glm::vec3 normalizedDirection = glm::fastNormalize(direction);
        initialVelocity = normalizedDirection * initialSpeedLength;
        int totalSteps = static_cast<int>(std::ceil(time * stepsPerSecond));
        float dt = time / totalSteps;
        glm::vec3 velocity = initialVelocity;
        glm::vec3 totalDisplacement(0.0f);
        for (int i = 0; i < totalSteps; ++i) {
            // 计算当前步的位移
            glm::vec3 stepDisplacement = velocity * dt;
            // 更新总位移
            totalDisplacement += stepDisplacement;
            // 计算阻尼力
            glm::vec3 dampingForce = -linearDamping * velocity;
            // 更新速度
            velocity += (gravity + dampingForce) * dt;
        }
        // 计算初始位置
        glm::vec3 initialPosition = targetPosition - totalDisplacement;

        return initialPosition;
    }
 

    glm::vec3 calcSpeedDirP2PinTimeGuess(const calcSpeedDirP2PinTimeGuess_Param& params) {
        bool keepVelLen = params.velLen > 0.0001f;
        glm::vec3 direction = glm::normalize(params.targetPosition - params.startPosition);
        glm::vec3 velocity = direction * (keepVelLen ? params.velLen : 1.f);
        float dt = 1.0f / params.stepsPerSecond;
        float flyTime=params.time;
        if (flyTime < 0.001f) {
            // Project vectors onto XZ plane
            glm::vec2 startXZ(params.startPosition.x, params.startPosition.z);
            glm::vec2 targetXZ(params.targetPosition.x, params.targetPosition.z);
            glm::vec2 initVelXZ(params.initVel.x, params.initVel.z);
            
            // Calculate distance in XZ plane
            float distanceXZ = glm::length(targetXZ - startXZ);
            
            if (glm::length(initVelXZ) > 0.001f) {
                // Use initial velocity if available
                flyTime = distanceXZ / glm::length(initVelXZ);
                
                // Account for damping effect
                if (params.linearDamping > 0.001f) {
                    // Adjust time for damping (approximate)
                    flyTime *= (1.0f + params.linearDamping);
                }
            } else {
                // No initial velocity - estimate based on distance and gravity
                float gravityXZ = glm::length(glm::vec2(params.gravity.x, params.gravity.z));
                if (gravityXZ > 0.001f) {
                    // Use projectile motion formula
                    flyTime = sqrt(2.0f * distanceXZ / gravityXZ);
                } else {
                    // Default fallback if no gravity in XZ plane
                    flyTime = distanceXZ / (params.velLen > 0.001f ? params.velLen : 1.0f);
                }
            }
            
            // Clamp to reasonable range
            flyTime = glm::clamp(flyTime, 0.1f, 10.0f);
        }
        int totalSteps = static_cast<int>(flyTime * params.stepsPerSecond);
        float errorMagnitude = 99999.f;
        float previousErrorMagnitude = errorMagnitude;
        float adaptiveStepSize = 1.0f;  // Initial step size
        float errorThreshold = 0.01f;  // Error reduction threshold
        glm::vec3 velocityLast = velocity;
        float minMagLast = 99999.f;
        glm::vec3 currentTargetPosition = params.targetPosition;
        float tgtlmt = params.tgtMoveLimit;
        int adc = 0;

        for (int guess = 0; guess < params.maxGuess; ++guess) {
            glm::vec3 currentPosition = params.startPosition;
            glm::vec3 currentVelocity = velocity;

            for (int step = 0; step < totalSteps; ++step) {
                // Update position
                currentPosition += currentVelocity * dt;
                // Update velocity
                currentVelocity += params.gravity * dt;
                currentVelocity *= std::pow(1.0f - params.linearDamping, dt);
            }

            // Calculate error
            glm::vec3 error = currentTargetPosition - currentPosition;
            errorMagnitude = glm::length(error);
            //DP(("adp %f error %f", adaptiveStepSize, errorMagnitude));

            // Check if close enough
            if (errorMagnitude < 0.01f) {
                DP(("currentTargetPosition len %f to len %f", glm::length(velocity), params.velLen));
                return velocity;
            }

            // Check if error significantly reduces
            if (std::abs(previousErrorMagnitude - errorMagnitude) < errorThreshold) {
                if (glm::length(currentPosition - params.targetPosition) > params.maxErrorMag) {
                    return calcVelocityP2PinTimeGuess(params.startPosition, params.targetPosition, params.gravity, flyTime, params.linearDamping, params.stepsPerSecond, params.maxGuess);
                }
                if (tgtlmt == 0.f) {
                    return calcVelocityP2PinTimeGuess(params.startPosition, currentTargetPosition, params.gravity, flyTime, params.linearDamping, params.stepsPerSecond, params.maxGuess);
                }

                if (errorMagnitude > minMagLast - 0.0001f ) {
                    DP(("glow ret len %f to len %f", glm::length(velocityLast), params.velLen));

                    if (abs(error.y) > params.ignoreVelLenLimit) {
                        velocity = calcVelocityP2PinTimeGuess(params.startPosition, params.targetPosition, params.gravity, flyTime, params.linearDamping, params.stepsPerSecond, params.maxGuess);
                    }
                    return velocity;
                }

                float dlen = glm::length(params.tgtMovDir) * errorMagnitude;
                if (tgtlmt > dlen)
                    tgtlmt -= dlen;
                else {
                    tgtlmt = 0.f;
                    errorMagnitude = tgtlmt/ glm::length(params.tgtMovDir);
                }
                minMagLast = errorMagnitude;
                // Move target position if error did not significantly decrease
                glm::vec3 mt1 = currentTargetPosition+ params.tgtMovDir * errorMagnitude ;
#if 0
                glm::vec3 mt2 = currentTargetPosition - params.tgtMovDir * errorMagnitude ;
                currentTargetPosition = (glm::length2(mt1- currentPosition)> glm::length2(mt2- currentPosition)) ? mt2 : mt1;
#else

                currentTargetPosition = mt1;

#endif
                adc++;
                guess = adc; adaptiveStepSize = 1.f;
                DP(("adjust %d tgt !!!!!!!!!!!!!!!!!!!!!!!!!!!!", adc, adaptiveStepSize, errorMagnitude));
                previousErrorMagnitude = errorMagnitude = 99999.f;
                continue;
            }

            previousErrorMagnitude = errorMagnitude;

            // Adjust velocity direction based on error while maintaining magnitude
            glm::vec3 adjustedDirection = glm::normalize(velocity + error * adaptiveStepSize);

            velocity = velocity + error * adaptiveStepSize / flyTime;
            if (keepVelLen)
                velocity = glm::normalize(velocity) * params.velLen;
            // Reduce step size over time
            adaptiveStepSize *= 0.9f;  // Gradually decrease
            adaptiveStepSize = std::max(adaptiveStepSize, 0.01f);  // Set minimum value
        }

        return velocity; // Return final velocity
    }
    glm::vec3 calcSpeedDirP2PinTimeGuess(const glm::vec3& startPosition, const glm::vec3& targetPosition, float velLen,
        glm::vec3 tgtMovDir, // if can not close, move target along tgtMovDir
        const glm::vec3& gravity, float time, float linearDamping, int stepsPerSecond, int maxGuess) {
        bool keepVelLen = velLen > 0.0001f;
        float flyTime= time;
        glm::vec3 direction = glm::normalize(targetPosition - startPosition);
        glm::vec3 velocity = direction * (keepVelLen ? velLen : 1.f);
        float dt = 1.0f / stepsPerSecond;
        int totalSteps = static_cast<int>(time * stepsPerSecond);
        float errorMagnitude = 99999.f;
        float previousErrorMagnitude = errorMagnitude;
        float adaptiveStepSize = 1.0f;  // 增大初始步长
        float errorThreshold = 0.01f;  // 误差减少阈值
        glm::vec3 velocityLast = velocity; float minMagLast = 99999.f;
        glm::vec3 currentTargetPosition = targetPosition;
        int adc = 0;
        for (int guess = 0; guess < maxGuess; ++guess) {
            glm::vec3 currentPosition = startPosition;
            glm::vec3 currentVelocity = velocity;

            for (int step = 0; step < totalSteps; ++step) {
                // 更新位置
                currentPosition += currentVelocity * dt;
                // 更新速度
                currentVelocity += gravity * dt;
                currentVelocity *= std::pow(1.0f - linearDamping, dt);
            }

            // 计算误差
            glm::vec3 error = currentTargetPosition - currentPosition;
            errorMagnitude = glm::length(error);
            //DP(("adp %f error %f", adaptiveStepSize, errorMagnitude));

            // 检查是否足够接近
            if (errorMagnitude < 0.01f) {
                DP(("currentTargetPosition len %f to len %f", glm::length(velocity), velLen));
                return velocity;
            }

            // 检查误差是否显著减少
            if (std::abs(previousErrorMagnitude - errorMagnitude) < errorThreshold) {
                if (errorMagnitude > minMagLast-0.0001f) {
                    DP(("glow ret len %f to len %f",glm::length(velocityLast), velLen));

                    if (abs(error.y) >10)
                    {
                        velocity=calcVelocityP2PinTimeGuess(startPosition, targetPosition, gravity, time, linearDamping, stepsPerSecond, maxGuess);
                       
                    }
                    return velocity;
                }
                //break;
                minMagLast = errorMagnitude;
                // 如果误差没有显著减少，移动目标位置
                currentTargetPosition += tgtMovDir * errorMagnitude *0.50f;
                //if (currentTargetPosition.y<0.f && startPosition.y>0.1f)	currentTargetPosition.y = 0.f;

                adc++;
                guess = adc; adaptiveStepSize = 1.f;
                DP(("adjust %d tgt !!!!!!!!!!!!!!!!!!!!!!!!!!!!", adc, adaptiveStepSize, errorMagnitude));
                previousErrorMagnitude = errorMagnitude = 99999.f;
                continue;
            }

            previousErrorMagnitude = errorMagnitude;

            // 基于误差调整速度方向，保持幅度不变
            glm::vec3 adjustedDirection = glm::normalize(velocity + error * adaptiveStepSize);
 
            velocity = velocity + error * adaptiveStepSize / time;
            if (keepVelLen)
            velocity = glm::normalize(velocity) * velLen;
            // 随时间减少步长
            adaptiveStepSize *= 0.9f;  // 逐渐减少
            adaptiveStepSize = std::max(adaptiveStepSize, 0.01f);  // 设置最小值
        }
       
       
        DP(("RET with error %f", errorMagnitude));
        // 如果在 maxGuess 次迭代内未找到解决方案，返回最佳猜测
        return velocity;
    }

    glm::vec3 calcVelocityP2PinTimeGuess(const glm::vec3& startPosition, const glm::vec3& targetPosition,
        const glm::vec3& gravity, float time, float linearDamping, int stepsPerSecond, int maxGuess) {

        glm::vec3 displacement = targetPosition - startPosition;
        float dt = 1.0f / stepsPerSecond;
        int totalSteps = static_cast<int>(std::ceil(time * stepsPerSecond));

        // Initial guess: assume no gravity or damping
        glm::vec3 initialVelocity = displacement / time;

        // Iterative refinement
        for (int iteration = 0; iteration < maxGuess; ++iteration) {
            glm::vec3 currentPosition = startPosition;
            glm::vec3 currentVelocity = initialVelocity;

            for (int step = 0; step < totalSteps; ++step) {
                currentPosition += currentVelocity * dt;
                glm::vec3 dampingForce = -linearDamping * currentVelocity;
                currentVelocity += (gravity + dampingForce) * dt;
            }

            glm::vec3 error = targetPosition - currentPosition;
            initialVelocity += error / time;

            // Check for convergence
            if (glm::length(error) < 0.01f) {
                break;
            }
        }
        return initialVelocity;
    }
 

    // 注意：本函数不再使用 adjustSpeed 参数，而是利用模拟中最近点对应的时间来计算速度修正量
    glm::vec3 calcP2PAdjustVelocityGuess(const glm::vec3& startPosition,
        const glm::vec3& targetPosition,
        glm::vec3 initVel,
        const glm::vec3& gravity,
        float linearDamping,
        float maxSimulateSeconds,
        int stepsPerSecond,
        int maxGuess, float adjustSpeed)
    {
        const float dt = 1.0f / stepsPerSecond;
        const int totalSteps = static_cast<int>(maxSimulateSeconds * stepsPerSecond);

        glm::vec3 bestVelocity = initVel;
        float lenErrorLast = 99999.f;
        for (int guess = 0; guess < maxGuess; ++guess)
        {
            glm::vec3 currentPosition = startPosition;
            glm::vec3 currentVelocity = bestVelocity;

            // 记录整个模拟过程里离目标最近的点以及对应的飞行时间
            glm::vec3 closestPosition = startPosition;
            float closestDistanceSq = glm::length2(startPosition - targetPosition);
            float closestTime = 0.0f;
            float time = 0.0f;

            // 运动模拟
            for (int step = 0; step < totalSteps; ++step)
            {
                // 更新位置
                currentPosition += currentVelocity * dt;
                // 更新速度：先加重力
                currentVelocity += gravity * dt;
                // 再施加阻尼（按照给定方式）
                currentVelocity *= std::pow(1.0f - linearDamping, dt);

                time += dt;
                // 记录距离目标最近的点及其对应时间
                float dSq = glm::length2(currentPosition - targetPosition);
                if (dSq < closestDistanceSq) {
                    closestDistanceSq = dSq;
                    closestPosition = currentPosition;
                    closestTime = time;
                }
            }

            // 计算模拟中离目标最近点与目标之间的误差
            glm::vec3 error = targetPosition - closestPosition;
            float lenError = glm::length(error);
			DP(("lenerr %f, clt %f  cl %f,%f,%f", lenError, closestTime, closestPosition.x, closestPosition.y, closestPosition.z));
            // 如果误差足够小，则认为找到了合适的初始速度
            if (glm::length(error) < 0.001f || lenError > lenErrorLast)
                break;
            lenErrorLast = lenError;
            // 避免除以0的情况
            if (closestTime < 1e-6f)
                break;

            // 利用最近点时的误差和飞行时间计算出修正量：
            // 要使得在 closestTime 时刻恰好达到目标，需要的额外初始速度约为 error / closestTime
            glm::vec3 correction = error / closestTime;
            bestVelocity += correction* adjustSpeed;
        }

        return bestVelocity;
    }

 
    glm::vec3 calcP2PAdjustVelocityGuess1(const glm::vec3& startPosition, const glm::vec3& targetPosition,
        glm::vec3 initVel, const glm::vec3& gravity, float linearDamping, float maxSimulateSeconds,
        int stepsPerSecond, int maxGuess, float adjustSpeed)
    {
        const float dt = 1.0f / stepsPerSecond;
        const int totalSteps = static_cast<int>(maxSimulateSeconds * stepsPerSecond);
		float lenErrorLast = 99999.f;
        glm::vec3 bestVelocity = initVel;
		float vLen = glm::length(initVel);
        for (int guess = 0; guess < maxGuess; ++guess)
        {
            glm::vec3 currentPosition = startPosition;
            glm::vec3 currentVelocity = bestVelocity;

            // 记录轨迹中离目标最近的点
            glm::vec3 closestPosition = startPosition;
            float closestDistanceSq = glm::length2(startPosition - targetPosition);

            // 模拟运动过程
            for (int step = 0; step < totalSteps; ++step) {
                // 更新位置
                currentPosition += currentVelocity * dt;
                // 更新速度：先施加重力
                currentVelocity += gravity * dt;
                // 再施加阻尼（使用给定的方式）
                currentVelocity *= std::pow(1.0f - linearDamping, dt);

                // 记录当前点与目标的距离是否更近
                float dSq = glm::length2(currentPosition - targetPosition);
                if (dSq < closestDistanceSq) {
                    closestDistanceSq = dSq;
                    closestPosition = currentPosition;
                }
            }

            // 计算误差：目标与轨迹上最近点的差值
            glm::vec3 error = targetPosition - closestPosition;
            float lenError = glm::length(error);
            // 如果误差足够小，则提前退出
            if (lenError< 0.001f || lenError>lenErrorLast)
                break;
			lenErrorLast = lenError;
            // 根据误差调整初始速度
            bestVelocity += error * adjustSpeed;
			//bestVelocity = glm::fastNormalize(bestVelocity) * vLen;
        }

        return bestVelocity;
    }




    // This function calculates the initial velocity required to hit a target in given duration with gravity.
    glm::vec3 calcVelocityP2PinT(const glm::vec3& startPos, const glm::vec3& targetPos, float durationTime,
        const glm::vec3& gravity) {
        // Calculate the horizontal distance to the target.
        glm::vec3 horizontalDisplacement = targetPos - startPos;
        horizontalDisplacement.y = 0; // We don't want the vertical displacement here.

        // Calculate the vertical distance to the target.
        float verticalDisplacement = targetPos.y - startPos.y;

        // Calculate the initial velocity in the horizontal direction.
        glm::vec3 horizontalVelocity = horizontalDisplacement / durationTime;

        // Using the formula: finalPosition = startPos + velocity * time + 0.5 * gravity * time^2
        // We can find the initial velocity in the vertical direction by solving for velocity.
        float verticalVelocity = (verticalDisplacement - 0.5f * gravity.y * durationTime * durationTime) / durationTime;

        // Combine the horizontal and vertical velocities to get the initial velocity vector.
        return glm::vec3(horizontalVelocity.x, verticalVelocity, horizontalVelocity.z);
    }

    glm::quat calculateJointRotation(
        const glm::vec3& currentPos,    // Current position of end effector
        const glm::vec3& posTarget,     // Target position to reach
        const glm::vec3& jointPos       // Position of the joint/pivot point
    ) {
        // Calculate vectors from joint to current and target positions
        glm::vec3 currentVector = glm::normalize(currentPos - jointPos);
        glm::vec3 targetVector = glm::normalize(posTarget - jointPos);

        // Calculate rotation axis (cross product gives axis perpendicular to both vectors)
        glm::vec3 rotationAxis = glm::cross(currentVector, targetVector);

        // Handle case where vectors are parallel
        if (glm::length(rotationAxis) < 1e-6f) {
            // If vectors point in same direction, no rotation needed
            if (glm::dot(currentVector, targetVector) > 0.0f) {
                return glm::quat(1.0f, 0.0f, 0.0f, 0.0f);
            }
            // If vectors point in opposite directions, rotate 180° around any perpendicular axis
            glm::vec3 perpAxis = glm::normalize(glm::vec3(1.0f, 0.0f, 0.0f));
            if (glm::abs(glm::dot(currentVector, perpAxis)) > 0.9f) {
                perpAxis = glm::normalize(glm::vec3(0.0f, 1.0f, 0.0f));
            }
            return glm::angleAxis(glm::pi<float>(), perpAxis);
        }

        rotationAxis = glm::normalize(rotationAxis);

        // Calculate rotation angle
        float cosAngle = glm::dot(currentVector, targetVector);
        float angle = glm::acos(glm::clamp(cosAngle, -1.0f, 1.0f));

        // Create quaternion from axis and angle
        return glm::angleAxis(angle, rotationAxis);
    }

    glm::quat directionToRotation(const glm::vec3& direction, const glm::vec3& upNormalized)
    {
        // Ensure the direction vector is normalized
        glm::vec3 normalizedDirection = glm::normalize(direction);

        // Check if direction is parallel to up vector
        if (glm::abs(glm::dot(normalizedDirection, upNormalized)) > 0.9999f)
        {
            // Direction is parallel to up, create a rotation that aligns with this
            if (glm::dot(normalizedDirection, upNormalized) > 0)
            {
                // Direction is same as up, no rotation needed
                return glm::quat(1.0f, 0.0f, 0.0f, 0.0f);
            }
            else
            {
                // Direction is opposite to up, rotate 180 degrees around X axis
                return glm::angleAxis(glm::pi<float>(), glm::vec3(1.0f, 0.0f, 0.0f));
            }
        }

        // Calculate the right vector
        glm::vec3 right = glm::cross(upNormalized, normalizedDirection);
        right = glm::normalize(right);

        // Recalculate the up vector to ensure orthogonality
        glm::vec3 up = glm::cross(normalizedDirection, right);

        // Create a rotation matrix
        glm::mat3 rotationMatrix(
            right,
            up,
            normalizedDirection
        );

        // Convert the rotation matrix to a quaternion
        return glm::quat_cast(rotationMatrix);
    }

    //right hand system
    glm::quat directionToRotationRHS(const glm::vec3& direction, const glm::vec3& upNormalized) 
    {
        // Ensure the direction vector is normalized
        glm::vec3 normalizedDirection = glm::normalize(direction);

        // Check if direction is parallel to up vector
        if (glm::abs(glm::dot(normalizedDirection, upNormalized)) > 0.9999f)
        {
            // Direction is parallel to up, create a rotation that aligns with this
            if (glm::dot(normalizedDirection, upNormalized) > 0)
            {
                // Direction is same as up, rotate 90 degrees around X axis
                return glm::angleAxis(glm::half_pi<float>(), glm::vec3(1.0f, 0.0f, 0.0f));
            }
            else
            {
                // Direction is opposite to up, rotate -90 degrees around X axis
                return glm::angleAxis(-glm::half_pi<float>(), glm::vec3(1.0f, 0.0f, 0.0f));
            }
        }

        // Calculate the right vector (note the reversed cross product order for RHS)
        glm::vec3 right = glm::cross(normalizedDirection, upNormalized);
        right = glm::normalize(right);

        // Recalculate the up vector to ensure orthogonality
        glm::vec3 up = glm::cross(right, normalizedDirection);

        // Create a rotation matrix
        // Note: In RHS, forward is typically -Z, so we negate normalizedDirection
        glm::mat3 rotationMatrix(
            right,
            up,
            -normalizedDirection  // Negate for RHS
        );

        // Convert the rotation matrix to a quaternion
        return glm::quat_cast(rotationMatrix);
    }






    //============================================================================
    bool isPointInTriangle(
        const glm::vec3& P,      // Point to test
        const glm::vec3& A,      // Triangle vertex
        const glm::vec3& B,      // Triangle vertex
        const glm::vec3& C       // Triangle vertex
    ) {
        // Calculate triangle normal
        glm::vec3 normal = glm::normalize(glm::cross(B - A, C - A));

        // Project point and triangle onto plane defined by triangle normal
        // by removing the normal component
        glm::vec3 planePoint = P - (glm::dot(P - A, normal) * normal);

        // Calculate barycentric coordinates
        glm::vec3 v0 = B - A;
        glm::vec3 v1 = C - A;
        glm::vec3 v2 = planePoint - A;

        float d00 = glm::dot(v0, v0);
        float d01 = glm::dot(v0, v1);
        float d11 = glm::dot(v1, v1);
        float d20 = glm::dot(v2, v0);
        float d21 = glm::dot(v2, v1);

        float denom = d00 * d11 - d01 * d01;
        float v = (d11 * d20 - d01 * d21) / denom;
        float w = (d00 * d21 - d01 * d20) / denom;
        float u = 1.0f - v - w;

        // Point is in triangle if barycentric coordinates are all between 0 and 1
        return v >= 0.0f && w >= 0.0f && u >= 0.0f && u <= 1.0f;
    }
    bool isPointInCorner(
        const glm::vec3& P,       // Point to test
        const glm::vec3& A,      // First point defining corner
        const glm::vec3& B,      // Corner point
        const glm::vec3& C      // Second point defining corner       
    ) {
#if 1
        // Get vectors
        glm::vec3 BA = glm::normalize(A - B);
        glm::vec3 BC = glm::normalize(C - B);
        glm::vec3 BP = glm::normalize(P - B);

        // Calculate angles
        float angleABC = glm::acos(glm::dot(BA, BC));  // Angle of the corner
        float anglePBA = glm::acos(glm::dot(BP, BA));  // Angle between point and first vector
        float anglePBC = glm::acos(glm::dot(BP, BC));  // Angle between point and second vector

        // Point is inside if sum of angles to point is less than or equal to corner angle
        return anglePBA + anglePBC <= angleABC + 0.001f; // Small epsilon for floating point comparison

#else  // should  <180 degree
        // Get vectors
        glm::vec3 BA = glm::normalize(A - B);
        glm::vec3 BC = glm::normalize(C - B);
        glm::vec3 BP = glm::normalize(P - B);

        // Calculate cross product to determine the normal of the plane
        glm::vec3 normal = glm::cross(BA, BC);

        // If cross product is zero (vectors are parallel), no valid corner exists
        if (glm::length(normal) < 1e-6f) {
            return false;
        }

        // Get signed angle from BA to BP using the normal for sign
        float angleBABP = glm::sign(glm::dot(glm::cross(BA, BP), normal)) *
            glm::acos(glm::dot(BA, BP));

        // Get signed angle from BA to BC
        float angleBABC = glm::sign(glm::dot(glm::cross(BA, BC), normal)) *
            glm::acos(glm::dot(BA, BC));

        // Always use the smaller angle (< 180 degrees)
        if (angleBABC > 0) {
            return angleBABP >= 0 && angleBABP <= angleBABC;
        }
        else {
            return angleBABP <= 0 && angleBABP >= angleBABC;
        }
#endif
    }
 

    bool isStickInTriangle(
        glm::vec3 A,      // Triangle vertex
        glm::vec3 B,      // Triangle vertex
        glm::vec3 C,      // Triangle vertex
        glm::vec3 E,      // Stick start
        glm::vec3 F ,      // Stick end
        float lenMul,
        glm::vec3* intersectPt
    ) {
        //// Check if either endpoint is very close to triangle vertices
        //float threshold = 0.001f;
        //if (glm::length(E - A) < threshold || glm::length(E - B) < threshold || glm::length(E - C) < threshold ||
        //    glm::length(F - A) < threshold || glm::length(F - B) < threshold || glm::length(F - C) < threshold) {
        //    return true;
        //}
        // 
                // If either endpoint is in the triangle, return true

        if (lenMul != 1.f) {
            glm::vec3 newE = F + (E - F) * lenMul;
            glm::vec3 newF = E + (F - E) * lenMul;
            E = newE;
            F = newF;

            glm::vec3 newA = B + (A - B) * lenMul;
		    glm::vec3 newC = C + (C - B) * lenMul;
			A = newA;
			C = newC;
        }


        if (isPointInTriangle(E, A, B, C) || isPointInTriangle(F, A, B, C)) {
            return true;
        }

        // Calculate triangle normal
        glm::vec3 normal = glm::normalize(glm::cross(B - A, C - A));

        // Calculate line direction
        glm::vec3 lineDir = glm::normalize(F - E);

        // Check if line is parallel to triangle
        float nd = glm::dot(normal, lineDir);
        if (std::abs(nd) < 1e-6f) {
            return false; // Line is parallel to triangle
        }

        // Calculate intersection point with triangle plane
        float d = glm::dot(normal, A); // Distance from origin to triangle plane
        float t = (d - glm::dot(normal, E)) / glm::dot(normal, lineDir);

        // Check if intersection point lies within line segment
        if (t < 0.0f || t > glm::length(F - E)) {
            return false;
        }

        // Calculate intersection point
        glm::vec3 intersection = E + lineDir * t;
        if (intersectPt) *intersectPt = intersection;
        // Check if intersection point lies within triangle
        return isPointInTriangle(intersection, A, B, C);
    }
     
    bool isStickInCornerABC(
        const glm::vec3& A,      // Triangle vertex
        const glm::vec3& B,      // corner point
        const glm::vec3& C,      // Triangle vertex
        glm::vec3 E,      // Line segment start
        glm::vec3 F,      // Line segment end
        float stickMul  ,
        glm::vec3* intersectPt
    ) {
#if 1
        // Get plane normal from triangle ABC
        glm::vec3 normal = glm::normalize(glm::cross(B - A, C - A));
        if (stickMul != 1.f) {            
            glm::vec3 newE = F+ (E-F) * stickMul;
			glm::vec3 newF = E + (F - E) * stickMul;
			E = newE;
			F = newF;
        }
        // Check if E and F are on different sides of plane
        float dE = glm::dot(E - A, normal);
        float dF = glm::dot(F - A, normal);

        if (dE * dF >= 0) {
            return false;  // Points on same side or on plane
        } 
        // Calculate intersection point
        float t = dE / (dE - dF);
        glm::vec3 P = E + t * (F - E);  // Intersection point
        if (intersectPt) *intersectPt = P;
        // Now check if P is in corner ABC
        glm::vec3 BA = glm::normalize(A - B);
        glm::vec3 BC = glm::normalize(C - B);
        glm::vec3 BP = glm::normalize(P - B);

        // Get corner angle (always use smaller angle < 180)
        glm::vec3 cornerNormal = glm::cross(BA, BC);
        float angleBABC = glm::sign(glm::dot(glm::cross(BA, BC), cornerNormal)) *
            glm::acos(glm::dot(BA, BC));
        float angleBABP = glm::sign(glm::dot(glm::cross(BA, BP), cornerNormal)) *
            glm::acos(glm::dot(BA, BP));

        // Check if P is in corner
        if (angleBABC > 0) {
            return angleBABP >= 0 && angleBABP <= angleBABC;
        }
        else {
            return angleBABP <= 0 && angleBABP >= angleBABC;
        }
#else
        // If either endpoint is in the triangle, return true
        if (isPointInCorner(E, A, B, C) || isPointInCorner(F, A, B, C)) {
            return true;
        }

        // Calculate triangle normal
        glm::vec3 normal = glm::normalize(glm::cross(B - A, C - A));

        // Calculate line direction
        glm::vec3 lineDir = glm::normalize(F - E);

        // Check if line is parallel to triangle
        float nd = glm::dot(normal, lineDir);
        if (std::abs(nd) < 1e-6f) {
            return false; // Line is parallel to triangle
        }

        // Calculate intersection point with triangle plane
        float d = glm::dot(normal, A); // Distance from origin to triangle plane
        float t = (d - glm::dot(normal, E)) / glm::dot(normal, lineDir);

        // Check if intersection point lies within line segment
        if (t < 0.0f || t > glm::length(F - E)) {
            return false;
        }

        // Calculate intersection point
        glm::vec3 intersection = E + lineDir * t;
        if (intersectPt) *intersectPt = intersection;
        // Check if intersection point lies within triangle
        return isPointInCorner(intersection, A, B, C);
#endif
    }
    //============================================================================
}