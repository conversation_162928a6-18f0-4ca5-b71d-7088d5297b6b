﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//
#pragma once


using PhyReal = float;
#define PHY_GPU					1
#define USE_DIRECT_GPU  0  // not ok yet
#define PHY_RBS_MUL				8  // 8-2 = 1~1.5 GB

#define PHYSX_SABA_INFLATE     PHY_GPU  //saba ALLOW_GPU

#define USE_JOLT				0
#define USE_PHYSX				1//(!USE_JOLT)

#define SABA_USE_PHYSX			1
#define SABA_RB_LOCAL_TRANSFORM	1
#define MMD_HIT_TEST			1
#include "PMXFile.h"
#include <queue>
#include <set>
#include <glm/gtx/norm.hpp> 
#include <vector>
#include <helpers/glmutils.h>
#include <functional>


#define HIT_GROUND_VMUL  0.58f // default material ball hit ground

#define PXFILTER0_DYNRB			0x10000000
#define PXFILTER0_MODCONTACT	0x01000000
#define PXFILTER0_FORCE_NOTIFY	0x00100000
#define OWNERID_PHYOBJ		0x1000
#define OWNERID_VEHICLE		0x6000
#define OWNERID_VOXEL		0x8000

#define CLDMASK_PMD				0x100000

#define PHRBF_ForceDynRb	0x00000001
#define PHRBF_ContactOffset 0x00000010  // grid performance if contact too big
#define PHYSX_MATERIALID_TEXTFWCUBE 1
#define PHYSX_MATERIALID_VOXELCUBE 2


namespace irr::scene
{
	struct PhyObj;
	enum ENdId { eNdIdNone = -1, ehL = 0, ehR, efL, efR, elL, elR, eHd, eYa, eoL, eoR };
	inline bool isNid_h(ENdId n) { return n == ehL || n == ehR; }
	inline bool isNid_f(ENdId n) { return n == efL || n == efR; }
	inline bool isNid_o(ENdId n) { return n == eoL || n == eoR; }
}
namespace saba
{
	class MMDPhysics;
	class MMDModel; class PMXModel;
	class MMDNode;
	class MMDJoint;
	class MMDMotionState;
	class PMXNode;
	class MMDRigidBody;
	class JoltManPP;
	class PhysXMan;
	class PhysXVehicle;
	struct TriggerCallback {
		std::function<void(MMDRigidBody* other)> onEnter;
		std::function<void(MMDRigidBody* other)> onExit;
	};


	struct PhysicsEngineObjectUserData
	{
		int userType = 0;  //10:ground
		int ownerType = 0; //0:irr 1:MMDRigid
		MMDRigidBody* owner{};
		PhysicsEngineObjectUserData* lastContact{};
		float contactTime = 0;
		glm::vec3 hitPos, hitImp;
		bool callHitCb = false;
		
	};

	struct InflateBufBind {
		MMDRigidBody* rb;
		int id, oldId;
		glm::vec3 pos;
	};
	struct TriggerParam {
		enum ETrigerParamFlag {
			//eAddVel = 1, eAddRtt = 2, eSclVel = 4, eSclRtt = 8, eSetVelP2P = 16, eSpeedToPm = 32
			eAddVel = 1 << 0, eAddRtt = 1 << 1, eSclVel = 1 << 2, eSclRtt = 1 << 3, eSetVelP2P = 1 << 4, 
			eSpeedTo = 1 << 5, eTeleport = 1 << 6, ePlayMPA = 1 << 7, eResetPhy = 1 << 8,
		};
		uint64_t flag;
		
		glm::vec3 addVel;
		glm::vec3 sclVel,sclRtt;
		std::wstring tgtNodeName,fileName;
		glm::vec3 speedToPm;
		saba::MMDRigidBody* trb{};
	};
	class MMDRigidBody
	{
	public:
		MMDRigidBody(MMDPhysics* phy);
		virtual ~MMDRigidBody();
		MMDRigidBody(const MMDRigidBody& rhs) = delete;
		MMDRigidBody& operator = (const MMDRigidBody& rhs) = delete;

		//bool Create(const PMDRigidBodyExt& pmdRigidBody, MMDModel* model, MMDNode* node);
		virtual bool CreatePMX(const PMXRigidbody& pmxRigidBody, MMDModel* model, MMDNode* node) = 0;
		virtual void Destroy() = 0;

		virtual const glm::mat4 GetTransform() = 0;  // not inlcude scale , but node GlobalTransform include
		virtual const glm::vec3 getPosition() = 0;
		glm::quat getRotation();

		virtual void ResetMovement(MMDPhysics* physics = nullptr) = 0;

		virtual void SetActivation(bool activation, bool force= false ) = 0;
		bool GetActivation() { return mActivation; }

		virtual void setScale(glm::vec3 scv) = 0;
		virtual void updateMassOnDensity() {};
		virtual void onAllRbCreated(MMDModel* model) {};

		uint16_t GetGroup() const;
		uint32_t GetGroupMask() const;

		void updateMotionState();
		void updateStateOnNode();
		void ResetTransform();
		void ReflectGlobalTransform();
		void CalcLocalTransform();
		void onPhyStepUpdate(int step, int stepCount) {
			if (resetRbCd > 0) {
				ResetMovement();
				resetRbCd--;
			}
			if (cbCallOnce) {
				cbCallOnce();
				cbCallOnce = nullptr;
			}
			if (cbPhyStep) {
				cbPhyStep(step, stepCount);
			}
		}

		//ckadd
		virtual void addToWorld() = 0;
		virtual void removeFromWorld() = 0;

		virtual void setGravityMul(float gm) = 0;
		virtual void setDamping(float d, int what) {};
		virtual glm::vec3 getLinearVel() = 0;
		virtual glm::vec3 getAngularVel() = 0;
		virtual void setLinearVel(const glm::vec3& vel) = 0;
		void setLinearVelToPos(const glm::vec3& pos, float mul) {
			setLinearVel((pos - getPosition()) * mul);
		};
		void setLinearVelToPosLen(const glm::vec3& vel, float len) {
			setLinearVel(glm::fastNormalize(vel - getPosition()) * len);
		};

		virtual glm::vec3 addLinearVel(const glm::vec3& vel) = 0;
		glm::vec3 addLinearVelToPos(const glm::vec3& pos, float mul) {
			glm::vec3 v = pos - getPosition();
			return addLinearVel(v * mul);
			 
		};
		glm::vec3 addLinearVelToPosSc(const glm::vec3& pos, float mul, float sc) {
			scaleVel(sc, 1);
			glm::vec3 v = pos - getPosition();
			return addLinearVel(v * mul); 
		};
		glm::vec3 addLinearVelToPosScXZ(const glm::vec3& pos, float mul, float sc) {
			scaleVel(sc, 1);
			glm::vec3 v = pos - getPosition();
			return addLinearVel(v * glm::vec3(mul, 0, mul)); return v;
		};
		glm::vec3 addLinearVelToPosScVec(const glm::vec3& pos, glm::vec3 mul, float sc) {
			scaleVel(sc, 1);
			glm::vec3 v = pos - getPosition();
			return addLinearVel(v * mul); 
		};
		glm::vec3 addForceToPos(const glm::vec3& pos, float mul) {
			glm::vec3 v = pos - getPosition(); 
			addForce(v * mul);
			return v * mul ;
		};
		glm::vec3 addLinearVelToPos(const glm::vec3& pos, glm::vec3 mul) {
			glm::vec3 v = pos - getPosition();
			return addLinearVel(v * mul); 
		};
		glm::vec3 addLinearVelToPosPow2(const glm::vec3& pos, float mul) {
			glm::vec3 v = pos - getPosition();
			return addLinearVel(v * mul * glm::length(v));
		};

		glm::vec3 addLinearVelToPosLimitDis(const glm::vec3& pos, float mul, float minDis, float maxDis, float ofsLen=0.f) {
			auto dir = (pos - getPosition()); float len = glm::clamp(glm::length(dir)+ofsLen, minDis, maxDis);
			return addLinearVel(glm::fastNormalize(dir) * len * mul);
		};
		glm::vec3 addLinearVelToPosLimitDis2(const glm::vec3& pos, float mul, float minDis, float maxDis) {
			auto dir = (pos - getPosition()); float len = glm::clamp(glm::length(dir), minDis, maxDis);
			return addLinearVel(glm::fastNormalize(dir) * len * len * mul);
		};
		
		virtual void addForce(const glm::vec3& f, bool isVelChange = true) = 0;
		virtual void addTorque(const glm::vec3& torque, bool isVelChange = true) = 0;
		virtual void addTorqueOnMatRtt(const glm::mat4& m, const glm::vec3& torque, bool isVelChange = true) {};
		void addTorqueLocal(const glm::vec3& torque, bool isVelChange = true);

		void addVelToRotateOnAxis(const glm::vec3& pos, const glm::vec3& axis, float velMul, float leaveVelMul);

		virtual void setAngularVel(const glm::vec3& vel) = 0;
		virtual void setCollideFilterMask(int setMode, uint32_t ft, int flag=0) = 0;//setmode 1:set 0:restore  ; flag:1=noreset

		void setRotateTo(glm::mat4 rttMat);
		void setRotateToOnNode(glm::mat4 rttMat) { setRotateTo(rttMat * getOfsMatInv()); }
		void applyTorqueRotateTo(glm::mat3 rttMat, float angleMul) {
			setAngVelToRotateOnNode(rttMat, angleMul);
		}

		virtual void setAngVelToRotateOnNode(glm::mat4 rttMat, float velMul) = 0;

		virtual void addRotationToMatOnNode(glm::mat3 rttMat, float angleMul = 1000.f, float chgVel=false) = 0;
		void addRotationToMatOnNode(glm::mat4 rttMat, float angleMul = 1000.f, float chgVel = false) {
			addRotationToMatOnNode(glm::mat3(rttMat), angleMul, chgVel);
		};
		glm::quat addRotationToMatOnNode_MatRttResetXZ(glm::mat4 rttMat, float angleMul = 1000.f,float x=0,float z=0) {
			// Extract the forward direction vector from the matrix
// This is typically the third column for OpenGL-style matrices (glm default)
			glm::vec3 forward(rttMat[2][0], rttMat[2][1], rttMat[2][2]);
			forward = glm::normalize(forward);

			// Project the forward vector onto the XZ plane to get the yaw direction
			glm::vec3 forwardXZ = glm::normalize(glm::vec3(forward.x, 0.0f, forward.z));

			// Calculate the desired yaw angle from this projected direction
			// atan2 properly handles the full 360-degree range
			float yaw = atan2f(forwardXZ.x, forwardXZ.z);

			// Create the final quaternion with only the yaw rotation preserved
			// and the specified pitch and roll
			glm::quat qr= glm::quat(glm::vec3(x, yaw, z));
			addRotationToMatOnNode(glm::mat3(qr), angleMul);
			return qr;
		}
 
		glm::quat addRotationToMatOnNode_MatRttResetXZ(glm::mat4 rbMat, glm::mat4 ndMat, float angleMul = 1000.f) {
			glm::quat qr = glm::quat(rbMat);
			glm::vec3 euler = glm::eulerAngles(qr);
			glm::quat ndqr = glm::quat(ndMat);
			glm::vec3 ndeuler = glm::eulerAngles(ndqr);
			euler.x = ndeuler.x, euler.z = ndeuler.z;
			qr = glm::quat(euler);
			qr = normalize(qr);
			addRotationToMatOnNode(glm::mat3(qr), angleMul);
			return qr;
		}
		glm::quat addRotationToMatOnNode_MatRttResetXZ(  float angleMul = 1000.f, float x = 0, float z = 0) {
			auto qr=addRotationToMatOnNode_MatRttResetXZ(getNodeTransform(), angleMul, x, z);
			return qr;
		}
		glm::quat addRotationToFaceDir(glm::vec3 dir, glm::vec3 localRtt, float angleMul = 1000.f) {
			auto qr = glh::directionToRotation(dir, glm::vec3(0, 1, 0) 
			); qr = qr * (glm::quat(localRtt) * glm::quat(glm::vec3(0, piFloat, 0)));
			addRotationToMatOnNode(glm::mat3(qr), angleMul);
			return qr;
		}
		glm::quat setRotation_MatRttResetXZ(float x = 0, float z = 0) {
			glm::quat qr = glm::quat(getNodeTransform());
			qr.x = x; qr.z = z;// Extract rotation and zero out x/z components
			qr = normalize(qr);
			setRotateToOnNode(glm::mat4(qr));
			return qr;
		}
		void rotateFromDirToDir(glm::vec3 dirSrc, glm::vec3 dirTgt, float amul);
		void rotateLocalDirTo(const glm::vec3 front, glm::vec3 toGlobalDir, float mul)// see rotateRbDir
		{
			glm::vec3 dirSrc = glm::fastNormalize(glh::matRotateVec(GetTransform(), front));
			//sbFw2LineD("pt",  getPosition(),  getPosition() + toGlobalDir * 10.5f, 0xFCFF8000, 60);
			//sbFw2LineD("pt",  getPosition(),  getPosition() + dirSrc * 10.5f, 0xFC00FF80, 60);
			rotateFromDirToDir(dirSrc, toGlobalDir, mul);
		}

		bool setAngVelToPos(glm::vec3 pos, float velMul, float angPow = 1.f, float maxAngleDeg = 180.f, bool backward = false);
		void setRotateToPos(glm::vec3 pos, float angMul);
		virtual void SetCoMTranslate(const glm::vec3& v, float dummy = 1) = 0;
		virtual void SetCoMTransform(const glm::mat4& m, float mul = 0.f) = 0;
		void setTranslateRate(const glm::vec3& v, float rat = 0.5f, float minDis = 0.1f, float maxDis = 1.f);
		virtual void scaleVel(PhyReal s, int what = 3) = 0;
		virtual void scaleVel3(glm::vec3 s, int what = 3) = 0;
		virtual float getMass() = 0;
		virtual void setGravityOn(bool on) = 0;
		glm::vec3 getSize() { return Pm.m_shapeSize * scaleMul; }
		virtual glm::vec3 predictPosition(float afterTimeS) = 0;

		void moveRbOfs(glm::vec3 ofs);
		void moveToPos(glm::vec3 pos, bool moveRootNode, bool resetRb);
		void setAngVelToPosByAxisY(glm::vec3 pos, float angleMul);
		void addTranslate(glm::vec3 ofs) { SetCoMTranslate(getPosition() + ofs); }
		void addTranslateAni(glm::vec3 ofs, float dur, uu::Ebf bf);
		void onFrameChgStep(float stepTime, int step, int stepCount);
		std::wstring	nameU;
		PMXRigidbody Pm;
		PMXNode* node{};
		MMDJoint* touchRBJoint{};
		virtual void setGlmMat(glm::mat4 m) = 0;
		virtual void setContactTest(bool con) = 0;

		MMDModel* m_mmdModel{}; //fast broad phase 
		size_t pmxRbIdx = -1;
		int dynRbType = 0;		//1:static to dyn
		bool isRootRb = false, isBodyPart=false;
		bool isSubRb = false;
		bool isWeightRb = false;
		bool collideOtherChar = false;
		bool rootHasParent = false; glm::mat4* mtRootParent{};
		glm::mat4 rttMat = glm::mat4(1.0f); //local rotation
		glm::quat rttLocal = glm::quat(1.f, 0.f, 0.f, 0.f); //local rotation

		irr::scene::PhyObj* phyObj{};
		uint32_t rbMask = 0; // 0x1= saba rb, 0x10 = phyobj
		float m_mass = 0.f;
		float   density1 = 1.f, density0=1.f, massMul = 1, densityMul = 1.f;
		virtual void setMassMul(float massMul) {};
		//float sumWeight = 0.f;
		MMDRigidBody* parentRb{}, * lastParentRb{};
		int rbTreeLevel = 0;
		std::vector<MMDJoint*> jtsToParent,jtsFromChild;
		virtual int connectJointCount() { return jtsToParent.size() + jtsFromChild.size(); }
		bool disableAtk = false;

		std::function<void(saba::PhysicsEngineObjectUserData*)> cbHit, cbMod;
		std::function<void()> cbCallOnce;
		std::function<void(int step, int stepCount)>cbPhyStep;
		PhysicsEngineObjectUserData usrDat;

		//phy state
		glm::vec3   vel, lvel, dtvel, acc, pos, lpos, initPos;
		glm::mat4 initTransform;
		float ltime;

		//modify contact  cb
		bool needModVel = false; glm::vec3 modVelValue;

		//stroke vars
		uint32_t stkFlag = 0;  //1:head, 2:tail
		int svgStrokeIdx = -1, svgRbIdx = -1, svgChIdx = -1;
		MMDJoint* svgJt{}; float svgJtDis = 99999.f;
		bool hasPhyState = false;
		void updatePhyState(float stepTime);
		enum class RigidBodyType
		{
			Kinematic,	//MMD Static,
			Dynamic,	//MMD Dynamic,
			Aligned,	//MMD DynamicAndBoneMerge
		};
		RigidBodyType	m_rigidBodyType;
		uint32_t		m_groupFlag = 0, m_groupMask = -1, curGourpMask = 0;
		inline glm::mat4 nodeToRbMat(glm::mat4 nm) { return nm * getOfsMat(); }
		const glm::mat4& getOfsMat() { return m_offsetMat; }
		const glm::mat4& getOfsMatInv() { return m_invOfsMat; }
		glm::mat4 getNodeTransform() { return GetTransform() * getOfsMatInv(); }
		glm::mat4 getNodeTransformByParentRB(MMDNode* parent = nullptr);
		glm::vec3 getNodePos() { return glm::vec3((GetTransform() * getOfsMatInv())[3]); }
		glm::vec3 rbTransformVec(glm::vec3 pos) { return glm::vec3(GetTransform() * glm::vec4(pos, 1)); }
		float disTo(MMDRigidBody* rb) { return glm::length(getPosition() - rb->getPosition()); }
		float disTo(glm::vec3 pos) { return glm::length(getPosition() - pos); }
		float dis2To(glm::vec3 pos) { return glm::length2(getPosition() - pos); }

		glm::vec3 scaleMul{ 1.f,1.f,1.f };
		int resetRbCd = 0;


		struct RBWorldState {
			glm::mat4 transform;
			glm::vec3 linearVelocity;
			glm::vec3 angularVelocity;
		};
		void saveState();
		void restoreState(int ofs, uint32_t flag = 0);

		TriggerCallback cbTrigger;
		bool canInTrigger=false;
		std::set<saba::MMDRigidBody*> inTriggerRbs,rbsInThisTrigger;
		std::function<void(MMDRigidBody* other)> cbOnTriggerEnter, cbOnTriggerExit;
		TriggerParam trigerParam;

		PhyMeshData* pdbPmd{}; //bind cloth
		virtual void pdbResetPos() {};
		int connFixCount = 0;
		//debug
		int dbgBreak_addVel=0;
	protected:

		uint16_t		m_group;

		alignas(16) glm::mat4	m_offsetMat, m_invOfsMat, m_offsetMatOrig;

		std::string					m_name;

		//ckadd
		bool mActivation = false;
		MMDPhysics* physics{};
		void* phyObjOwner{};

		glm::vec3 mLocalInteria{};


		glm::vec3 aniS, aniT;
		int aniMode = 0;
		float aniDur = 1.f, aniStart = -999;
		uu::Ebf aniBf;

		std::deque<RBWorldState> mRbStates; //max 3 worlds
		std::deque<RBWorldState>::iterator itRbState = mRbStates.end();
	};

	class MMDJoint
	{
	public:
		MMDJoint() {}

		virtual ~MMDJoint() {

		}

		MMDJoint(const MMDJoint& rhs) = delete;
		MMDJoint& operator = (const MMDJoint& rhs) = delete;

		virtual bool CreateJointPmx(PMXJoint pmxJoint, MMDRigidBody* rigidBodyA, MMDRigidBody* rigidBodyB, float scale = 1.f) = 0;
		virtual bool CreateJoint2pt(const PMXJoint& pmxJoint, MMDRigidBody* rigidBodyA, MMDRigidBody* rigidBodyB, int lockRtt, int lockPos) = 0;
		virtual void setDrive(float spring, float damping) = 0;
		virtual void destroy() = 0; // delete if connectRb, dot not call directly
		virtual void setBreakThreshold(float s) = 0;
		virtual void scaleBreakThreshold(float s) = 0;
		virtual bool isConnected() = 0;
		virtual void setScale() = 0;

		virtual void getLocalFrame(int id, glm::vec3& t, glm::quat& r) = 0;
		virtual void setLocalFrame(int id, const glm::vec3& t, const glm::quat& r) = 0;
		virtual void lockRtt(bool lk, glm::vec3 angLimit = glm::vec3{ 0 }) = 0;
		virtual void setDriveMul(float springMultiplier, float  dampingMultiplier = 1.f) {};
		virtual void setDrivePose(glm::mat4 m, uint32_t flag, float driveMul) {};
		virtual void setDriveVel(glm::vec3 lv, glm::vec3 av, uint32_t flag) {};
		virtual glm::vec3 getPosition() = 0;
		MMDJoint* setRttDegAdd(glm::vec3 rtt);

		MMDJoint* setRttAdd(glm::quat rtt);
		void driveRb2toPos(glm::vec3 pos, float mul = 1.f); 

		void rotateTowards(const glm::vec3& targetDirection, float rotationSpeed);
		MMDRigidBody* getAnotherRb(MMDRigidBody* rb);

		bool onCreatedJoint(PMXJoint pmxJoint, MMDRigidBody* rigidBodyA, MMDRigidBody* rigidBodyB);

		MMDRigidBody* getRb(int id) { return id == 0 ? rb1 : rb2; }

		PMXJoint Pm;
	protected:




		MMDRigidBody* rb1{}, * rb2{};

		float mScale{ 1 };
		bool rttlocked = false;
		float springMul = 1.f, oldSpringMul = 1.f;
	};


	class MMDPhysics
	{
	public:
		MMDPhysics();
		virtual ~MMDPhysics();

		MMDPhysics(const MMDPhysics& rhs) = delete;
		MMDPhysics& operator = (const MMDPhysics& rhs) = delete;
		virtual EPhysicsEngine getType() = 0;
		virtual bool Create() = 0;
		void onCreated() {};
		virtual MMDRigidBody* newRigidBody() = 0;
		virtual MMDJoint* newJoint() = 0;

		virtual void senPhyDbgVisual(bool show) = 0;
		virtual void setGravity(glm::vec3 g) = 0;

		virtual void Destroy() = 0;

		void Update(float time) {};
		static void UpdateFrameStart(float time, int subStep = 0);
		static void UpdateFrameWait();
		static void DebugRender(void* driver);
		void SetFPS(float fps);
		float GetFPS() const;
		void SetMaxSubStepCount(int numSteps);
		int GetMaxSubStepCount() const;

		inline static bool phyDbgView = false;
		inline static std::function<void(int step, int maxStep)> cbOnPhysicsStep;
		inline static bool paused = false;
		void AddRigidBody(MMDRigidBody* mmdRB);
		void RemoveRigidBody(MMDRigidBody* mmdRB);
		void AddJoint(MMDJoint* mmdJoint);
		void RemoveJoint(MMDJoint* mmdJoint);
		virtual void test1() {};
		struct DebugLine
		{
			glm::vec3	pos0;
			uint32_t	color0;
			glm::vec3	pos1;
			uint32_t	color1;
		};

		virtual void getLines(DebugLine*& ptr, int& num) = 0;
		int phyDbgLineNum = 0; saba::MMDPhysics::DebugLine* phyDbgLines{};
		std::vector<saba::MMDPhysics::DebugLine> phyDbgLinesBuf;
		void getDbgLineBuf();

		//#if SABA_USE_PHYSX
		//		physx::PxScene* GetDynamicsWorld() const;	 		
		//#else
		//		btDiscreteDynamicsWorld* GetDynamicsWorld() const;
		//#endif
		bool ownPhysicsWorld() { return ownWorld; }
		virtual void clearForces() = 0;



		// Vehicle creation (implemented by derived classes)
		virtual PhysXVehicle* createVehicle(const glm::vec3& startPos = glm::vec3(0.0f, 0.5f, 0.0f)) { return nullptr; }

		bool mForceUpdateDyn = false;
		inline static PhyReal phyTimeMul = 1, gameTimeMul = 1, stepTimeMul = 1, stepTimeMulPow2 = 1, stepTimeMulInv = 1, curStep = -1, stepCount = 0;
		//void enablePlane(uint32_t mask);

	protected:

		bool ownWorld = 1;
		double	m_fps;
		int		m_maxSubStepCount;


	};

	MMDPhysics* newPhysics(EPhysicsEngine type);



}
