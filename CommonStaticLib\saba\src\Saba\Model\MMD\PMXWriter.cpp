#include "mmdPCH.h"
#include "PMXWriter.h"
#include <Saba/Base/Log.h>
#include <Saba/Base/File.h>
#include <Saba/Base/UnicodeUtil.h>

#include <vector>
#include <glm/ext/scalar_constants.hpp>
 
#include <jsoncpp/json5cpp.h>
#include <Helpers/glmUtils.h>
namespace saba {

template<typename T>
bool Write(const T* val, File& file) {
    return file.Write(val);
}

template<typename T>
bool Write(const T* valArray, size_t size, File& file) {
    return file.Write(valArray, size);
}

bool PMXWriter::WriteString(const std::string& str, File& file) {
    if (str.empty()) {
        uint32_t bufSize = 0;
        if (!Write(&bufSize, file)) {
            SABA_ERROR("Failed to write empty string size");
            return false;
        }
        return true;
    }

    if (pmx->m_header.m_encode == 0) {
        // UTF-16
        std::u16string utf16Str;
        if (!ConvU8ToU16(str, utf16Str)) {
            SABA_ERROR("Failed to convert string to UTF-16");
            return false;
        }

        uint32_t bufSize = (uint32_t)(utf16Str.size() * 2);
        if (!Write(&bufSize, file)) {
            SABA_ERROR("Failed to write UTF-16 string size");
            return false;
        }

        if (!Write(utf16Str.c_str(), utf16Str.size(), file)) {
            SABA_ERROR("Failed to write UTF-16 string data");
            return false;
        }
    }
    else if (pmx->m_header.m_encode == 1) {
        // UTF-8
        uint32_t bufSize = (uint32_t)str.size();
        if (!Write(&bufSize, file)) {
            SABA_ERROR("Failed to write UTF-8 string size");
            return false;
        }

        if (!Write(str.c_str(), str.size(), file)) {
            SABA_ERROR("Failed to write UTF-8 string data");
            return false;
        }
    }
    else {
        SABA_ERROR("Unsupported string encoding: {}", pmx->m_header.m_encode);
        return false;
    }

    return !file.IsBad();
}

template<typename T>
bool PMXWriter::WriteIndex(int32_t index, uint8_t size, File& file, int sub) {
    if (index >= 0) {
        index -= sub;
    }
    
    switch (size) {
    case 1:
    {
        int8_t idx = (int8_t)index;
        return Write(&idx, file);
    }
    case 2:
    {
        int16_t idx = (int16_t)index;
        return Write(&idx, file);
    }
    case 4:
    {
        int32_t idx = (int32_t)index;
        return Write(&idx, file);
    }
    default:
        return false;
    }
}

bool PMXWriter::WriteHeader(const PMXFile* pmx, File& file) {
    const auto& header = pmx->m_header;
    
    // Write magic and version
    if (!Write(header.m_magic.ToCString(), header.m_magic.ToString().size(), file) ||
        !Write(&header.m_version, file)) {
        SABA_ERROR("Failed to write PMX header magic and version");
        return false;
    }


    // Write globals
    if (!Write(&header.m_dataSize, file) ||
        !Write(&header.m_encode, file) ||
        !Write(&header.m_addUVNum, file) ||
        !Write(&header.m_vertexIndexSize, file) ||
        !Write(&header.m_textureIndexSize, file) ||
        !Write(&header.m_materialIndexSize, file) ||
        !Write(&header.m_boneIndexSize, file) ||
        !Write(&header.m_morphIndexSize, file) ||
        !Write(&header.m_rigidbodyIndexSize, file)) {
        SABA_ERROR("Failed to write PMX header globals");
        return false;
    }

    return !file.IsBad();
}

// ... More write methods implementation ...

bool PMXWriter::writeFile(const PMXFile* pmx, const char* filename) {
    File file;
    if (!file.Create(filename)) {
        SABA_ERROR("PMX File Create Failed. {}", filename);
        return false;
    }

    if (!writeFile(pmx, file)) {
        SABA_ERROR("PMX File Write Failed. {}", filename);
        return false;
    }

    SABA_INFO("PMX File Write Succeeded. {}", filename);
    return true;
}

bool PMXWriter::writeFile(const PMXFile* pmx, File& file) {
    // Write Header
    if (!WriteHeader(pmx, file)) {
        SABA_ERROR("WriteHeader Failed.");
        return false;
    }

    // Write Info (model name, comments)
    if (!WriteInfo(pmx, file)) {
        SABA_ERROR("WriteInfo Failed.");
        return false;
    }

    // Write Vertex Data
    if (!WriteVertex(pmx, file)) {
        SABA_ERROR("WriteVertex Failed.");
        return false;
    }

    // Write Face Indices
    if (!WriteFace(pmx, file)) {
        SABA_ERROR("WriteFace Failed.");
        return false;
    }

    // Write Textures
    if (!WriteTexture(pmx, file)) {
        SABA_ERROR("WriteTexture Failed.");
        return false;
    }

    // Write Materials
    if (!WriteMaterial(pmx, file)) {
        SABA_ERROR("WriteMaterial Failed.");
        return false;
    }

    // Write Bones
    if (!WriteBone(pmx, file)) {
        SABA_ERROR("WriteBone Failed.");
        return false;
    }

    // Write Morphs (Shape Deformations)
    if (!WriteMorph(pmx, file)) {
        SABA_ERROR("WriteMorph Failed.");
        return false;
    }

    // Write Display Frames
    if (!WriteDisplayFrame(pmx, file)) {
        SABA_ERROR("WriteDisplayFrame Failed.");
        return false;
    }

    // Write Rigid Bodies
    if (!WriteRigidbody(pmx, file)) {
        SABA_ERROR("WriteRigidbody Failed.");
        return false;
    }

    // Write Joints
    if (!WriteJoint(pmx, file)) {
        SABA_ERROR("WriteJoint Failed.");
        return false;
    }

    // Write Softbodies (if present in the PMX version)
    if (!pmx->m_softbodies.empty()) {
        if (!WriteSoftbody(pmx, file)) {
            SABA_ERROR("WriteSoftbody Failed.");
            return false;
        }
    }

    // Check if any write operations failed
    if (file.IsBad()) {
        SABA_ERROR("File write operation failed.");
        return false;
    }

    return true;
}

bool PMXWriter::WriteInfo(const PMXFile* _pmx, File& file) {
  
    pmx = _pmx;
    const auto& info = pmx->m_info;
    // Write model name (Japanese)
    if (!WriteString(info.m_modelName, file)) {
        SABA_ERROR("Failed to write model name");
        return false;
    }

    // Write English model name
    if (!WriteString(info.m_englishModelName, file)) {
        SABA_ERROR("Failed to write English model name");
        return false;
    }

    // Write comment (Japanese)
    if (!WriteString(info.m_comment, file)) {
        SABA_ERROR("Failed to write comment");
        return false;
    }

    // Write English comment
    if (!WriteString(info.m_englishComment, file)) {
        SABA_ERROR("Failed to write English comment");
        return false;
    }

    return !file.IsBad();
}

bool PMXWriter::WriteVertex(const PMXFile* pmx, File& file) {
    // Write vertex count
    int32_t vertexCount = (int32_t)pmx->m_vertices.size();
    if (!Write(&vertexCount, file)) {
        SABA_ERROR("Failed to write vertex count");
        return false;
    }

    // Write each vertex
    for (const auto& vertex : pmx->m_vertices) {
        // Write position
        if (!Write(&vertex.m_position, file)) {
            SABA_ERROR("Failed to write vertex position");
            return false;
        }

        // Write normal
        if (!Write(&vertex.m_normal, file)) {
            SABA_ERROR("Failed to write vertex normal");
            return false;
        }

        // Write UV
        if (!Write(&vertex.m_uv, file)) {
            SABA_ERROR("Failed to write vertex UV");
            return false;
        }

        // Write additional UVs
        for (uint8_t i = 0; i < pmx->m_header.m_addUVNum; i++) {
            if (!Write(&vertex.m_addUV[i], file)) {
                SABA_ERROR("Failed to write vertex additional UV {}", i);
                return false;
            }
        }

        // Write weight type
        if (!Write(&vertex.m_weightType, file)) {
            SABA_ERROR("Failed to write vertex weight type");
            return false;
        }

        // Write weight data based on type
        switch (vertex.m_weightType) {
        case PMXVertexWeight::BDEF1:
            if (!WriteIndex<int32_t>(vertex.m_boneIndices[0], 
                pmx->m_header.m_boneIndexSize, file)) {
                SABA_ERROR("Failed to write BDEF1 bone index");
                return false;
            }
            break;

        case PMXVertexWeight::BDEF2:
            if (!WriteIndex<int32_t>(vertex.m_boneIndices[0], 
                    pmx->m_header.m_boneIndexSize, file) ||
                !WriteIndex<int32_t>(vertex.m_boneIndices[1], 
                    pmx->m_header.m_boneIndexSize, file) ||
                !Write(&vertex.m_boneWeights[0], file)) {
                SABA_ERROR("Failed to write BDEF2 data");
                return false;
            }
            break;

        case PMXVertexWeight::BDEF4:
            for (int i = 0; i < 4; i++) {
                if (!WriteIndex<int32_t>(vertex.m_boneIndices[i], 
                    pmx->m_header.m_boneIndexSize, file)) {
                    SABA_ERROR("Failed to write BDEF4 bone index {}", i);
                    return false;
                }
            }
            for (int i = 0; i < 4; i++) {
                if (!Write(&vertex.m_boneWeights[i], file)) {
                    SABA_ERROR("Failed to write BDEF4 weight {}", i);
                    return false;
                }
            }
            break;

        case PMXVertexWeight::SDEF:
            if (!WriteIndex<int32_t>(vertex.m_boneIndices[0], 
                    pmx->m_header.m_boneIndexSize, file) ||
                !WriteIndex<int32_t>(vertex.m_boneIndices[1], 
                    pmx->m_header.m_boneIndexSize, file) ||
                !Write(&vertex.m_boneWeights[0], file) ||
                !Write(&vertex.m_sdefC, file) ||
                !Write(&vertex.m_sdefR0, file) ||
                !Write(&vertex.m_sdefR1, file)) {
                SABA_ERROR("Failed to write SDEF data");
                return false;
            }
            break;

        case PMXVertexWeight::QDEF:
            for (int i = 0; i < 4; i++) {
                if (!WriteIndex<int32_t>(vertex.m_boneIndices[i], 
                    pmx->m_header.m_boneIndexSize, file)) {
                    SABA_ERROR("Failed to write QDEF bone index {}", i);
                    return false;
                }
            }
            for (int i = 0; i < 4; i++) {
                if (!Write(&vertex.m_boneWeights[i], file)) {
                    SABA_ERROR("Failed to write QDEF weight {}", i);
                    return false;
                }
            }
            break;

        default:
            SABA_ERROR("Unknown vertex weight type");
            return false;
        }

        // Write edge magnitude
        if (!Write(&vertex.m_edgeMag, file)) {
            SABA_ERROR("Failed to write edge magnitude");
            return false;
        }
    }

    return !file.IsBad();
}

bool PMXWriter::WriteFace(const PMXFile* pmx, File& file) {
    // Write face vertex count (number of indices)
    int32_t faceVertexCount = (int32_t)(pmx->m_faces.size() * 3); // Each face has 3 vertices
    if (!Write(&faceVertexCount, file)) {
        SABA_ERROR("Failed to write face vertex count");
        return false;
    }

    // Write face indices
    for (const auto& face : pmx->m_faces) {
        // Write three vertex indices for each face
        for (int i = 0; i < 3; i++) {
            if (!WriteIndex<int32_t>(face.vtxId[i], 
                pmx->m_header.m_vertexIndexSize, file)) {
                SABA_ERROR("Failed to write face vertex index {}", i);
                return false;
            }
        }
    }

    return !file.IsBad();
}

bool PMXWriter::WriteTexture(const PMXFile* pmx, File& file) {
    // Write texture count
    int32_t textureCount = (int32_t)pmx->m_textures.size();
    if (!Write(&textureCount, file)) {
        SABA_ERROR("Failed to write texture count");
        return false;
    }

    // Write each texture path
    for (const auto& texture : pmx->m_textures) {
        if (!WriteString(texture.m_textureName, file)) {
            SABA_ERROR("Failed to write texture name");
            return false;
        }
    }

    return !file.IsBad();
}

bool PMXWriter::WriteMaterial(const PMXFile* pmx, File& file) {
    // Write material count
    int32_t materialCount = (int32_t)pmx->m_materials.size();
    if (!Write(&materialCount, file)) {
        SABA_ERROR("Failed to write material count");
        return false;
    }

    // Write each material
    for (const auto& material : pmx->m_materials) {
        // Write names
        if (!WriteString(material.m_name, file) ||
            !WriteString(material.m_englishName, file)) {
            SABA_ERROR("Failed to write material names");
            return false;
        }

        // Write colors and parameters
        if (!Write(&material.m_diffuse, file) ||
            !Write(&material.m_specular, file) ||
            !Write(&material.m_specularPower, file) ||
            !Write(&material.m_ambient, file)) {
            SABA_ERROR("Failed to write material colors");
            return false;
        }

        // Write draw mode flags
        if (!Write(&material.m_drawMode, file)) {
            SABA_ERROR("Failed to write material draw mode");
            return false;
        }

        // Write edge properties
        if (!Write(&material.m_edgeColor, file) ||
            !Write(&material.m_edgeSize, file)) {
            SABA_ERROR("Failed to write material edge properties");
            return false;
        }

        // Write texture indices
        if (!WriteIndex<int32_t>(material.m_textureIndex, 
                pmx->m_header.m_textureIndexSize, file) ||
            !WriteIndex<int32_t>(material.m_sphereTextureIndex, 
                pmx->m_header.m_textureIndexSize, file)) {
            SABA_ERROR("Failed to write material texture indices");
            return false;
        }

        // Write sphere mode
        if (!Write(&material.m_sphereMode, file)) {
            SABA_ERROR("Failed to write material sphere mode");
            return false;
        }

        // Write toon properties
        if (!Write(&material.m_toonMode, file)) {
            SABA_ERROR("Failed to write material toon mode");
            return false;
        }

        if (!WriteIndex<int32_t>(material.m_toonTextureIndex, 
            pmx->m_header.m_textureIndexSize, file)) {
            SABA_ERROR("Failed to write material toon texture index");
            return false;
        }

        // Write memo
        if (!WriteString(material.m_memo, file)) {
            SABA_ERROR("Failed to write material memo");
            return false;
        }

        // Write face vertex count
        if (!Write(&material.m_numFaceVertices, file)) {
            SABA_ERROR("Failed to write material face vertex count");
            return false;
        }
    }

    return !file.IsBad();
}

bool PMXWriter::WriteBone(const PMXFile* pmx, File& file) {
    // Write bone count
    int32_t boneCount = (int32_t)pmx->m_bones.size();
    if (!Write(&boneCount, file)) {
        SABA_ERROR("Failed to write bone count");
        return false;
    }

    // Write each bone
    for (const auto& bone : pmx->m_bones) {
        // Write names
        if (!WriteString(bone.m_name, file) ||
            !WriteString(bone.m_englishName, file)) {
            SABA_ERROR("Failed to write bone names");
            return false;
        }

        // Write position
        if (!Write(&bone.m_position, file)) {
            SABA_ERROR("Failed to write bone position");
            return false;
        }

        // Write parent bone index
        if (!WriteIndex<int32_t>(bone.m_parentBoneIndex, 
            pmx->m_header.m_boneIndexSize, file)) {
            SABA_ERROR("Failed to write bone parent index");
            return false;
        }

        // Write deform layer
        if (!Write(&bone.m_deformDepth, file)) {
            SABA_ERROR("Failed to write bone deform depth");
            return false;
        }

        // Write bone flags
        if (!Write(&bone.m_boneFlag, file)) {
            SABA_ERROR("Failed to write bone flags");
            return false;
        }

        // Write position offset or link bone based on flag
        if (bone.m_boneFlag & PMXBoneFlags::TargetShowMode) {
            if (!WriteIndex<int32_t>(bone.m_linkBoneIndex, 
                pmx->m_header.m_boneIndexSize, file)) {
                SABA_ERROR("Failed to write bone link index");
                return false;
            }
        } else {
            if (!Write(&bone.m_positionOffset, file)) {
                SABA_ERROR("Failed to write bone position offset");
                return false;
            }
        }

        // Write append bone data if flag is set
        if (bone.m_boneFlag & (PMXBoneFlags::AppendRotate | PMXBoneFlags::AppendTranslate)) {
            if (!WriteIndex<int32_t>(bone.m_appendBoneIndex, 
                    pmx->m_header.m_boneIndexSize, file) ||
                !Write(&bone.m_appendWeight, file)) {
                SABA_ERROR("Failed to write bone append data");
                return false;
            }
        }

        // Write fixed axis if flag is set
        if (bone.m_boneFlag & PMXBoneFlags::FixedAxis) {
            if (!Write(&bone.m_fixedAxis, file)) {
                SABA_ERROR("Failed to write bone fixed axis");
                return false;
            }
        }

        // Write local axes if flag is set
        if (bone.m_boneFlag & PMXBoneFlags::LocalAxis) {
            if (!Write(&bone.m_localXAxis, file) ||
                !Write(&bone.m_localZAxis, file)) {
                SABA_ERROR("Failed to write bone local axes");
                return false;
            }
        }

        // Write external parent deform key if flag is set
        if (bone.m_boneFlag & PMXBoneFlags::DeformOuterParent) {
            if (!Write(&bone.m_keyValue, file)) {
                SABA_ERROR("Failed to write bone external parent key");
                return false;
            }
        }

        // Write IK data if flag is set
        if (bone.m_boneFlag & PMXBoneFlags::IK) {
            if (!WriteIndex<int32_t>(bone.m_ikTargetBoneIndex, 
                    pmx->m_header.m_boneIndexSize, file) ||
                !Write(&bone.m_ikIterationCount, file) ||
                !Write(&bone.m_ikLimit, file)) {
                SABA_ERROR("Failed to write bone IK data");
                return false;
            }

            // Write IK links
            int32_t ikLinkCount = (int32_t)bone.m_ikLinks.size();
            if (!Write(&ikLinkCount, file)) {
                SABA_ERROR("Failed to write bone IK link count");
                return false;
            }

            for (const auto& link : bone.m_ikLinks) {
                if (!WriteIndex<int32_t>(link.m_ikBoneIndex, 
                        pmx->m_header.m_boneIndexSize, file) ||
                    !Write(&link.m_enableLimit, file)) {
                    SABA_ERROR("Failed to write bone IK link data");
                    return false;
                }

                if (link.m_enableLimit != 0) {
                    if (!Write(&link.m_limitMin, file) ||
                        !Write(&link.m_limitMax, file)) {
                        SABA_ERROR("Failed to write bone IK link limits");
                        return false;
                    }
                }
            }
        }
    }

    return !file.IsBad();
}

bool PMXWriter::WriteMorph(const PMXFile* pmx, File& file) {
    // Write morph count
    int32_t morphCount = (int32_t)pmx->m_morphs.size();
    if (!Write(&morphCount, file)) {
        SABA_ERROR("Failed to write morph count");
        return false;
    }

    // Write each morph
    for (const auto& morph : pmx->m_morphs) {
        // Write names
        if (!WriteString(morph.m_name, file) ||
            !WriteString(morph.m_englishName, file)) {
            SABA_ERROR("Failed to write morph names");
            return false;
        }

        // Write control panel and type
        if (!Write(&morph.m_controlPanel, file) ||
            !Write(&morph.m_morphType, file)) {
            SABA_ERROR("Failed to write morph panel and type");
            return false;
        }

        // Write offset count based on morph type
        int32_t offsetCount = 0;
        switch (morph.m_morphType) {
        case PMXMorphType::Position:
            offsetCount = (int32_t)morph.m_positionMorph.size(); break;
        case PMXMorphType::UV:
        case PMXMorphType::AddUV1:
        case PMXMorphType::AddUV2:
        case PMXMorphType::AddUV3:
        case PMXMorphType::AddUV4:
            offsetCount = (int32_t)morph.m_uvMorph.size(); break;
        case PMXMorphType::Bone:
            offsetCount = (int32_t)morph.m_boneMorph.size(); break;
        case PMXMorphType::Material:
            offsetCount = (int32_t)morph.m_materialMorph.size(); break;
        case PMXMorphType::Group:
            offsetCount = (int32_t)morph.m_groupMorph.size(); break;
        case PMXMorphType::Flip:
            offsetCount = (int32_t)morph.m_flipMorph.size(); break;
        case PMXMorphType::Impluse:
            offsetCount = (int32_t)morph.m_impulseMorph.size(); break;
        default:
            SABA_ERROR("Unknown morph type: {}", (int)morph.m_morphType);
            return false;
        }

        if (!Write(&offsetCount, file)) {
            SABA_ERROR("Failed to write morph offset count");
            return false;
        }

        // Write offsets based on morph type
        switch (morph.m_morphType) {
        case PMXMorphType::Position:
            for (const auto& offset : morph.m_positionMorph) {
                if (!WriteIndex<int32_t>(offset.m_vertexIndex,
                    pmx->m_header.m_vertexIndexSize, file) ||
                    !Write(&offset.m_position, file)) {
                    SABA_ERROR("Failed to write position morph data");
                    return false;
                }
            }
            break;

        case PMXMorphType::UV:
        case PMXMorphType::AddUV1:
        case PMXMorphType::AddUV2:
        case PMXMorphType::AddUV3:
        case PMXMorphType::AddUV4:
            for (const auto& offset : morph.m_uvMorph) {
                if (!WriteIndex<int32_t>(offset.m_vertexIndex,
                    pmx->m_header.m_vertexIndexSize, file) ||
                    !Write(&offset.m_uv, file)) {
                    SABA_ERROR("Failed to write UV morph data");
                    return false;
                }
            }
            break;

        case PMXMorphType::Bone:
            for (const auto& offset : morph.m_boneMorph) {
                if (!WriteIndex<int32_t>(offset.m_boneIndex,
                    pmx->m_header.m_boneIndexSize, file) ||
                    !Write(&offset.m_position, file) ||
                    !Write(&offset.m_quaternion, file)) {
                    SABA_ERROR("Failed to write bone morph data");
                    return false;
                }
            }
            break;

        case PMXMorphType::Material:
            for (const auto& offset : morph.m_materialMorph) {
                if (!WriteIndex<int32_t>(offset.m_materialIndex,
                    pmx->m_header.m_materialIndexSize, file) ||
                    !Write(&offset.m_opType, file) ||
                    !Write(&offset.m_diffuse, file) ||
                    !Write(&offset.m_specular, file) ||
                    !Write(&offset.m_specularPower, file) ||
                    !Write(&offset.m_ambient, file) ||
                    !Write(&offset.m_edgeColor, file) ||
                    !Write(&offset.m_edgeSize, file) ||
                    !Write(&offset.m_textureFactor, file) ||
                    !Write(&offset.m_sphereTextureFactor, file) ||
                    !Write(&offset.m_toonTextureFactor, file)) {
                    SABA_ERROR("Failed to write material morph data");
                    return false;
                }
            }
            break;

        case PMXMorphType::Group:
            for (const auto& offset : morph.m_groupMorph) {
                if (!WriteIndex<int32_t>(offset.m_morphIndex,
                    pmx->m_header.m_morphIndexSize, file) ||
                    !Write(&offset.m_weight, file)) {
                    SABA_ERROR("Failed to write group morph data");
                    return false;
                }
            }
            break;

        case PMXMorphType::Flip:
            for (const auto& offset : morph.m_flipMorph) {
                if (!WriteIndex<int32_t>(offset.m_morphIndex,
                    pmx->m_header.m_morphIndexSize, file) ||
                    !Write(&offset.m_weight, file)) {
                    SABA_ERROR("Failed to write flip morph data");
                    return false;
                }
            }
            break;

        case PMXMorphType::Impluse:
            for (const auto& offset : morph.m_impulseMorph) {
                if (!WriteIndex<int32_t>(offset.m_rigidbodyIndex,
                    pmx->m_header.m_rigidbodyIndexSize, file) ||
                    !Write(&offset.m_localFlag, file) ||
                    !Write(&offset.m_translateVelocity, file) ||
                    !Write(&offset.m_rotateTorque, file)) {
                    SABA_ERROR("Failed to write impulse morph data");
                    return false;
                }
            }
            break;
        }
    }

    return !file.IsBad();
}

bool PMXWriter::WriteDisplayFrame(const PMXFile* pmx, File& file) {
    // Write display frame count
    int32_t frameCount = (int32_t)pmx->m_displayFrames.size();
    if (!Write(&frameCount, file)) {
        SABA_ERROR("Failed to write display frame count");
        return false;
    }

    // Write each display frame
    for (const auto& frame : pmx->m_displayFrames) {
        // Write names
        if (!WriteString(frame.m_name, file) ||
            !WriteString(frame.m_englishName, file)) {
            SABA_ERROR("Failed to write display frame names");
            return false;
        }

        // Write special flag (frame type)
        if (!Write(&frame.m_flag, file)) {
            SABA_ERROR("Failed to write display frame type");
            return false;
        }

        // Write number of targets
        int32_t targetCount = (int32_t)frame.m_targets.size();
        if (!Write(&targetCount, file)) {
            SABA_ERROR("Failed to write display frame target count");
            return false;
        }

        // Write each target
        for (const auto& target : frame.m_targets) {
            // Write target type
            if (!Write(&target.m_type, file)) {
                SABA_ERROR("Failed to write display frame target type");
                return false;
            }

            // Write target index based on type
            switch (target.m_type) {
            case PMXDispalyFrame::TargetType::BoneIndex:
                if (!WriteIndex<int32_t>(target.m_index,
                    pmx->m_header.m_boneIndexSize, file)) {
                    SABA_ERROR("Failed to write display frame bone index");
                    return false;
                }
                break;

            case PMXDispalyFrame::TargetType::MorphIndex:
                if (!WriteIndex<int32_t>(target.m_index,
                    pmx->m_header.m_morphIndexSize, file)) {
                    SABA_ERROR("Failed to write display frame morph index");
                    return false;
                }
                break;

            default:
                SABA_ERROR("Unknown display frame target type: {}", (int)target.m_type);
                return false;
            }
        }
    }

    return !file.IsBad();
}

bool PMXWriter::WriteRigidbody(const PMXFile* pmx, File& file) {
    // Write rigidbody count
    int32_t rbCount = (int32_t)pmx->m_rigidbodies.size();
    if (!Write(&rbCount, file)) {
        SABA_ERROR("Failed to write rigidbody count");
        return false;
    }

    // Write each rigidbody
    for (PMXRigidbody rb : pmx->m_rigidbodies) {
        if (rb.m_shape > PMXRigidbody::Shape::Capsule) {
			rb.m_shape = PMXRigidbody::Shape::Box;
		}
        // Write names
        if (!WriteString(rb.m_name, file) ||
            !WriteString(rb.m_englishName, file)) {
            SABA_ERROR("Failed to write rigidbody names");
            return false;
        }

        // Write bone index
        if (!WriteIndex<int32_t>(rb.m_boneIndex,
            pmx->m_header.m_boneIndexSize, file)) {
            SABA_ERROR("Failed to write rigidbody bone index");
            return false;
        }

        // Write group and collision mask
        if (!Write(&rb.m_group, file)) {
            SABA_ERROR("Failed to write rigidbody group");
            return false;
        }

        uint16_t collideMaskU16 = (uint16_t)rb.m_collideMask32;
        if (!Write(&collideMaskU16, file)) {
            SABA_ERROR("Failed to write rigidbody collide mask");
            return false;
        }

        // Write shape and size
        if (!Write(&rb.m_shape, file) ||
            !Write(&rb.m_shapeSize, file)) {
            SABA_ERROR("Failed to write rigidbody shape data");
            return false;
        }

        // Write position and rotation
        if (!Write(&rb.m_translate, file) ||
            !Write(&rb.m_rotate, file)) {
            SABA_ERROR("Failed to write rigidbody transform");
            return false;
        }

        // Write physics parameters
        if (!Write(&rb.m_mass, file) ||
            !Write(&rb.m_translateDimmer, file) ||
            !Write(&rb.m_rotateDimmer, file) ||
            !Write(&rb.m_repulsion, file) ||
            !Write(&rb.m_friction, file)) {
            SABA_ERROR("Failed to write rigidbody physics parameters");
            return false;
        }

        // Write operation type
        if (!Write(&rb.m_op, file)) {
            SABA_ERROR("Failed to write rigidbody operation type");
            return false;
        }
    }

    return !file.IsBad();
}

bool PMXWriter::WriteJoint(const PMXFile* pmx, File& file) {
    // Write joint count
    int32_t jointCount = (int32_t)pmx->m_joints.size();
    if (!Write(&jointCount, file)) {
        SABA_ERROR("Failed to write joint count");
        return false;
    }

    // Write each joint
    for (const auto& joint : pmx->m_joints) {
        // Write names
        if (!WriteString(joint.m_name, file) ||
            !WriteString(joint.m_englishName, file)) {
            SABA_ERROR("Failed to write joint names");
            return false;
        }

        // Write joint type
        if (!Write(&joint.m_type, file)) {
            SABA_ERROR("Failed to write joint type");
            return false;
        }

        // Write rigidbody indices
        if (!WriteIndex<int32_t>(joint.m_rigidbodyAIndex,
            pmx->m_header.m_rigidbodyIndexSize, file) ||
            !WriteIndex<int32_t>(joint.m_rigidbodyBIndex,
                pmx->m_header.m_rigidbodyIndexSize, file)) {
            SABA_ERROR("Failed to write joint rigidbody indices");
            return false;
        }

        // Write transform
        if (!Write(&joint.translate, file) ||
            !Write(&joint.rotate, file)) {
            SABA_ERROR("Failed to write joint transform");
            return false;
        }

        // Write limits
        if (!Write(&joint.limitMinT, file) ||
            !Write(&joint.limitMaxT, file) ||
            !Write(&joint.limitMinR, file) ||
            !Write(&joint.limitMaxR, file)) {
            SABA_ERROR("Failed to write joint limits");
            return false;
        }

        // Write spring constants
        // Note: Divide by 100 to reverse the multiplication done in reading
        glm::vec3 springT = joint.springT / 100.0f;
        glm::vec3 springR = joint.springR / 100.0f;
        if (!Write(&springT, file) ||
            !Write(&springR, file)) {
            SABA_ERROR("Failed to write joint spring constants");
            return false;
        }
    }

    return !file.IsBad();
}

bool PMXWriter::WriteSoftbody(const PMXFile* pmx, File& file) {
    // Write softbody count
    int32_t sbCount = (int32_t)pmx->m_softbodies.size();
    if (!Write(&sbCount, file)) {
        SABA_ERROR("Failed to write softbody count");
        return false;
    }

    // Write each softbody
    for (const auto& sb : pmx->m_softbodies) {
        // Write names
        if (!WriteString(sb.m_name, file) ||
            !WriteString(sb.m_englishName, file)) {
            SABA_ERROR("Failed to write softbody names");
            return false;
        }

        // Write shape type and flags
        if (!Write(&sb.m_type, file) ||
            !Write(&sb.m_flag, file)) {
            SABA_ERROR("Failed to write softbody type and flags");
            return false;
        }

        // Write material index
        if (!WriteIndex<int32_t>(sb.m_materialIndex,
            pmx->m_header.m_materialIndexSize, file)) {
            SABA_ERROR("Failed to write softbody material index");
            return false;
        }

        // Write group and mask
        if (!Write(&sb.m_group, file) ||
            !Write(&sb.m_collisionGroup, file)) {
            SABA_ERROR("Failed to write softbody groups");
            return false;
        }

        // Write flags
        if (!Write(&sb.m_flag, file)) {
            SABA_ERROR("Failed to write softbody flags");
            return false;
        }

        // Write B-Type parameters
        if (!Write(&sb.m_BLinkLength, file) ||
            !Write(&sb.m_numClusters, file)) {
            SABA_ERROR("Failed to write softbody B-Type parameters");
            return false;
        }

        // Write simulation parameters
        if (!Write(&sb.m_totalMass, file) ||
            !Write(&sb.m_collisionMargin, file)) {
            SABA_ERROR("Failed to write softbody mass and margin");
            return false;
        }

        // Write aero model
        if (!Write(&sb.m_aeroModel, file)) {
            SABA_ERROR("Failed to write softbody aero model");
            return false;
        }

        // Write config parameters
        if (!Write(&sb.m_VCF, file) ||
            !Write(&sb.m_DP, file) ||
            !Write(&sb.m_DG, file) ||
            !Write(&sb.m_LF, file) ||
            !Write(&sb.m_PR, file) ||
            !Write(&sb.m_VC, file) ||
            !Write(&sb.m_DF, file) ||
            !Write(&sb.m_MT, file) ||
            !Write(&sb.m_CHR, file) ||
            !Write(&sb.m_KHR, file) ||
            !Write(&sb.m_SHR, file) ||
            !Write(&sb.m_AHR, file)) {
            SABA_ERROR("Failed to write softbody config parameters");
            return false;
        }

        // Write cluster parameters
        if (!Write(&sb.m_SRHR_CL, file) ||
            !Write(&sb.m_SKHR_CL, file) ||
            !Write(&sb.m_SSHR_CL, file) ||
            !Write(&sb.m_SR_SPLT_CL, file) ||
            !Write(&sb.m_SK_SPLT_CL, file) ||
            !Write(&sb.m_SS_SPLT_CL, file)) {
            SABA_ERROR("Failed to write softbody cluster parameters");
            return false;
        }

        // Write iteration parameters
        if (!Write(&sb.m_V_IT, file) ||
            !Write(&sb.m_P_IT, file) ||
            !Write(&sb.m_D_IT, file) ||
            !Write(&sb.m_C_IT, file)) {
            SABA_ERROR("Failed to write softbody iteration parameters");
            return false;
        }

        // Write material parameters
        if (!Write(&sb.m_LST, file) ||
            !Write(&sb.m_AST, file) ||
            !Write(&sb.m_VST, file)) {
            SABA_ERROR("Failed to write softbody material parameters");
            return false;
        }

        // Write anchor rigidbodies
        int32_t anchorCount = (int32_t)sb.m_anchorRigidbodies.size();
        if (!Write(&anchorCount, file)) {
            SABA_ERROR("Failed to write anchor rigidbody count");
            return false;
        }

        for (const auto& anchor : sb.m_anchorRigidbodies) {
            if (!WriteIndex<int32_t>(anchor.m_rigidBodyIndex,
                pmx->m_header.m_rigidbodyIndexSize, file) ||
                !WriteIndex<int32_t>(anchor.m_vertexIndex,
                    pmx->m_header.m_vertexIndexSize, file) ||
                !Write(&anchor.m_nearMode, file)) {
                SABA_ERROR("Failed to write anchor rigidbody data");
                return false;
            }
        }

        // Write pin vertices
        int32_t pinCount = (int32_t)sb.m_pinVertexIndices.size();
        if (!Write(&pinCount, file)) {
            SABA_ERROR("Failed to write pin vertex count");
            return false;
        }

        for (const auto& pinIdx : sb.m_pinVertexIndices) {
            if (!WriteIndex<int32_t>(pinIdx,
                pmx->m_header.m_vertexIndexSize, file)) {
                SABA_ERROR("Failed to write pin vertex index");
                return false;
            }
        }
    }

    return !file.IsBad();
}

} // namespace saba 