﻿  AppGlobal.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'src/AppGlobal.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.h(41,45): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'src/AppGlobal.cpp')
  
D:\AProj\CommonStaticLib\MidiMan.h(91,31): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'src/AppGlobal.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.h(400,63): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'src/AppGlobal.cpp')
  
  ExampleConnection.c
  ass.c
  ass_library.c
  ass_parse.c
  ass_strtod.c
  ass_utils.c
D:\AProj\AppMainLib\src\FFHelper\libass\ass_utils.c(142,44): warning C4267: 'initializing': conversion from 'size_t' to 'unsigned int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_utils.c(225,12): warning C4244: '=': conversion from '__int64' to 'uint32_t', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_utils.c(382,17): warning C4244: 'return': conversion from '__int64' to 'unsigned int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass.c(315,13): warning C4013: 'strcasecmp' undefined; assuming extern returning int
D:\AProj\AppMainLib\src\FFHelper\libass\ass.c(689,15): warning C4244: '=': conversion from '__int64' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(75,15): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass.c(729,11): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass.c(756,10): warning C4013: 'strncasecmp' undefined; assuming extern returning int
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(193,27): warning C4244: '=': conversion from 'double' to 'unsigned int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(198,27): warning C4244: '=': conversion from 'double' to 'unsigned int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(360,56): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass.c(1080,15): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(362,56): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(364,56): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(366,56): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(677,56): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(679,56): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(681,56): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(683,56): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(782,48): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(791,48): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(800,48): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(744,33): warning C4244: 'initializing': conversion from '__int64' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(884,67): warning C4244: '=': conversion from '__int64' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(911,67): warning C4244: '=': conversion from '__int64' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(953,36): warning C4244: '=': conversion from '__int64' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\libass\ass_parse.c(983,33): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
  winUtils.cpp
  AppMainAMP.cpp
  AppMainAMP_P2.cpp
  SnArItem.cpp
  SnArRoot.cpp
  AppBase.cpp
  AppMainTextFwP2.cpp
  EQMan.cpp
  FlutterDartFFI.cpp
  FMAndroid.cpp
  FT2Man.cpp
  FwClock.cpp
  FwCommon.cpp
  FwManager.cpp
  FwShaderEmu.cpp
  ImgVideoEncoder.cpp
  EQV.cpp
  EqvFw.cpp
  EqvHelpers.cpp
  EqvItemBar.cpp
  EqvItemBar2DSn.cpp
  EqvItemBar3D.cpp
  EqvItemBar3DObj.cpp
  EqvItemSn.cpp
D:\AProj\AppMainLib\src\dsp\FFT.hpp(82,27): warning C4334: '<<': result of 32-bit shift implicitly converted to 64 bits (was 64-bit shift intended?)
  (compiling source file '/src/dsp/EQMan.cpp')
  
D:\AProj\AppMainLib\src\dsp\EQMan.cpp(117,35): warning C4267: 'argument': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/dsp/EQMan.cpp')
  
D:\AProj\AppMainLib\src\dsp\EQMan.cpp(123,20): warning C4267: '-=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/dsp/EQMan.cpp')
  
D:\AProj\AppMainLib\src\dsp\EQMan.cpp(229,107): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/dsp/EQMan.cpp')
  
D:\AProj\AppMainLib\src\dsp\EQMan.cpp(283,20): warning C4267: '+=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/dsp/EQMan.cpp')
  
D:\AProj\AppMainLib\src\dsp\EQMan.cpp(288,16): warning C4244: '=': conversion from 'double' to 'int64_t', possible loss of data
  (compiling source file '/src/dsp/EQMan.cpp')
  
D:\AProj\AppMainLib\src\dsp\EQMan.cpp(299,17): warning C4244: '=': conversion from 'double' to 'int64_t', possible loss of data
  (compiling source file '/src/dsp/EQMan.cpp')
  
D:\AProj\AppMainLib\src\dsp\EQMan.cpp(540,33): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/dsp/EQMan.cpp')
  
D:\AProj\CommonStaticLib\gif-h\gif.h(393,23): warning C4334: '<<': result of 32-bit shift implicitly converted to 64 bits (was 64-bit shift intended?)
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\CommonStaticLib\gif-h\gif.h(394,26): warning C4334: '<<': result of 32-bit shift implicitly converted to 64 bits (was 64-bit shift intended?)
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\AppMainLib\src\FT\FMAndroid.cpp(25,49): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/FT/FMAndroid.cpp')
  
  EqvItemWave.cpp
  EqvItemWaveFw.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
  EqvItemWaveLine.cpp
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(193,46): warning C4244: 'argument': conversion from 'std::streamoff' to 'FT_Long', possible loss of data
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(234,41): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(255,61): warning C4244: 'argument': conversion from 'std::streamoff' to 'FT_Long', possible loss of data
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(213,9): warning C4101: 'ppem': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(214,15): warning C4101: 'tm': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(212,8): warning C4101: 'num_recs': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(407,17): warning C4101: 'ft_bitmap_glyph': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(503,39): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/FT/FT2Man.cpp')
  
  EqvItemWaveMesh.cpp
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(683,52): warning C4244: '+=': conversion from 'float' to 'FT_Pos', possible loss of data
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(469,16): warning C4101: 'matrix': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(618,13): warning C4101: 'iy_': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(618,8): warning C4101: 'ix_': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
  EqvItemWaveStrip.cpp
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(815,7): warning C4101: 'uAlignPrev': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(820,10): warning C4101: 'hr': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(817,7): warning C4101: 'szCount': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
D:\AProj\AppMainLib\src\FT\FT2Man.cpp(821,10): warning C4101: 'pcch': unreferenced local variable
  (compiling source file '/src/FT/FT2Man.cpp')
  
  EqvLoader.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\FwShaderEmu.cpp(375,30): warning C4018: '<=': signed/unsigned mismatch
  (compiling source file '/src/FwShaderEmu.cpp')
  
D:\AProj\AppMainLib\src\FwShaderEmu.cpp(462,28): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/FwShaderEmu.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\FwShaderEmu.cpp(474,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/FwShaderEmu.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\FwManager.cpp(81,20): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/FwManager.cpp')
  
D:\AProj\AppMainLib\src\FwShaderEmu.cpp(514,109): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/FwShaderEmu.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\FwShaderEmu.cpp(663,34): warning C4018: '>=': signed/unsigned mismatch
  (compiling source file '/src/FwShaderEmu.cpp')
  
D:\AProj\AppMainLib\src\FwManager.cpp(263,27): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/FwManager.cpp')
  
D:\AProj\AppMainLib\src\FwManager.cpp(284,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/FwManager.cpp')
  
D:\AProj\AppMainLib\src\FwManager.cpp(505,19): warning C4267: '+=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/FwManager.cpp')
  
D:\AProj\AppMainLib\src\FwManager.cpp(507,21): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/FwManager.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemWave.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveLine.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemWave.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveLine.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppBase.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
  EqvNode.cpp
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveFw.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvLoader.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(113,77): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(113,72): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(118,16): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(124,17): warning C4244: 'initializing': conversion from 'const _Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(124,17): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(124,17): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(124,17): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(124,17): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(221,23): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(221,23): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(221,23): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(221,23): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(221,23): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,42): warning C4244: 'argument': conversion from 'int' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,42): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,42): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,42): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,42): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,24): warning C4244: 'argument': conversion from 'int' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,24): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,24): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,24): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(226,24): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(233,24): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(233,24): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(233,24): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(233,24): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(233,24): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(234,38): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(234,38): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(234,38): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(234,38): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(234,38): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveFw.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244: 'argument': conversion from 'const S' to 'const T', possible loss of data
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         with
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         [
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:             S=double
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         ]
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         and
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         [
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3D.cpp')
      D:\AProj\UaIrrlicht\include\vector3d.h(494,83):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3D.cpp(106,44):
          see reference to function template instantiation 'irr::core::vector3d<irr::f32> irr::core::operator *<double,irr::f32>(const S,const irr::core::vector3d<irr::f32> &)' being compiled
          with
          [
              S=double
          ]
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvLoader.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppBase.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
  EqvNodeBand.cpp
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(45,11): warning C4244: '=': conversion from 'float' to 'irr::u32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveLine.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(51,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveLine.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(52,26): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(52,26): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(52,26): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(52,26): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(52,26): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveLine.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(53,26): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(53,26): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(53,26): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(53,26): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(53,26): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveLine.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(56,38): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(56,38): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(56,38): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(56,38): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(56,38): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveLine.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(57,38): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(57,38): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(57,38): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(57,38): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveLine.cpp(57,38): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveLine.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(93,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(132,16): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(169,17): warning C4244: 'initializing': conversion from 'const _Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(169,17): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(169,17): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(169,17): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(169,17): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(173,9): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(72,77): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(72,72): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(77,16): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(108,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(257,18): warning C4244: 'initializing': conversion from 'const _Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(257,18): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(257,18): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(257,18): warning C4244:             _Ty=float
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(257,18): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(264,18): warning C4244: 'initializing': conversion from 'const _Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(264,18): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(264,18): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(264,18): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(264,18): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(265,49): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(272,107): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(272,107): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(272,107): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(272,107): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(272,107): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(273,107): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(273,107): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(273,107): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(273,107): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar2DSn.cpp(273,107): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar2DSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(189,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(190,14): warning C4244: 'initializing': conversion from 'const _Ty' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(190,14): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(190,14): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(190,14): warning C4244:             _Ty=int
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(190,14): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(199,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(200,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(202,47): warning C4244: 'argument': conversion from '_Ty' to 'irr::u32', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(202,47): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(202,47): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(202,47): warning C4244:             _Ty=float
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(202,47): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppBase.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244: 'argument': conversion from 'const S' to 'const T', possible loss of data
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         with
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         [
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:             S=double
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         ]
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         and
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         [
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemSn.cpp')
      D:\AProj\UaIrrlicht\include\vector3d.h(494,83):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemSn.cpp(65,43):
          see reference to function template instantiation 'irr::core::vector3d<irr::f32> irr::core::operator *<double,irr::f32>(const S,const irr::core::vector3d<irr::f32> &)' being compiled
          with
          [
              S=double
          ]
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
  EqvFwNode.cpp
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(105,77): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(105,72): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(110,16): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(116,17): warning C4244: 'initializing': conversion from 'const _Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(116,17): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(116,17): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(116,17): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(116,17): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppBase.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(215,23): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(215,23): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(215,23): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(215,23): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(215,23): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,42): warning C4244: 'argument': conversion from 'int' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,42): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,42): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,42): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,42): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,24): warning C4244: 'argument': conversion from 'int' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,24): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,24): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,24): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(220,24): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(227,24): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(227,24): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(227,24): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(227,24): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(227,24): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(228,38): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(228,38): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(228,38): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(228,38): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(228,38): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(230,22): warning C4244: '+=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(230,22): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(230,22): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(230,22): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(230,22): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWave.cpp(44,16): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWave.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244: 'argument': conversion from 'const S' to 'const T', possible loss of data
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         with
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         [
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:             S=double
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         ]
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         and
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         [
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\include\vector3d.h(494,83): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemBar3DObj.cpp')
      D:\AProj\UaIrrlicht\include\vector3d.h(494,83):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemBar3DObj.cpp(98,44):
          see reference to function template instantiation 'irr::core::vector3d<irr::f32> irr::core::operator *<double,irr::f32>(const S,const irr::core::vector3d<irr::f32> &)' being compiled
          with
          [
              S=double
          ]
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(43,10): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(67,18): warning C4244: '=': conversion from 'const _Ty' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(67,18): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(67,18): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(67,18): warning C4244:             _Ty=int
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(67,18): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(70,13): warning C4244: '=': conversion from 'float' to 'irr::u32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(108,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(111,40): warning C4244: '=': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(113,31): warning C4244: '=': conversion from 'irr::u32' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(116,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveStrip.cpp(117,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveStrip.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvLoader.cpp(175,24): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EqvLoader.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveFw.cpp(112,83): warning C4244: '=': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveFw.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveFw.cpp(183,10): warning C4244: 'initializing': conversion from '_Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveFw.cpp(183,10): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveFw.cpp(183,10): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveFw.cpp(183,10): warning C4244:             _Ty=float
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveFw.cpp(183,10): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveFw.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(76,9): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(138,13): warning C4244: '=': conversion from 'float' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(147,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(150,41): warning C4244: '=': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(152,32): warning C4244: '=': conversion from 'uint32_t' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(155,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(156,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
  EqvNodeWave.cpp
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(184,25): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(191,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(194,79): warning C4244: '=': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(195,11): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(197,32): warning C4244: '=': conversion from 'uint32_t' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(201,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(202,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(224,25): warning C4267: 'initializing': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(236,13): warning C4244: 'initializing': conversion from 'double' to 'irr::u32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvItemWaveMesh.cpp(244,87): warning C4244: '=': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvItemWaveMesh.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNode.cpp(73,34): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EqvNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNode.cpp(75,34): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EqvNode.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppBase.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppBase.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNode.cpp(118,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EqvNode.cpp')
  
  EqvTouchActionManager.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(437,23): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
  MrCsParticle.cpp
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(590,41): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
  SnCsParticle.cpp
D:\AProj\CommonStaticLib\winUtils.cpp(65,38): warning C4101: 'name': unreferenced local variable
  (compiling source file '/CommonStaticLib/winUtils.cpp')
  
D:\AProj\CommonStaticLib\winUtils.cpp(123,46): warning C4101: 'name': unreferenced local variable
  (compiling source file '/CommonStaticLib/winUtils.cpp')
  
  SnGuQin.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppBase.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(1097,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(1098,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(1094,45): warning C4101: 'fl_y': unreferenced local variable
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppBase.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
  SnLevelWheel.cpp
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
  SnPiano.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
  SnWater.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
  SvgMan.cpp
  CCubeGridSceneNode.cpp
  CharacterAttacker.cpp
D:\AProj\AppMainLib\src\irrmmd\CCubeGridSceneNode.cpp(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EqvNodeWave.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
  CharacterCatcher.cpp
  CInstancedMeshSceneNode.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
  CLabelSceneNode.cpp
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EqvNodeWave.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\CLabelSceneNode.cpp(114,39): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CLabelSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLabelSceneNode.cpp(127,42): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CLabelSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLabelSceneNode.cpp(130,87): warning C5055: operator '+': deprecated between enumerations and floating-point types
  (compiling source file '/src/irrmmd/CLabelSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLabelSceneNode.cpp(131,87): warning C5055: operator '+': deprecated between enumerations and floating-point types
  (compiling source file '/src/irrmmd/CLabelSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLabelSceneNode.cpp(132,87): warning C5055: operator '+': deprecated between enumerations and floating-point types
  (compiling source file '/src/irrmmd/CLabelSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLabelSceneNode.cpp(133,87): warning C5055: operator '+': deprecated between enumerations and floating-point types
  (compiling source file '/src/irrmmd/CLabelSceneNode.cpp')
  
  CLineGridSceneNode.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLineGridSceneNode.h(86,16): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CLineGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLineGridSceneNode.cpp(26,50): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CLineGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLineGridSceneNode.cpp(32,27): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CLineGridSceneNode.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
  CMidiPlateSceneNode.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(141,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(322,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(353,33): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(387,30): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(680,19): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(710,13): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(860,72): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(864,16): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244: 'initializing': conversion from 'const T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244:             T=irr::u32
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244:         ]
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244: 'initializing': conversion from 'const T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244:             T=irr::u32
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244:         ]
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244: 'initializing': conversion from 'const _Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244:             _Ty=float
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244:         ]
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1047,38): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(987,7): warning C4101: 'fwtId': unreferenced local variable
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1084,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1092,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/MusicFirework/AppBase.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1153,11): warning C4244: 'initializing': conversion from 'float' to 'irr::u8', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
  CVoxelMeshSceneNode.cpp
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1123,40): warning C4101: 'imgRing': unreferenced local variable
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1123,31): warning C4101: 'imgSQ': unreferenced local variable
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/MusicFirework/AppBase.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppBase.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(268,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(273,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(285,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(389,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(392,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,33): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,33): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,33): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,33): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,33): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,57): warning C4244: 'argument': conversion from 'const int' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,57): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,57): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,57): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(400,57): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(418,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(419,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(508,14): warning C4244: 'initializing': conversion from 'const _Ty' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(508,14): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(508,14): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(508,14): warning C4244:             _Ty=int
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(508,14): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(514,18): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/MusicFirework/AppBase.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(583,72): warning C4244: 'argument': conversion from '_Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(583,72): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(583,72): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(583,72): warning C4244:             _Ty=float
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvFwNode.cpp(583,72): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EqvFwNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/SnLevelWheel.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/SnLevelWheel.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(220,49): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(271,16): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(283,27): warning C4244: '=': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(294,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(299,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(308,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(323,13): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(390,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(406,25): warning C4267: 'initializing': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(414,15): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EqvNodeBand.cpp(415,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EqvNodeBand.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/SvgMan.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/SnWater.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/SvgMan.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/SnWater.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/SnPiano.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SnLevelWheel.cpp(65,48): warning C4305: 'argument': truncation from 'double' to 'irr::f32'
  (compiling source file '/src/IrrFw/SnLevelWheel.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnLevelWheel.cpp(108,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnLevelWheel.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(31,79): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(31,79): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(31,79): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(31,79): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(31,79): warning C4305:         ]
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(37,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(38,46): warning C4305: 'argument': truncation from 'double' to 'irr::f32'
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,75): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,75): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,75): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,75): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,75): warning C4305:         ]
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,71): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,71): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,71): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,71): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(40,71): warning C4305:         ]
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(41,20): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(41,20): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(41,20): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(41,20): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(41,20): warning C4305:         ]
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(45,96): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(45,96): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(45,96): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(45,96): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(45,96): warning C4305:         ]
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(45,41): warning C4305: 'argument': truncation from 'double' to 'irr::f32'
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(76,8): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(96,60): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(102,47): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(102,47): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(102,47): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(102,47): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(102,47): warning C4305:         ]
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(103,48): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(103,48): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(103,48): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(103,48): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SnGuQin.cpp(103,48): warning C4305:         ]
  (compiling source file '/src/IrrFw/SnGuQin.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/SnPiano.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SnWater.cpp(62,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnWater.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnWater.cpp(72,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SnWater.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
  ImGuiMmdHelper.cpp
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/SvgMan.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/MusicFirework/AppBase.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
  IrrMMD.cpp
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
  irrSaba.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/ImGuiMmdHelper.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/ImGuiMmdHelper.cpp')
  
  irrSabaAnimation.cpp
  irrSabaPhysics.cpp
D:\AProj\AppMainLib\app\MusicFirework\AppBase.cpp(179,39): warning C4244: '=': conversion from 'double' to 'int64_t', possible loss of data
  (compiling source file '/app/MusicFirework/AppBase.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppBase.cpp(403,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/app/MusicFirework/AppBase.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\app\MusicFirework\AppBase.cpp(504,32): warning C4244: 'return': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppBase.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/SnPiano.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
  irrSabaWalk.cpp
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
  MmdMidiPlayer.cpp
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
  MmdNodeHandler.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
  MmdNodePhyAnimator.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/SnPiano.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSabaWalk.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaWalk.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/ImGuiMmdHelper.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\ccubegridscenenode.h(12,15): warning C4099: 'irr::scene::PhyObj': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.h(13,15): warning C4099: 'irr::scene::PhyObj': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(46,57): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(46,57): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(46,57): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(46,57): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(46,57): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(79,39): warning C4244: '-=': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(146,67): warning C4244: 'argument': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(147,56): warning C4244: 'argument': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(150,65): warning C4244: 'argument': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(151,56): warning C4244: 'argument': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(267,16): warning C4244: '=': conversion from 'uint64_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(376,44): warning C4244: 'argument': conversion from 'float' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArItem.cpp(463,37): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArItem.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\base\VulkanUIOverlay.h(110,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(86,35): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(156,50): warning C4244: 'argument': conversion from 'int64_t' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(177,79): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(177,67): warning C4244: 'argument': conversion from 'float' to 'int64_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(179,49): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(179,30): warning C4244: 'argument': conversion from 'float' to 'int64_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(265,22): warning C4244: 'argument': conversion from 'const int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(285,33): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(471,34): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(524,49): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(524,35): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(568,52): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(568,44): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(631,61): warning C4244: 'argument': conversion from 'const int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(631,54): warning C4244: 'argument': conversion from 'const int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(655,63): warning C4244: 'argument': conversion from 'float' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(658,51): warning C4244: 'argument': conversion from 'const int64_t' to 'int32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(661,48): warning C4244: 'argument': conversion from 'const int64_t' to 'int32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(678,34): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(684,52): warning C4244: 'argument': conversion from 'const int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(733,26): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(737,35): warning C4244: '=': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,27): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,27): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,27): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,27): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,27): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,20): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,20): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,20): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,20): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(751,20): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,33): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,33): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,33): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,33): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,33): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,23): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,23): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,23): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,23): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(753,23): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(760,52): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(760,44): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(764,63): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1034,94): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(340,24): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(340,30): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(340,36): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(340,42): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1234,9): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1234,9): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1234,9): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1234,9): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1234,9): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(352,25): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(352,31): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(352,37): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1236,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(352,43): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1238,58): warning C4244: '+=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1238,58): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1238,58): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1238,58): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1238,58): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1239,30): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1239,30): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1239,30): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1239,30): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1239,30): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1241,21): warning C4305: 'argument': truncation from 'double' to 'const T'
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1241,21): warning C4305:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1241,21): warning C4305:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1241,21): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1241,21): warning C4305:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1244,11): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1244,11): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1244,11): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1244,11): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1244,11): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1245,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1247,63): warning C4244: '+=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1247,63): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1247,63): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1247,63): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1247,63): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1248,31): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1248,31): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1248,31): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1248,31): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1248,31): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1292,25): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1292,25): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1292,25): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1292,25): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1292,25): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1293,52): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1293,52): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1293,52): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1293,52): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1293,52): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1294,31): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1294,31): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1294,31): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1294,31): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1294,31): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1297,21): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1297,21): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1297,21): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1297,21): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1297,21): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1298,47): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1298,47): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1298,47): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1298,47): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1298,47): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1299,26): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1299,26): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1299,26): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1299,26): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1299,26): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1302,14): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1302,14): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1302,14): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1302,14): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1302,14): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1303,40): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1303,40): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1303,40): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1303,40): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1303,40): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1304,19): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1304,19): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1304,19): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1304,19): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1304,19): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(480,11): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(481,11): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(482,11): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(483,11): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(570,1): warning C4102: 'testCapScrEnd': unreferenced label
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1448,41): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(728,28): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(729,28): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(742,28): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(743,28): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(745,42): warning C4244: '=': conversion from 'float' to 'int64_t', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(795,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(795,54): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(802,9): warning C4244: 'initializing': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1665,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1686,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(1119,25): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(1123,26): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1813,38): warning C4244: 'argument': conversion from 'const irr::f32' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(1813,32): warning C4244: 'argument': conversion from 'const irr::f32' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(2017,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(2251,35): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(2251,62): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(2330,34): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(2332,36): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\SnArRoot.cpp(2334,36): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/SnArRoot.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(63,60): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(263,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(263,34): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(264,11): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(297,118): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(607,45): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(625,19): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/irrSabaWalk.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
  MmdPhyAnimator.cpp
D:\AProj\AppMainLib\src\irrmmd\CLineGridSceneNode.h(86,16): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/SvgMan.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\base\VulkanUIOverlay.h(110,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaWalk.cpp')
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(508,14): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(547,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSabaWalk.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244: 'initializing': conversion from 'const _Ty' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1136,49): warning C4244: 'argument': conversion from 'double' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1249,17): warning C4244: '=': conversion from 'float' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1275,25): warning C4244: 'argument': conversion from 'float' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1282,29): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1287,17): warning C4244: 'argument': conversion from 'double' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1319,41): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
  MmdPhyAnimator_part2.cpp
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1493,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1507,39): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1537,16): warning C4244: '+=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1593,44): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1682,69): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1755,12): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244: 'initializing': conversion from 'T' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244: 'initializing': conversion from 'T' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2002,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2073,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2105,8): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2127,34): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2201,66): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305: 'argument': truncation from 'double' to 'const T'
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2329,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2489,33): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2859,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(3691,12): warning C4244: 'initializing': conversion from 'irr::u32' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(3839,19): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(3841,17): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(3906,19): warning C4305: 'initializing': truncation from 'double' to 'irr::f32'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4133,34): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244: 'argument': conversion from 'float' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244: 'argument': conversion from 'float' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4221,13): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4241,15): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4413,49): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4457,10): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4457,27): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4467,68): warning C4244: 'argument': conversion from 'size_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4467,58): warning C4244: 'argument': conversion from 'uint32_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4471,66): warning C4244: 'argument': conversion from 'size_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4471,56): warning C4244: 'argument': conversion from 'uint32_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4472,67): warning C4244: 'argument': conversion from 'size_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4472,57): warning C4244: 'argument': conversion from 'uint32_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4595,17): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4596,17): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244: '=': conversion from 'float' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244:             T=int
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244: '=': conversion from 'float' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244:             T=int
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSabaWalk.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/irrSabaWalk.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(63,82): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(64,82): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(83,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(169,31): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(191,13): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(210,15): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(213,21): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(241,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,27): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,27): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,27): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,27): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,27): warning C4305:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,21): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,21): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,21): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,21): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(328,21): warning C4305:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(335,13): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(354,20): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(373,17): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(384,15): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(390,22): warning C4244: 'initializing': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(390,22): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(390,22): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(390,22): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(390,22): warning C4244:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(391,20): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(398,22): warning C4244: 'initializing': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(398,22): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(398,22): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(398,22): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(398,22): warning C4244:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(399,20): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(410,71): warning C4244: '+=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(430,33): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(432,57): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(466,43): warning C4244: '-=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(466,43): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(466,43): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(466,43): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(466,43): warning C4244:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,51): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,51): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,51): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,51): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,51): warning C4305:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,45): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,45): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,45): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,45): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(510,45): warning C4305:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,61): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,61): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,61): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,61): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,61): warning C4305:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,55): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,55): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,55): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,55): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(511,55): warning C4305:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(513,16): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(546,10): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(546,37): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(637,11): warning C4244: 'initializing': conversion from 'T' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(637,11): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(637,11): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(637,11): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(637,11): warning C4244:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(638,11): warning C4244: 'initializing': conversion from 'T' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(638,11): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(638,11): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(638,11): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(638,11): warning C4244:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(688,14): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(768,23): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(794,16): warning C4244: 'initializing': conversion from 'float' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(891,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(808,68): warning C4244: '+=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1081,10): warning C4244: 'initializing': conversion from 'double' to 'glm::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1084,46): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1140,46): warning C4244: 'argument': conversion from 'double' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1141,22): warning C4244: 'argument': conversion from 'double' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(1214,14): warning C4244: 'initializing': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(1214,14): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(1214,14): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(1214,14): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\IrrFw\SvgMan.cpp(1214,14): warning C4244:         ]
  (compiling source file '/src/IrrFw/SvgMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1244,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDMorph.h(26,1): warning C4172: returning address of local variable or temporary 
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2051,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CInstancedMeshSceneNode.cpp(290,32): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CInstancedMeshSceneNode.cpp(318,25): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CInstancedMeshSceneNode.cpp(323,34): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CInstancedMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\src\MatrixRecorder.h(17,31):
          see reference to class template instantiation 'EQVisual::DataRecorderBase<EQVisual::MatRecData>' being compiled
          D:\AProj\AppMainLib\src\DataRecorderBase.h(226,7):
          while compiling class template member function 'int EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs(EQVisual::DrTimeType,const DT *&,const DT *&,float &,size_t)'
          with
          [
              DT=EQVisual::MatRecData
          ]
              D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1286,13):
              see the first reference to 'EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs' in 'EQVisual::EQV::ARCamUpdate'
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(252,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2139,13): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2349,25): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2376,8): warning C4244: 'argument': conversion from 'float' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSabaWalk.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2508,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2509,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2529,67): warning C4244: 'argument': conversion from 'float' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2580,19): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2691,5):
          while compiling class template member function 'std::function<void (irr::scene::IrrSaba *)>::function(_Fx &&)'
          D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2691,5):
          while processing the default template argument of 'std::function<void (irr::scene::IrrSaba *)>::function(_Fx &&)'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\functional(1102,92):
          see reference to alias template instantiation 'std::_Func_class<_Ret,irr::scene::IrrSaba *>::_Enable_if_callable_t<AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2>,std::function<void (irr::scene::IrrSaba *)>>' being compiled
          with
          [
              _Ret=void
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\functional(940,47):
          see reference to variable template 'const bool conjunction_v<std::negation<std::is_same<`AppNameSpace::AppMainAMP::onKeyUpdate'::`6'::<lambda_2>,std::function<void __cdecl(irr::scene::IrrSaba *)> > >,std::_Is_invocable_r<void,`AppNameSpace::AppMainAMP::onKeyUpdate'::`6'::<lambda_2> &,irr::scene::IrrSaba *> >' being compiled
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\functional(940,47):
          see reference to class template instantiation 'std::_Is_invocable_r<_Ret,AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2> &,irr::scene::IrrSaba *>' being compiled
          with
          [
              _Ret=void
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1827,26):
          see reference to alias template instantiation 'std::_Is_invocable_r_<_Rx,_Callable,irr::scene::IrrSaba*>' being compiled
          with
          [
              _Rx=void,
              _Callable=AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2> &
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1789,38):
          see reference to alias template instantiation 'std::_Decltype_invoke_nonzero<_Callable,irr::scene::IrrSaba*,>' being compiled
          with
          [
              _Callable=AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2> &
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1785,71):
          while compiling class template member function 'unknown-type std::_Invoker_functor::_Call(_Callable &&,_Types ...) noexcept(<expr>)'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1576,46):
          see reference to function template instantiation 'auto AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2>::operator ()<irr::scene::IrrSaba>(_T1 *) const' being compiled
          with
          [
              _T1=irr::scene::IrrSaba
          ]
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2734,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2746,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2909,53): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2930,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2974,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2975,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2976,29): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2981,28): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2982,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2983,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2984,29): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3196,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
  MmdPhysicsHelper.cpp
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3670,56): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3699,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3783,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3783,35): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3783,56): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3803,32): warning C4244: 'argument': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3820,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4586,71): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4594,77): warning C4244: '*=': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4654,38): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4660,27): warning C4244: '*=': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4706,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(45,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(51,31): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4713,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(55,34): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(101,34): warning C4305: '=': truncation from 'double' to 'T'
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(101,34): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(101,34): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(101,34): warning C4305:             T=float
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(101,34): warning C4305:         ]
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4780,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(146,33): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(189,11): warning C4305: '+=': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(198,90): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(202,20): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(206,89): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(207,94): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(230,19): warning C4244: '-=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4843,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(231,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(236,88): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(237,95): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(238,104): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(239,104): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(246,63): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(246,99): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4868,57): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(262,94): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(263,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(264,106): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(265,105): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4893,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(302,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(303,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(308,100): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(309,99): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(311,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(312,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(314,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(315,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(333,87): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(335,89): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4959,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4971,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(371,102): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(372,102): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(448,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(469,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5145,32): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(241,36): warning C4244: 'argument': conversion from 'double' to 'irr::u32', possible loss of data
  PhyObjMan.cpp
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(253,20): warning C4244: '-=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(268,19): warning C4244: '-=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(293,38): warning C4244: 'argument': conversion from 'double' to 'irr::u32', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(301,18): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(302,27): warning C4244: 'argument': conversion from 'float' to 'irr::u32', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(302,64): warning C4244: 'argument': conversion from 'float' to 'irr::u32', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(303,29): warning C4244: 'argument': conversion from 'float' to 'irr::u32', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(519,19): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(307,28): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnPiano.cpp(318,29): warning C4244: 'argument': conversion from 'double' to 'irr::u32', possible loss of data
  (compiling source file '/src/IrrFw/SnPiano.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(539,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(540,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(552,91): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(553,91): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(813,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(867,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(877,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(930,30): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5233,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5252,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5282,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5291,125): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5307,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5331,125): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5337,123): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5341,70): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5343,17): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(264,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(272,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(286,24): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5880,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5881,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5882,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5883,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(385,24): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
  
  PhysicsHelper.cpp
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(555,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(569,47): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(590,83): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(618,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(790,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(848,53): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(863,20): warning C4244: 'initializing': conversion from 'Json::Value::Int64' to 'double', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(867,18): warning C4244: 'initializing': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(867,41): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(894,38): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(932,24): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(944,35): warning C4244: 'argument': conversion from 'double' to 'int64_t', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1008,10): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1026,25): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1044,34): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1047,47): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1066,47): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1081,32): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\CommonStaticLib\saba\src\saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6251,123): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6251,118): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1116,55): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1116,86): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1122,37): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1106,23): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6348,33): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6358,10): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1254,20): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1267,20): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1299,32): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1299,32): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1299,32): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1299,32): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1299,32): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1300,32): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1300,32): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1300,32): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1300,32): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1300,32): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,15): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,15): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,15): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,15): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,15): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,30): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,30): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,30): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,30): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1308,30): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1310,13): warning C4244: 'initializing': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1310,13): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1310,13): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1310,13): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1310,13): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1313,28): warning C4244: '+=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1313,28): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1313,28): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1313,28): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1313,28): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,15): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,15): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,15): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,15): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,15): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,30): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,30): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,30): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,30): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1321,30): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1323,13): warning C4244: 'initializing': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1323,13): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1323,13): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1323,13): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1323,13): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1325,28): warning C4244: '+=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1325,28): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1325,28): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1325,28): warning C4244:             T=glm::f64
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1325,28): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdMidiPlayer.cpp(1482,34): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdMidiPlayer.cpp')
  
D:\AProj\CommonStaticLib\saba\src\saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6741,37): warning C4305: 'argument': truncation from 'double' to 'PhyReal'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6748,42): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6759,23): warning C4244: 'argument': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6760,23): warning C4244: 'argument': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6761,117): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6769,101): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6841,54): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6959,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodePhyAnimator.cpp(146,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodePhyAnimator.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7071,21): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7143,41): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7146,28): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7302,50): warning C4244: 'argument': conversion from 'int64_t' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7359,21): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7490,20): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7491,23): warning C4244: 'argument': conversion from 'const int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7544,42): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7550,40): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7558,19): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(221,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7594,57): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::s32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(235,36): warning C4244: 'argument': conversion from 'int' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(235,36): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(235,36): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(235,36): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(235,36): warning C4244:         ]
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7614,57): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::s32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7717,64): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7755,60): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::s32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7906,27): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7912,27): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(322,61): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(323,61): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(324,61): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(322,173): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(323,173): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(324,173): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\CommonStaticLib\saba\src\saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/PhysicsHelper.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,66): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,71): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,76): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,86): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,91): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(72,74): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(72,79): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(72,84): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(72,89): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(84,182): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(714,30): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(742,25): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CVoxelMeshSceneNode.cpp(747,34): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CVoxelMeshSceneNode.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8351,3): warning C4002: too many arguments for function-like macro invocation 'FRAMEWAITER_CALL_B'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8365,43): warning C4804: '>': unsafe use of type 'bool' in operation
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8378,89): warning C4244: 'argument': conversion from 'double' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(332,77): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(425,96): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(454,4): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\CommonStaticLib\saba\src\saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/PhysicsHelper.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(537,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(599,16): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(747,52): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(748,52): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(789,16): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(862,55): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(866,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(867,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(868,46): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(869,46): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CCubeGridSceneNode.cpp(71,38): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(996,52): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(997,52): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1039,16): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1130,55): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1134,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1135,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1136,46): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1137,46): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1191,7): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CCubeGridSceneNode.cpp(447,34): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1267,48): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1276,48): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1289,58): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CCubeGridSceneNode.cpp(819,56): warning C4244: 'argument': conversion from 'float' to 'const unsigned __int64', possible loss of data
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CCubeGridSceneNode.cpp(821,65): warning C4244: 'argument': conversion from 'float' to 'const unsigned __int64', possible loss of data
  (compiling source file '/src/irrmmd/CCubeGridSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\IrrMMD.cpp(496,44): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\IrrMMD.cpp(1008,3): warning C4065: switch statement contains 'default' but no 'case' labels
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(651,20): warning C4101: 'R2': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(651,16): warning C4101: 'L2': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(894,20): warning C4101: 'R2': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(894,16): warning C4101: 'L2': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1433,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1509,5): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1538,4): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1609,37): warning C4305: 'argument': truncation from 'double' to 'irr::f32'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1657,6): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1454,7): warning C4101: 'insDis': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
  sabaCloth.cpp
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(165,35): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(205,14): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(236,48): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(236,48): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(236,48): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(236,48): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(236,48): warning C4244:         ]
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(252,39): warning C4312: 'type cast': conversion from 'int' to 'void *' of greater size
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(256,38): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(554,39): warning C4312: 'type cast': conversion from 'int' to 'void *' of greater size
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(605,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(727,26): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(744,20): warning C4267: 'initializing': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(810,20): warning C4267: 'initializing': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(812,30): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244: 'initializing': conversion from '_Ty' to '_Ty2', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:             _Ty=int
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         and
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:             _Ty2=float
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         ]
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(971,64):
          see reference to function template instantiation 'std::pair<std::string,float>::pair<const char(&)[30],int,0>(_Other1,_Other2 &&) noexcept(false)' being compiled
          with
          [
              _Other1=const char (&)[30],
              _Other2=int
          ]
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
  
D:\AProj\AppMainLib\src\irrfw\eqv\EQV.h(29,8): warning C4099: 'irr::scene::PhyObj': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1049,47): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1242,14): warning C4244: 'initializing': conversion from '_Ty' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1242,14): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1242,14): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1242,14): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1242,14): warning C4244:         ]
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1295,32): warning C4244: '=': conversion from '_Ty' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1295,32): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1295,32): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1295,32): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1295,32): warning C4244:         ]
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1405,60): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1406,60): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1430,66): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1438,63): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\src\MatrixRecorder.h(17,31):
          see reference to class template instantiation 'EQVisual::DataRecorderBase<EQVisual::MatRecData>' being compiled
          D:\AProj\AppMainLib\src\DataRecorderBase.h(226,7):
          while compiling class template member function 'int EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs(EQVisual::DrTimeType,const DT *&,const DT *&,float &,size_t)'
          with
          [
              DT=EQVisual::MatRecData
          ]
              D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2374,15):
              see the first reference to 'EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs' in 'AppNameSpace::AppMainAMP::processVideo::<lambda_1>::operator ()'
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(252,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1480,68): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1556,45): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1618,35): warning C4244: '+=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1638,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.cpp(1650,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CMidiPlateSceneNode.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305: 'argument': truncation from 'const irr::f64' to 'const T'
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         with
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         [
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:             T=irr::f32
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         ]
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\UaIrrlicht\include\vector3d.h(347,40):
      the template instantiation context (the oldest one first) is
          D:\AProj\UaIrrlicht\include\matrix4.h(1190,13):
          see reference to class template instantiation 'irr::core::vector3d<irr::f32>' being compiled
          D:\AProj\UaIrrlicht\include\vector3d.h(345,15):
          while compiling class template member function 'irr::core::vector3d<irr::f32> irr::core::vector3d<irr::f32>::getHorizontalAngleRad(void) const'
              D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(612,16):
              see the first reference to 'irr::core::vector3d<irr::f32>::getHorizontalAngleRad' in 'irr::scene::CharacterAttacker::attackLockNodes'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
  
  SbFwLauncher.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(55,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(56,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(125,25): warning C4305: 'argument': truncation from 'double' to 'PhyReal'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(157,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(165,61): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(198,23): warning C4305: 'argument': truncation from 'double' to 'PhyReal'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(414,89): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(414,116): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(463,89): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(463,116): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(501,89): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(501,116): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(539,89): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(539,116): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(668,13): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaPhysics.cpp(930,34): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaPhysics.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSabaAnimation.cpp(69,11): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSabaAnimation.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(50,37): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(50,41): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(50,46): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(50,50): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(50,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(50,58): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(873,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(896,58): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(1015,91): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(1015,91): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(1015,91): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(1015,91): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\MmdNodeHandler.cpp(1015,91): warning C4244:         ]
  (compiling source file '/src/irrmmd/MmdNodeHandler.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
  SnPhyCloth.cpp
  SnPhyInflatable.cpp
  SnPhyMesh.cpp
D:\AProj\AppMainLib\src\irrmmd\SnPhyMesh.cpp(13,10): warning C4067: unexpected tokens following preprocessor directive - expected a newline
  (compiling source file '/src/irrmmd/SnPhyMesh.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
  SnTestAI.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/SnPhyInflatable.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
  KawaiiLyricGenerator.cpp
  VmdEventExt.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyInflatable.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(14,10): warning C4067: unexpected tokens following preprocessor directive - expected a newline
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyInflatable.cpp(14,10): warning C4067: unexpected tokens following preprocessor directive - expected a newline
  (compiling source file '/src/irrmmd/SnPhyInflatable.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/VmdEventExt.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(877,16): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1089,41): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1216,34): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1231,9): warning C4101: 'isR': unreferenced local variable
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1559,15): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1661,16): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1753,8): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1760,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1975,63): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2023,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2109,23): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2268,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2306,13): warning C4305: '+=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2313,62): warning C4244: 'argument': conversion from 'irr::f32' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2321,36): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2493,17): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2647,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2665,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2750,39): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244: 'argument': conversion from 'const _Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244:             _Ty=float
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2762,24): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244: '=': conversion from 'const _Ty' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         ]
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         and
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2877,18): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2892,59): warning C4244: 'argument': conversion from 'int32_t' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3094,61): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3132,18): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3316,59): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3420,18): warning C4553: '==': result of expression not used; did you intend '='?
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
  LeapMan.cpp
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3485,148): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3646,91): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3862,11): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3864,11): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3890,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3891,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3910,35): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3916,15): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3917,43): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3977,35): warning C4305: '+=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3980,54): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4011,41): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4071,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4147,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4393,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/sv/KawaiiLyricGenerator.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4626,17): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhysicsHelper.cpp(103,121): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4677,17): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhysicsHelper.cpp(157,35): warning C4101: 'newNear': unreferenced local variable
  (compiling source file '/src/irrmmd/MmdPhysicsHelper.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4876,38): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4899,11): warning C4267: 'initializing': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4905,35): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(5055,25): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(5688,68): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(5740,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/sv/KawaiiLyricGenerator.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,32): warning C4838: conversion from 'float' to 'LONG' requires a narrowing conversion
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,32): warning C4244: 'initializing': conversion from 'float' to 'LONG', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,62): warning C4838: conversion from 'float' to 'LONG' requires a narrowing conversion
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,62): warning C4244: 'initializing': conversion from 'float' to 'LONG', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6540,32): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6554,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6609,76): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6750,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6864,3): warning C4305: 'argument': truncation from 'double' to 'irr::f32'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7027,58): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7041,55): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7190,28): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\ccubegridscenenode.h(12,15): warning C4099: 'irr::scene::PhyObj': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7349,15): warning C4305: '+=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7401,45): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
  MatrixRecorder.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/LeapMan.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\base\VulkanUIOverlay.h(110,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(454,23): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(881,34): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(889,42): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(895,47): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/sv/KawaiiLyricGenerator.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\base\VulkanUIOverlay.h(110,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\ccubegridscenenode.h(12,15): warning C4099: 'irr::scene::PhyObj': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
  NetMan.cpp
D:\AProj\AppMainLib\src\irrmmd\SnPhyInflatable.cpp(395,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SnPhyInflatable.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyInflatable.cpp(400,39): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SnPhyInflatable.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(48,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.cpp(72,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(49,38): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.cpp(207,34): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(1284,72): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.cpp(407,17): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.cpp(439,56): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
  PythonMan.cpp
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.cpp(649,39): warning C4553: '==': result of expression not used; did you intend '='?
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.cpp(655,49): warning C4805: '|': unsafe mix of type 'bool' and type 'int' in operation
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(75,15): warning C4244: 'initializing': conversion from 'double' to 'physx::PxReal', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(88,55): warning C4311: 'type cast': pointer truncation from 'irr::scene::SnPhyCloth *' to 'physx::PxU32'
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(88,55): warning C4302: 'type cast': truncation from 'irr::scene::SnPhyCloth *' to 'physx::PxU32'
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(494,16): warning C4244: 'initializing': conversion from '__int64' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(538,47): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(200,53): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(200,53): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(200,53): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(200,53): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(200,53): warning C4244:         ]
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(201,34): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(201,34): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(201,34): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(201,34): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(201,34): warning C4244:         ]
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(544,45): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(206,94): warning C4101: 'z': unreferenced local variable
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(206,92): warning C4101: 'y': unreferenced local variable
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(206,90): warning C4101: 'x': unreferenced local variable
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(244,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(557,56): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(329,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(355,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(363,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.cpp(974,15): warning C4312: 'type cast': conversion from 'int' to 'void *' of greater size
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(409,23): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,31): warning C4244: 'argument': conversion from 'physx::PxU32' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,31): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,31): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,31): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,31): warning C4244:         ]
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,48): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,48): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,48): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,48): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(411,48): warning C4244:         ]
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(422,23): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,31): warning C4244: 'argument': conversion from 'physx::PxU32' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,31): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,31): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,31): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,31): warning C4244:         ]
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,48): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,48): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,48): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,48): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\SnPhyCloth.cpp(424,48): warning C4244:         ]
  (compiling source file '/src/irrmmd/SnPhyCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(1597,76): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(1610,64): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CMidiPlateSceneNode.h(13,15): warning C4099: 'irr::scene::PhyObj': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.cpp(1407,42): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(682,46): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(1766,31): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(1773,31): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator_part2.cpp(1781,31): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/MmdPhyAnimator_part2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(723,19): warning C4244: '=': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
  ShaderToy.cpp
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(753,77): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(768,73): warning C4244: '+=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\MatrixRecorder.cpp(175,48): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/MatrixRecorder.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(1113,58): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(1114,64): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(1127,45): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(1253,32): warning C4244: '=': conversion from '__int64' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(1261,12): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(1274,9): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(1291,9): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/sv/KawaiiLyricGenerator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2054,32): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2104,23): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2106,26): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
  UaJsonSetting.cpp
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305: 'argument': truncation from 'const irr::f64' to 'const T'
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         with
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         [
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:             T=irr::f32
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         ]
  (compiling source file '/src/irrmmd/PhyObjMan.cpp')
      D:\AProj\UaIrrlicht\include\vector3d.h(347,40):
      the template instantiation context (the oldest one first) is
          D:\AProj\UaIrrlicht\include\matrix4.h(1190,13):
          see reference to class template instantiation 'irr::core::vector3d<irr::f32>' being compiled
          D:\AProj\UaIrrlicht\include\vector3d.h(345,15):
          while compiling class template member function 'irr::core::vector3d<irr::f32> irr::core::vector3d<irr::f32>::getHorizontalAngleRad(void) const'
              D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.cpp(766,45):
              see the first reference to 'irr::core::vector3d<irr::f32>::getHorizontalAngleRad' in 'irr::scene::PhyObjManager::calculatePositionAndSpeed'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2178,14): warning C4101: 'rtt': unreferenced local variable
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2575,57): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2743,16): warning C4244: 'initializing': conversion from '__int64' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2782,20): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2783,18): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/NetMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2804,50): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2855,11): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2856,11): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2902,21): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2903,19): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2955,12): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
  UaLibContext.cpp
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/sv/KawaiiLyricGenerator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(3051,22): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(3063,23): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
  UaLibEvtRcv.cpp
  UaLibMain.cpp
D:\AProj\AppMainLib\src\UaLibEvtRcv.cpp(96,30): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/UaLibEvtRcv.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.cpp(97,30): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/UaLibEvtRcv.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.cpp(173,24): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/UaLibEvtRcv.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.cpp(174,24): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/UaLibEvtRcv.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.cpp(182,29): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/UaLibEvtRcv.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,95): warning C4244: 'argument': conversion from 'T' to 'irr::u32', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,95): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,95): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,95): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,95): warning C4244:         ]
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,66): warning C4244: 'argument': conversion from 'T' to 'irr::u32', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,66): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,66): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,66): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,66): warning C4244:         ]
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,37): warning C4244: 'argument': conversion from 'T' to 'irr::u32', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,37): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,37): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,37): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(91,37): warning C4244:         ]
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(149,30): warning C4305: '+=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SbFwLauncher.cpp(208,30): warning C4305: '+=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/SbFwLauncher.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.cpp(277,17): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/UaLibEvtRcv.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
  UaLibStage.cpp
D:\AProj\AppMainLib\src\UaLibMain.cpp(196,17): warning C4244: 'initializing': conversion from '_Rep' to 'float', possible loss of data
D:\AProj\AppMainLib\src\UaLibMain.cpp(196,17): warning C4244:         with
D:\AProj\AppMainLib\src\UaLibMain.cpp(196,17): warning C4244:         [
D:\AProj\AppMainLib\src\UaLibMain.cpp(196,17): warning C4244:             _Rep=double
D:\AProj\AppMainLib\src\UaLibMain.cpp(196,17): warning C4244:         ]
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibMain.cpp(203,43): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibMain.cpp(212,16): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibMain.cpp(248,39): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibMain.cpp(251,35): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibMain.cpp(254,27): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibMain.cpp(260,40): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibMain.cpp(261,36): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibMain.cpp(374,39): warning C4244: '=': conversion from '_Rep' to 'float', possible loss of data
D:\AProj\AppMainLib\src\UaLibMain.cpp(374,39): warning C4244:         with
D:\AProj\AppMainLib\src\UaLibMain.cpp(374,39): warning C4244:         [
D:\AProj\AppMainLib\src\UaLibMain.cpp(374,39): warning C4244:             _Rep=double
D:\AProj\AppMainLib\src\UaLibMain.cpp(374,39): warning C4244:         ]
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibMain.cpp(393,33): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaLibMain.cpp')
  
D:\AProj\AppMainLib\src\UaLibStage.cpp(53,13): warning C4244: 'initializing': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\UaLibStage.cpp(53,13): warning C4244:         with
D:\AProj\AppMainLib\src\UaLibStage.cpp(53,13): warning C4244:         [
D:\AProj\AppMainLib\src\UaLibStage.cpp(53,13): warning C4244:             T=irr::u32
D:\AProj\AppMainLib\src\UaLibStage.cpp(53,13): warning C4244:         ]
  (compiling source file '/src/UaLibStage.cpp')
  
D:\AProj\AppMainLib\src\UaLibStage.cpp(54,13): warning C4244: 'initializing': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\UaLibStage.cpp(54,13): warning C4244:         with
D:\AProj\AppMainLib\src\UaLibStage.cpp(54,13): warning C4244:         [
D:\AProj\AppMainLib\src\UaLibStage.cpp(54,13): warning C4244:             T=irr::u32
D:\AProj\AppMainLib\src\UaLibStage.cpp(54,13): warning C4244:         ]
  (compiling source file '/src/UaLibStage.cpp')
  
D:\AProj\AppMainLib\src\UaLibStage.cpp(64,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaLibStage.cpp')
  
  UaUtils.cpp
  MediaProcessorAndroid.cpp
D:\AProj\AppMainLib\src\UaUtils.cpp(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  rgb2yuv.cpp
D:\AProj\AppMainLib\src\UaUtils.cpp(83,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaUtils.cpp')
  
D:\AProj\AppMainLib\src\NetMan.cpp(83,19): warning C4101: 'buf0': unreferenced local variable
  (compiling source file '/src/NetMan.cpp')
  
  yuv2rgb.cpp
  VideoFrameProcessor.cpp
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(114,11): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244:         ]
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244:         ]
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
  VideoHelpers.cpp
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/ShaderToy.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305: 'argument': truncation from 'const irr::f64' to 'const T'
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         with
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         [
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:             T=irr::f32
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         ]
  (compiling source file '/src/irrmmd/MmdPhyAnimator.cpp')
      D:\AProj\UaIrrlicht\include\vector3d.h(347,40):
      the template instantiation context (the oldest one first) is
          D:\AProj\UaIrrlicht\include\matrix4.h(1190,13):
          see reference to class template instantiation 'irr::core::vector3d<irr::f32>' being compiled
          D:\AProj\UaIrrlicht\include\vector3d.h(345,15):
          while compiling class template member function 'irr::core::vector3d<irr::f32> irr::core::vector3d<irr::f32>::getHorizontalAngleRad(void) const'
              D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(2180,23):
              see the first reference to 'irr::core::vector3d<irr::f32>::getHorizontalAngleRad' in 'irr::scene::MmdPhyAnimator::doUpdateNode::<lambda_1>::operator ()'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/ShaderToy.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDMorph.h(26,1): warning C4172: returning address of local variable or temporary 
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/UaLibContext.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/UaLibContext.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/sv/KawaiiLyricGenerator.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/sv/KawaiiLyricGenerator.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\VideoFrameProcessor.cpp(70,64): warning C4267: 'argument': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/VideoFrameProcessor.cpp')
  
D:\AProj\AppMainLib\src\VideoFrameProcessor.cpp(104,11): warning C4101: 'res': unreferenced local variable
  (compiling source file '/src/VideoFrameProcessor.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/VideoHelpers.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/VideoHelpers.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/sv/KawaiiLyricGenerator.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/LeapMan.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/LeapMan.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\saba\src\saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/UaLibContext.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/UaLibContext.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.cpp(162,92): warning C4305: 'argument': truncation from 'double' to 'irr::f32'
  (compiling source file '/src/UaLibContext.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,69): warning C4244: 'argument': conversion from 'int' to 'T', possible loss of data
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,69): warning C4244:         with
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,69): warning C4244:         [
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,69): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,69): warning C4244:         ]
  (compiling source file '/src/UaLibContext.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,53): warning C4244: 'argument': conversion from 'int' to 'T', possible loss of data
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,53): warning C4244:         with
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,53): warning C4244:         [
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,53): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\UaLibContext.cpp(193,53): warning C4244:         ]
  (compiling source file '/src/UaLibContext.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.cpp(269,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/UaLibContext.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.cpp(274,98): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/UaLibContext.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.cpp(294,36): warning C4244: '=': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/src/UaLibContext.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/VideoHelpers.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/VideoHelpers.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/VideoHelpers.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/LeapMan.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\VideoHelpers.cpp(379,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/VideoHelpers.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.cpp(384,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/VideoHelpers.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.cpp(814,28): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/VideoHelpers.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\AppMainLib\src\LeapMan.cpp(76,12): warning C4305: 'argument': truncation from 'double' to 'const T'
D:\AProj\AppMainLib\src\LeapMan.cpp(76,12): warning C4305:         with
D:\AProj\AppMainLib\src\LeapMan.cpp(76,12): warning C4305:         [
D:\AProj\AppMainLib\src\LeapMan.cpp(76,12): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\LeapMan.cpp(76,12): warning C4305:         ]
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\AppMainLib\src\LeapMan.cpp(147,46): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/LeapMan.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.cpp(432,1): warning C4715: 'irr::scene::MmdPhyAnimator::toggleCamera': not all control paths return a value
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(5875,1): warning C4715: 'irr::scene::IrrSaba::needControl': not all control paths return a value
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDMorph.h(26,1): warning C4172: returning address of local variable or temporary 
  AssHelper.cpp
  UaFfmpeg.cpp
  UaFfmpegFile.cpp
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'src/FFHelper/AssHelper.cpp')
  
D:\AProj\AppMainLib\src\FFHelper\assUtils.h(59,11): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'src/FFHelper/AssHelper.cpp')
  
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(126,11): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(165,27): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(194,39): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(195,37): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(251,37): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(252,35): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(141,8): warning C4101: 'i': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(352,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(357,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(378,15): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(378,15): warning C4244:         with
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(378,15): warning C4244:         [
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(378,15): warning C4244:             T=float
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(378,15): warning C4244:         ]
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(379,15): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(379,15): warning C4244:         with
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(379,15): warning C4244:         [
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(379,15): warning C4244:             T=float
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(379,15): warning C4244:         ]
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(404,15): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(404,15): warning C4244:         with
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(404,15): warning C4244:         [
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(404,15): warning C4244:             T=float
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(404,15): warning C4244:         ]
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(405,15): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(405,15): warning C4244:         with
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(405,15): warning C4244:         [
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(405,15): warning C4244:             T=float
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(405,15): warning C4244:         ]
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(406,15): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(406,15): warning C4244:         with
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(406,15): warning C4244:         [
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(406,15): warning C4244:             T=float
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(406,15): warning C4244:         ]
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,15): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,15): warning C4244:         with
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,15): warning C4244:         [
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,15): warning C4244:             T=float
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,15): warning C4244:         ]
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,32): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,32): warning C4244:         with
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,32): warning C4244:         [
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,32): warning C4244:             T=float
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(438,32): warning C4244:         ]
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,15): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,15): warning C4244:         with
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,15): warning C4244:         [
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,15): warning C4244:             T=float
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,15): warning C4244:         ]
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,32): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,32): warning C4244:         with
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,32): warning C4244:         [
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,32): warning C4244:             T=float
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(439,32): warning C4244:         ]
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(440,13): warning C4244: '=': conversion from '__int64' to 'long', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(440,28): warning C4244: '=': conversion from '__int64' to 'long', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(486,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(500,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(528,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(542,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(556,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(570,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(581,17): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(583,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(593,51): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(593,63): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(637,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(653,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(688,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(688,25): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(693,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(696,25): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(698,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(703,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(703,24): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(708,22): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(716,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(721,22): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(729,11): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(734,22): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(371,17): warning C4101: 'v3': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(414,10): warning C4101: 'k': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(412,21): warning C4101: 'delta_t': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(413,13): warning C4101: 'y': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(413,10): warning C4101: 'x': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(412,30): warning C4101: 't': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(487,9): warning C4101: 'family': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(501,9): warning C4101: 'family': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(529,9): warning C4101: 'family': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(543,9): warning C4101: 'family': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\AssHelper.cpp(557,9): warning C4101: 'family': unreferenced local variable
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'src/FFHelper/UaFfmpeg.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'src/FFHelper/UaFfmpegFile.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.h(41,45): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'src/FFHelper/UaFfmpegFile.cpp')
  
D:\AProj\CommonStaticLib\MidiMan.h(91,31): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'src/FFHelper/UaFfmpegFile.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.h(400,63): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'src/FFHelper/UaFfmpegFile.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.h(41,45): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'src/FFHelper/UaFfmpeg.cpp')
  
D:\AProj\CommonStaticLib\MidiMan.h(91,31): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'src/FFHelper/UaFfmpeg.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.h(400,63): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'src/FFHelper/UaFfmpeg.cpp')
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'src/FFHelper/UaFfmpeg.cpp')
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'src/FFHelper/UaFfmpegFile.cpp')
  
D:\AProj\AppMainLib\src\FFHelper\UaFfmpeg.cpp(135,43): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpeg.cpp(168,13): warning C4101: 'seekTarget': unreferenced local variable
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'src/FFHelper/UaFfmpegFile.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file 'src/FFHelper/UaFfmpegFile.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(499,75): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(602,16): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(674,56): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(715,84): warning C4244: '=': conversion from 'int64_t' to 'double', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(796,27): warning C4018: '<': signed/unsigned mismatch
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(760,18): warning C4101: 'packet': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1470,41): warning C4244: '=': conversion from 'double' to 'int64_t', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1375,18): warning C4101: 'got_frame': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1607,50): warning C4244: '=': conversion from 'double' to '__int64', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1608,40): warning C4244: '=': conversion from 'double' to '__int64', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1622,18): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1623,16): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1821,9): warning C4101: 'size': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1990,94): warning C4244: 'argument': conversion from 'double' to 'int64_t', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1990,69): warning C4267: 'argument': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(2006,94): warning C4244: 'argument': conversion from 'double' to 'int64_t', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(2006,69): warning C4267: 'argument': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1949,9): warning C4101: 'size': unreferenced local variable
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(2032,27): warning C4244: '=': conversion from 'double' to 'int64_t', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(2135,45): warning C4244: '=': conversion from 'double' to 'int64_t', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(2136,31): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(2140,92): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(2464,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\AProj\AppMainLib\src\FFHelper\UaFfmpegFile.cpp(1588,1): warning C4715: 'ualib::UaFFmpegFile::hardwareDecodeFile': not all control paths return a value
  AppMainLib.vcxproj -> D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib
