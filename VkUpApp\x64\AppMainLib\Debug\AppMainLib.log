﻿  AppMainAMP.cpp
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLineGridSceneNode.h(86,16): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\base\VulkanUIOverlay.h(110,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(891,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1081,10): warning C4244: 'initializing': conversion from 'double' to 'glm::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1084,46): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1140,46): warning C4244: 'argument': conversion from 'double' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1141,22): warning C4244: 'argument': conversion from 'double' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1244,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2051,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2139,13): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2349,25): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2376,8): warning C4244: 'argument': conversion from 'float' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2508,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2509,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2529,67): warning C4244: 'argument': conversion from 'float' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2580,19): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2691,5):
          while compiling class template member function 'std::function<void (irr::scene::IrrSaba *)>::function(_Fx &&)'
          D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2691,5):
          while processing the default template argument of 'std::function<void (irr::scene::IrrSaba *)>::function(_Fx &&)'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\functional(1102,92):
          see reference to alias template instantiation 'std::_Func_class<_Ret,irr::scene::IrrSaba *>::_Enable_if_callable_t<AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2>,std::function<void (irr::scene::IrrSaba *)>>' being compiled
          with
          [
              _Ret=void
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\functional(940,47):
          see reference to variable template 'const bool conjunction_v<std::negation<std::is_same<`AppNameSpace::AppMainAMP::onKeyUpdate'::`6'::<lambda_2>,std::function<void __cdecl(irr::scene::IrrSaba *)> > >,std::_Is_invocable_r<void,`AppNameSpace::AppMainAMP::onKeyUpdate'::`6'::<lambda_2> &,irr::scene::IrrSaba *> >' being compiled
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\functional(940,47):
          see reference to class template instantiation 'std::_Is_invocable_r<_Ret,AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2> &,irr::scene::IrrSaba *>' being compiled
          with
          [
              _Ret=void
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1827,26):
          see reference to alias template instantiation 'std::_Is_invocable_r_<_Rx,_Callable,irr::scene::IrrSaba*>' being compiled
          with
          [
              _Rx=void,
              _Callable=AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2> &
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1789,38):
          see reference to alias template instantiation 'std::_Decltype_invoke_nonzero<_Callable,irr::scene::IrrSaba*,>' being compiled
          with
          [
              _Callable=AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2> &
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1785,71):
          while compiling class template member function 'unknown-type std::_Invoker_functor::_Call(_Callable &&,_Types ...) noexcept(<expr>)'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1576,46):
          see reference to function template instantiation 'auto AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2>::operator ()<irr::scene::IrrSaba>(_T1 *) const' being compiled
          with
          [
              _T1=irr::scene::IrrSaba
          ]
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2734,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2746,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2909,53): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2930,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2974,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2975,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2976,29): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2981,28): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2982,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2983,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2984,29): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3196,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3670,56): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3699,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3783,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3783,35): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3783,56): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3803,32): warning C4244: 'argument': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3820,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4587,71): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4595,77): warning C4244: '*=': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4655,38): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4661,27): warning C4244: '*=': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4707,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4714,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4781,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4844,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4869,57): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4894,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4960,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4972,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5146,32): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5234,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5253,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5283,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5292,125): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5308,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5332,125): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5338,123): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5342,70): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5344,17): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5581,60): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5581,60): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5581,60): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5581,60): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5581,60): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5713,48): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5713,48): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5713,48): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5713,48): warning C4244:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5713,48): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5881,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5882,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5883,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5884,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6252,123): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6252,118): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6349,33): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6359,10): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6742,37): warning C4305: 'argument': truncation from 'double' to 'PhyReal'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6749,42): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6760,23): warning C4244: 'argument': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6761,23): warning C4244: 'argument': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6762,117): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6770,101): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6842,54): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6960,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7072,21): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7144,41): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7147,28): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7303,50): warning C4244: 'argument': conversion from 'int64_t' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7360,21): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7491,20): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7492,23): warning C4244: 'argument': conversion from 'const int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7545,42): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7551,40): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7559,19): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7595,57): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::s32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7615,57): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::s32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7718,64): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7756,60): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::s32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7907,27): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7913,27): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8352,3): warning C4002: too many arguments for function-like macro invocation 'FRAMEWAITER_CALL_B'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8366,43): warning C4804: '>': unsafe use of type 'bool' in operation
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8379,89): warning C4244: 'argument': conversion from 'double' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\src\MatrixRecorder.h(17,31):
          see reference to class template instantiation 'EQVisual::DataRecorderBase<EQVisual::MatRecData>' being compiled
          D:\AProj\AppMainLib\src\DataRecorderBase.h(226,7):
          while compiling class template member function 'int EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs(EQVisual::DrTimeType,const DT *&,const DT *&,float &,size_t)'
          with
          [
              DT=EQVisual::MatRecData
          ]
              D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2374,15):
              see the first reference to 'EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs' in 'AppNameSpace::AppMainAMP::processVideo::<lambda_1>::operator ()'
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(252,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
  AppMainLib.vcxproj -> D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib
