#include "AppGlobal.h"
#include <cppIncDefine.h>
#include "SnArRoot.h"
#ifdef _WIN32
#include <windows.h>
#include <commdlg.h>
#endif
#include "irrmmd/IrrMMD.h"

#include "AppMainAMP.h"
#include "CSceneNodeAnimatorCameraTouchControl.h"
#include "FlutterDartFFI.h"
#if USE_IMGUI
#include "VulkanRenderer/base/VulkanUIOverlay.h"
#endif

using namespace irr;
using namespace irr::scene;
using namespace irr::video;
using namespace irr::core;
using namespace ualib;


#define CTRL_CAM1 (!IS_WIN)

static vector2di pickOfs[] = { 
	{-1,-1},{1,-1},{1,1},{-1,1},
	{0,-2},{2,0},{0,2},{-2,0},
	{-2,-2},{2,-2},{2,2},{-2,2}, 
	{0,-3},{3,0},{0,3},{-3,0},
	{-3,-3},{3,-3},{3,3},{-3,3},
};


SnArRoot::SnArRoot(ISceneNode* parent, ISceneManager* mgr, int id, ArRootParam pm)
	:SnArItem(parent, mgr, id,pm.aip)
{
#ifdef _DEBUG
	setDebugName("SnArRoot");
#endif
	setAutomaticCulling(scene::EAC_OFF);
	//arRoot = this;
	Pm = pm;
 
	pVp = pm.vp;
	Amp = pm.stage;

	Cs.lookAtCam = true;
#ifdef HAS_ARCORE
	Cs.hasARCore = 1 ;
#endif
	Cs.psCanMove = 1;
	Cs.psTrsSpace = 1;
	Cs.pnTrsSpace = 1;
	Cs.resetOrigin=1;
#if SVG_MMD_WRITE 
	//Cs.outputAlphaVideo = 1;
	Cs.mdHandWrite = 1;
#endif
	snSubArItemsRoot = SceneManager->addEmptySceneNode(this,-1);
	snSubArItemsRoot->paraChildrenAnimate = !SABA_ONE_WORLD && !SABA_USE_PHYSX;

	loadSettings();
#ifndef _DEBUG
	//Cs.mdHandWrite = jssARE.GetInt("mdHandWrite", 0);
#endif
	//snIPT = SceneManager->addCubeSceneNode(100);
	snMmdSpaceRoot = SceneManager->addEmptySceneNode(this, -1); snMmdSpaceRoot->setScale(MMD_SABA_SCALE);
}

SnArRoot::~SnArRoot()
{
	if (curArSn) curArSn->drop();
	saveSettings();
}

void SnArRoot::loadSettings()
{
	jssARE.SetFile(Ctx->getDataFilePath("settings.dat"), true);

	Cs.showModelIKPtr = &mmd->ikVisable;
 	Cs.showArPath = jssARE.GetInt("showArPath", 0);
	Cs.showGroundPlane = jssARE.GetInt("showGroundPlane", 0);
	Cs.scrOrnAllowLandscpae = jssARE.GetInt("scrOrnAllowLandscpae", 1);
	Cs.skyDomeR =   jssARE.GetInt("skyDomeR", -20000);
	Cs.skyDomeRttY = jssARE.GetDouble("skyDomeRttY",0.0);
	Cs.skyDomePath = ualib::Utf8toWcs(jssARE.GetString("skyDomePath", "data/360/venice.jpg"));

	jssARE.getFloats(jssARE.GetChild("lightPos"),Cs.lightPos,3);
}
void SnArRoot::saveSettings()
{
	jssARE.AddValueT("showModelIK", mmd->ikVisable);
	jssARE.AddValueT("showArPath", Cs.showArPath);
	jssARE.AddValueT("showGroundPlane", Cs.showGroundPlane);
	jssARE.AddValueT("scrOrnAllowLandscpae", Cs.scrOrnAllowLandscpae);
	//jssARE.AddValueT("mdHandWrite", Cs.mdHandWrite);
	jssARE.AddValueT("skyDomeR",  Cs.skyDomeR);
	jssARE.AddValueT("skyDomeRttY", Cs.skyDomeRttY);
	jssARE.AddValueT("skyDomePath", ualib::WcstoUtf8(Cs.skyDomePath));

	jssARE.setFloats(jssARE.GetChild("lightPos"), Cs.lightPos, 3);
	jssARE.SaveFile();
}

void SnArRoot::OnRegisterSceneNode()
{
	if (IsVisible)
		SceneManager->registerNodeForRendering(this);
	ISceneNode::OnRegisterSceneNode();
}

void SnArRoot::OnAnimate(u32 timeMs)
{
	ISceneNode::OnAnimate(timeMs);
	//if (Eqv->GetPPT()) Eqv->GetPPT()->PtScale=(getScale().X);
	if (snSticking) stickMoving(snSticking);

}

void irr::scene::SnArRoot::loadMMD(const saba::PMXFileCreateParam& fcp, uint32_t aiFlag) {
	bool isCharacter = aiFlag & 0x10;
	auto arRoot = this;
	irr::SEvent evo{ EET_CMD_INPUT_EVENT };
	auto& p = arRoot->Cs.icp;
	p.itemType = 1;
	p.itemSubType = SVG_MMD_WRITE ? 2 : 0;
	p.posType = 0;
	p.posFrontDistance = 6000;;

	evo.CmdInput.cmdId = 0x10000000 | cceItemNew;
	evo.CmdInput.pm1 = handleEditorCmd(evo.CmdInput);
	arRoot->curArSn->AIFlag = aiFlag;
	evo.CmdInput.cmdId = 0x10000000 | cceItemUpdate;
	CsEditorItem* ei = (CsEditorItem*)evo.CmdInput.pm1;
#if IS_WIN
	ei->pos.x = 0;
	ei->pos.y = 0;
	ei->pos.z = 0;
#else
	ei->pos.x = cc == 0 ? 0 : ualib::UaRandm1to1() * 500;
	ei->pos.y = cc == 0 ? 0 : -500;
	ei->pos.z = 000;
#endif
	handleEditorCmd(evo.CmdInput);


	io::path fp = fcp.filePath.c_str();
	if (fp.pathGetExt() == ".zip")
	{
		auto rf = Ctx->getFileSystem()->createAndOpenFile(fp);
		if (rf) {
			evo.CmdInput.cmdId = 20127;
			evo.CmdInput.pm1 = (uint64_t)malloc(rf->getSize());
			evo.CmdInput.pm2 = rf->getSize();
			rf->read((void*)evo.CmdInput.pm1, evo.CmdInput.pm2);
			rf->drop();
			handleEditorCmd(evo.CmdInput);
		}
	}
	else {
 
		mmd->Pm.spm.fcp = fcp;
		mmd->Pm.spm.fcp.phType = mmd->curPhyType;
		//fcp.massMul = mmd->sabas.size() == 0 ? 1.f : 0.1f;


		if (isCharacter) mmd->Pm.spm.clsType = ESabaClass::SabaBase;
		else if (mmd->Pm.spm.clsType == ESabaClass::SabaBase) mmd->Pm.spm.clsType = ESabaClass::SimpleItem;
		arRoot->onModelUploaded(mmd, nullptr, fp, arRoot->curArSn, false, aiFlag & 0x1010);
		arRoot->curArSn->modelFilePath = fp;
	}
}

void SnArRoot::updateMediaFrame(float timeS, float dtime)
{
	if (Cs.animEditOn) 	mmd->setOverrideTIme(Cs.animEditFrame*1000000/30, Ctx->gd.time, 0 );
	else if (pVp->working && pVp->recMode==10)
		mmd->setOverrideTIme(timeS * 1000000 , Ctx->gd.time, 0);
	else mmd->setOverrideTIme(-1, 0, 0);
	
	if (toLoadModel)
	{
		toLoadModel = false;
		loadStaticModelFromDataDir(leiBk.modelFile, nullptr);
	}

	snSubArItemsRoot->forEachChild([=](ISceneNode* childSn) {
		auto sn = static_cast<SnArItem*>(childSn);
		
		if (sn->toLoadModel) {
			sn->toLoadModel = false;
			if (sn->Cei->cp.itemSubType==0)
				loadPmxZipFromDataDir(sn->leiBk.modelFile, sn->leiBk.motionFile, sn);
			else 
				loadStaticModelFromDataDir(sn->leiBk.modelFile, sn);
			if (Cs.mdLoadingMax > 0) {
				Cs.mdLoadingIdx++;
				Dart::notifyUI(cceNtLoading, Cs.mdLoadingIdx == Cs.mdLoadingMax ? 0 : 1, 0);
			}
		}
		sn->updateMediaFrame(timeS, dtime);

		});

	//update for UI 
	{
		auto& vp = *pVp;
		Cs.opened = vp.working;
		Cs.playing = vp.paused || vp.stage() != 1 ? 0 : 1;
		Cs.renderring = renderring();
		Cs.durMs = (int)(vp.durationMs);
		Cs.posMs = (int)((Amp->editMode == 2 ? vp.recTimeS : vp.mediaTime) * 1000 + 0.5f);
		Cs.timeS = vp.mediaTime;
	}


}

void SnArRoot::onArDatLoaded(bool hasData)
{
	auto& vp = *pVp;
	if (hasData) {
		Cs.newVideo = 1;
		Cs.videoRotation = Eqv->ARCamRotation;
		if (Cs.videoRotation == 90 || Cs.videoRotation == 270) {
			Cs.width = vp.ivpm.h; Cs.height = vp.ivpm.w;
		}
		else {
			Cs.width = vp.ivpm.w; Cs.height = vp.ivpm.h;
		}
		DP(("New Video %d x %d     r %d", Cs.width, Cs.height, Cs.videoRotation));
	}
	else {
		Dart::notifyUI(cceGestureLongPress, 0, 0);//No data
	}
}

int64_t SnArRoot::handleEditorCmd(const irr::SEvent::SCmdInput& ce)
{
	auto& vp = *pVp;
	int cmd = ce.cmdId & 0x0FFFFFFF;
	switch (cmd)
	{
	case cceGetSharedDatPtr: {
		return (int64_t)&Cs;
	}

	case cceOnSettingChanged:{
		switch (ce.pm2)
		{
		case 11:
			Cs.lookAtCam = (Cs.lookAtCam+1)%2;
			snSubArItemsRoot->forEachChild([this](ISceneNode* childSn) {
				auto sn = static_cast<SnArItem*>(childSn);
				if (sn->sb) sn->sb->canLookAtCam = Cs.lookAtCam;
				});
			break;
		case 12:	Amp->snGrid->setVisible(arRoot->Cs.showGroundPlane);break;
		case 21:		setAddRttId();break;
		}		

	}break;
	case cceSetCamera: {
		Ctx->setCameraId(ce.pm1);
		camRttFocus();
	}break;
	case cceToggleArRes: {
#if HAS_ARCORE
		hello_ar::gAr->CamId++;
#endif
	}break;

	case cceFovGetSet: {
		if (ce.pm1 == 1)
			SceneManager->getActiveCamera()->setFOV(Cs.fovy*DEGTORAD);
		Cs.fovy = SceneManager->getActiveCamera()->getFOV() * RADTODEG;
		return (int64_t)&Cs;
	}


	case cceStartDecode:case cceStartConvert: {
		if (ce.pm2) {
			Amp->inVFilePtr = (uint8_t*)ce.pm1;
			Amp->inVFileSize = (size_t)ce.pm2;
			Amp->startDecVideo(cmd == cceStartConvert ? 2 : 1, 0);
		}
		else {
			Amp->inVFilePtr = 0;
			Amp->inVFileSize = 0;
			if (ce.pm1)	Amp->curInVideo = (const char*)ce.pm1;
			Amp->startDecVideo(cmd == cceStartConvert ? 2 : 1, 0);

		}

		
	} break;
 
	case cceStartPreview: {	//start dec		
		Amp->inVFilePtr = 0;
		Amp->inVFileSize = 0;

		Amp->startDecVideo(1, 0);
		//editMode=2;
		//startDecVideo();
	} break;
	case cceStopVideo: {
		Amp->stopRecord();

	} break;
	case ccePlayPause:	vp.paused = ce.pm1 ? 0 : 1;
		if (!vp.working)
			Amp->startDecVideo(1, 0.f);
		break;
	case cceSeekTo1W:	Amp->mediaSeekToPercent(ce.pm1 / 10000.0);	break;//set media pos

	case cceReloadStyle: 
		//Eqv->recreateOnUpdate(); 
		mmd->paraRender = !mmd->paraRender;		DP(("para %d",mmd->paraRender));
		break;

	case cceSaveCam: {
		//if (vp.stage() == 1)			saveCam(SceneManager->getActiveCamera());
	}break;
	case cceStartWaterMark: Amp->drawWaterMarkCC = 0;break;
	case cceUseMidRT: Ctx->useMidRT = ce.pm1; break;
		
	//ITEM
	case cceItemRoot: { 
		ArSnToCs(this, Cei);
		return (int64_t)Cei;
	}
	case cceItemNew: {// cmdItemNew
		auto p = new CsEditorItem();		
		initCsEditorItem(p);
			p->startS = 0;// vp.mediaTime;
			p->endS = 999;// vp.durationMs / 1000.f - 0.0001f;
		auto sn=newArSn(p);
		if (getItemCount() > 1) {
			auto sn0 = (*snSubArItemsRoot->getChildren().begin());
			sn->setScale(sn0->getScale().X);			
			sn->setPosY(sn0->getPosition().Y);
		}
		ArSnToCs(sn, p);
		return (int64_t)p;
	}
	case cceItemCopyAnchor: {// cmdItemNew
		auto p = new CsEditorItem();		
		initCsEditorItem(p);
		p->startS = 0;// vp.mediaTime;
		p->endS = 999;// vp.durationMs / 1000.f - 0.0001f;
		auto old = curArSn;
		auto sn=newArSn(p);
		if (getItemCount() > 1) {
			auto sn0 = (*snSubArItemsRoot->getChildren().begin());
			sn->setScale(old->getScale().X);
			sn->setPosition(old->getPosition());
			sn->setRotation(old->getRotation());
		}
		ArSnToCs(sn, p);
		return (int64_t)p;
	}
	case cceItemDel: {
		CsEditorItem* p = (CsEditorItem*)ce.pm1;
		if (p->sn == curArSn)setCurSn(nullptr);
		static_cast<scene::ISceneNode*>(p->sn)->remove();

	}break;
	case cceItemClear: {
		snSubArItemsRoot->removeAll(); setCurSn(nullptr);
	}break;
	case cceItemUpdate: {
		CsEditorItem* p = (CsEditorItem*)ce.pm1;
		//if (p->label)
		{
			DP(("update item %s x=%d", p->label, p->pos.x));
			ISceneNode* sn = (ISceneNode*)p->sn;
			sn->setPosition(p->pos.x, p->pos.y, p->pos.z);
			sn->setRotation({ p->rtt.x, p->rtt.y, p->rtt.z });
			if (p->lockScale)
				sn->setScale({ p->scl.x, p->scl.x, p->scl.x });
			else
				sn->setScale({ p->scl.x, p->scl.y, p->scl.z });			
			onItemUpdated(p, sn, ce.pm2);
		}
		return (int64_t)p;
	}
	case cceItemConverted: {
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
		sn->itemTypeChanged();
		DP(("LSL %p",sn));
	}break;	
	case cceItemSelect: {
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
		setCurSn(sn);
		curArSn->onSelected();
		DP(("LSL %p",sn));
	}break;
	case cceItemSetValue: {
		assert(curArSn);
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;		 
		DP(("cceItemSetValue %X",(u32)ce.pm2));
		sn->setValue(ce.pm1, ce.pm2);
	}break;
	case cceItemGetValue: {
		assert(curArSn);
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
		DP(("cceItemSetValue %X", (u32)ce.pm2));
		return sn->getValue(ce.pm1, ce.pm2);
		
	}break;
	case cceItemCopyValue: {
		assert(curArSn);
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
		switch (ce.pm2)
		{
		case 0x3: {
			auto pos = CopyPos, rtt = CopyRtt;
			matrix4  m;
			m=sn->getAbsoluteTransformation();
			CopyPos = m.getTranslation();
			CopyRtt = m.getRotationDegrees();
			//CopyScale = m.getScale();
			ArSnToCs(sn,p);
		}break;
		case 0x101: if (curPickNode.sbNode) {
			auto pos = CopyPos, rtt = CopyRtt;
			matrix4  m;
			m = sn->sb->getAbsoluteTransformation()*curPickNode.sbNode->GetGlobalTransform();
			CopyPos = m.getTranslation();
		}break;
		}
		return sn->getValue(ce.pm1, ce.pm2);
		
	}break;
	case cceItemPasteValue: {
		assert(curArSn);
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
		switch (ce.pm2)
		{
		case 0x3:case 0x7: {
			auto pos = CopyPos, rtt = CopyRtt;
			matrix4 mi, m; m.setTranslation(CopyPos); m.setRotationDegrees(CopyRtt);
			sn->getParent()->getAbsoluteTransformation().getInverse(mi); m = mi * m;
			sn->setPosition(m.getTranslation());
			sn->setRotation(m.getRotationDegrees());
			ArSnToCs(sn,p);
		}break;
		case 0x101: if (curPickNode.sbNode) {
			sn->sb->setNodeRealPos(curPickNode.sbNode, CopyPos);
		}break;
		}
		return sn->getValue(ce.pm1, ce.pm2);
		
	}break;
	case cceItemRotateByCam:
	{
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
		if (p->root) {
			sn->setPosition({0,0,0}); sn->setRotation({ 0,0,0 }); sn->setScale({ 1,1,1 });
		}
		else {
			sn->rotateByItemRttType();
		}
		ArSnToCs(sn, p);
	}
	break;	
	case cceItemModelReset:
	{
		if (ce.pm2 & 0x10 ) {
			resetPickingNodeAnimationTR(ce.pm2);
			break;
		}
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
		if (!sn->sb) break;
		if (ce.pm2 & 0x100) {
			sn->setPosition({ 0,0,0 });
			sn->setRotation({ 0,0,0 });
			sn->setScale(1);
			ArSnToCs(sn, p);
		}
		else  {
			sn->sb->resetAnimation();
			sn->sb->loadPose("",-1);
			sn->sb->setPlaying(false);
			sn->sb->Pmx->InitializeAnimation();
			sn->sb->Pmx->ResetPhysics();
		}
		ArSnToCs(sn, p);
	}
	break;	
	case cceItemLoadPmxZip:
	{		
		auto sn = curArSn;
		std::string fn((char*)ce.pm1);
		Cs.mdLoadingIdx = Cs.mdLoadingMax = 0;
		sn->leiBk.modelFile = fn;
		sn->toLoadModel = true;		
		//std::thread t1([this,fn,sn]() {
		//	UP_LOCK_GUARD(*Ctx->getLib()->getLock());
		//	loadPmxZipFromDataDir(fn,"", sn);
		//	});	 t1.detach();
	}break;
	case cceItemLoadStaticModel:
	{		
		auto sn = curArSn;
		std::string fn((char*)ce.pm1);
		if (ce.pm2!=999) {
			Cs.mdLoadingIdx = Cs.mdLoadingMax = 0;
			sn->leiBk.modelFile = fn;
			sn->toLoadModel = true;
		}
		else {
			leiBk.modelFile = fn;
			toLoadModel = true;
		}

	}break;

	case cceItemTapOnScr:
	{
		ptDown = false; ptScaling = false;
		int view = 0;
		u32 pickdata = pickPoint(Cs.ptX + 0.5, Cs.ptY + 0.5,view);
		u32 pick, vtxid;	pickGetResult(pickdata, pick, vtxid);

		if (pick > 80) {
			DP(("Picked %d - %d",pick, vtxid));
			SnArItem* snPick{}; forEachArChild([=,&snPick](SnArItem* sn) {if (sn->getMaterial(0).PickColor == pick) {snPick = sn;}});
			if (snPick) {
				if (snPick!=curArSn) Dart::notifyUI(cceItemSelect, (int64_t)snPick->Cei, 0); //select model
			}
		}
		else if (pick == EPD_PointCloud) {
			auto snm = vp.snPly;
			if (!snm) {
				//SnArItem* snPick{};
				//forEachArChild([=, &snPick](SnArItem* sn) {
				//	if (sn->snMesh && sn->snMesh->getMaterial(0).PickColor == pick) {
				//		snPick = sn;
				//	}});			
				//if (!snPick) break;
				snm = snPointCloud;// snPick->snMesh;
				if (!snm) break;
			}
			DP(("Picked Cloud  %d of %d.%d", vtxid, snm->getMesh()->getMeshBufferCount(), snm->getMesh()->getMeshBuffer(0)->getVertexCount())); assert(vtxid != 0);
			int cldId=vtxid-1;
			
			auto cldpt=(S3DVertex*)snm->getMesh()->getMeshBuffer(0)->getVertices();
			vector3df pickPos= cldpt[cldId].Pos;
			snm->getAbsoluteTransformation().transformVect(pickPos);
			Eqv->LaunchFw3D(pickPos, Eqv->getFwIdxByFwIdStr(1, "fwxxE"), { 0,0,0 }, SColorf(0xCFFF2020));
			CopyPos = pickPos;
			Dart::notifyUI(cceNtItemValueCopied, 1, 0);
		}
		else if (ce.pm1)  //item 
		{
			CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
			if (Cs.curVecMode == 1 && sn->ptSpd.getLengthSQ() > 100.f) {
				sn->ptSpd.set(0, 0, 0);
				break;
			}
			if (p->root) throw;
			else {
				plane3df pln(vector3df(0, Cs.tapOnGroundY? sn->getAbsolutePosition().Y:0.f, 0), vector3df(0, 1, 0));
				vector3df ipt;

				if (Ctx->getScrPtIntersectionOnPlane(Cs.ptX, Cs.ptY, pln, ipt, SceneManager->getActiveCamera()))
				{
					if (snIPT) snIPT->setPosition(ipt);

					//ipt.Y = sn->getPosition().Y;
					float dis = ipt.getDistanceFrom(SceneManager->getActiveCamera()->getAbsolutePosition());
					if (100 < dis && dis < 100000) {
						matrix4 mi;
						getAbsoluteTransformation().getInverse(mi);
						mi.transformVect(ipt);
						CopyPos = ipt;
						Dart::notifyUI(cceNtItemValueCopied, 1, 0);
						//sn->setPosition(ipt);
					}
				}
			}
			//ArSnToCs(sn, p);
		}
	}
	break;

	case cceGestureTapDown:{
		ptDown = true;
		DP(("cceGestureTapDown"));
		ptScaling = true;// ptSpd.set(0, 0, 0); 
	}	break;

	case cceGestureScaleStart:{
		ptDown = false;
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
		auto& gt = Cs.gts;
		gstScale = sn->getScale().X;
		if (Ctx->gd.CamRtt)
			gstZoomPow=((CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin())->tld.zoomPow;
		ArSnToCs(sn, p, -1);		
	}	break;
	case cceGestureScaleEnd: {
		CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
		ArSnToCs(sn, p, 1);
		ptScaling = false;
	}	break;
	case cceGestureScaleUpdate: onGeatureScaleUpdate(ce); break;
	case cceGesturePad2ScaleUpdate:	onGestureRtt((CsEditorItem*)ce.pm1); break;

	case ccePickGetInfo: {
		Cs.pnCanMove = 0;
		if (curPickNode.sbNode) {
			Cs.mdCurNodeName = curPickNode.sbNode->GetName().c_str();
			Cs.pnCanMove = curPickNode.sbNode->flMove ? 1 : 0;			
		}
		else	Cs.mdCurNodeName = "(null)";		
	}break;

	//SCENE node trs
	case ccePickSetPsTrs: onPickSetPsTrs(ce); break;
	case ccePickSetPsRtt: onPickSetPsRtt(ce); break;
	case ccePickSetPsScl: onPickSetPsScl(ce); break;
	//MMD node trs
	case ccePickSetPnTrs: onPickSetPnTrs(ce, Cs.pnTrsSpace); break;
	case ccePickSetPnRtt: onPickSetPnRtt(ce, Cs.pnTrsSpace); break;
	case ccePickSubNodesRcsRtt: rotateSubPickNodes(Cs.pnRtt.y * core::DEGTORAD); break;
	case cceGyroscope: 		if (curArSn) 	onCceGyroScope();		break;
	case cceGetNodeBtnList: {		buildNodeButtonList();	}break;
	case cceSetNodeBtnHoverId: onSelectNodeButtonList(ce.pm1,ce.pm2); break;
	case ccePickVtxHold:  curArSn->sb->Pmx->clearVtxPickNodes(); break;
	case ccePickSetBonePhyActive: if (curPickNode.sbNode ) {
		auto rb = curPickNode.sbNode->rb0;
		if (rb && rb->dynRbType==1) {
			rb->SetActivation(!rb->GetActivation());
		}
	}break;
	case cceLaunchPhyObj: { PhyObjParam pm;  pm.tgtSb->Pom->addObj(pm); } break;
	case cceSetTmpIkMode: {
		if (ce.pm1)	tempIkMode = true;
		else {
			tempIkMode = false;
			mmd->tempIKRoot = nullptr;
		}
	}break;
	case cceSetArCamRoot: {
		Amp->setAROriginPoint = true;
#if HAS_ARCORE
		hello_ar::gAr->attiHit.pos=glm::vec3(0,0,0);
#endif
	}break;
	//Animation Edit
	case cceAniKeyFrameSet: if (curPickNode.sbNode ) {
		curPickNode.sn->sb->aniSetNodeKeyFrame(curPickNode.sbNode,Cs.animEditFrame);
	}break;
	case cceAniKeyFrameGet: if (curPickNode.sbNode && curPickNode.sbNode->nodeCtrl) {
		return curPickNode.sbNode->nodeCtrl->getKeyAt(ce.pm1)?1:0;
	}break;
	case cceAniKeyFrameDel: if (curPickNode.sbNode && curPickNode.sbNode->nodeCtrl) {
		if (curPickNode.sbNode->nodeCtrl->delKeyAt(ce.pm1)) 	return 1;		
	}break;
	case cceSaveSettings:
		saveSettings();
		break;
	case cceSaveItems:
		saveItems();
		break;
	case cceSaveVMD:	{			
		 curArSn->sb->saveVmd(  Ctx->getTempFilePath("save.vmd") );
		 Cs.vpdSaveString = 0;
		 Cs.vpdSaveStringLength = 0;
	}	break;
	case cceSaveVPD:	{	
		auto s = curArSn->sb->Pmx->getPoseVPD();
		if (Cs.vpdSaveString) freeUtf8String(Cs.vpdSaveString);
		Cs.vpdSaveString = newUtf8String(s);
		Cs.vpdSaveStringLength = s.size();
	}	break;
	case cceLoadItemsStart: {
		uiLoadItemIdx = 0; uiLoadModelNum = 0; Cs.mdLoadingMax=Cs.mdLoadingIdx = 0;
		//loadEIs.clear();
		Dart::notifyUI(cceNtLoading, true, 0);
		loadItems(ce.pm1==0?Cs.jsonItemsLoadString:"", ce.pm1);
	}break;
	case cceLoadItemNew: {
		if (loadEIs.size() < 1 || uiLoadItemIdx >= loadEIs.size()) {
			if (Cs.mdLoadingMax==0)	Dart::notifyUI(cceNtLoading, false, 0);
			DP(("cceLoadItemNew %d END", uiLoadItemIdx));
			return 0;
		}
		DP(("cceLoadItemNew %d", uiLoadItemIdx));
		auto p = new CsEditorItem();
		auto& lei = loadEIs[uiLoadItemIdx];
		*p = lei.cei;
		Cs.icp = p->cp;
		auto sn=newArSn(p);
 
		ArSnToCs(sn, p);
		if (lei.modelFile.size() > 4 && lei.modelFile.substr(lei.modelFile.size() - 4) == ".zip")
		{
 
			uiLoadModelNum++;
			Cs.mdLoadingMax++;
			sn->leiBk = lei;
			sn->toLoadModel = true;
 
		}


		uiLoadItemIdx++;
		return (int64_t)p;
	
	}break;

	}

	return 0;
}

void SnArRoot::onGeatureScaleUpdate(const irr::SEvent::SCmdInput& ce)
{
	ptScaling = true;
	CsEditorItem* p = (CsEditorItem*)ce.pm1; SnArItem* sn = (SnArItem*)p->sn;
	auto& gt = Cs.gts;
	Ctx->gd.ptCount = gt.scPointCount;
	if (snPickMap.size() > 0) {  //SWIPE PICK

	}
	else if (gt.scPointCount == 2) {

		if (Ctx->getCameraId() == 0)
			sn->setScale(gstScale * gt.scale);
		else if (Ctx->getCameraId() == 1) {
			if (snPickMap.size() == 0) {
				auto anm = ((CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());
				anm->tld.zoomPow = gstZoomPow / sqrt(gt.scale);
			}
		}
		Ctx->getEvtRcv()->setCurPtsFlag(IEventReceiver::ef_2ptScaled);
	}
	else if (gt.scPointCount == 1 && (Ctx->getCameraId() != 1)) {
		Eqv->LaunchFw3D(Ctx->getPointerHitRealPos(), Eqv->getFwIdx(2, 0), { 0,0,0 });
		if (Cs.curVecMode == 1 || gt.scStartY>Ctx->gd.scrHeight * 0.9f)  //ROTATE
		{
			onGestureRtt(p);
		}
		else if (!Cs.pickPhysics)//TRANSLATE
		{
			auto pos = sn->getAbsolutePosition();
			vector3df ofs(gt.scDX, -gt.scDY, 0);
			if (Cs.curPlaneMode != 0)
				ofs = vector3df(gt.scDX, 0, -gt.scDY);
			auto cam = SceneManager->getActiveCamera();
			{
				matrix4 cvm, cvmi;
				auto snPos = sn->getAbsolutePosition();
				plane3df pln(snPos, (cam->getTarget() - cam->getAbsolutePosition()).normalize());
				vector3df ipt;
				if (Ctx->getScrPtIntersectionOnPlane(Cs.ptX, Cs.ptY, pln, ipt, cam))
					snPos = ipt;
				float dis = cam->getAbsolutePosition().getDistanceFrom(snPos); //headPos.Z/2;// cam->getAbsolutePosition().getDistanceFrom(mmd->sabas[0]->camTgt->getAbsolutePosition());
				cvm = cam->getViewMatrix();	cvm.transformVect(pos);//to scr space
				pos += ofs * 2 * dis / cam->getScreenPlaneDistance(Ctx->gd.scrHeight); //move
				cvm.getInverse(cvmi); cvmi.transformVect(pos);//to world space
				if (sn->arRoot) {
					matrix4 mi;	getAbsoluteTransformation().getInverse(mi);
					mi.transformVect(pos);//to arRoot space
				}
			}
			sn->setPosition(pos);
		}
	}
	ArSnToCs(sn, p, 0);
}

void SnArRoot::onPickSetPsScl(const irr::SEvent::SCmdInput& ce)
{
	if (curArSn) {
#if PS_TO_MOVE_ROOT_NODE 
#if 0
		if (ce.pm1 == 1) {
			curArSn->saba->Pmx->rootSc *= (1.f + Cs.pnScl.x / 100.f);
		}
		else curArSn->saba->Pmx->rootSc = { 1,1,1 };
#else
	auto nm = curArSn->sb->Pmx->GetNodeManager();
	auto nd = nm->getRootNode();;
	if (curPickNode.sbNode != nd) {
		curPickNode.sbNode = nd;
		curPickNode.sn = curArSn;
		setLastPickNode(curPickNode);
	}
	onPickSetPnScl(ce, Cs.psTrsSpace);
	return;
#endif
#else

		if (ce.pm1 == 1) {
#if CTRL_CAM1
			if (Ctx->getCameraId() == 1)
			{
				auto anm = ((CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());
				anm->ZoomPow -= Cs.pnScl.x / 1000;
			}
			else
#endif
				curArSn->setScale(curArSn->getScale() * (1.f + Cs.pnScl.x / 100.f));
		}
		else curArSn->setScale(1);
		ArSnToCs(curArSn, curArSn->Cei, 0);

#endif
	}
}

void SnArRoot::onPickSetPsRtt(const irr::SEvent::SCmdInput& ce)
{
#if PS_TO_MOVE_ROOT_NODE 
	auto nm = curArSn->sb->Pmx->GetNodeManager();
	auto nd = nm->getRootNode();;
	if (curPickNode.sbNode != nd) {
		curPickNode.sbNode = nd;
		curPickNode.sn = curArSn;
		setLastPickNode(curPickNode);
	}
	onPickSetPnRtt(ce, Cs.psTrsSpace);
 
#else
	if (curArSn && (!CTRL_CAM1 || Ctx->getCameraId() != 1)) {
		if (ce.pm1 == 1) {
			if (Cs.psTrsSpace == 0 || Cs.psTrsSpace == 1) {
				auto rtt = curArSn->getRotation();
				rtt += glm::vec3(Cs.pnRtt.x, Cs.pnRtt.y, Cs.pnRtt.z) / 10.f;
				curArSn->setRotation(rtt);
			}
			else {
				core::vector3df addpos(Cs.pnRtt.x, Cs.pnRtt.y, Cs.pnRtt.z);
				curArSn->rotateInCamSpace(Cs.psTrsSpace, addpos * core::DEGTORAD);
			}
		}
		else {
			auto pos = curArSn->getPosition();
			if (ce.pm2 == 1) pos.X = 0;
			if (ce.pm2 == 2) pos.Y = 0;
			if (ce.pm2 == 3) pos.Z = 0;
			curArSn->setPosition(pos);
		}
		ArSnToCs(curArSn, curArSn->Cei, 0);
	}
#endif
}

void SnArRoot::onPickSetPsTrs(const irr::SEvent::SCmdInput& ce)
{
#if PS_TO_MOVE_ROOT_NODE 
	auto nm = curChar()->sb->Pmx->GetNodeManager();
	auto nd = nm->getRootNode();;
	if (curPickNode.sbNode != nd) {
		curPickNode.sbNode = nd;
		curPickNode.sn = curArSn;
		setLastPickNode(curPickNode);
	}
	onPickSetPnTrs(ce, Cs.psTrsSpace);
	return;
#else
	if (curArSn) {
		if (ce.pm1 == 1) {
			core::vector3df addpos(Cs.pnTrs.x, Cs.pnTrs.y, Cs.pnTrs.z);
#if CTRL_CAM1
			if (Ctx->getCameraId() == 1)
			{
				auto ani = (*Ctx->gd.CamRtt->getAnimators().begin());
				ani->setTargetOffset(-addpos * 5);
			}
			else
#endif
			{
				curArSn->translateInCamSpace(Cs.psTrsSpace, addpos * 5);
			}
		}
		else {
#if CTRL_CAM1
			if (Ctx->getCameraId() != 1)
#endif
			{
				auto pos = curArSn->getPosition();
				if (ce.pm2 == 1) pos.X = 0;
				if (ce.pm2 == 2) pos.Y = 0;
				if (ce.pm2 == 3) pos.Z = 0;
				curArSn->setPosition(pos);
			}
		}
		ArSnToCs(curArSn, curArSn->Cei, 0);
	}
#endif
}

void SnArRoot::onPickSetPnTrs(const irr::SEvent::SCmdInput& ce, int space)
{
	if (curPickNode.sbNode) {
		if (ce.pm1 == 1) {			
			core::vector3df addpos(Cs.pnTrs.x, Cs.pnTrs.y, Cs.pnTrs.z);
			if (curPickNode.sn->sb) curPickNode.sn->sb->translateNodeInCamSpace(curPickNode.sbNode, space, addpos);
		}
		else {
			auto pos = curPickNode.sbNode->GetAnimationTranslate();
			if (ce.pm2 == 1) pos.x = 0;
			if (ce.pm2 == 2) pos.y = 0;
			if (ce.pm2 == 3) pos.z = 0;
			curPickNode.sbNode->SetAnimationTranslate(pos);
		}
	}
}

void SnArRoot::onPickSetPnRtt(const irr::SEvent::SCmdInput& ce, int space)
{
	if (curPickNode.sbNode) {
		auto nd = curPickNode.sbNode;
		if (ce.pm1 == 1)
		{
			if (nd->flFixedAxis) {
				auto gq = nd->GetAnimationRotate();
				glm::vec3 rtt{ Cs.pnRtt.x * core::DEGTORAD, Cs.pnRtt.y * core::DEGTORAD, Cs.pnRtt.z * core::DEGTORAD };
				glm::quat m = glm::rotate(glm::quat(1.0f, 0, 0, 0), rtt.x + rtt.y, nd->fixedAxis);
				nd->SetAnimationRotate(gq * m);
			}
			else if (space == 0)
			{
				auto gq = nd->GetAnimationRotate();
				{
					glm::vec3 rtt{ Cs.pnRtt.x * core::DEGTORAD, Cs.pnRtt.y * core::DEGTORAD, Cs.pnRtt.z * core::DEGTORAD };
					nd->SetAnimationRotate(gq * glm::quat(rtt));
				}
			}
			else
			{
				core::vector3df addpos(Cs.pnRtt.x * core::DEGTORAD, Cs.pnRtt.y * core::DEGTORAD, Cs.pnRtt.z * core::DEGTORAD);
				curPickNode.sn->sb->rotateNodeInCamSpace(nd, space, addpos);
			}
		}
		else
		{
			auto gq = curPickNode.sbNode->GetAnimationRotate();
			glm::vec3 rtt = glm::eulerAngles(gq);
			if (ce.pm2 == 1) rtt.x = 0;
			if (ce.pm2 == 2) rtt.y = 0;
			if (ce.pm2 == 3) rtt.z = 0;
			curPickNode.sbNode->SetAnimationRotate(rtt);
		};
	}
}

void irr::scene::SnArRoot::onPickSetPnScl(const irr::SEvent::SCmdInput& ce, int space)
{
	if (!curPickNode.sbNode) return;
	auto nd = curPickNode.sbNode;
	if (ce.pm1 == 1) {

		nd->SetInitialScale(nd->GetInitialScale() * (1.f + Cs.pnScl.x / 100.f));
	}
	else nd->SetInitialScale({1,1,1});
}

void irr::scene::SnArRoot::OnUpdateUIOverlay(vks::UIOverlay* ol)
{
	using namespace glm;
	if (!Driver->renderUI) return;
#if USE_IMGUI
	{
		using namespace ImGui;
		if (!Begin("AR")) {
			End(); return;
		}
		Checkbox("Gizmo", &showGizmo);
		ImGui::BeginTabBar("SequencerTabs");
		if (ImGui::BeginTabItem("AR")) {
			if (auto pickNode = curPickNode.sbNode) {
				PushStyleColor(ImGuiCol_Text, ImVec4(0.5, 1, 0, 1));
				Text(pickNode->GetName().c_str());
				PopStyleColor();
				auto pn = pickNode->GetParent();
				
				if (Button("Root.N")) {
					curPickNode.sn = curArSn;
					curPickNode.sbNode = curArSn->sb->Pmx->nodeRoot;
					setLastPickNode(curPickNode);

				};
				if (pn && (SameLine(), Button("P.N"))) {
					curPickNode.sn = curArSn;
					curPickNode.sbNode = pn;
					setLastPickNode(curPickNode);

				};
				if (pickNode->rb0 && pickNode->rb0->parentRb) {
					SameLine();
					if ( Button("P.R")) {
						auto pn = pickNode->rb0->parentRb->node;
						if (pn && pn->rb0) {
							curPickNode.sn = curArSn;
							curPickNode.sbNode = pn;
							setLastPickNode(curPickNode);
						}
					}
					float den = pickNode->rb0->density1;
					if (DragFloat("density", &den, 0.1f, 1.f, 1000.f)) {
						pickNode->rb0->density1 = den;
						pickNode->rb0->updateMassOnDensity();
					}
					
				}
				auto isL = pickNode->GetNameU()[0] == L'左';
				if ((isL || pickNode->GetNameU()[0] == L'右') && (Button("L|R"))) {
					std::wstring symmetryName = isL? L"右":L"左";
					symmetryName = symmetryName + pickNode->GetNameU().substr(1);
					auto pn = pickNode->model->GetNodeManager()->FindNode(symmetryName);
					if (pn  ) {
						curPickNode.sn = curArSn;
						curPickNode.sbNode = pn;
						setLastPickNode(curPickNode);
					}
				};
				// Create child nodes combobox
				if (pickNode->GetChild()) {
					std::vector<const char*> childNodeNames;
					std::vector<MMDNode*> childNodes;
					for (auto childNode = pickNode->GetChild(); childNode; childNode = childNode->GetNext()) {
						childNodeNames.push_back(childNode->GetName().c_str());
						childNodes.push_back(childNode);
					}

					int childNodeIndex = -1;
					if (ListBox("##Child Nodes", &childNodeIndex, childNodeNames.data(), childNodeNames.size())) {
						curPickNode.sn = curArSn;
						curPickNode.sbNode = childNodes[childNodeIndex];
						setLastPickNode(curPickNode);
					}
				}

			}
			ImGui::EndTabItem();
		}//tab AR
		if (ImGui::BeginTabItem("TR")) {
			if (auto pickNode = curPickNode.sbNode) {
				auto pos = pickNode->GetAnimationTranslate();
				auto rtt = pickNode->GetAnimationRotate(); 
				if (DragFloat3("Pos", &pos.x, 0.1f)) {
					pickNode->SetAnimationTranslate(pos);
				}
				if (DragFloat3("Rtt", &rtt.x, 0.1f)) {
					pickNode->SetAnimationRotate(rtt);
				} 
				auto pm = pickNode->pmxBone->anim;
				if (pm && pm->aniFlap) {
					DragFloat3("flapAm", &pm->flapAngTmul[0], 0.1f); 
					DragFloat3("flapAxis", &pm->flapAxis[0], 0.1f);
					pm->flapAxis = glm::fastNormalize(pm->flapAxis);
					SliderFloat("flapMul", &pm->flapPM[0], 0, 10);
					SliderFloat("flapT", &pm->flapPM[1], 0, 10);
 
				}
			}
			ImGui::EndTabItem();
		}//tab Seq
		if (ImGui::BeginTabItem("Seq")) {
			if (auto sb = curAiChar->sb) {
				if (ImGui::Button("Reset")) {
					mmd->MPA = mmd->mainMPA;
				}
				if (sb->sbMpa && sb->sbMpa->mpaMap.size()) {
					if (ImGui::BeginListBox("mpaFiles")) {
						for (const auto& name : sb->sbMpa->mpaMap) {
							if (ImGui::Selectable(name.first.c_str(), mmd->MPA->curFilePath == name.first)) {
								mmd->MPA = sb->sbMpa->mpaMap[name.first].mpa.get();
								mmd->MPA->createUI();
							}
							if (ImGui::IsItemHovered())
							{
								ImGui::SetTooltip(name.first.c_str());
							}
						}
						ImGui::EndListBox();
					}					
				}

			}
			ImGui::EndTabItem();
		}//tab Seq

		if (ImGui::BeginTabItem("Tmp")) {
			if (auto sb = curAiChar->sb) {
				ImGui::Text("%.3f", sb->Pmx->wingFlapTMul);

			}
			ImGui::EndTabItem();
		}//tab Seq
		ImGui::EndTabBar();

		End();
	}
	if (showGizmo   && curPickNode.sbNode && setPickReleased && mmd->ikVisable)
	{ 
		//ImGuizmo::SetOrthographic(false);
		
		auto node = curPickNode.sbNode;
		auto sb = curPickNode.sn->sb;
		auto& ui = *ol;
		auto cam = SceneManager->getActiveCamera();
		glm::mat4 viewMatrix = cam->getViewMatrix(), projMatrix = cam->getProjectionMatrix();

		glm::mat4 m;
		vec3 pos = node->AnimateTranslate();
		quat rtt = node->AnimateRotate();
		m = glm::translate(mat4(1), pos) * glm::mat4(rtt);
		 
		auto basemat = node->GetParent() ? node->GetParent()->mGlobalAnim : mat4(1);
		mat4 irm = sb->mmd2irr(basemat * m);
		// 绘制Gizmo

		if (gizmoType == 0 && !node->flMove) gizmoType = 1;
		ui.setGizmoMode(gizmoType, gizmoGlobalMode?1:0);
		ui.drawGizmo(viewMatrix, projMatrix, irm); 

		//ol->showTransformEditor("Object Transform", viewMatrix, projMatrix, irm);
		m = sb->irr2mmd(irm);
		// 检查Gizmo是否被使用
		if (ol->isUsingGizmo()) {
			// matrix已被更新，应用到你的对象上   
			auto lm = glm::inverse(basemat) * m;
			pos = lm[3];
			rtt = glm::quat_cast(lm);			
			node->SetAnimationTranslate(pos-node->GetTranslate());
			node->SetAnimationRotate(rtt);
			if (mmd->syncMovNode) for (auto sb1 : mmd->sabas) {
				if (sb1 != sb) {
					auto nd = sb1->findNode(node->GetNameU());
					nd->SetAnimationTranslate(node->GetAnimationTranslate());
					nd->SetAnimationRotate(node->GetAnimationRotate());
				}
			}
			core::matrix4 mm = m;
			if (abs(mm.getScale().x - 1.f) > 0.001f)
			{
				node->SetScale(mm.getScale());
			}
			
		}


		//ImGuizmo::SetDrawlist();
		
		ui.setNextWinPosFrom3D(irm[3], viewMatrix, projMatrix);
		ImGui::Begin("##GizmoLabel", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoMouseInputs);
		ImGui::TextColored({1,1,0,1}, node->GetName().c_str());

		ImGui::End();

	}


#endif
}

void irr::scene::SnArRoot::switchGizmoMode()
{
	if (!curPickNode.sbNode) return;
	auto node = curPickNode.sbNode;
	gizmoType = (gizmoType + 1)%3;


}

void irr::scene::SnArRoot::switchGizmoGL()
{
	gizmoGlobalMode =!gizmoGlobalMode;
}

void SnArRoot::onCceGyroScope()
{
	core::vector3df addpos(-Cs.gts.gyroscope[0], -Cs.gts.gyroscope[1], Cs.gts.gyroscope[2]);
	addpos *= 0.2f;
	if (auto nd = curPickNode.sbNode) {
		if (nd->flFixedAxis) {
			auto gq = nd->GetAnimationRotate();

			glm::quat m = glm::rotate(glm::quat(1.0f, 0.f, 0.f, 0.f), addpos.X + addpos.Y, nd->fixedAxis);
			nd->SetAnimationRotate(gq * m);
		}
		else if (Cs.pnTrsSpace == 0)
		{
			auto gq = nd->GetAnimationRotate();
			nd->SetAnimationRotate(gq * glm::quat(addpos));
		}
		else
		{
			curPickNode.sn->sb->rotateNodeInCamSpace(nd, Cs.pnTrsSpace, addpos);
		}
	}
	else {

		//curArSn->rotateInCamSpace(Cs.psTrsSpace, addpos );
		auto rtt = curArSn->getRotation();
		rtt += addpos * core::RADTODEG;
		curArSn->setRotation(rtt);
	}
}

void SnArRoot::camRttFocus( )
{
	if (Ctx->getCameraId() == 1 && curArSn) {
		matrix4 tgt;
		getFocusPos(tgt);
		(*Ctx->gd.CamRtt->getAnimators().begin())->setTarget(tgt.getTranslation());
	}
}

void irr::scene::SnArRoot::getFocusPos(irr::core::matrix4& tgt)
{
	if (curArSn->sb && curPickNode.sbNode) {
		auto m = curArSn->sb->getAbsoluteTransformation() * curPickNode.sbNode->GetGlobalTransform();
		tgt= m;
	}
	else tgt= curArSn->getModelAbsCenter();
}


void SnArRoot::onGestureRtt(const CsEditorItem *cei) const
{
	auto& gt = Cs.gts;
	SnArItem* sn = (SnArItem*)cei->sn;
	vector3df rtt;// = sn->getRotation();
	//DP(("DXY %8.3f,%8.3f", gt.scDX, gt.scDY));
	rtt.Y =- gt.scDX ;

	float dx = abs(gt.scDX);
	if (dx >= 5) {
		if (gt.scDX*sn->ptSpd.X>0) sn->ptSpd.X += gt.scDX * dx *.005f;
		else sn->ptSpd.X = gt.scDX *.2f;
	} else if (dx < 2) sn->ptSpd.set(0, 0, 0);
	else  sn->ptSpd *= 0.9;

	if (cei->cp.itemType != 1 || Cs.swipeModelRttPitch) {
		rtt.X = -gt.scDY;
		float dy = abs(gt.scDY);
		if (dy >= 5) {
			if (gt.scDY * sn->ptSpd.Y > 0) sn->ptSpd.Y += gt.scDY * dy * .005f;
			else sn->ptSpd.Y = gt.scDY * .2f;
		}
		else if (dy < 2) sn->ptSpd.set(0, 0, 0);

	}
	//ptSpd.Y = abs(gt.scDY) > 10?gt.scDY:0;

	{
		if (!sn->addRotation(rtt * .2f))
		sn->setRotation(sn->getRotation()+rtt*.2f);
	}
}

void SnArRoot::setLastPickNode(const PickNodeInfo& pni)
{
	lastPickNode = curPickNode;
	curPickNode = pni;
	bool isR = false;
#if 0 // finger mark 
	if (pni.sbNode && (pni.sbNode->GetNameU() == L"左手首" || (isR=pni.sbNode->GetNameU() == L"右手首")))
	{
		pni.sn->sb->createIkLabels(isR?2:1,pni.sbNode, isR);
	}
	if (pni.sbNode && !pni.sn->sb->ikEnable && (pni.sbNode->GetNameU() == L"左足" || (isR = pni.sbNode->GetNameU() == L"右足")))
	{
		pni.sn->sb->createIkLabels(isR ? 2 : 1, pni.sbNode, isR);
	}
#endif
	if (pni.sbNode && pni.sbNode->flMove)
	{
		gizmoType = 0;
	}
	setPickReleased = false;
	Dart::notifyUI(90000201, curPickNode.sbNode!=0?1:0, 0);
}

void SnArRoot::stickMoving( SnArItem *sn)
{

	const CsEditorItem *p=sn->Cei;
	vector3df vec;
	switch (Cs.curVecMode)
	{
	default://TRANSLATE
		vec.X = Cs.stkDX * 20 * sn->getScale().X;
		if (Cs.curPlaneMode == 0) vec.Y = -Cs.stkDY * 20 * sn->getScale().Y;
		else vec.Z = -Cs.stkDY * 20 * sn->getScale().Z;
		break;
	case 1:
		vec.X = -Cs.stkDY * 2;
		if (Cs.curPlaneMode == 0) vec.Y = -Cs.stkDX * 2;
		else vec.Z = -Cs.stkDX * 2;
		break;
	case 2:
		vec.X = (1 + Cs.stkDX * 0.02);
		if (Cs.curPlaneMode == 0) vec.Y = (1 + Cs.stkDY * 0.02);
		else vec.Z = (1 + Cs.stkDY * 0.02);
		break;
	}
	//if (Cs.itemAxisStick)
	//{
	//	matrix4 m,snm;
	//	snm = sn->getAbsoluteTransformation();
	//	switch (Cs.curVecMode)
	//	{
	//	default:
	//		m.setTranslation(vec);
	//		sn->setPosition( (snm*m).getTranslation());
	//		break;
	//	case 1:
	//		m.setRotationDegrees(vec);
	//		sn->setRotation((snm * m).getRotationDegrees());
	//		break;
	//	case 2:
	//		m.setScale(vec);
	//		sn->setScale((snm* m).getScale());
	//		break;
	//	}
	//}
	//else
	{
		switch (Cs.curVecMode)
		{
		default:
			vec += sn->getPosition();
			sn->setPosition(vec);
			break;
		case 1:
			vec += sn->getRotation();
			sn->setRotation(vec);
			break;
		case 2:
			vec *= sn->getScale();
			if (p->lockScale) vec.Y = vec.Z = vec.X;
			sn->setScale(vec);
			break;
		}
	}
	ArSnToCs(snSticking, snSticking->Cei);
}

void SnArRoot::onItemUpdated(CsEditorItem* p, ISceneNode* sn, uint64_t flag)
{
	if (p->root == 0) {
		//if (p->cp.itemType == 2) Eqv->clearFws();
		SnArItem* asn = (SnArItem*)sn;
		asn->onUpdatedState(flag);
		asn->redrawOldStroke();
	}
}

SnArItem* SnArRoot::newArSn(CsEditorItem* p)
{
	static int cc = 128; cc++;
	if (cc > 255) cc=255;
	ArItemParam pm; pm.eqv = Eqv; pm.ctx = Ctx; p->cp = Cs.icp; pm.cei = p; pm.arRoot = this;
	pm.pickId = cc;
	auto sn =  new SnArItem(snSubArItemsRoot, SceneManager, -1, pm);
	sn->drop();
	sn->setPickData(cc);
	setCurSn(sn);
	
	p->sn = sn;	
	return sn;
}

void SnArRoot::loadPmxZipFromDataDir(const std::string fn, const std::string vmd, SnArItem* sn)
{		
	//UP_LOCK_GUARD(*Ctx->getLib()->getLock());
	auto fp = Ctx->getDataFilePath("models/") + fn.c_str()  ;
	auto fs = Ctx->getFileSystem();
	auto rfz = fs->createAndOpenFile(fp);
	if (!rfz) return;
	io::IFileArchive* fa{};
	std::string dir = fn+"/"; //must low case
	strToLower(dir);
	if (fs->addFileArchive(rfz, true, false, io::EFAT_ZIP, "", dir.c_str(), &fa))
	{
		 
		if (onModelUploaded(mmd, fa, dir.c_str(), sn,false)) {
			sn->leiBk.modelFile = fn ;

			if (vmd.size() > 0) {
				sn->leiBk.motionFile = vmd;
				auto fp = Ctx->getDataFilePath("motions/") + vmd.c_str();
				sn->loadMotion(fp);
			}
			if (!sn->leiBk.jsToLoadPose.isNull()) {
				sn->loadPose(sn->leiBk.jsToLoadPose);
			}			 
		}
		fs->removeFileArchive(fa);
	}
	rfz->drop();
}
void SnArRoot::loadStaticModelFromDataDir(const std::string fn,  SnArItem* sn)
{
	//UP_LOCK_GUARD(*Ctx->getLib()->getLock());
	auto fp = Ctx->getDataFilePath("static_models/") + fn.c_str();
	fp.make_lower();
	if (fp.pathGetExt() == ".ply")
	{
		auto sm = Ctx->getSceneManager();
		if (!cmRoot) cmRoot = sm->addEmptySceneNode();
		if (snPointCloud) snPointCloud->remove();
		cmRoot->setMatrix(Ctx->gd.CMCloudMat);
		auto msPly = sm->getMesh(fp);
		snPointCloud = sm->addMeshSceneNode(msPly, cmRoot, -1);//		snPly->setScale(CMPosScale);
		{
			core::matrix4 mtr = glm::rotate(glm::mat4(1), core::PI, glm::vec3(0, 1, 0)) *
				glm::scale(glm::mat4(1), glm::vec3(CMPosScale, -CMPosScale, CMPosScale));
			snPointCloud->setMatrix(mtr);
			snPointCloud->setMaterialType(EMT_POINT_CLOUD);
			snPointCloud->setMaterialFlag(EMF_LIGHTING, false);
			snPointCloud->setPickData(EPD_PointCloud);

		}
	}
	else sn->loadMeshSN(fp);
}
std::string SnArRoot::saveItems()
{
	std::string ret;
	ualib::UaJsonSetting jss;
	Json::Value vItRoot;
	
	jss.AddChildValue("root", itemToJson());

	snSubArItemsRoot->forEachChild([&](ISceneNode* childSn) {
		Json::Value vr = ((SnArItem*)childSn)->itemToJson();
		vItRoot.append(vr);
		});
	jss.AddChildValue("items", vItRoot);

	ret = jss.saveToString(true);
#if defined(_WIN32) && defined(_DEBUG)
	ualib::stringSaveToFile(ret, "out/aritems.json",false);
#endif
	if (Cs.jsonItemsSaveString) freeUtf8String(Cs.jsonItemsSaveString);
	Cs.jsonItemsSaveString = newUtf8String(ret);
	Cs.jsonItemsSaveStringLength = ret.size();
#ifdef _WIN32

	{
		WCHAR sz[1024]=L"aritems.json";
		OPENFILENAME ofn;      //todo: convert utf-8 to utf-16 according to locale and use *W API

		// fill out 'ofn'
		memset(&ofn, 0, sizeof(OPENFILENAME));
		ofn.lStructSize = sizeof(OPENFILENAME);
		ofn.hwndOwner = NULL;
		ofn.Flags = OFN_EXPLORER;
		ofn.lpstrFilter = L"JSON\0*.json\0";
		ofn.lpstrFile = sz;
		ofn.nMaxFile = 1023;

		if (GetSaveFileName(&ofn))// err todo
		{
			io::path fp(ofn.lpstrFile);
			if (!fp.pathGetExt().equals_ignore_case(".json"))
				fp += L".json";
			std::ofstream outfile(fp.c_str());
			outfile.write((char*)ret.c_str(), ret.size());
			outfile.close();			 
		}
		else 
		{
			DWORD e = GetLastError();
			DP(("ERROR %X",e));
		}
	}
#endif
	return ret;
}

void SnArRoot::loadItems(const char* str, int fromId)
{
	clearLoadEIs();
	if (fromId == 0) // from json file
	{
		ualib::UaJsonSetting jss;
#if 0
		jss.SetFile("out/aritems.json", true);
#else
		jss.LoadJsonString(str);
#endif
		//ROOT
		LoadItemStruct lei{};
		SnArItem::jsonToCei(jss.GetChild("root"),lei);
		Cei->pos = lei.cei.pos;
		Cei->rtt = lei.cei.rtt;
		Cei->scl = lei.cei.scl;

		//ITEMS
		Json::Value vItRoot = jss.GetChild("items");
		for (Json::Value& vr : vItRoot DOT_AS_ARRAY)
		{
			loadEIs.emplace_back(); LoadItemStruct &lei = loadEIs[loadEIs.size() - 1];
			SnArItem::jsonToCei(vr,lei);
 
		}
	}
	else // from anchor list
	{
		for (auto &a:Eqv->ancDatVec)
		{
			loadEIs.emplace_back(); LoadItemStruct& lei = loadEIs[loadEIs.size() - 1];
			CsEditorItem& ei = lei.cei;
			initCsEditorItem(&ei);
			lei.label = "A";
			lei.text = "";
			lei.setStringPtr();
			ei.cp.itemType = 0; ei.cp.itemSubType = 0;
			ei.startS = a.timeS;
			*((vector3df*)&ei.pos) = (a.pos + Eqv->ar.camSrcOfs) * Eqv->ar.camPosScale;
			*((vector3df*)&ei.rtt) = a.rtt;
			 

		}
	}
}





void SnArRoot::clearLoadEIs()
{
 
	loadEIs.clear(); loadEIs.reserve(MAX_AR_ITEM);
}

void SnArRoot::setAddRttId()
{
	snSubArItemsRoot->forEachChild([this](ISceneNode* childSn) {
		auto sn = static_cast<SnArItem*>(childSn);
		sn->setAddRttNode(Cs.swipeModelRttNodeId, { 0,0,0 }, true);
		});
	
}

void SnArRoot::resetPickingNodeAnimationTR(u32 flag)
{
	if (curPickNode.sbNode && curArSn==curPickNode.sn) {		
		curPickNode.sbNode->SetAnimationTranslate(glm::vec3(0));
		curPickNode.sbNode->SetAnimationRotate(glm::quat(1, 0, 0, 0));
		if (flag & 0x1) {
			curPickNode.sbNode->forEachSubNodes([=](saba::MMDNode* node) {
				node->SetAnimationTranslate(glm::vec3(0));
				node->SetAnimationRotate(glm::quat(1, 0, 0, 0));
				});
		}
			
	}
}

 

void SnArRoot::setCurSn(SnArItem* sn, bool chgCamera)
{
	if (!sn) return;
	//if (sn!=curArSn)		curPickNode.node = nullptr;
	mmd->tempIKRoot = nullptr;
	
	lastArSn = curArSn;
	if (curArSn) curArSn->drop();
	curArSn = sn;
	curArSn->grab();
	if (curArSn->sb) {
		 
		if (curArSn->sb->hasVtxFw) {
			vtxFwSn = curArSn;
		}
		if (curArSn->sb->isAiCharacter()) {
			curAiChar = curArSn;
			if (curArSn->sb != mmd->curAiSb) {
				
				mmd->lastAiSb = mmd->curAiSb;
				mmd->curAiSb = curArSn->sb;
#if CAM_MMD_SELFIE 
				if (mmd->camRbFpvDmyJt) delete mmd->camRbFpvDmyJt;
				mmd->camRbFpvDmyJt = mmd->curAiSb->Pmx->connectRb(mmd->curAiSb->ndUpper2->rb0, mmd->camRbFpvDmy, true, glh::matTransformVec(mmd->curAiSb->ndUpper2->rb0->getOfsMatInv(), { 0,0.7,-2.6 }), false);
#endif
			}
		}
		if (curArSn->sb->ndHead || curArSn->sb->ndCamPos)
		Amp->setCamSb(curArSn->sb,chgCamera);
	}
 
		
}

irr::scene::SnArItem* irr::scene::SnArRoot::curChar()
{
	//setCurSn(curAiChar);
	return curArSn;// curAiChar;
}

void SnArRoot::ArSnToCs(SnArItem* sn, CsEditorItem* p, int redraw)
{
	auto vec = sn->getPosition();
	p->pos.x = vec.X; p->pos.y = vec.Y; p->pos.z = vec.Z;
	vec = sn->getRotation();
	p->rtt.x = vec.X; p->rtt.y = vec.Y; p->rtt.z = vec.Z;
	vec = sn->getScale();
	p->scl.x = vec.X; p->scl.y = vec.Y; p->scl.z = vec.Z;;
#if SVG_MMD_WRITE_X
	if (redraw==1) sn->redrawOldStroke();
	else if (redraw==-1 && sn->isText) Eqv->clearFws();
#endif
}

bool SnArRoot::onModelUploaded(IrrMMD* mmd, io::IFileArchive* fa, irr::io::path dir,SnArItem *parentArSn, 
	bool onlyGetInfo,
	bool allowAllNodePhysics)
{
	io::path fullPath = "", vmdPath;
	io::IFileList* fl{};
	bool ret = false;
	auto fs = Ctx->getFileSystem();
	//if (faPMX)	fs->removeFileArchive(faPMX);
	//if (!fs->addFileArchive(Ctx->getTempFilePath("ulpmx.zip").c_strA(), true, false,irr::io::EFAT_ZIP,"","ulpmx/",& faPMX))	return;
	io::IFileList* fl1{};
	if (dir.pathGetExt() == ".pmx") {
		fullPath = dir.replace("\\", "/");
		dir = Ctx->getFileSystem()->getFileDir(dir);

	}
	else fl = fs->createFileList(dir);


	std::vector<int> dirIds;
	if (fl && fl->getFileCount() == 0  && fa)
	{	//no root dir file, eg. Windows created zip
		auto fl2 = fa->getFileList();
		if (fl2->getFileCount() == 0)return false;
		auto fp = fl2->getFullFileName(0);
		DPWCS((L"FP %s",fp.c_str()));
		int i = 0, cd = 2; io::path dirs;
		while (fp[i]) // find 2nd '/'
		{
			dirs += fp[i];
			if (fp[i] == '/') cd--;
			if (cd == 0) {
				break;
			}
			i++;
		}
		fl->drop();
		fl = fs->createFileList(dirs);
	}
	//if (fl->getFileCount() == 1 && fl->isDirectory(0)) {
	//	auto dir = fl->getFullFileName(0);
	//	fl->drop();
	//	fl = fs->createFileList(dir);
	//}
	if (fullPath.size() < 1) 
		for (int i = 0; i < fl->getFileCount(); i++)
	{
		auto ext = fl->getFullFileName(i).pathGetExt();
		if (ext == ".pmx")
		{
			fullPath = fl->getFileNameOrigCase(i);
		}
		else if (ext == ".vmd")
		{
			vmdPath = fl->getFileNameOrigCase(i);
		}
		if (fl->isDirectory(i))
			dirIds.push_back(i);
	}
	if (fullPath.size() < 1)
	{


		for (auto& dirId : dirIds) {

			fl1= fs->createFileList(fl->getFullFileName(dirId));
			for (int i = 0; i < fl1->getFileCount(); i++)
			{
				if (fl1->getFullFileName(i).pathGetExt() == ".pmx")
				{
					fullPath = fl1->getFileNameOrigCase(i);
					break;
				}
 
			}
		}
	}

	if (onlyGetInfo)
	{
		static IrrMMD::MMDInfo mi;
		mmd->getInfo(fullPath,mi);
		Cs.pmxInfo.name_jp = mi.info.m_modelName.c_str();
		Cs.pmxInfo.info_jp = mi.info.m_comment.c_str();
		Cs.pmxInfo.name_en = mi.info.m_englishModelName.c_str();
		Cs.pmxInfo.info_en = mi.info.m_englishComment.c_str();
	}
	else if (fullPath.size() >= 5) {

		int sabaNum = getSabaNum();
		irrSabaParam spm;
		spm = mmd->Pm.spm;
		spm.eqv = Eqv;
		spm.ctx = Ctx;
		spm.snMmdRoot = this;
		spm.mmd = mmd;
		spm.pmxFile = fullPath;
#if SVG_MMD_WRITE
		spm.vmdFile = "d:/mmd/vmd/write.vmd";
#endif
		spm.idx = 1;
		spm.scale = COLMAP_MODEL?100:MMD_SABA_SCALE ;
		spm.freeAllVtxData = !IS_WIN && sabaNum >2;
		spm.snArItem = parentArSn;
		spm.pickData = parentArSn->getMaterial(0).PickColor;
		//spm.handWrite = Cs.mdHandWrite;
		spm.allowAllNodePhysics = allowAllNodePhysics;
#if MMD_SAVE_CAMVMD
		spm.needSaveCamVmd = true;
 
		spm.drawCam = 1;
		//spm.camVmdFile = camPath;
#endif
#if PHYSICS_MMD_FW
		//Eqv->pfNodes.clear(); Eqv->ft.mmdThrowTimer = 0;
#endif
		parentArSn->createModel(&spm);
		//auto ws = fullPath.pathWithOutExt().pathGetFileName();
		//parentArSn->textBack = ws;
		//parentArSn->Cei->text = parentArSn->textBack.c_str();
		
		if (vmdPath.size()>=5)
			parentArSn->loadMotion(vmdPath);

		//inits on settings
		parentArSn->setAddRttNode(Cs.swipeModelRttNodeId, { 0,0,0 }, true);
			auto sb = parentArSn->sb;		 
		if (sb && sb->isAiCharacter())
				curAiChar = curArSn;
		

		int sbc = 0;
		snSubArItemsRoot->forEachChild([this,&sbc](ISceneNode* childSn) {
			auto sn = static_cast<SnArItem*>(childSn);
			if (sn->sb) sbc++;

			});
		if (sbc == 1) parentArSn->Cei->previewPhysics = !Ctx->isApp(APPID_ArDatCam);
		else if (sbc>=2) snSubArItemsRoot->forEachChild([this](ISceneNode* childSn) {
			auto sn = static_cast<SnArItem*>(childSn);

			if (sn->sb) sn->Cei->previewPhysics = IS_WIN;

			});

		//ualib::SleepMs(10000);
	
		Dart::notifyUI(80020129,0,0);
		ret = true;
	}
	if (fl1) fl1->drop();
	if (fl) fl->drop();
	return ret;
}

bool SnArRoot::onPointerEvent(const irr::SEvent::SPointInput& pe)
{
	framePicked = false;
	bool processed = false;
	irr::core::vector2df xy = { pe.X,pe.Y };
	auto addPickNode=[=](int id, PickNodeInfo& pni) {
		pni.startXY = xy;
		if (pni.sbNode && pni.sbNode->model->saba) {
			pni.sbNode->model->saba->grab();
			onSbNodePicked(pni.sbNode);
			mmd->lastPickNode = pni.sbNode;
			framePicked = true;
		}
		snPickMap[id] = pni;
	};
	
	switch (pe.act)
	{
	case EPA_Down: {
		if (!curArSn || IS_WIN && pe.isMouse && !(pe.ButtonStates & 1) || pe.isMouse && pe.Control )
			break;
		PickNodeInfo pni{};
		bool ikvis = mmd->ikVisable && curArSn->sb->ikVRoot && curArSn->sb->ikVRoot->isVisible();
		if (Cs.pickPhysics || ikvis)
		{
			//Eqv->LaunchFw3D(Ctx->getPointerHitRealPos(&xy), Eqv->getFwIdx(2, 0), { 0,0,0 });			 
			if (ikvis &&
				curArSn && curArSn->sb && curArSn->sb->mdViewNode || pe.Shift && pe.btnId==0) {
				pni.sbNode = curArSn->sb->getPickNodeByMarker(xy,pe.Shift?1:0);
				if (pni.sbNode) {
					pni.sn = curArSn;
					addPickNode(pe.ptrId, pni);
					pickingVtxId = -1;
					processed = true;
				}
			}
			if (!pni.sbNode && Cs.pickPhysics) {
				static u32 pdr[32]; glm::vec3 pkpos[32]; int posid = 0; int view = 0;
				u32 pickdata = pickPoint(pe.X, pe.Y,view, 20, pdr, pkpos);
				int ri = 0;
				while ((pickdata & 0xFFFF) < 80 && ri < 8) {
					DP(("TRY Range %d", ri));
					pickdata = pdr[ri++]; posid++;
				}
				u32 pick, vtxid;	pickGetResult(pickdata, pick, vtxid);

				if (pick >= 80) {
					DP(("Picked %d - %d", pick, vtxid));
					SnArItem* snPick{};
					forEachArChild([=, &snPick](SnArItem* sn) {
						if (sn->getMaterial(0).PickColor == pick) {
							snPick = sn; 
							
							setCurSn(sn,pe.Alt); 
						}},0x1);
					if (snPick) {
						//if ((vtxid & 0x800000) && mmd->ikVisable) {int nodeIdx = int(vtxid & 0xFFFF);pni.sn = snPick;pickingVtxId = -1;pni.node = snPick->saba->getPickNodeByNodeId(nodeIdx);addPickNode(pe.ptrId, pni);}	else 
						if (Cs.pickPhysics && vtxid > 0 && snPickMap.size() == 0 && snPick->sb && snPick->sb->hasVtxData()) {
							pni.sn = snPick;
							pni.view = view;
							pickingVtxId = vtxid - 1;//added 1 an shader gl_VertexIndex+1
							 
							pni.altDown = pe.Alt;
							pni.altDownXY.set(pe.X, pe.Y);
							pni.sbNode = snPick->sb->getPickNodeByVtxId(pickingVtxId, 0,&pkpos[posid],!ImGui::GetIO().KeyShift);
							mmd->camSbTgtNd = pni.sbNode;
							mmd->camSbTgtSb = pni.sn->sb; 

							if (!pni.sbNode || !pni.sbNode->rb0
#if MMD_PICK_CHARACTER_ONLY
								|| !pni.sbNode->rb0->GetActivation() && !pni.sbNode->model->isCharacter
#endif
								|| !pni.sbNode->rb0->GetActivation()   
								)
								break;
							pni.lastMovePe = pe;
							addPickNode(pe.ptrId, pni);
							mmd->LookOverride = true;
							processed = true;

							if (view > 0)
							{
								pickRelease(pe);
							}
						}
					}
					else pickingVtxId = -1;
				}
				else pickingVtxId = -1;				
			}
			DP(("PickMap %d, %d", snPickMap.size(), pe.ptrId));

			mouseCursorPos(pe, xy);
		}
		if (processed) setLastPickNode(pni);
	}break;
	case EPA_Move: {
		//SWIPE PICK

		//if (pe.Shift!= lastShift && snPickMap.find(pe.ptrId)!= snPickMap.end()) { 
		//	pickRelease(pe, processed, xy);  
		//}
		//else
		{
			processed = onPtrMove(pe);
			Ctx->gd.frameMoved = 1;
		}
	}break;
	case EPA_Up: {
		setPickReleased = true;
		processed=pickRelease(pe);
	}break;
	}
	lastShift = pe.Shift;
	if (pe.flag & IEventReceiver::ef_2ptScaled)
		processed = true;
	
	return processed;
}

bool irr::scene::SnArRoot::pickRelease(const irr::SEvent::SPointInput& pe)
{
	bool processed = false;
	shiftCurPosGet = false;
	auto it = snPickMap.find(pe.ptrId);
	if (it != snPickMap.end()) {
		processed = true;
		if (pickingVtxId == -1)//if (xy.getDistanceFrom(it->second.startXY) < 5.f)
		{
			saba::PMXNode* node = dynamic_cast<saba::PMXNode*>(it->second.sbNode);
			auto snb = it->second.sn->sb;
			if (snb && node == snb->Pmx->getTmpIkNode()) {
				snb->Pmx->finishTmpIK();
			}
			else if (irr::core::vector2df( pe.X,pe.Y ).getDistanceFrom(it->second.startXY) < 5.f) {
				mmd->tempIKRoot = it->second.sbNode;
			}
			if (node->GetIKSolver()) {
				auto nodPos = core::matrix4(node->GetGlobalTransform()).getTranslation();
				auto tgtPos = core::matrix4(node->GetIKSolver()->GetTargetNode()->GetGlobalTransform()).getTranslation();
				if (tgtPos.getDistanceFrom(nodPos) > 10.f && node->GetParent()) {  //too far return 
					auto prtPos = core::matrix4(node->GetParent()->GetGlobalTransform()).getTranslation();
				//	node->SetAnimationTranslate(tgtPos - prtPos - node->GetTranslate());
				}
			}
		}
		it->second.sbNode->model->saba->getPickNodeByVtxId(Cs.pickingVtxHold ? PICK_NODE_KEEP : PICK_NODE_DROP, 0);
		Cs.pickingVtxHold = 0; pickingVtxId = -1; it->second.sn = nullptr; mmd->LookOverride = false;
		
		pickMapErase(it);
	}
	else {
		float dx = (pe.X - pe.startX), dy = (pe.Y - pe.startY);
		if (curPickNode.sbNode && dx * dx + dy * dy < 100) {
			//PickNodeInfo pni{}; setLastPickNode(pni);
#if HAS_ARCORE
			if (Cs.resetOrigin == 1)
				hello_ar::gAr->OnTouched(xy.X, xy.Y);
#endif
		}
	}
	return processed;
}

void irr::scene::SnArRoot::pickMapErase(std::map<int, irr::scene::SnArRoot::PickNodeInfo>::iterator& it)
{
	if (it->second.sbNode && it->second.sbNode->model->saba) it->second.sbNode->model->saba->drop();
	snPickMap.erase(it);
}

void SnArRoot::addFakePtrMove()
{
	for (auto &it:snPickMap){
		if (!it.second.altDown)
		onPtrMove(it.second.lastMovePe);
	}
}

void irr::scene::SnArRoot::rotateSubPickNodes(float a) 
{
	if (!curPickNode.sbNode) return;
	auto node = curPickNode.sbNode;
	node->exd.rttRecursionAdd = { 0,0,0 };
	node->forEachSubNodes([=](saba::MMDNode* nd) 	{
			//nd->exd.rttRecursionAdd = nd->GetParent()->exd.rttRecursionAdd + glm::vec3(a, 0, 0);
			nd->SetAnimationRotate(nd->GetAnimationRotate() * glm::quat(nd->exd.rttRecursionAdd*a*0.5f));
		});
}


void irr::scene::SnArRoot::connectSbs(bool useLastPickRbOfs,glm::vec3 ofs)
{
	//auto sbA = arRoot->curArSn->saba;
	//auto sbB = arRoot->lastArSn->saba;
	//if (sbA == sbB || sbB==nullptr) return;
	//auto rbA = sbA->Pmx->GetNodeManager()->FindNode(L"左手首")->rb0;
	//auto rbB = sbB->Pmx->GetNodeManager()->FindNode(L"左手首")->rb0;
	if (!curPickNode.sbNode || !lastPickNode.sbNode) return;
	auto rbA = lastPickNode.sbNode->rb0;
	auto rbB = curPickNode.sbNode->rb0;
	if (!rbA || !rbB) return;
	curArSn->sb->Pmx->connectRb(rbA, rbB, true, useLastPickRbOfs? curPickNode.sbNode->model->pickRbOfs: ofs);
}
void irr::scene::SnArRoot::connectConnToPick(int lid,glm::vec3 ofs)
{ 
	auto rbB = curAiChar->sb->lastConRb[lid];
	auto rbA = curPickNode.sbNode->rb0;
	if (!rbA || !rbB) return;
	curAiChar->sb->Pmx->connectRb(rbA, rbB, true, ofs,false);
}
void irr::scene::SnArRoot::connectConns(int lid0, int lid1,glm::vec3 ofs)
{
	auto rbB = curAiChar->sb->lastConRb[lid1];
	auto rbA = curAiChar->sb->lastConRb[lid0];
	if (!rbA || !rbB) return;
	curAiChar->sb->Pmx->connectRb(rbA, rbB, true, ofs, false);
}
void irr::scene::SnArRoot::updateTmpIk() {
#if MMD_HAND_OXR
	if (tempIkMode)
	{
		SceneManager->getActiveCamera()->bindTargetAndRotation(true);
		vector3df pos= SceneManager->getActiveCamera()->getAbsolutePosition();
		core::matrix4 m,mc= SceneManager->getActiveCamera()->getAbsoluteTransformation();
		m.setTranslation({ 100,  -150, 0 });
		m = mc * m;
		DP(("+ %f,%f,%f ", pos.X, pos.Y, pos.Z)); pos.set(0, 0, 0);
		m.transformVect(pos);
		DP(("  %f,%f,%f ",pos.X,pos.Y,pos.Z));
		(matAbsInv).transformVect(pos);//mmd global Pos
		curArSn->saba->Pmx->getTmpIkNode()->SetAnimationTranslate(pos);
		//node.setCmPosGlobal(pos, 0.02f, 0.02f);
	}
#endif
	if (0)
	for (int i=0;i<mmd->sabas.size();i++)
	{
		auto sb=mmd->sabas[i];
		auto cam = SceneManager->getActiveCamera();
		auto campos= sb->irr2mmd(cam->getTarget());
		
		float r = 6;
		float t = Ctx->gd.time * core::PI*2/6 + core::PI*2*i/3;
		sb->ndRoot->SetAnimationTranslate( float3(r * sin(t), std::min(20.f,campos.y-(sb->ndHead->mGlobalInit[3].y*0.9f)), r * cos(t)));
	}
}

bool SnArRoot::onPtrMove(const SEvent::SPointInput &pe)
{
	irr::core::vector2df xy = { pe.X,pe.Y };
	//DP(("ptrmov %f",pe.Y));
	bool processed=false;

	auto it = snPickMap.find(pe.ptrId);
	if (it != snPickMap.end()) {
		auto saba= it->second.sbNode ? it->second.sbNode->model->saba:nullptr;
		if (!saba || !saba->Pmx) {  //freed todo:better way

			pickMapErase(it);
			return true;
		}
		processed = true;
		if (Cs.pickPhysics && pickingVtxId >= 0) //MOVE PHYSICS RB
		{
			//DP(("mxy %f %f ", Ctx->gd.mouseX, Ctx->gd.mouseY));
			bool skipEvt = false;
			auto& pni = it->second;
			if (it->second.altDown) {
				it->second.altDown = false;
				int byNode = 0; if (pe.Alt) {
					vector2df dir(pe.X - pni.altDownXY.X, -pe.Y + pni.altDownXY.Y);
					if (dir.getLength() < 8) {
						it->second.altDown = true;
						return true;
					}
					dir.normalize();
					auto f = dir.getAngleTrig();
					if (f > 30 && f < 150) byNode = f>90?3:7;
					else if (f >= 150 && f <= 210) byNode = 1;
					else if (f <= 30 || f >= 330) byNode = 2;
					else byNode = f > 287 ? 8 :f<253?9: 5;
					it->second.sn->sb->getPickNodeByVtxId(PICK_NODE_DROP, 0);

					it->second.sbNode = it->second.sn->sb->getPickNodeByVtxId(pickingVtxId, byNode, nullptr);
					if (!it->second.sbNode || !it->second.sbNode->rb0->GetActivation())
						return false;
					it->second.lastMovePe = pe;


					setLastPickNode(pni);
					mmd->LookOverride = true;

					processed = true;
				}
			}
			else  
			{
				if (pe.Shift != it->second.lastMovePe.Shift)
				{
					DP(("PMOVE %d  ", pe.Shift));
					if (!pe.Shift)
					{
						DP(("skipEvt")); skipEvt = true;
					}
					saba->getPickNodeByVtxId(PICK_NODE_KEEP, 0);
					mouseCursorPos(pe, xy);
					auto& pni = it->second;
					pni.lastMovePe = pe;
					pni.startXY = xy;
					saba->getPickNodeByVtxId(PICK_NODE_CONTINUE, saba->pickFilter);//update mouse startpos		

					processed = true;
				}
#if IS_WIN
				if (shiftDragging) {
					GetCursorPos(&shiftCurPos);
					saba->pickDisMul *= 1 - (shiftCurPos.y - shiftStartPos.y) / 2000.f;
					//saba->pickDisMul +=  - (shiftCurPos.y - shiftStartPos.y) / 1000.f;
					shiftDragOfs += vector2df(shiftCurPos.x - shiftStartPos.x, shiftCurPos.y - shiftStartPos.y);
					DP(("SHIFT DRAG DET %d,%d disMul %f  %f", shiftCurPos.x - shiftLastPos.x, shiftCurPos.y - shiftLastPos.y, saba->pickDisMul, 1 + (shiftCurPos.y - shiftStartPos.y) / 10.f));
					shiftLastPos = shiftCurPos;
					xy.Y = (shiftDragStart + shiftDragOfs).Y;
					SetCursorPos(shiftCurPos.x, shiftStartPos.y);
				}

#endif
				//MOVE
				saba->pickVtxMove(it->second.sbNode, xy, pe.Shift);
			}
		}
		else if ( it->second.sn) //MOVE NODE
		{
			bool firstMove = false;

			if (!it->second.isMoveStarted && xy.getDistanceFromSQ({ pe.startX,pe.startY }) > 100.f)
				firstMove = it->second.isMoveStarted = true;
			if (firstMove && tempIkMode)
				saba->ifPickTmpIK(it->second.sbNode, xy, mmd->tempIKRoot);
			if (it->second.isMoveStarted) {
				if (pe.Shift != it->second.lastMovePe.Shift) {
					 
					saba->getPickNodeByNodeId(it->second.sbNode->GetIndex());
					mouseCursorPos(pe,xy);
				}

				saba->pickNodeMove(it->second.sbNode, xy, pe.Shift, pe.Alt);
			}
		}
		it->second.lastMovePe = pe;
		
	}
	else if (Ctx->isApp(APPID_WinTest) && Ctx->getCameraId() != 1) {
		if (pe.isMouseBtnDownWhenMove(EMBSM_LEFT))
		{
			Cs.pnTrs.x = pe.X - pe.lastX;
			Cs.pnTrs.y = (pe.Shift) ? 0 : -(pe.Y - pe.lastY);
			Cs.pnTrs.z = (pe.Shift) ? -(pe.Y - pe.lastY) : 0;
			SEvent evo{ EET_CMD_INPUT_EVENT };
			evo.CmdInput.cmdId = 0x10000000 | ccePickSetPsTrs;
			evo.CmdInput.pm1 = 1;
			Amp->StageOnCmdEvent(evo.CmdInput);
		}
		else if (pe.isMouseBtnDownWhenMove(EMBSM_RIGHT))
		{
			Cs.pnRtt.y = -(pe.X - pe.lastX);
			Cs.pnRtt.x = (pe.Shift) ? 0 : -(pe.Y - pe.lastY);
			Cs.pnRtt.z = (pe.Shift) ? -(pe.Y - pe.lastY) : 0;
			SEvent evo{ EET_CMD_INPUT_EVENT };
			evo.CmdInput.cmdId = 0x10000000 | ccePickSetPsRtt;
			evo.CmdInput.pm1 = 1;
			Amp->StageOnCmdEvent(evo.CmdInput);
		}
	}
	else if (!IS_WIN && Ctx->getCameraId() == 1) {


	}
 
	return processed;
}

bool irr::scene::SnArRoot::onKeyEvent(const irr::SEvent::SKeyInput& ke)
{
	switch (ke.Key)
	{
	case irr::KEY_LMENU: {
		if (ke.PressedDown && !ke.LastPressDown && snPickMap.size()) {
			auto it = snPickMap.begin();
			auto &pni=it->second;
			pni.altDown = true;
			pni.altDownXY.set(Ctx->gd.mouseX, Ctx->gd.mouseY);
			DP(("ALT DOWNNNNNNNNNNNNNNNNNNNNNNNNNN"));
		}
	}break;
		
	}
	return false;
}

void irr::scene::SnArRoot::mouseCursorPos(const irr::SEvent::SPointInput& pe, irr::core::vector2df &xy)
{
#if IS_WIN
	if (pe.Shift || !shiftCurPosGet) {
		
		GetCursorPos(&shiftCurPos);
		if (!shiftDragging) {
			shiftStartPos = shiftLastPos = shiftCurPos;
			shiftDragStart.set(pe.X, pe.Y);
			mouseDeltaOfs = vector2df(shiftCurPos.x - pe.X, shiftCurPos.y - pe.Y);
			shiftDragOfs.set(0,0);
			DP(("mouseCursorPos"));
		}

		shiftCurPosGet = true;
	}
	else if (shiftCurPosGet) {
		int y = shiftCurPos.y;
		GetCursorPos(&shiftCurPos); shiftCurPosGet = true;
	//	SetCursorPos(shiftCurPos.x,  y);
		Ctx->gd.updateStartPos = true;
		//xy.Y -= shiftCurPos.y - y;
		//Ctx->gd.mouseX = xy.X;
		//Ctx->gd.mouseX = xy.Y;
	}
	shiftDragging = pe.Shift;
#endif	
}

int SnArRoot::getSabaNum()
{
	int sabaNum = 0;
	forEachArChild([this,&sabaNum](SnArItem* sn) {
		if (sn->sb)
			sabaNum++;
		});
	return sabaNum;
}


uint32_t SnArRoot::pickPoint(int x, int y, int& view, int range, uint32_t* pd, glm::vec3 *pos )
{
	using namespace irr::core;
	int ret = 0;
	irr::video::VkDriver* VkDrv = (irr::video::VkDriver*)Driver;
	{
		view = 0;
		auto scs = Ctx->getViewSize(0);
		for (int i = 0; i < MULTI_VIEW_COUNT; i++) {
			auto rc=Ctx->getViewRC(i);
			if (rc.isPointInside({ x,y })) {
				view = i;
				x -= rc.UpperLeftCorner.X;
				y -= rc.UpperLeftCorner.Y;
				break;
			}
		}
		scs = Ctx->getViewSize(view);


		auto oldCam = SceneManager->getActiveCamera();
	//	matrix4 ivm;		oldCam->getViewMatrix().getInverse(ivm);
		

		SceneManager->setActiveCamera(Ctx->getViewCamera(view));
		scs /= 2; x /= 2; y /= 2;
		Driver->setRenderTarget(Driver->getPickRT(scs.Width, scs.Height), true, true, 0x00000080);
		VkDrv->resetFrameCache();
		SceneManager->drawPassType(IrrPassType_PickPoint);
		VkDrv->UploadSharedBuffers();
		Driver->setRenderTarget(nullptr, true, true, 0);

		{
			auto tex = Driver->getPickRT(0, 0);
			glm::uvec4* pu = (glm::uvec4*)tex->lock(video::ETLM_READ_ONLY);

			auto getData = [=](int x, int y) {
				glm::uvec4 ret = (x >= 0 && x < scs.Width && y >= 0 && y < scs.Height) ? *(pu + y * scs.Width + x) : glm::uvec4{0,0,0,0};
				return ret;
			};
			//for (Int i)
			auto dat = getData(x, y);
			ret = dat.w;
			if (pos) {
				pos[0].x = float(int(dat.x)/1000.f);
				pos[0].y = float(int(dat.y)/1000.f);
				pos[0].z = float(int(dat.z)/1000.f);
			}
			for (int i = 0; i < range; i++)
			{	
				int xx = x + pickOfs[i].X*5, yy = y + pickOfs[i].Y*5;				
				auto dat = getData(xx, yy);
				pd[i] = dat.w;
				if (pos) {
					pos[i+1].x = float(int(dat.x)/1000.f);
					pos[i+1].y = float(int(dat.y)/1000.f);
					pos[i+1].z = float(int(dat.z)/1000.f);
				}
			}
			DP(("PICK %X", *pu));
			tex->unlock();
		}
		SceneManager->setActiveCamera(oldCam);		 
	}
	return ret;
}

void SnArRoot::pickGetResult(u32 data, u32& id, u32& vtxId)
{
	id = data & 0xFF;
	vtxId = ((u32)data) >> 8;
}


void SnArRoot::buildNodeButtonList()
{
		

	Cs.nodeBtnCount = 0; Cs.nodeBtnCountP = 0; Cs.nodeBtnCountC = 0;
	nodeBtns.clear(); nodeBtnsP.clear(); nodeBtnsC.clear();
	if (curArSn && curArSn == curPickNode.sn && curArSn->sb && curPickNode.sbNode) {
		CsNodeButton bt;
		auto sb = curArSn->sb;
		auto node = curPickNode.sbNode->GetParent();
		if (node) {
			bt.level = -1;
			bt.mmdNodeId = node->GetIndex();
			bt.name = node->GetName().c_str();
			if (node->canControl()) nodeBtnsP.emplace_back(bt);
			auto pn = node;
			while (bt.level > -5 && (pn = pn->GetParent()) ) {
				bt.level --;
				bt.mmdNodeId = pn->GetIndex();
				bt.name = pn->GetName().c_str();
				if (pn->canControl()) nodeBtnsP.emplace_back(bt);
			}

			node=node->GetChild();
			while (node) {
				bt.level = 0;
				bt.mmdNodeId = node->GetIndex();
				bt.name = node->GetName().c_str();
				if (node->canControl()) nodeBtns.emplace_back(bt);
				node = node->GetNext();
			}
		}

		node = curPickNode.sbNode->GetChild();
		while (node) {
			bt.level = 1;
			bt.mmdNodeId = node->GetIndex();
			bt.name = node->GetName().c_str();
			if (node->canControl()) nodeBtnsC.emplace_back(bt);
			node = node->GetNext();
		}

		Cs.nodeBtnCount = nodeBtns.size();
		Cs.nodeBtns = nodeBtns.data();
		Cs.nodeBtnCountP = nodeBtnsP.size();
		Cs.nodeBtnsP = nodeBtnsP.data();
		Cs.nodeBtnCountC = nodeBtnsC.size();
		Cs.nodeBtnsC = nodeBtnsC.data();
	}
}

void SnArRoot::onSelectNodeButtonList(int nodeId, int act)
{
	if (!(curArSn && curArSn == curPickNode.sn && curArSn->sb && curPickNode.sbNode))
		return;
	if (nodeId>0)
	{
		auto nm = curArSn->sb->Pmx->GetNodeManager();
		if (act == 1) {			
			curPickNode.sbNode = nm->GetMMDNode(nodeId);
			setLastPickNode(curPickNode);
		}
		 
		{
			core::matrix4 mt = curArSn->sb->getAbsoluteTransformation() * nm->GetMMDNode(nodeId)->GetGlobalTransform();
			Eqv->LaunchFw3D(mt.getTranslation(), Eqv->getFwIdxByFwIdStr(1, "hovNode"), {0,0,0}, SColorf(0xCFFF2020));
		}
		camRttFocus();
	}

}

void SnArRoot::selectItemOfs(int ofs, uint32_t aiFlag)
{
	assert(abs(ofs) < 2);
	auto Children = snSubArItemsRoot->getChildren();
	bool found = false;

	ISceneNodeList::Iterator it = Children.begin();
	for (; it != Children.end(); ++it)
	{
		if (*it == curArSn) {

			while (!found) {
				if (it == Children.begin() && ofs < 0)
					it = Children.getLast();
				else
					it = it + ofs;

				if (it == Children.end())
					it = Children.begin();

				auto s = (SnArItem*)*it;
				if (s->AIFlag & aiFlag) {
					found = true;
					setCurSn(s, true); s->onSelected();
					break;
				}
			}
		}

	}
}

void SnArRoot::selectClosetItemOfs(int ofs, uint32_t aiFlag)
{
	assert(abs(ofs) < 2);
	auto Children = snSubArItemsRoot->getChildren();
	SnArItem* closest = nullptr;
	float closestDis = 1000000;
	bool found = false;
	glm::vec3 camPos = glh::matTransformVec(mmd->sb0->mmdBaseInv , SceneManager->getActiveCamera()->getPosition());
	ISceneNodeList::Iterator it = Children.begin();
	for (; it != Children.end(); ++it)
	{
		auto ar = (SnArItem*)*it;
		auto sb = ar->sb;
		if (sb && (ar->AIFlag & aiFlag)) {
			float dis = sb->ndYao->disTo(camPos);
			glm::vec3 dir = glm::fastNormalize(sb->ndYao->rb0->getLinearVel());
			bool towardsCam = glm::dot(dir, camPos - sb->ndYao->rb0->getPosition()) > 0;
			if (dis < closestDis && ((ofs>0) ^ towardsCam) ) {
				closestDis = dis;
				closest = (SnArItem*)*it;
				found = true;
			}

		}
	}
	if (found)
	{
		setCurSn(closest, true); closest->onSelected();
	}
}