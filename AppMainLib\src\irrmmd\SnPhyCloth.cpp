// Copyright (C) 2002-2012 <PERSON><PERSON>
// This file is part of the "Irrlicht Engine".
// For conditions of distribution and use, see copyright notice in irrlicht.h
#include "AppGlobal.h"
#include "SnPhyCloth.h"

#include "IVideoDriver.h"
#include "ISceneManager.h"
#include "S3DVertex.h"
#include "SMeshBuffer.h"
#include "os.h"
#include "CShadowVolumeSceneNode.h"
#include "SMesh.h"
#include "IMesh.h"`
#include "PxPhysicsAPI.h"
#include "extensions/PxParticleExt.h"
#include "cudamanager/PxCudaContext.h"
#include "cudamanager/PxCudaContextManager.h"

using namespace physx;
using namespace physx::ExtGpu;

namespace saba {
	extern PxPhysics* gPxPhysics;
	extern PxScene* gPxScene;
}
using namespace saba;
static PxPBDParticleSystem*			gParticleSystem		= NULL;
static PxParticleCloth<PERSON>er*		gClothBuffer		= NULL;

static PxRigidDynamic* sphere=NULL;

namespace irr
{
namespace scene
{
	using namespace irr::video;


	const float spaceMulX = 0.5f, spaceMulY = 0.5f;

static PX_FORCE_INLINE PxU32 id(PxU32 x, PxU32 y, PxU32 numY)
{
	return x * numY + y;
}

void SnPhyCloth::initCloth(const u32 numX, const u32 numZ, const float3& position )
{
	if (gParticleSystem) 
		return;
	PxCudaContextManager* cudaContextManager = gPxScene->getCudaContextManager();
	if (cudaContextManager == NULL)
		return;

	const PxU32 numParticles = numX * numZ;
	const PxU32 numSprings = (numX - 1) * (numZ - 1) * 4 + (numX - 1) + (numZ - 1);
	const PxU32 numTriangles = (numX - 1) * (numZ - 1) * 2;

	const PxReal restOffset = particleSpacing;

	const PxReal stretchStiffness =10.f*totalClothMass;
	const PxReal shearStiffness = 6.f*totalClothMass;
	const PxReal springDamping = 0.01f;

	// Material setup
	PxPBDMaterial* defaultMat = gPxPhysics->createPBDMaterial(0.08f, 0.005f, 1e+6f, 0.001f, 0.5f, 0.005f, 0.05f, 0.f, 0.5f,1,1); //gPhysics->createPBDMaterial(0.8f, 0.05f, 1e+6f, 0.001f, 0.5f, 990.005f, 990.05f, 0.f, 0.f);
	//PxPBDMaterial* defaultMat=gPhysics->createPBDMaterial(0.8f, 0.05f, 1e+6f, 0.001f, 0.5f, 0.005f, 0.05f, 0.f, 0.f);//gPhysics->createPBDMaterial(0.8f, 0.05f, 1e+6f, 0.001f, 0.5f, 0.005f, 0.05f, 0.f, 0.f);

	PxPBDParticleSystem *particleSystem = gPxPhysics->createPBDParticleSystem(*cudaContextManager);
	gParticleSystem = particleSystem;

	// General particle system setting

	const PxReal particleMass = totalClothMass / numParticles;
	PxReal invPM = 1.0 / particleMass;
	particleSystem->setRestOffset(restOffset*2);
	particleSystem->setContactOffset(restOffset*2 + restOffset);
	particleSystem->setParticleContactOffset(restOffset + restOffset);
	particleSystem->setSolidRestOffset(restOffset);
	particleSystem->setFluidRestOffset(0.0f);
	//gParticleSystem->setWind(PxVec3(0,0,1)*10000000.f);
	gPxScene->addActor(*particleSystem);

	// Create particles and add them to the particle system
	const PxU32 particlePhase = particleSystem->createPhase(defaultMat, PxParticlePhaseFlags(PxParticlePhaseFlag::eParticlePhaseSelfCollideFilter | PxParticlePhaseFlag::eParticlePhaseSelfCollide));
	//const PxU32 particlePhas1 = particleSystem->createPhase(defaultMa1, PxParticlePhaseFlags(PxParticlePhaseFlag::eParticlePhaseSelfCollideFilter | PxParticlePhaseFlag::eParticlePhaseSelfCollide));

	particleSystem->setSimulationFilterData(PxFilterData((PxU32)this, 111,-1, 0xFFFF0000));
	PxParticleClothBufferHelper* clothBuffers = PxCreateParticleClothBufferHelper(1, numTriangles, numSprings, numParticles, cudaContextManager);
	
	PxU32* phase = cudaContextManager->allocPinnedHostBuffer<PxU32>(numParticles);
	PxVec4* positionInvMass = cudaContextManager->allocPinnedHostBuffer<PxVec4>(numParticles);
	PxVec4* velocity = cudaContextManager->allocPinnedHostBuffer<PxVec4>(numParticles);

	PxReal x = position.x;
	PxReal y = position.y;
	PxReal z = position.z;

	// Define springs and triangles
	PxArray<PxParticleSpring> springs;
	springs.reserve(numSprings);
	PxArray<PxU32> triangles;
	triangles.reserve(numTriangles * 3);
	


	auto spaceX = particleSpacing *spaceMulX;
	auto spaceZ = particleSpacing *spaceMulY;
	for (PxU32 i = 0; i < numX; ++i)
	{
		for (PxU32 j = 0; j < numZ; ++j)
		{
			const PxU32 index = i * numZ + j;

			PxVec4 pos(x, y/*+abs(x+z)*/, z, invPM);
			phase[index] = particlePhase;
			positionInvMass[index] = pos;
			velocity[index] = PxVec4(0.0f);

			if (i > 0)
			{

				PxParticleSpring spring = { id(i - 1, j, numZ), id(i, j, numZ), spaceX, stretchStiffness, springDamping, 0 };
				springs.pushBack(spring);
			}
			if (j > 0)
			{
				PxParticleSpring spring = { id(i, j - 1, numZ), id(i, j, numZ), spaceZ/10, stretchStiffness, springDamping, 0 };
				springs.pushBack(spring);
			}

			if (i > 0 && j > 0) 
			{
				PxParticleSpring spring0 = { id(i - 1, j - 1, numZ), id(i, j, numZ), PxSqrt(2.0f) * spaceX, shearStiffness, springDamping, 0 };
				springs.pushBack(spring0);
				PxParticleSpring spring1 = { id(i - 1, j, numZ), id(i, j - 1, numZ), PxSqrt(2.0f) * spaceX, shearStiffness, springDamping, 0 };
				springs.pushBack(spring1);

				//Triangles are used to compute approximated aerodynamic forces for cloth falling down
				triangles.pushBack(id(i - 1, j - 1, numZ));
				triangles.pushBack(id(i - 1, j, numZ));

				triangles.pushBack(id(i, j - 1, numZ));

				triangles.pushBack(id(i - 1, j, numZ));
				//triangles.pushBack(id(i, j - 1, numZ));
				triangles.pushBack(id(i, j, numZ));
				triangles.pushBack(id(i, j - 1, numZ));
			}

			z += particleSpacing;
		}
		z = position.z;
		x += particleSpacing;
	}
	width = particleSpacing*(numX-1);
	height = particleSpacing*(numZ-1);;
	origCD.v[0].set(-width / 2, 0, -height / 2);
	origCD.v[1].set(-width / 2, 0, height / 2);
	origCD.v[2].set(width / 2, 0, -height / 2);
	origCD.v[3].set(width / 2, 0, height / 2);
	mCD = origCD;
	PX_ASSERT(numSprings == springs.size());
	PX_ASSERT(numTriangles == triangles.size()/3);

	clothBuffers->addCloth(0.0f, 0.0f, 0.0f, triangles.begin(), numTriangles, springs.begin(), numSprings, positionInvMass, numParticles);

	ExtGpu::PxParticleBufferDesc bufferDesc;
	bufferDesc.maxParticles = numParticles;
	bufferDesc.numActiveParticles = numParticles;
	bufferDesc.positions = positionInvMass;
	bufferDesc.velocities = velocity;
	bufferDesc.phases = phase;

	const PxParticleClothDesc& clothDesc = clothBuffers->getParticleClothDesc();
	PxParticleClothPreProcessor* clothPreProcessor = PxCreateParticleClothPreProcessor(cudaContextManager);

	PxPartitionedParticleCloth output;
	clothPreProcessor->partitionSprings(clothDesc, output);
	clothPreProcessor->release();

	gClothBuffer = physx::ExtGpu::PxCreateAndPopulateParticleClothBuffer(bufferDesc, clothDesc, output, cudaContextManager);
	gParticleSystem->addParticleBuffer(gClothBuffer);

	clothBuffers->release();

	cudaContextManager->freePinnedHostBuffer(positionInvMass);
	cudaContextManager->freePinnedHostBuffer(velocity);
	cudaContextManager->freePinnedHostBuffer(phase);



}

// ��������������Ҷ�Ӧ��ģ������

core::vector3df SnPhyCloth::mapTextureToModel(core::vector3df textureCoord, video::SColorf* scf,core::vector3df*normal) {
	//std::swap(textureCoord.x, textureCoord.y);
	int gw = numPointsX, gh = numPointsZ;
	textureCoord.X = textureCoord.X *(gh - 1)/ (gw - 1)+0.5  ;
	textureCoord.Y = textureCoord.Y + 0.5;
	int uIdx = core::clamp(static_cast<int>(textureCoord.X * (gw - 1)),0,numPointsX-2);
	int vIdx = core::clamp(static_cast<int>(textureCoord.Y * (gh - 1)),0,numPointsZ-2);  // ʹ�� gridHeight
	SMeshBuffer* buffer = (SMeshBuffer*)Mb;
	const auto& modelVertices = buffer->Vertices;
	float rtx = (textureCoord.X * (gw - 1) - uIdx) , rty = textureCoord.Y * (gh - 1) - vIdx,x,y,z;
	rtx = 1 - rtx; rty = 1 - rty;
	// ��ֵ����ģ������
	//glm::vec4 q = { (1 - rtx) * rty,rtx * rty,rtx * (1 - rty),(1 - rtx) * (1 - rty) };
	core::vector3df v[4] = {
		modelVertices[(uIdx + 0) * gh + vIdx + 0].Pos,
		modelVertices[(uIdx + 0) * gh + vIdx + 1].Pos,
		modelVertices[(uIdx + 1) * gh + vIdx + 0].Pos,
		modelVertices[(uIdx + 1) * gh + vIdx + 1].Pos };
	auto vu = v[0] * rtx + v[1] * (1 - rtx),vd = v[2] * rtx + v[3] * (1 - rtx);
	auto pos = vu * rty + vd * (1 - rty);

	if (normal)
	{
		//*normal = modelVertices[(uIdx + 0) * gh + vIdx + 0].Normal;

		core::vector3df v[4] = {
			modelVertices[(uIdx + 0) * gh + vIdx + 0].Normal,
			modelVertices[(uIdx + 0) * gh + vIdx + 1].Normal,
			modelVertices[(uIdx + 1) * gh + vIdx + 0].Normal,
			modelVertices[(uIdx + 1) * gh + vIdx + 1].Normal };
		auto vu = v[0] * rtx + v[1] * (1 - rtx),vd = v[2] * rtx + v[3] * (1 - rtx);
		*normal = (vu * rty + vd * (1 - rty)).normalize();
		
	}

	if (scf) {
		*scf=video::SColorf(img->getPixelUV(textureCoord.x,1-textureCoord.y));
		std::swap(scf->r, scf->b);
	}
	return pos;
}

void SnPhyCloth::toggleImage()
{
	if (clothImgs.size()==0) {
		auto driver = SceneManager->getVideoDriver();
		irr::io::IFileList* fl = SceneManager->getFileSystem()->createFileList("data/img/tololo");
		for (int i = 0; i < fl->getFileCount(); i++) {
			auto tex = driver->getTexture(fl->getFullFileName(i));
			clothImgs.push_back(tex);
		}
	}

	imgId++; if (imgId >= clothImgs.size()) imgId = 0;
	auto& mtr=Mesh->getMeshBuffer(0)->getMaterial();
	mtr.setTexture(0, clothImgs[imgId]);



}

SnPhyCloth::SnPhyCloth(f32 size, ISceneNode* parent, ISceneManager* mgr,
		s32 id, const core::vector3df& position,
		const core::vector3df& rotation, const core::vector3df& scale)
	: IMeshSceneNode(parent, mgr, id, position, rotation, scale),
	Mesh(0), Shadow(0), Size(size)
{
	#ifdef _DEBUG
	setDebugName("CCubeSceneNode");
	#endif

	// Setup Cloth
 
	initCloth(numPointsX, numPointsZ, { -0.5f * numPointsX * particleSpacing, 0.f, -0.5f * numPointsZ * particleSpacing });
	//gParticleSystem->setWind({100000000,10000,0});

	//for (int i = 0; i < 360; i++) {
	//	StrokePt pt;
	//	pt.p2d.X = 0.5f + cos(core::PI*2 * i / 360)*0.25f;
	//	pt.p2d.Y = 0.5f + sin(core::PI*2 * i / 360)*0.25f;
	//	pts.emplace_back(pt);
	//}

}

SnPhyCloth::~SnPhyCloth()
{
	if (Shadow)
		Shadow->drop();
	if (Mesh)
		Mesh->drop();

	if (img) img->drop();

	IRR_REMOVE_TEX(tex);
}

void SnPhyCloth::updateMesh()
{

	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	PxParticleClothBuffer* userBuffer = gClothBuffer;
	PxVec4* positions = userBuffer->getPositionInvMasses();

	const PxU32 numParticles = userBuffer->getNbActiveParticles();

	PxScene* scene;
	PxGetPhysics().getScenes(&scene, 1);
	PxCudaContextManager* cudaContextManager = scene->getCudaContextManager();

	cudaContextManager->acquireContext();

	PxCudaContext* cudaContext = cudaContextManager->getCudaContext();

	u32 trNum = userBuffer->getNbTriangles() * 3;


	if (!Mesh) {
		int gx = numPointsX - 1;
		int gz = numPointsZ - 1;
		int gnum = gx * gz;
		assert(numParticles == numPointsX * numPointsZ);
		SMeshBuffer* buffer = new SMeshBuffer();
		Mb = buffer;
		buffer->Indices.set_used(trNum);
		buffer->Vertices.set_used(numParticles);

		Mesh = new SMesh;
		Mesh->addMeshBuffer(buffer);
		buffer->drop();
		IsVisible = true;

		for (int i = 0; i < numParticles; i++) {
			int y = i % numPointsZ, x = i / numPointsZ;
			bool side = false;// x == 0 || y == 0 || x == gx || y == gz;
			auto& v = buffer->Vertices[i];
			v.Color.set(side?0:MMD_JOYSTICK_GAMECAST?225: 255, 255, 255, 255);
		
			v.TCoords.set(float(x) / (numPointsX - 1), (1 - float(y) / (numPointsZ - 1))*CLOSCR_HEIGHT_RATE);
		}
		video::SMaterial& mt = Mesh->getMeshBuffer(0)->getMaterial();
#if MMD_JOYSTICK_GAMECAST
		mt.Lighting = true;
		mt.MaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES;
		mt.DiffuseColor = mt.AmbientColor = 0xFFFFFFFF;
		mt.EmissiveColor = 0x00707070;
		mt.SpecularColor = 0;
#else
		mt.Lighting = true;
		mt.MaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES;
		mt.DiffuseColor = mt.AmbientColor = 0xCFFFFFFF;
		mt.SpecularColor = 0x00111111;
#endif
		img = driver->createImageFromFile("data/img/pink.png");// 1.jpg");// .jpg");
		mt.setTexture(0, tex=driver->addTexture("clothtex",img));
		//gParticleSystem->addRigidAttachment(sphere);
		if (!idxBuf) idxBuf = new u32[trNum];
		cudaContext->memcpyDtoH(idxBuf, CUdeviceptr(userBuffer->getTriangles()), sizeof(PxReal) * trNum);
		for (int i = 0; i < trNum; i++)
			buffer->Indices[i] = idxBuf[i];
	}
	if ( 1) {
		SMeshBuffer* buffer = (SMeshBuffer*)Mb;

		if (!ptcBuf) ptcBuf = new float4[numParticles];
		cudaContext->memcpyDtoH(ptcBuf, CUdeviceptr(positions), sizeof(PxVec4) * numParticles);
		for (int i = 0; i < numParticles; i++)
			buffer->Vertices[i].Pos.set(ptcBuf[i].x, ptcBuf[i].y, -ptcBuf[i].z);
#if 1
		thread = &driver->threadPool.getNextThread();
		thread->get()->addJob([=]() {
			SceneManager->getMeshManipulator()->recalculateNormals(Mesh, true);
			});
#else	
		SceneManager->getMeshManipulator()->recalculateNormals(Mesh, true);
#endif
	}


	if (!sphere) {
		static PxMaterial* gMaterial = gPxPhysics->createMaterial(0.0f, 0.0f, 0.0f);
		PxShape* shape = gPxPhysics->createShape(PxSphereGeometry(1.f), *gMaterial);
		sphere = gPxPhysics->createRigidDynamic(PxTransform(PxVec3(0.f, 0.0f, 0.f)));
		sphere->attachShape(*shape);
		sphere->setRigidBodyFlag(PxRigidBodyFlag::eKINEMATIC, true);
		gPxScene->addActor(*sphere);
		shape->release();	//shape->setSimulationFilterData(PxFilterData((PxU32)this, 111,0, 0));
	}

	PxParticleAttachmentBuffer* pab = PxCreateParticleAttachmentBuffer(*gClothBuffer, *gParticleSystem);
#if 0
	float x = 0,y =0,z = 0;
	for (PxU32 i = 0; i < numPointsX; ++i)
	{
		for (PxU32 j = 0; j < numPointsZ; ++j)
		{
			PxVec4 pos(x, y/*+abs(x+z)*/, z, 1.0f / 100);
			//if (i == 0 || j == 0 || i == numX - 1 || j == numZ - 1)
			pab->addRigidAttachment(sphere, id(i, j, numPointsZ), PxVec3(0, 0, 0) + pos.getXYZ());
			z += particleSpacing;
		}
		z = 0;
		x += particleSpacing;
	}
#else
	int cnn = 2;

	for (int i = 0; i <= 1; i++) 
	{
		auto& l0 = mCD.v[i * 2];
		auto& l1 = mCD.v[i * 2 + 1];
		int ix = i == 0 ? 0 : numPointsX - 1;
		for (PxU32 j = 0; j < numPointsZ; ++j) // if (j<cnn || j>numPointsZ-1-cnn  )
		{
			auto p = l0 + (l1 - l0) * (j) / (numPointsZ - 1);
			PxVec3 pos(p.x, p.y, -p.z);
			pab->addRigidAttachment(sphere, id(ix, j, numPointsZ), pos);
		}
	}

	for (int i = 0; i <= 1; i++) 
	{
		auto& l0 = mCD.v[i];
		auto& l1 = mCD.v[i + 2];
		int iz = i == 0 ? 0 : numPointsZ - 1;
		for (PxU32 j = 1; j < numPointsX - 1; ++j)// if (j<cnn || j>numPointsX-1-cnn)
		{
			auto p = l0 + (l1 - l0) * (j) / (numPointsX - 1);
			PxVec3 pos(p.x, p.y, -p.z);
			pab->addRigidAttachment(sphere, id(j, iz, numPointsZ), pos);
		}
	}

#endif		
	pab->copyToDevice(); 
	 
	PX_DELETE(pab);
	// for debug purposes only:
	cudaContextManager->releaseContext();

	thread->get()->wait();
}

//std::chrono::high_resolution_clock::time_point a_tprecalculateNormals; 

void SnPhyCloth::OnAnimate(u32 timeMs)
{
	ISceneNode::OnAnimate(timeMs);

	//uu::cpuCountBegin(a_tprecalculateNormals, "recalculateNormals");;
	updateMesh();
	//CPU_COUNT_E(recalculateNormals);
}
//! renders the node.
void SnPhyCloth::render()
{
	if (!gParticleSystem)
		return;
	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	driver->setTransform(video::ETS_WORLD, AbsoluteTransformation);

	// overwrite half transparency	


	video::SMaterial& mat = Mesh->getMeshBuffer(0)->getMaterial();
	mat.MaterialType = drawLine?EMT_SOLID_LINE_TRIANGLE_LIST://driver->getPassType()==IrrPassType_Mirror?EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES_REVERSE:
		EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES;
	driver->setMaterial(mat);
	//driver->drawMeshBuffer(Mesh->getMeshBuffer(0));
	auto mb = Mesh->getMeshBuffer(0);

#if SHOW_SOLID_SDF_SLICE
	particleSystem->copySparseGridData(sSparseGridSolidSDFBufferD, PxSparseGridDataFlag::eGRIDCELL_SOLID_GRADIENT_AND_SDF);
#endif
	//if (APP_THREAD_POOL_NEED_WAIT) thread->get()->wait();//no wait is OK?
	driver->drawVertexPrimitiveList(mb->getVertices(), mb->getVertexCount(), mb->getIndices(), mb->getIndexCount() / 3, mb->getVertexType(), scene::EPT_TRIANGLES, mb->getIndexType());



}


//! returns the axis aligned bounding box of this node
const core::aabbox3d<f32>& SnPhyCloth::getBoundingBox() const
{
	return Mesh->getMeshBuffer(0)->getBoundingBox();
}


//! Removes a child from this scene node.
//! Implemented here, to be able to remove the shadow properly, if there is one,
//! or to remove attached childs.
bool SnPhyCloth::removeChild(ISceneNode* child)
{
	if (child && Shadow == child)
	{
		Shadow->drop();
		Shadow = 0;
	}

	return ISceneNode::removeChild(child);
}

//! Creates shadow volume scene node as child of this node
//! and returns a pointer to it.
IShadowVolumeSceneNode* SnPhyCloth::addShadowVolumeSceneNode(
		const IMesh* shadowMesh, s32 id, bool zfailmethod, f32 infinity)
{
#if _SHADOW_VOLUME_SCENE_NODE_
	if (!SceneManager->getVideoDriver()->queryFeature(video::EVDF_STENCIL_BUFFER))
		return 0;

	if (!shadowMesh)
		shadowMesh = Mesh; // if null is given, use the mesh of node

	if (Shadow)
		Shadow->drop();

	Shadow = new CShadowVolumeSceneNode(shadowMesh, this, SceneManager, id,  zfailmethod, infinity);
#endif
	return Shadow;
}


void SnPhyCloth::OnRegisterSceneNode()
{
	if (IsVisible)
		SceneManager->registerNodeForRendering(this);
	ISceneNode::OnRegisterSceneNode();
}




//! returns the material based on the zero based index i.
video::SMaterial& SnPhyCloth::getMaterial(u32 i)
{
	static video::SMaterial s;
	if (!Mesh) return s;
	return Mesh->getMeshBuffer(0)->getMaterial();
}


//! returns amount of materials used by this scene node.
u32 SnPhyCloth::getMaterialCount() const
{
	return 1;
}
 

//! Creates a clone of this scene node and its children.
ISceneNode* SnPhyCloth::clone(ISceneNode* newParent, ISceneManager* newManager)
{
	if (!newParent)
		newParent = Parent;
	if (!newManager)
		newManager = SceneManager;

	SnPhyCloth* nb = new SnPhyCloth(Size, newParent,
		newManager, ID, RelativeTranslation);

	nb->cloneMembers(this, newManager);
	nb->getMaterial(0) = getMaterial(0);
	nb->Shadow = Shadow;
	if ( nb->Shadow )
		nb->Shadow->grab();

	if ( newParent )
		nb->drop();
	return nb;
}


} // end namespace scene
} // end namespace irr

 