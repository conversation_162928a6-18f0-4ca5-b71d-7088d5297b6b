﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//
#include "mmdPCH.h"

#define PEI_ZHONG		0
#define BOX_FOOT  1
#define OTHER_RB_MASS_CHANGE 1
#define CVT_ALIGN_TO_DYNAMIC_JOINT 1

#define MAX_Thread 8 //orig: 16
#define MAX_EXT_NODE 32

#define SMALL_DYNAMIC_RB_DIMMER 10.f
#define WINGS_TO_ANIM_Y 0
#include "MMDNode.h"	//Types
#include "PMXModel.h"
#include "PMXFile.h"
#include "MMDPhysics.h"	//"MMDPhysics.cpp"

#include <Saba/Base/Path.h>
#include <Saba/Base/File.h>
#include <Saba/Base/Log.h>
#include <Saba/Base/Singleton.h>
 
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

#include <glm/gtx/quaternion.hpp>
#include <glm/gtx/dual_quaternion.hpp>
#include <map>
#include <limits>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <thread>
#include <mutex>
#include <condition_variable>
#include "Helpers/ThreadPool/ThreadPool.h"
#include <glm/gtx/matrix_decompose.hpp>
#include <jsoncpp/json5cpp.h>
#include "../../../AppMainLib/src/UaJsonSetting.h"
#include "MMDPhysicsPhysX.h"
#define INFLATE_MATERIAL PHYSX_SABA_INFLATE
#include <unordered_set>
using namespace glm;
 float gFloatTestPm=1;
namespace saba
{
	const float PI = glm::pi<float>();
	const float PIx2 = glm::pi<float>()*2.f;
}
using namespace saba;

PMXModel::PMXModel()
	: m_parallelUpdateCount(0)
{

}

PMXModel::~PMXModel()
{
	Destroy();
	//childModels.clear();
}

void PMXModel::clearPNUV(bool freeAll) {
	Sd->positions.clear();
	Sd->normals.clear();
	Sd->uvs.clear();
	m_updatePositions.clear();
	m_updateNormals.clear();
	m_updateUVs.clear();
	if (freeAll)
		Sd->vertexBoneInfos.clear();
}

void PMXModel::InitializeAnimation()
{
	ClearBaseAnimation();

	for (auto& node : (*m_nodeMan.GetNodes()))
	{
		node->SetAnimationTranslate(glm::vec3(0));
		node->SetAnimationRotate(glm::quat(1, 0, 0, 0));
	}

	InitializeAnimationUpdate();

	//ResetPhysics();
}
void PMXModel::InitializeAnimationUpdate()
{
	BeginAnimation();

	for (auto& node : (*m_nodeMan.GetNodes()))
	{
		node->UpdateLocalTransform();
		node->mLocalInit = node->GetLocalTransform();
	}

	for (auto& morph : (*Sd->morphMan.GetMorphs()))
	{
		morph->SetWeight(0);
	}

	for (auto& ikSolver : (*m_ikSolverMan.GetIKSolvers()))
	{
		ikSolver->Enable(true);
	}
if (fcp.addRootBone)
	m_nodeMan.GetNode(0)->UpdateGlobalTransform(false);
else
	for (const auto& node : (*m_nodeMan.GetNodes()))
	{
		if (node->GetParent() == nullptr)
		{
			node->UpdateGlobalTransform(0);
		}
	}
 
	for (auto pmxNode : m_sortedNodes)
	{
		if (pmxNode->GetAppendNode() != nullptr)
		{
			pmxNode->UpdateAppendTransform();
			pmxNode->UpdateGlobalTransform();
		}
		if (pmxNode->GetIKSolver() != nullptr)
		{
			auto ikSolver = pmxNode->GetIKSolver();
			ikSolver->Solve();
			pmxNode->UpdateGlobalTransform();
		}
	}
if (fcp.addRootBone)
	m_nodeMan.GetNode(0)->UpdateGlobalTransform(false);
else
	for (const auto& node : (*m_nodeMan.GetNodes()))
	{
		if (node->GetParent() == nullptr)
		{
			node->UpdateGlobalTransform(0);
		}
	}
 
	EndAnimation();
}
void PMXModel::BeginAnimation()
{
	{
		auto r = glm::mat4_cast(glm::quat(rt1Rt));
		auto t = glm::translate(glm::mat4(1), rt1Tr);
		MAT_MUL_ACC(rootM1, t, r);
#if SABA_HAS_SCALE 
		auto s = glm::scale(glm::mat4(1), rt1Sc);
		MAT_MUL_ACC(rootM1, rootM1, s);
#endif
	}

	auto r = glm::mat4_cast(rootRt);
	auto t = glm::translate(glm::mat4(1), rootTr);

	MAT_MUL_ACC(rootM0, t, r);
#if SABA_HAS_SCALE 
	auto s = glm::scale(glm::mat4(1), rootSc);
	MAT_MUL_ACC(rootM0, rootM0, s);
#endif
	rootM0i = glm::inverse(rootM0);

	MAT_MUL_ACC(mmdRootMat, rootM0, rootM1);
	mmdRootMatInv = glm::inverse(mmdRootMat);


	for (auto& node : (*m_nodeMan.GetNodes()))
	{
		node->BeginUpdateTransform();
	}
	//CPU_COUNT_B(MC);
#if 1
	memset(m_morphPositions.data(), 0, sizeof(glm::vec4) * m_morphPositions.size());
	memset(m_morphUVs.data(), 0, sizeof(glm::vec4) * m_morphUVs.size());
#else
	size_t vtxCount = m_morphPositions.size();
	for (size_t vtxIdx = 0; vtxIdx < vtxCount; vtxIdx++)
	{
		m_morphPositions[vtxIdx] = glm::vec3(0);
		m_morphUVs[vtxIdx] = glm::vec4(0);
	}
#endif
	//CPU_COUNT_E(MC);

}
void PMXModel::resetRigidBodies(int flag)
{
	auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	if (flag & 1) {
		for (auto& rb : (*rigidbodys))
		{

			rb->ResetTransform();// updateStateOnNode();
		}
	}

	for (auto& rb : (*rigidbodys))
	{
		rb->ResetMovement(0);
	}


}

void PMXModel::resetDynBodies()
{
	auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		if (rb->dynRbType == 1)
			rb->ResetMovement(0);
	}
}

std::vector<MMDRigidBody*> saba::PMXModel::getRigidBodys(int dyn)
{ 
	  std::vector<MMDRigidBody*> bds;
	  auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	  for (auto& rb : (*rigidbodys))
	  {
		  if (rb->dynRbType == 1 || dyn==0)
			  bds.push_back(rb);
	  }
	  return bds;
}

void PMXModel::EndAnimation()
{
	for (auto& node : (*m_nodeMan.GetNodes()))
	{
		node->EndUpdateTransform();
	}
	if (resetPhyCD > 0) {
		resetPhyCD--;
		resetRigidBodies();
	}
}

void PMXModel::UpdateMorphAnimation()
{
	if (!needMorph)
		return;
	// Morph の処理
	BeginMorphMaterial();
	morphVtxMin = mVtxCount; morphVtxMax = 0;
	if (!Sd) throw;

	memset(controlPanelAccum, 0, sizeof(controlPanelAccum));

	auto& morphs = (*Sd->morphMan.GetMorphs());
	for (size_t i = 0; i < morphs.size(); i++)
	{
		auto& morph = morphs[i];
		if (morph->exd.flag & MPFlag::mpfCb)  cbMorph(1, *morph.get());
		Morph(morph.get(), i == Sd->morphMan.previewIdx ? 1.f : i == Sd->morphMan.addWeightIdx ? Sd->morphMan.addWeight : morph->GetWeight());
	}

	EndMorphMaterial();
}


glm::quat getWingFlapRotation(
	float time,
	float flapSpeed,
	float minAngle,
	float maxAngle,
	const glm::vec3& flapAxis,
	bool isRightWing = false,
	float phaseOffset = 0.0f)
{
	// Calculate phase based on time and speed, with potential offset for left/right wing coordination
	float phase = time * flapSpeed * glm::pi<float>() * 2.0f;

	// Apply phase offset
	phase += phaseOffset * glm::pi<float>() * 2.0f;

	// Apply wing side offset if needed (opposite flapping)
	if (isRightWing) {
		phase += glm::pi<float>();  // 180 degree phase shift for opposite wing
	}

	// Calculate smoothed angle using sine wave for natural flapping motion
	float angleFactor = 0.5f + 0.5f * glm::sin(phase);
	float currentAngle = glm::mix(minAngle, maxAngle, angleFactor);

	// Convert to radians
	float angleRadians = glm::radians(currentAngle);

	// Create rotation quaternion around the specified axis
	return glm::angleAxis(angleRadians, glm::normalize(flapAxis));
}

int PMXModel::UpdateNodeAnimation(bool afterPhysicsAnim, bool realMove)
{
	int afcc = 0;
	afterPhysics = afterPhysicsAnim;
	if (afterPhysicsAnim && !needPhysics) return afcc;



	for (auto nd : m_sortedNodes)
	{
		float scale = pmx.fcp.modelScaleVec3.x * nd->absScale.x;
		if (!afterPhysicsAnim)
		{



		}
		else {

			//if (pmxNode->pmxBone->updateTrs) {
			//	vec3 pos = pmxNode->mGlobalInit[3]; pos.y = 0;  vec3 norm = glm::normalize(pos);
			//	float tp = -gPhyTime * PI * 2 * pmxNode->pmxBone->trsTps;
			//	float c = cos(tp), s = sin(tp);
			//	pos = pos * std::max(-0.825f, (0.2f + c * 2.f));
			//	pos.y = 1 + s * 5;
			//	pmxNode->SetAnimationTranslate(pos);
			//	pmxNode->UpdateLocalTransform();
			//	pmxNode->UpdateGlobalTransform();
			//}
		}
		if (nd->IsDeformAfterPhysics() != afterPhysicsAnim)
		{
			continue;
		}
		bool updated = nd->afterPhyUpdatedAnim = false;
		const auto& bpm = nd->pmxBone->anim;
		if (bpm) {
			float st2pi = (attachParentNode? attachParentNode->model->flapT: flapT) * PIx2 * animSpdMul;

			//Flap
			if (bpm->aniFlap) {
				float ang = glm::mix(bpm->flapAngTmul.x, bpm->flapAngTmul.y, 0.5f + 0.5f * glm::sin(st2pi * bpm->flapAngTmul.z ));
				if (nd->rb0 && nd->rb0->GetActivation())
				{
#if 0
					auto qr=getWingFlapRotation(gSceneTime, bpm->flapAngTmul.z, bpm->flapAngTmul.x, bpm->flapAngTmul.y, bpm->flapAxis, 0, 0);
					nd->rb0->jtsToParent[0]->setDrivePose(glm::mat4(qr), 3, bpm->flapPM[0]);

#else
					nd->rb0->jtsToParent[0]->setDrivePose( //nd->mLocalAnim *
						glm::rotate(glm::mat4(1.0f), glm::radians(ang)
							, bpm->flapAxis), 3, bpm->flapPM[0]);
#endif

					//nd->rb0->jtsToParent[0]->setDriveVel(vec3(0, 100, 0), vec3(0, 111, 00), 3);

					
				}
				else {
					nd->SetAnimationRotate(glm::rotate(glm::mat4(1.0f), glm::radians(ang), bpm->flapAxis));
				}
				updated = true;
			}
			//Rotate
			if (bpm->aniRtt) {
				if (nd->rb0 && nd->rb0->GetActivation())
				{
					nd->rb0->setAngularVel(glm::vec3(bpm->rttSpeed * animSpdMul));
				}
				else nd->SetAnimationRotate(glm::quat((bpm->rttSpeed) * (bpm->aniFlap ? gSceneTime : gFrameTime) * animSpdMul) * nd->GetAnimationRotate());
				updated = true;
			}
			//Translate
			if (bpm->aniTrs)
			{
				updated = true;
				switch (bpm->trsType)
				{
				case bttSrcTgt:
					vec3 pos = glm::mix(bpm->trsV0* scale, bpm->trsV1* scale, 0.5f + 0.5f * glm::sin((bpm->trsPM[0] * st2pi + PIx2 * bpm->trsPM[1])));
					if (nd->rb0 && nd->rb0->GetActivation())
					{
					if (attachParentNode) {
						float& wfm = attachParentNode->model->wingFlapTMul;

						wingFlapTMul = wfm;

						auto yp = attachParentNode->rb0->getPosition(); auto dir = glm::fastNormalize(attachParentNode->getRbAnimGlobalPos() - yp);
						 
							attachParentNode->rb0->addLinearVel((dir * wingFlapTMul * 0.01f * nd->absScale.x * pos.y)*vec3(1,0,1)); //CENTER FORCE
							attachParentNode->rb0->setAngVelToRotateOnNode(attachParentNode->mGlobalAnim, 3);

#if WINGS_TO_ANIM_Y
						nd->rb0->scaleVel(0.5f, 3);
						nd->rb0->addLinearVel( pos*nd->absScale.x * wingFlapTMul * gFloatTestPm );
#endif

					}
					else {
						nd->SetAnimationTranslate(pos);
						//nd->rb0->addLinearVelToPos(anim, nd->absScale.x * wingFlapTMul * gFloatTestPm); 
					}
					}

					else  
					nd->SetAnimationTranslate(pos);
					break;
				case bttTrack:
					if (!tgtNode) tgtNode = nd->GetParent();
 					if (tgtNode && nd->rb0  )
					{
						float timePast = gSceneTime - tgtNodeChgTime;
						bool chg = bpm->trsFlag & 1;
						if (chg) {
							bool timePastChg = timePast > 1.f && (rand() % 100 < 10);
							if (nd->rb0 == rootRb && !nd->rb0->cbHit && timePast > 0.2f) {
							nd->rb0->usrDat.callHitCb = true;
							nd->rb0->cbHit = [=](saba::PhysicsEngineObjectUserData* ud) {
								auto lcrb = ud->lastContact->owner;
								if (lcrb && lcrb->dynRbType == 1 && lcrb->node && (tgtNode != lcrb->node || timePastChg)) {
									if ( lcrb->parentRb && lcrb->parentRb->node  &&( lcrb->node->isHand|| timePastChg)) tgtNode = lcrb->parentRb->node;					
									else if (timePastChg && lcrb->parentRb == nullptr && lcrb->node->GetChild())
									{
										auto nd = lcrb->node->GetChild();
										while (!nd->rb0 && !nd->rb0->dynRbType) nd = nd->GetNext();
										if (nd->rb0) tgtNode = nd;
										else tgtNode = lcrb->node;
									}
									else
										tgtNode = lcrb->node;

									tgtNodeChgTime = gSceneTime;
								}
								};
							}
							else if (timePastChg)
							{
								auto lcrb = tgtNode->rb0;
								if (lcrb && lcrb->parentRb && lcrb->parentRb->node || lcrb->node->isHand) {
									tgtNode = lcrb->parentRb->node;

									tgtNodeChgTime = gSceneTime;
								}

							}
						}
						vec3 tgt = tgtNode->getIfRbPos();
						vec3 dt = tgt - nd->rb0->getPosition();
						float dtLen = glm::length(dt); float dtLen1 = std::max(scale, dtLen)/scale;
						vec3 dir = glm::fastNormalize(dt);
						vec3 up = (tgtNode->rb0->getRotation() * tgtNode->rb0->rttLocal) * vec3(0, 1, 0);
						vec3 dir90 = glm::cross(dir, up);
						//nd->rb0->scaleVel(std::min(1.f,0.9f + 0.17f/dtLen1), 1);
						float tpmul = (!chg || timePast < 0.5f ? 1.f : timePast);
						nd->rb0->addLinearVel(dir90* bpm->trsPM[0] * tpmul);
						nd->rb0->addLinearVel(dt * bpm->trsPM[1]+up* bpm->trsPM[2] * tpmul);
						nd->rb0->addLinearVel((nd->rb0->getRotation()* nd->rb0->rttLocal)* vec3(0, bpm->trsPM[3]/ dtLen1 * tpmul, 0));
						//irr::scene::sbFw2LineD("pt",nd->rb0->getPosition(), nd->rb0->getPosition() + up*10.f, 0xFF00FFFF, 60);
					}
 
					break;
				default:
					updated = false;
					break;
				}

			}
		}
		if (updated) nd->apuaMat0 = nd->GetLocalTransform();
		nd->UpdateLocalTransform();
		if (realMove && updated ) {
			nd->UpdateGlobalTransform(0);
			if (nd->rb0 && nd->rb0->parentRb) {
				nd->apuaMat1 = nd->GetLocalTransform();
				nd->afterPhyUpdatedAnim = afterPhysicsAnim;
				auto pn = nd->GetParent();
				while (pn && pn->rb0!=nd->rb0->parentRb) { 
					nd->apuaMat0 = pn->GetLocalTransform() * nd->apuaMat0;
					nd->apuaMat1 = pn->GetLocalTransform() * nd->apuaMat1;

					pn = pn->GetParent();
				}
				//nd->rb0->setGlmMat(nd ->GetGlobalTransform()* nd->rb0->getOfsMat());
			}
			
			
		}
	}

	if (fcp.addRootBone) {
		auto rootNode = m_nodeMan.GetNode(0);
		if (!afterPhysicsAnim) {
			rootNode->UpdateGlobalTransform(0);
		}
	}
	else	
	for (auto pmxNode : m_sortedNodes)
	{
		if (pmxNode->IsDeformAfterPhysics() != afterPhysicsAnim)
		{
			continue;
		}

		if (pmxNode->GetParent() == nullptr)//root node only
		{
			pmxNode->UpdateGlobalTransform(1);
		}
	}

	for (auto pmxNode : m_sortedNodes)
	{
		if (pmxNode->IsDeformAfterPhysics() != afterPhysicsAnim)
		{
			continue;
		}

		if (pmxNode->GetAppendNode() != nullptr)
		{
			pmxNode->UpdateAppendTransform();
			pmxNode->UpdateGlobalTransform(0);
			if (afterPhysicsAnim && pmxNode->rb0 && !pmxNode->rb0->GetActivation()) pmxNode->rb0->updateMotionState();
		}

		MMDNode* pn = pmxNode;
		if (realMove && (pmxNode->exd.flag & MNFlag::mnfCb) && cbNode)
		{
			cbNode(NodeCbStage::NS10, *pn);
			//DP((L"AFTER %d  %s",afterPhysicsAnim,pmxNode->GetNameU().c_str()));
		}

		auto ikSolver = pmxNode->GetIKSolver();
		if (ikSolver != nullptr)
		{
			if (realMove && (pmxNode->exd.flag & MNFlag::mnfSaveVMD) && cbNode) 
				cbNode(NodeCbStage::NS18, *pmxNode);
			//if (!(allPhyAct && ALL_NODE_PHYSICS)) 
			{
				ikSolver->maxChain = (allPhyAct && ALL_NODE_PHYSICS) ? 2 : 999;
				ikSolver->Solve();
				pmxNode->UpdateGlobalTransform(0);
			}

		}
	}

#if 1
	if (realMove && saveVMD && !afterPhysicsAnim)
		for (auto pmxNode : m_sortedNodes) if (pmxNode->GetIKSolver() == nullptr)
		{
			if ((pmxNode->exd.flag & MNFlag::mnfSaveVMD) && cbNode)
				cbNode(NodeCbStage::NSSaveVmd, *pmxNode);
		}
#endif
	bool jellyfish = false; float tp = 0; MMDNode* jpn = nullptr;
	for (auto pmxNode : m_sortedNodes)
	{
#if FRAME_NODE_FW
		if (afterPhysicsAnim) {
			if (pmxNode->pmxBone->ndFwCount > 0) {
				cbNode(NodeCbStage::NS_NodeFw, *pmxNode);
			}
		}
#endif
		if (pmxNode->IsDeformAfterPhysics() != afterPhysicsAnim)
		{
			continue;
		}

		if (pmxNode->GetParent() == nullptr) //root node only
		{
			pmxNode->UpdateGlobalTransform(0);
		}
		//if (!afterPhysicsAnim)
		{
			pmxNode->mGlobalAnim = pmxNode->GetGlobalTransform();
			pmxNode->mLocalAnim = pmxNode->GetLocalTransform();
		}
		if (auto ba=pmxNode->pmxBone->anim )
		{ 
			if (ba->aniTrs && ba->trsType == bttJellyfish && pmxNode->rb0) {
				//JQL Rina skirt 
				jellyfish = true; auto& v0 = ba->trsV0; auto& v1 = ba->trsV1;
				vec3 pos = pmxNode->mGlobalInit[3]; pos.y = 0;  float hR = glm::length(pos);
				tp = -gPhyTime * PI * 2 * ba->trsTps;
				float c = cos(tp), s = sin(tp);
				pos = pos * std::max(v0.z, (v0.x + c * v0.y));  //trsV0: x + cos y, max z
				pos.y = hR * (v1.x + s * v1.y);
				//pos *= 0.1f;
				jpn = pmxNode->GetParent();
				pos = glh::matTransformVec(pmxNode->GetParent()->GetGlobalTransform() * pmxNode->GetLocalTransform(), pos);
				pmxNode->rb0->setLinearVelToPos(pos, hR * ba->trsPM[0]);
				pmxNode->rb0->ReflectGlobalTransform();
				//pmxNode->rb0->SetCoMTransform(pmxNode->GetGlobalTransform());
				//pmxNode->rb0->setAngVelToPos(glm::mat4(1), 10,10);
			}

		}
		afcc++;

	}
	//if (afterPhysicsAnim && jellyfish) {
	//	float  s = sin(tp);
	//	vec3 dir = vec3(0, std::max(0.f, - s * 10), 0); glh::matRotateVec(jpn->GetGlobalTransform(), dir);
	//	addBodyVel(dir,true);
	//}
	return afcc;
}

void PMXModel::ResetPhysics(bool dynActive)
{
	MMDPhysicsManager* physicsMan = GetPhysicsManager();
	auto physics = physicsMan->GetMMDPhysics();
	resetRigidBodies();
	if (physics == nullptr)
	{
		return;
	}

	auto rigidbodys = physicsMan->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		//rb->SetActivation(false); //physx may error when set kim, todo: other problem when commented
		rb->ResetTransform();
	}

	//physics->Update(1.0f / 60.0f);

	for (auto& rb : (*rigidbodys))
	{
		rb->ReflectGlobalTransform();
	}

	for (auto& rb : (*rigidbodys))
	{
		rb->CalcLocalTransform();
	}
	if (fcp.addRootBone)
	m_nodeMan.GetNode(0)->UpdateGlobalTransform(false);
 else
	for (const auto& node : (*m_nodeMan.GetNodes()))
	{
		if (node->GetParent() == nullptr)
		{
			node->UpdateGlobalTransform(false);
		}
	}

	for (auto& rb : (*rigidbodys))
	{
		rb->ResetMovement(physics);
	}

	for (auto& rb : (*rigidbodys))
	{

		rb->SetActivation(rb->dynRbType ? dynActive : true);

	}

}
void PMXModel::setCollision(bool isOn, bool preDelete)
{

	auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		//rb->SetActivation(false); //physx may error when set kim, todo: other problem when commented
		rb->setCollideFilterMask(isOn ? 0 : 1, 0);
		if (preDelete)
			rb->SetActivation(false);
	}

}
void PMXModel::UpdatePhysicsAnimation(float elapsed)
{
	if (!needPhysics) return;
	MMDPhysicsManager* physicsMan = GetPhysicsManager();
	auto physics = physicsMan->GetMMDPhysics();

	if (physics == nullptr)
	{
		return;
	}


	auto rigidbodys = physicsMan->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		rb->updateMotionState(); // set kim/nonActive rb to node
	}

	physics->Update(elapsed);
	//CPU_COUNT_B(cc);
	for (auto& rb : (*rigidbodys))
	{
		if (!rb->isSubRb)
			rb->ReflectGlobalTransform();
	}
	if (elapsed == 0)
		return;
	//CPU_COUNT_E(cc);
	for (auto& rb : (*rigidbodys))
	{
		if (!rb->isSubRb)
			rb->CalcLocalTransform();
	}
	if (fcp.addRootBone)
	m_nodeMan.GetNode(0)->UpdateGlobalTransform();
	else
	for (const auto& node : (*m_nodeMan.GetNodes()))
	{
		if (node->GetParent() == nullptr)
		{
			node->UpdateGlobalTransform();
		}
	}


	if (attachedWing) {
		vec3 posA = rootRb->node->getRbAnimGlobalPos();
		float dy = rootRb->node->rb0->pos.y - posA.y;
#if WINGS_TO_ANIM_Y
		wingFlapTMul = clamp(glm::mix(wingFlapTMul, pow(1.1f, -dy), 0.2f), 0.33f, 3.f);
#else
		wingFlapTMul = clamp(glm::mix(wingFlapTMul, pow(1.2f, rootRb->node->rb0->vel.y*0.3f) , 0.2f), 0.33f, 3.f);
#endif
		flapT += elapsed * wingFlapTMul ;
	}
	else {
		flapT += elapsed ;
	}
}
void PMXModel::onPhysicsStep(int step, int stepCount, float stepTime) //call by IrrSaba::updatePhysicsStep
{
	auto rigidbodys = m_physicsMan.GetRigidBodys();


	for (auto& rb : (*rigidbodys)) {
		rb->onPhyStepUpdate(step, stepCount);
		if (rb->isBodyPart  //&& (MMDPhysics::curStep == 1 || rb->GetActivation())
			) // eKINEMATIC update pos on curStep=1
		{
			rb->updatePhyState(0);
		}
		if (step == stepCount - 1) if (rb->node && rb->node->afterPhyUpdatedAnim) {
			auto nd = rb->node;
			float ratio = float(step + 1) / float(stepCount);

			auto pt = nd->rb0->parentRb->getNodeTransform();
			for (auto& rb : nd->rbs) {
				auto m0 = pt * nd->apuaMat0 * rb->getOfsMat();
				auto m1 = pt * nd->apuaMat1 * rb->getOfsMat();
				//rb->setGlmMat(glh::interpolateTransform(m0, m1, ratio));
				rb->setGlmMat(glm::interpolate(m0, m1, ratio));
				//rb->setLinearVelToPos(glm::interpolate(m0, m1, ratio)[3],100);

				if (step == stepCount - 1) {
					//rb->addForce(vec3(0),true);
					//rb->ReflectGlobalTransform();
#if 0
					vec3 v, av;
					glh::getTransformDeltaVel(m0, m1, stepTime, v, av);
					rb->setLinearVel(v);
					rb->setAngularVel(av);
#endif
				}

				//rb->addRotationToMatOnNode(m, 0.1);rb->scaleVel(0.2f, 3);rb->addLinearVelToPos(m[3], 25);
			}
		}
	}
	if (m_physicsMan.sortedRBs.size()) {
		auto rb = m_physicsMan.sortedRBs[0];
		const glm::mat4 m = rb->getNodeTransform();
		const glm::quat qr(m);
		const glm::vec3 downDir = qr * glm::vec3(0, -1, 0);
		groundAngleDeg = glm::degrees(glm::acos(glm::dot(glm::vec3(0, -1, 0), downDir)));
	}
}

void PMXModel::UpdatePhysicsBodiesToNodes()
{
	auto rigidbodys = m_physicsMan.GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		rb->ResetTransform();
	}
}

void PMXModel::moveAllOffset(glm::vec3 ofs, bool moveRootNode, bool resetRb)
{
	auto rn = GetNodeManager()->getRootNode();
	if (moveRootNode) rn->setAnimGlobalPos(rn->getGlobalPos() + ofs);
	auto rigidbodys = m_physicsMan.GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		rb->moveRbOfs(ofs);
	}
	if (resetRb) resetRigidBodies();
}
void saba::PMXModel::moveAllRbTo(glm::vec3 pos, bool moveRootNode, bool resetRb)
{
	vec3 ofs = pos - getRb(0)->getPosition();
	moveAllOffset(ofs, moveRootNode, resetRb);
}
void saba::PMXModel::moveAllRbToAnim()
{
	auto rb0 = (*GetPhysicsManager()->GetRigidBodys())[0];
	moveAllOffset(vec3(rb0->node->mGlobalAnim[3]) - rb0->getPosition(), false);
}
void PMXModel::UpdatePhysicsAnimation2Pass(float elapsed, uint32_t flag)
{
	if (!needPhysics) return;
	bool nonDyn = flag & 1;
	MMDPhysicsManager* physicsMan = GetPhysicsManager();
	auto physics = physicsMan->GetMMDPhysics();

	if (physics == nullptr)
	{
		return;
	}

	auto rigidbodys = physicsMan->GetRigidBodys();
	//if (!nonDyn )
	for (auto& rb : (*rigidbodys))
	{
		//rb->SetActivation(true);
		bool af = rb->node && rb->node->IsDeformAfterPhysics();
		if (!nonDyn && !af || nonDyn && af)
			rb->updateMotionState();
	}
	if (!nonDyn)
		physics->Update(elapsed);
	//CPU_COUNT_B(cc);
	for (auto& rb : (*rigidbodys))
	{
		if (!rb->isSubRb) {
			bool af = rb->node && rb->node->IsDeformAfterPhysics();
			if (!nonDyn && !af || nonDyn && af && !rb->collideOtherChar)
				//if (!nonDyn || !rb->collideOtherChar)
				rb->ReflectGlobalTransform();
		}
	}
	//CPU_COUNT_E(cc);
	for (auto& rb : (*rigidbodys))
	{
		if (!rb->isSubRb) {
			bool af = rb->node && rb->node->IsDeformAfterPhysics();
			if (!nonDyn && !af || nonDyn && af && !rb->collideOtherChar)
				//if (!nonDyn || !rb->collideOtherChar)
				rb->CalcLocalTransform();
		}
	}
	if (!nonDyn) {
	if (fcp.addRootBone)
		m_nodeMan.GetNode(0)->UpdateGlobalTransform();
	else		
		for (const auto& node : (*m_nodeMan.GetNodes()))
		{
			if (node->GetParent() == nullptr)
			{
				node->UpdateGlobalTransform();
			}
		}
 	}
}

void PMXModel::Update(bool onlyTrans)
{
	auto& nodes = (*m_nodeMan.GetNodes());

	// スキンメッシュに使用する変形マトリクスを事前計算
	for (size_t i = 0; i < nodes.size(); i++)
	{
		m_transforms[i] = nodes[i]->GetGlobalTransform() * nodes[i]->GetInverseInitTransform();
		//MAT_MUL_ACC(m_transforms[i], nodes[i]->GetGlobalTransform(), nodes[i]->GetInverseInitTransform());
	}
	if (onlyTrans)
	{
		//if (!uvUploaded) {
		//	auto* updateUV = Sd->uvs.data();
		//	char* pb = ((char*)gpuVtxPtr);

		//	if (!texInvV)
		//		for (size_t i = 0; i < Sd->positions.size(); i++)
		//		{
		//			glm::vec2* vect = (glm::vec2*)(pb + gpuUVOfs);
		//			glm::vec2* vecs = updateUV;
		//			*vect = *vecs;
		//			updateUV++;
		//			pb += gpuVtxStride;
		//		}
		//	else
		//		for (size_t i = 0; i < Sd->positions.size(); i++)
		//		{
		//			glm::vec2* vect = (glm::vec2*)(pb + gpuUVOfs);
		//			glm::vec2* vecs = updateUV;
		//			vect->x = vecs->x;
		//			vect->y = 1 - vecs->y;
		//			updateUV++;
		//			pb += gpuVtxStride;
		//		}
		//	uvUploaded = true;
		//}
		return;
	}

#if 0
	if (saveVMD)
		for (size_t i = 0; i < nodes.size(); i++)
			if ((nodes[i]->exd.flag & MNFlag::saveVMD) && cbNode)
				cbNode(19, *nodes[i]);
#endif
	if (m_parallelUpdateCount != m_updateRanges.size())
	{
		SetupParallelUpdate();
	}
#if 1  //0.12ms
	std::vector< std::future<void> > results;

	for (size_t i = 1; i < m_parallelUpdateCount; i++)
		if (m_updateRanges[i].m_vertexCount != 0)
		{
			results.emplace_back(gpRenderThreadPool->enqueue(//mPm.rotate90? &MFImgVideoEncoder::WorkThreadRtt :
				&PMXModel::UpdateRg, this, m_updateRanges[i]));
		}
	UpdateRg(m_updateRanges[0]);
	for (auto&& result : results)
	{
		result.get();
	}
#else   //0.15ms


	size_t futureCount = m_parallelUpdateFutures.size();
	for (size_t i = 0; i < futureCount; i++)
	{
		size_t rangeIndex = i + 1;  // 1 ~ count
		if (m_updateRanges[rangeIndex].m_vertexCount != 0)
		{
			m_parallelUpdateFutures[i] = std::async(
				std::launch::async,
				[this, rangeIndex]() { this->UpdateRg(this->m_updateRanges[rangeIndex]); }
			);
		}
	}

	UpdateRg(m_updateRanges[0]);

	for (size_t i = 0; i < futureCount; i++)
	{
		size_t rangeIndex = i + 1;
		if (m_updateRanges[rangeIndex].m_vertexCount != 0)
		{
			m_parallelUpdateFutures[i].wait();
		}
	}
#endif

	uvUploaded = true;


	for (int i = 0; i < Materials.size(); i++)
	{
		auto& m = Materials[i];
		if (!m.ptcDat.ptcBuf) continue;
 
		PhysXMan* pm = dynamic_cast<PhysXMan*>(g_mmdPhysics);
		m.ptcDat.posBuf = (float*) ((char*)gpuVtxPtr) ;
		m.ptcDat.stride = gpuVtxStride;

 

		pm->updateInflate(m.ptcDat,&ibbArr);

	}
}

void PMXModel::SetParallelUpdateHint(uint32_t parallelCount)
{
	m_parallelUpdateCount = parallelCount;
}

bool PMXModel::createRbs()
{
 
	for (size_t pi = 0; pi < pmx.m_rigidbodies.size(); pi++)
	{
		int retFlag;
		bool retVal = createRb(pi, retFlag);
		if (retFlag == 3) continue;
		if (retFlag == 1) return retVal;
 
	}
	return true;
}

bool saba::PMXModel::createRb(size_t pi, int& retFlag)
{
	retFlag = 1;
	auto i = rbids[pi];  //i;//
	auto& pmxRB = pmx.m_rigidbodies[i];
	auto rb = rbs[i];

	pmxRB.m_massOrig = pmxRB.m_mass;
	rb->mtRootParent = &mtRootParent;
	rb->initPos = pmxRB.m_translate;
	rb->isRootRb = i == 0;
	if (pmxRB.attachFlag) anchorRBs.push_back(rb);
	if (rb->isRootRb)
		rootRb = rb;
	//if (rb->isRoot) rb->dynRbType = true;

	//RB english name json setting: $`{"key":value}
	if (pmxRB.pjv) {
		auto& v = *pmxRB.pjv;
		if (v.isMember("shape")) {
			auto sname = v["shape"].asString();
			if (sname == "cylinder") {
				pmxRB.m_shape = PMXRigidbody::Shape::Cylinder;
				pmxRB.m_shapeSize.y *= 2;
			}
		}
		if (v.isMember("noDynRb")) pmxRB.noDynRb = v["noDynRb"].asInt();
		if (v.isMember("keepMass")) pmxRB.keepMass = v["keepMass"].asInt();
		if (auto vd = v["group"]) {
			pmxRB.m_group = vd.asUInt() - 1;
			pmxRB.filterW3 = pmxRB.m_group;
			//pmxRB.m_collideMask32 = 0;
		}
		if (v["cldGrAdd"].isArray()) {
			for (auto& c : v["cldGrAdd"])
				pmxRB.m_collideMask32 |= (1 << (c.asUInt() - 1));
		}
		if (v.isMember("noIgGrp")) {
			pmxRB.doNotIgnoreSameGroupCollision = true;
		}
		if (v.isMember("massCtr")) { 
			ualib::UaJson::getVec3(v["massCtr"], pmxRB.massCtr);
		}			
		if (v.isMember("modelMtrId")) { 
			pmxRB.modelMtrId = v["modelMtrId"].asInt();
		}		
		if (auto vd = v["maxLinVel"]) {
			pmxRB.maxLinVel = vd.asFloat();
		}
		if (v["rbAtk"].asInt())		
			rb->node->rbAtk = rb; //subRb set engName $`{"rbAtk":1}

		if (v.isMember("trigger")) {
			pmxRB.isTrigger = true;
			auto& tgp = rb->trigerParam;
			tgp.trb = rb;
			auto& vtg = v["trigger"];
			auto tv = vtg["addVel"]; if (!tv.isNull()) {
				tgp.flag |= TriggerParam::eAddVel;
				ualib::UaJson::getVec3(tv, tgp.addVel);
			}
			tv = vtg["sclVel"]; if (!tv.isNull()) {
				tgp.flag |= TriggerParam::eSclVel;
				if (tv.isDouble()) tgp.sclVel = glm::vec3(tv.asFloat());
				else ualib::UaJson::getVec3(tv, tgp.sclVel);
			}
			tv = vtg["sclRtt"]; if (!tv.isNull()) {
				tgp.flag |= TriggerParam::eSclRtt;
				if (tv.isDouble()) tgp.sclRtt = glm::vec3(tv.asFloat());
				else ualib::UaJson::getVec3(tv, tgp.sclRtt);
			}
			tv = vtg["setVelP2P"]; if (!tv.isNull()) {
				tgp.flag |= TriggerParam::eSetVelP2P;
				tgp.tgtNodeName = ualib::Utf8toWcs(tv["tgtNode"].asString());
			}
			tv = vtg["speedTo"]; if (!tv.isNull()) {
				tgp.flag |= TriggerParam::eSpeedTo;
				ualib::UaJson::getVec3(tv, tgp.speedToPm);
			}
			tv = vtg["teleport"]; if (!tv.isNull()) {
				tgp.flag |= TriggerParam::eTeleport;
				tgp.tgtNodeName = ualib::Utf8toWcs(tv["tgtNode"].asString());

			}
			tv = vtg["resetPhy"]; if (!tv.isNull()) {
				if (tv.asInt()>0)
				tgp.flag |= TriggerParam::eResetPhy;				 
			}
			tv = vtg["playMPA"]; if (!tv.isNull()) {
				tgp.flag |= TriggerParam::ePlayMPA;
				tgp.fileName = ualib::Utf8toWcs(tv["file"].asString());
			}
		}
	}

	MMDNode* node = nullptr;
	if (pmxRB.m_boneIndex != -1)
	{

		pmxRB.m_opOrig = pmxRB.m_op;

		node = rb->node;
		assert(node == m_nodeMan.GetMMDNode(pmxRB.m_boneIndex));

		MMDNode* np = node->GetParent(), * lnp{};
		//if (leg) pmxRB.m_translate.z = yaoPos.z;
		while (np && !np->rb0) {
			lnp = np;
			np = np->GetParent();
		}
		if (!np) np = lnp;
		if (np)
			rb->parentRb = np->rb0;
		if (rb->parentRb)
			rb->rbTreeLevel = rb->parentRb->rbTreeLevel + 1;

		if (allowAllPhysicsNode || fcp.massDec)
		{

			DPWCS((L"prbnd %s (%d, %s ) -> %s", node->GetNameU().c_str(), i, ualib::Utf8toWcs(pmxRB.m_name).c_str(), np ? np->GetNameU().c_str() : L"[NULL]"));
			auto prbDat = (np && np->rb0) ? &pmx.m_rigidbodies[np->rb0->pmxRbIdx] : nullptr;
			bool prIsStatic = rb->parentRb && rb->parentRb->Pm.m_opOrig == PMXRigidbody::Operation::Static
			
				//prbDat && prbDat->m_opOrig == PMXRigidbody::Operation::Static
				;
			if ((  !pmxRB.noDynRb) && (//1||
				(pmxRB.m_op == PMXRigidbody::Operation::Static && pmxRB.m_mass >= 0 && pmxRB.dynMass > 0
					//&& (!node->IsDeformAfterPhysics())
					// || prOK	
					)
				))
			{

				rb->dynRbType = 1; rb->rbMask = isCharacter ? 1 : 2; rb->isBodyPart = true;
				//DP(("pr %d", prOK));
				const auto& nm = node->GetNameU();
				bool R = nm[0] == L'右', L = nm[0] == L'左';
				bool bodypart = false, finger = false, shd = false, arm = false, armC = false, hand = false, knee = false,
					foot = false, toe = false, bodyud = false, leg = false, head = false;
				if (pmxRB.isEye) {}
				if (node->pmxBone->isFinger) bodypart = finger = true;
				else if (nm.size()>2 && nm[1]==L'半' && nm[2] == L'身') {
					//if (nm == L"上半身2" || nm == L"上半身3" || nm == L"上半身" || nm == L"下半身")
						bodypart = bodyud = true;
				}
				else if (nm == L"頭") node->isHead = bodypart = head = true;
				else if (R || L) {
					if (nm == L"左腕" || nm == L"右腕") bodypart = arm = true;
					else if (nm == L"左ひじ" || nm == L"右ひじ") bodypart = armC = true;
					else if (nm == L"左手首" || nm == L"右手首") node->isHand = bodypart = hand = true;
					else if (nm == L"左ひざD" || nm == L"右ひざD" || nm == L"左ひざ" || nm == L"右ひざ") bodypart = knee = true;
					if (nm[1] == L'足') {
						if (nm == L"左足首D" || nm == L"右足首D" || nm == L"左足首" || nm == L"右足首") node->isFoot = bodypart = foot = true;
						else if (nm[2] == L'先') { bodypart = toe = true; if (L) ndToeL = node; else if (R) ndToeR = node; }
						else if (nm == L"左足D" || nm == L"右足D" || nm == L"左足" || nm == L"右足") bodypart = leg = true;
					}
					else if (nm[1] == L'肩') {
						bodypart = shd = true;
					}
				}
				else if (nm == L"首")  bodypart =  true;
				else if (i == 0) {
					bodypart = true;
					auto ndu = GetNodeManager()->FindNode(L"上半身");
					auto ndd = GetNodeManager()->FindNode(L"下半身");
					if (ndu && ndd)	pmxRB.m_translate = (ndu->pmxBone->m_position + ndd->pmxBone->m_position) * .5f;
				}
#if 0
				if (nm == L"髪P")
				{
					DP(("DEBUG NODE"));
				}
#endif
				bool prb = np && np->rb0;

				rb->isWeightRb = (i == weightRbIdx || i == 0);
				if (rb->isWeightRb) {
					DP(("wb"));
				}
				if (foot) { 
					DP(("wtb"));
				}
				rb->collideOtherChar = fcp.createPhyMesh||(pmxRB.m_mass == 0 || pmxRB.m_op == PMXRigidbody::Operation::Static) && i != weightRbIdx && i != 0 && pmxRB.m_collideMask32 != 0;
				if (rb->collideOtherChar) {
					//pmxRB.dynMass = 1000.1;
					if (pmxRB.keepMass) {
						pmxRB.dynMass = pmxRB.m_mass;
					}
					else if (finger)
						pmxRB.m_mass = pmxRB.dynMass * initMassMulByVolume * 0.07f * pow(0.87f, std::clamp(node->pmxBone->fgN, 1, 3));
					else if (!bodypart) {						
						pmxRB.dynMass = pmxRB.m_mass = glm::clamp(pmxRB.m_mass,1.f,10.f);
						DPWCS((L"NBD %s %d %f", node->GetNameU().c_str(), i, pmxRB.m_mass));
					} else {
						pmxRB.m_mass = /*foot  ? 10 :*/ (i == 0 ? 2 : bodyud ? 1 : 1) * pmxRB.dynMass * initMassMulByVolume;
						if (np && np->rb0 && np->rb0->collideOtherChar)
							pmxRB.m_mass = std::min(pmxRB.m_mass, np->rb0->m_mass * 2.f);
		
					} 

					pmxRB.m_translateDimmer = 1.f;
					pmxRB.m_rotateDimmer = std::max(pmxRB.m_rotateDimmer, 0.5f);;
					pmxRB.m_friction = 0.5;
					pmxRB.m_repulsion = 0.1f;

				}

				PMXJoint jt{ "add","",  PMXJoint::JointType::SpringDOF6 };
				jt.springT = defaultDynRbJointParam.springT;
				jt.springR = defaultDynRbJointParam.springR;//						glm::vec3(1000, 1000, 1000);
				jt.dampingT = defaultDynRbJointParam.dampingT;
				jt.dampingR = defaultDynRbJointParam.dampingR;//					glm::vec3(1000, 1000, 1000);

				jt.limitMinT = { 0.f,0.f,0.f };
				jt.limitMaxT = { 0.f,0.f,0.f };

				//pmxRB.m_shape = PMXRigidbody::Shape::Box;
				pmxRB.m_friction = 0.5f;
				if (head) {
					//pmxRB.m_translateDimmer = 0.999;
					pmxRB.m_rotateDimmer = 0.5;
				}
				else if (pmxRB.isEye) {
					pmxRB.m_rotateDimmer = 0.5;
					pmxRB.m_repulsion = 1;
					pmxRB.m_friction = 0;
				}

				if (bodyud)
				{
					//pmxRB.dbgVisual = true;
					//pmxRB.m_translateDimmer = 0;
					//pmxRB.m_rotateDimmer = 100.5;
				}
				else if (leg) {
					legLength = node->mGlobalInit[3].y;
				}
				else if (knee) {
					if (L) legLRbIdx = i; else if (R) legRRbIdx = i;
				}
				else if (foot) {
					pmxRB.m_friction = 1; pmxRB.m_repulsion = 0.f;
					hasFoot = true;
					if (L) footLRbIdx = i;	else if (R) footRRbIdx = i;
				}
				else if (toe) {
					pmxRB.m_friction = 1; pmxRB.m_repulsion = 0.3;		hasFootFront = true;
					jt.springR = glm::vec3(1000.f);
					jt.dampingR = glm::vec3(100.f);//	
				}
				else if (hand) {
					pmxRB.m_friction = 1; pmxRB.m_repulsion = 0.3;		handRbId = i;
					//pmxRB.dbgVisual = true;
					armLength = abs(node->mGlobalInit[3].x) * 1.37f;
				}
				else if (finger)
				{
					pmxRB.m_friction = 1;
					pmxRB.m_repulsion = 0;
					pmxRB.m_translateDimmer = 1;
					pmxRB.m_rotateDimmer = 1;
				}
				pmxRB.rbIdxOfBone = node->rbcc++;

				if (!rb->isSubRb)
				{

					if ((prb || lastRbIdx != -1)) {

						pmxRB.m_op = PMXRigidbody::Operation::DynamicAndBoneMerge;

						jt.m_rigidbodyAIndex = i != weightRbIdx && prb && np->rb0->pmxRbIdx > 0 ? np->rb0->pmxRbIdx : lastRbIdx;
						jt.m_rigidbodyBIndex = i;
						if (pmx.m_rigidbodies[jt.m_rigidbodyAIndex].m_opOrig != PMXRigidbody::Operation::Static)
						{
							DP(("Static->Dyn link to non static %d->%d !!!!!!!!!!!!", jt.m_rigidbodyBIndex, jt.m_rigidbodyAIndex));
							pmxRB.m_mass = pmxRB.m_massOrig;
						}
						if (!bodypart && nm[0] == L'髪')//髪P
							jt.m_type = PMXJoint::JointType::Fixed;
						jt.translate = // prb?(pmx.m_rigidbodies[np->rb0->pmxRbIdx].m_translate+ pmxRB.m_translate)/2.f:
							//pmxRB.m_translate;
							pmx.m_bones[node->GetIndex()].m_position;
						if (arm || armC || hand) {
							jt.rotate = glm::vec3(0, 0, (R ? 1 : -1) * glm::quarter_pi<float>());
						}
						//jt.m_rotate = pmxRB.m_rotate;
						{
							//jt.m_rotateLowerLimit = { -1,-1,-1 };				jt.m_rotateUpperLimit = { 1,1,1 };

							//if (knee) jt.m_springR = {500,200,200};

							const auto pi = glm::pi<double>(), deg1 = pi / 180;
							const auto hp = glm::half_pi<double>();
							if (fcp.dynJointFree)
							{
								jt.limitMinR = vec3(-999);
								jt.limitMaxR = vec3(999);
							}
							else if (hand) {
								jt.limitMinR = glm::vec3(-pi / 12, -0, -pi / 4);
								jt.limitMaxR = glm::vec3(pi / 12, 0, pi / 4);
							}
							else if (finger) 
							{
								if (nm[1] == L'親') {
									jt.limitMinR = glm::vec3(-0.1f, R ? -0.1 : -0.2, -0.1f);
									jt.limitMaxR = glm::vec3(+0.1f, R ? 0.2 : 0.1, +0.1f);
								}
								else {
									int nid = nm[3] - L'０';
									auto isRoot = nid < 2;
									jt.limitMinR = glm::vec3(-0.0f, isRoot ? -0.01f : -0.0f, R ? -0.6 : 0);
									jt.limitMaxR = glm::vec3(+0.0f, isRoot ? +0.01f : +0.0f, R ? 0 : 0.6);
								}
								jt.springR = vec3(1000);
								jt.dampingR = vec3(1000);
							}
							else if (pmxRB.isEye) {
								jt.limitMinR = glm::radians(vec3(-26, -30, 0));
								jt.limitMaxR = glm::radians(vec3(21, 30, 0));
								jt.limitMinT = vec3(-10.f);
								jt.limitMaxT = vec3(10);
							}
							//else if ( armC) {jt.limitMinR = glm::radians(vec3(-90, -90, -0)),jt.limitMaxR = glm::radians(vec3(90, 90, 0));}
							
							//else if (foot) {
							//	jt.m_rotateLowerLimit = glm::radians(vec3(-180, -180, 0));
							//	jt.m_rotateUpperLimit = glm::radians(vec3(180, 180, 0));
							//}r
							else {
								jt.limitMinR = glm::vec3(
									knee ? 0.01 : armC ? -1 : foot ? -0.76 : toe ? -pi / 6 : bodyud ? -0.3 : leg ? -1.66 : shd ? -0.1 : arm ? -1 : head ? -1 : -0.05f,
									knee ? -0.1 : armC ? (R ? -0.1f : -hp * 1.5f) : leg ? (R ? -1.5 : 0.1) : arm ? (R ? -0.6 : -hp) : head ? -.9f : -0.05f,
									knee ? -0.1 : foot ? (R ? -pi / 18 : -pi / 18) : leg ? (R ? -0.3 : -1) : armC ? (R ? -0.1 : -0.1) : shd ? (R ? -0.1 : -0.6) : arm ? (R ? -0.6 : -pi / 2) : head ? -0.1 : -0.05f
								);
								jt.limitMaxR = glm::vec3(
									knee ? 2.37 : armC ? 0.2f : foot ? 1 : toe ? pi / 6 : bodyud ? 0.3 : leg ? 0.157 : shd ? 0.1 : arm ? 0.1f : head ? 0.6 : 0.05f,
									knee ? 0.1 : armC ? (R ? hp * 1.5f : 0.1f) : leg ? (R ? -0.1 : 1.5) : arm ? (R ? hp : 0.6) : head ? .9f : 0.05f,
									knee ? 0.1 : foot ? (R ? pi / 18 : pi / 18) : leg ? (R ? 1 : 0.3) : armC ? (R ? 0.1 : 0.1) : shd ? (R ? 0.6 : 0.1) : arm ? (R ? pi / 2 : 0.6) : head ? 0.1 : 0.05f
								);
							}
							//if (arm || armC || hand) 		{ jt.limitMinR -= 0.3f; jt.limitMaxR += 0.3f; }
							//  { jt.limitMinR = vec3(- pi/2); jt.limitMaxR = vec3(pi/2); }
						}
						//if (armC) 									pmxRB.m_mass *= .5;
						if (hand || foot) 					pmxRB.m_mass *= .25f;
						else if (toe)					pmxRB.m_mass *= .1f;
						else if (knee || armC) 										pmxRB.m_mass *= .8f;
						pmx.m_joints.push_back(jt);
						pmxRB.jtToParent = jt;
						DPWCS((L"ADD JT %s", ualib::Utf8toWcs(pmxRB.m_name).c_str()));
						//if (leg) {						jt.m_rotateLowerLimit *= 0.0;		jt.m_rotateUpperLimit *= 0.0;				}
					}
					else {
						pmxRB.m_op = PMXRigidbody::Operation::Dynamic;

						if (lastRbIdx == -1)
							lastRbIdx = i;
					}
					if (np) { DPWCS((L"rb%3d J     t=%d %s[%s] ->P %d %s  Co %d  m %f", i, pmxRB.m_op, ualib::Utf8toWcs(pmxRB.m_name).c_str(), node->GetNameU().c_str(), prb ? np->rb0->pmxRbIdx : 0, np->GetNameU().c_str(), rb->collideOtherChar, pmxRB.m_mass)); }
#if BOX_FOOT
					if (foot && pmxRB.m_shape != PMXRigidbody::Shape::Box) {
						pmxRB.m_shape = PMXRigidbody::Shape::Box;


						pmxRB.m_translate.y += -pmxRB.m_shapeSize.x * 0.35;
						//pmxRB.m_translate.z += -pmxRB.m_shapeSize.x / 8;
						pmxRB.m_shapeSize.x *= 0.57;
						pmxRB.m_shapeSize.z = pmxRB.m_shapeSize.x * 0.75;
						pmxRB.m_shapeSize.y = pmxRB.m_shapeSize.x * 3.75; //1;// std::max(0.3, pmxRB.m_shapeSize.y - 0.8);

						pmxRB.m_translate.y = pmxRB.m_shapeSize.z;
						if (pmxRB.m_shapeSize.x < 0.5f) pmxRB.m_shapeSize.x = 0.5f;
						//pmxRB.m_collideMask32 |= (1 << pmxRB.m_group);
						pmxRB.m_rotate = { irr::core::PI / 2,0,0 };
						//pmxRB.massCtr = { 0,0,pmxRB.m_shapeSize.z };// pmxRB.m_mass = 1000;
					}

					else if (fcp.addFingerRbs && hand && pmxRB.m_shape != PMXRigidbody::Shape::Box) {
						pmxRB.m_shape = PMXRigidbody::Shape::Box;
						pmxRB.m_translate = node->pmxBone->m_position;
						pmxRB.m_shapeSize.y = 0.5f; //.75;
						pmxRB.m_shapeSize.x *= 0.25;
						pmxRB.m_shapeSize.z = pmxRB.m_shapeSize.x * 2;
						//pmxRB.m_shapeSize.y = pmxRB.m_shapeSize.x * 2; //1;// std::max(0.3, pmxRB.m_shapeSize.y - 0.8);
						//pmxRB.m_collideMask32 = 0;// |= (1 << pmxRB.m_group);
						//pmxRB.m_rotate = { irr::core::PI / 2,0,0 };
					}
#endif
				}
				else
				{

					if ((prb || lastRbIdx != -1)) {
						auto pprb = pmx.m_rigidbodies[node->rb0->pmxRbIdx];
						if (pmxRB.m_group != pprb.m_group) {
							pmxRB.m_group = pprb.m_group; DP(("XXXXXXXXX GROUP XXXXXXXXXXX"));
							//assert(pmxRB.m_group == pprb.m_group);
						}
						if (foot) { //jiao gen ...
							pmxRB.m_shape = PMXRigidbody::Shape::Sphere;
							pmxRB.m_shapeSize = { 0.01,0.01,0.01 };
							pmxRB.m_collideMask32 = 0;
							rb->collideOtherChar = false;

							//pmxRB.m_mass = 0.001f;
						}
						else {
							pmxRB.m_op = PMXRigidbody::Operation::DynamicAndBoneMerge;
							PMXJoint jt{ "fxj","",  PMXJoint::JointType::Fixed };
							jt.m_rigidbodyAIndex = node->rb0->pmxRbIdx;
							jt.m_rigidbodyBIndex = i;
							jt.translate = pmxRB.m_translate;
							jt.rotate = pmxRB.m_rotate;

							//jt.springTranslateFactor = glm::vec3(1, 1000, 1);
							//jt.m_springR = glm::vec3(1, 1, 1);
							pmx.m_joints.push_back(jt);
							pmxRB.jtToParent = jt;
						}

					}
					else throw;
					DPWCS((L"rb%3d FIXED  t=%d %s[%s] -- rb%d, S%d %f %f m:%.2f f:%.2f", i, pmxRB.m_op, ualib::Utf8toWcs(pmxRB.m_name).c_str(), node->GetNameU().c_str(), rb->isSubRb ? node->rb0->pmxRbIdx : -1, pmxRB.m_shape, pmxRB.m_shapeSize.x, pmxRB.m_shapeSize.y, pmxRB.m_mass, pmxRB.m_friction));
				}
			}
			else if (isCharacter) // not static, original rb
			{
				//if (!isCharacter) {
				//	rb->collideOtherChar = true;
				//	if (pmxRB.m_op != PMXRigidbody::Operation::Dynamic)
				//		pmxRB.m_op = PMXRigidbody::Operation::DynamicAndBoneMerge;
				//}

#if CVT_ALIGN_TO_DYNAMIC_JOINT // DynamicAndBoneMerge && MASS < 0 to set 主要是原static饰品转dynRb太重，改成align省的加joint
				if (prIsStatic) {

					if (pmxRB.m_opOrig != PMXRigidbody::Operation::Dynamic && pmxRB.m_mass < 0) {
						pmxRB.m_op = PMXRigidbody::Operation::Dynamic; // OR if error, DynamicAndBoneMerge ?
						pmxRB.m_mass = -pmxRB.m_mass;
						//pmxRB.m_op = PMXRigidbody::Operation::Dynamic;
						PMXJoint jt = { "fxj","",  PMXJoint::JointType::P2P };
						jt.m_rigidbodyAIndex = np->rb0->pmxRbIdx;
						jt.m_rigidbodyBIndex = i;
						jt.translate = pmxRB.m_translate;
						jt.rotate = pmxRB.m_rotate;

						float a = -PI / 18;
						//jt.m_rotateLowerLimit = glm::vec3(-a,-a,-a);
						//jt.m_rotateUpperLimit = glm::vec3(a,a,a);
						//jt.springTranslateFactor = glm::vec3(1, 1, 1);
						//jt.m_springR = glm::vec3(1, 1, 1);
						pmx.m_joints.push_back(jt);
						pmxRB.jtToParent = jt;
					}
				}
#endif
				if (fcp.massDec) {
					if (prIsStatic || i == 0) {
						pmxRB.massChanged = true; //if (i == 0)  pmxRB.m_mass = 100;
						pmxRB.masLvl = 0;
						//if (np) { DPWCS((L"ChMassDec S %d-%d   m:%.2f", i, np->rb0->pmxRbIdx, pmxRB.m_mass)); }
					}
					else if (node && np && np->rb0) {
						auto& prd = pmx.m_rigidbodies[np->rb0->pmxRbIdx];
						if (prd.massChanged) {
							pmxRB.masLvl = prd.masLvl + 1;
							pmxRB.m_mass = prd.m_mass * std::min(1.f, fcp.massDecRate + pmxRB.masLvl * fcp.masLvlAdd);
							pmxRB.m_translateDimmer = std::min(60.f, prd.m_translateDimmer / fcp.massDecRate);
							pmxRB.m_rotateDimmer = prd.m_rotateDimmer / fcp.massDecRate;
							pmxRB.massChanged = true;
							//	pmxRB.doNotIgnoreSameGroupCollision = true;	
							pmxRB.m_shapeSize.x *= std::max(0.1f, 1 - pmxRB.masLvl * 0.1f);
							DPWCS((L"ChMassDec S %d-%d   m:%.2f  td:%.2f", i, np->rb0->pmxRbIdx, pmxRB.m_mass, pmxRB.m_translateDimmer));
						}
					}
				}
#if OTHER_RB_MASS_CHANGE
				else {
					const auto& n = node->GetNameU();
					if (prIsStatic) {
						

						if (pmxRB.m_mass > 0.1f) {
							pmxRB.isOppai = (n[1] == L'胸' && (n[0] == L'右' || n[0] == L'左')); // always false ???
							const auto& rn = pmxRB.m_englishName;
							bool keepMass = false; float kmass = pmxRB.m_mass;
							if (pmxRB.pjv) {
								auto const& pjv = *pmxRB.pjv;
								if (pjv.isMember("mass")) {
									auto v = pjv["mass"];
									keepMass = true;
									if (v.isNumeric()) {
										kmass = v.asFloat();
									}
									else  kmass = pmxRB.m_mass;
								}
							}
							//else { keepMass = true; kmass = 0.1; }//test
							float mass = pmxRB.isOppai ? std::max(1.f, pmxRB.m_mass) : keepMass ? kmass : 1.f;
							pmxRB.cpMassMul = mass / pmxRB.m_mass; pmxRB.m_mass = mass;
							pmxRB.massChanged = true;
							if (pmxRB.isOppai) {
								//Only can Do On Joint pmxRB.m_translateDimmer = 30; pmxRB.m_rotateDimmer = 3000;
								rb->isBodyPart = true;
							}

							DPWCS((L"ChW P %d  %8s - %8s  => m:%.2f f:%.2f c:%.2f", i, ualib::Utf8toWcs(pmxRB.m_name).c_str(), node->GetNameU().c_str(), pmxRB.m_mass, pmxRB.m_friction, pmxRB.cpMassMul));
						}
					}
					else if (node && np && np->rb0) {

						auto& prd = pmx.m_rigidbodies[np->rb0->pmxRbIdx];
						if (prd.massChanged) {
							pmxRB.cpMassMul = prd.cpMassMul;
							pmxRB.m_mass *= prd.cpMassMul;
							pmxRB.massChanged = true;
							DPWCS((L"ChW S %d-%d %8s - %8s   m:%.2f f:%.2f  c:%.2f", i, np->rb0->pmxRbIdx, ualib::Utf8toWcs(pmxRB.m_name).c_str(), node->GetNameU().c_str(), pmxRB.m_mass, pmxRB.m_friction, pmxRB.cpMassMul));
						}
						//pmxRB.m_translateDimmer = SMALL_DYNAMIC_RB_DIMMER / std::clamp(pmxRB.m_mass, 0.1f, 10.f);

					}
				}

#endif						
				if (pmxRB.m_collideMask32 == 0)
					rb->collideOtherChar = false;
				pmxRB.m_friction = std::clamp(pmxRB.m_friction, 0.0f, 1.f);
				//pmxRB.massMul = 0.001f;
			}
		} //
		else
		{
			//assert(pmxRB.m_translateDimmer == 0);
		}
	}

	if (allowAllPhysicsNode && !rb->collideOtherChar) {
		const float maxf = 1000.f;
		if (pmxRB.m_mass > maxf && i != weightRbIdx && i != 0)
		{
			pmxRB.m_mass = maxf;
		}
		//DPWCS((L"nc%3d S%d %f %f m %f", i, pmxRB.m_op, pmxRB.m_shape, pmxRB.m_shapeSize.x, pmxRB.m_shapeSize.y, pmxRB.m_mass));
	}
	if (fcp.fixFriction) {
		pmxRB.m_friction =fcp.setFixFriction;
		pmxRB.m_repulsion = 0.f;
		pmxRB.m_translateDimmer = !isCharacter || rb->dynRbType?0.0:0.5f;
		pmxRB.m_rotateDimmer = 0;
		pmxRB.maxLinVel = 1000.f;
		pmxRB.maxAngVel = 1000.f;
	}
	//if (pmxRB.m_op == PMXRigidbody::Operation::Static)
	//	pmxRB.m_op = PMXRigidbody::Operation::DynamicAndBoneMerge;
	pmxRB.pOwner = SABA_USE_PHYSX ? (void*)createIdx : (void*)rb;


	PhyMeshData pd{};
	bool hasPhyMesh = false;
	if ( pmxRB.pjv && pmxRB.pjv->isMember("phyMtrName")) {

		auto& jv = *pmxRB.pjv;
		auto phyMtrName = jv["phyMtrName"].asString();

		for (auto& v : pmx.m_vertices) 
			pd.vtxs.emplace_back(v.m_position);

#if 1
		for (int i=0;i<GetSubMeshCount();i++)
		{
			auto sm = GetSubMeshes()[i];
			int  ieSize = GetIndexElementSize();
			auto indices = (uint16_t*)GetIndices();
			if (GetMaterials()[sm.m_materialID].m_name== phyMtrName && GetMaterials()[sm.m_materialID].isPhyMesh)
			for (int vi = 0; vi < sm.m_vertexCount; vi++)
			{

				pd.idxs.emplace_back(indices[sm.m_beginIndex+vi]);
			}

			
		}
#else
		for (auto& t : pmx.m_faces) {
			pd.idxs.emplace_back(t.vtxId[0]);
			pd.idxs.emplace_back(t.vtxId[1]);
			pd.idxs.emplace_back(t.vtxId[2]);
		}
#endif
		pmxRB.pmd = &pd;
		hasPhyMesh = true;
	}
	pmxRB.massMul = fcp.massMul;
	if (pmx.allDynRb || hasPhyMesh) rb->dynRbType = 1;

	if (fcp.frictionMul!=1.0f) pmxRB.m_friction *= fcp.frictionMul;
	
	if (!rb->CreatePMX(pmxRB, this, node))
	{
		SABA_ERROR("Create Rigid Body Fail.\n");
		return false;
	}


	//end check

#define NEWRBCODE 	int idx = pmx.m_rigidbodies.size(); auto nrb = m_physicsMan.newRigidBody(false); nrb->pmxRbIdx = idx;\
			rbids.push_back(idx); pmx.m_rigidbodies.push_back(pm);	rbs.push_back(nrb);nd->rbAtk = nd->rb0 = nrb; nrb->isSubRb = false; nrb->node=(PMXNode*)nd;

	if (i == pmx.m_rigidbodies.size() - 1)
	{
		if (!hasFoot && legRRbIdx) {
			hasFoot = true;
			DP(("NO FOOT RB !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"));
			{
				PMXRigidbody pm = pmx.m_rigidbodies[legRRbIdx];
				pm.m_name = ToUtf8String(L"右足首D");
				pm.m_op = PMXRigidbody::Operation::Static; pm.m_mass = 100;
				pm.m_shape = PMXRigidbody::Shape::Box; pm.m_shapeSize = vec3(0.6, 0.3, 1);
				auto nd = GetNodeManager()->FindNode(L"右足首D"); if (!nd) nd = GetNodeManager()->FindNode(L"右足首");
				pm.m_boneIndex = nd->GetIndex();
				pm.m_translate = nd->pmxBone->m_position; pm.m_translate.y = 0.3f; pm.m_translate.z -= 0.6f;
				pm.m_rotate = { 0,0,0 };
				NEWRBCODE;
			}
			{
				PMXRigidbody pm = pmx.m_rigidbodies[legLRbIdx];
				pm.m_name = ToUtf8String(L"左足首D");
				pm.m_op = PMXRigidbody::Operation::Static; pm.m_mass = 100;
				pm.m_shape = PMXRigidbody::Shape::Box; pm.m_shapeSize = vec3(0.6, 0.3, 1);
				auto nd = GetNodeManager()->FindNode(L"左足首D"); if (!nd) nd = GetNodeManager()->FindNode(L"左足首");
				pm.m_boneIndex = nd->GetIndex();
				pm.m_translate = nd->pmxBone->m_position; pm.m_translate.y = 0.3f; pm.m_translate.z -= 0.6f;
				pm.m_rotate = { 0,0,0 };
				NEWRBCODE;
			}
		}
		else if (!hasFootFront && footRRbIdx) {
			hasFootFront = true;
			DP(("NO FOOT RB !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"));


			{
				PMXRigidbody pm = pmx.m_rigidbodies[footRRbIdx];
				pm.massCtr = { 0,0,0 };
				pm.m_name = ToUtf8String(L"右足先EX");
				pm.m_op = PMXRigidbody::Operation::Static; pm.m_mass = 100;
				pm.m_shape = PMXRigidbody::Shape::Box; pm.m_shapeSize = vec3(0.35, 0.1, 0.35);
				auto nd = GetNodeManager()->FindNode(L"右足先EX"); if (!nd) nd = GetNodeManager()->FindNode(L"右足先");//左足先(素足用)
				if (!nd) { retFlag = 3; return {}; };
				pm.m_boneIndex = nd->GetIndex();
				pm.m_translate = nd->pmxBone->m_position; pm.m_translate.y = 0.1f; pm.m_translate.z -= 0.6f;
				pm.m_rotate = { 0,0,0 };
				pm.m_friction = 1;
				NEWRBCODE;

			}
			{
				PMXRigidbody pm = pmx.m_rigidbodies[footLRbIdx];
				pm.massCtr = { 0,0,0 };
				pm.m_name = ToUtf8String(L"左足先EX");
				pm.m_op = PMXRigidbody::Operation::Static; pm.m_mass = 100;
				pm.m_shape = PMXRigidbody::Shape::Box; pm.m_shapeSize = vec3(0.35, 0.1, 0.35);
				auto nd = GetNodeManager()->FindNode(L"左足先EX"); if (!nd) nd = GetNodeManager()->FindNode(L"左足先");
				pm.m_boneIndex = nd->GetIndex();
				pm.m_translate = nd->pmxBone->m_position; pm.m_translate.y = 0.1f; pm.m_translate.z -= 0.6f;
				pm.m_rotate = { 0,0,0 };
				pm.m_friction = 1;
				NEWRBCODE;

			}
		}
	}
	retFlag = 0;
	return {};
}


bool PMXModel::Load(const std::string& filepath, const std::string& mmdDataDir, bool& useGPU, bool onlyGetInfo)
{
	useGPU = false;
	Destroy();
	std::string realPath;
	pmx.fcp = fcp;
 


	if (onlyGetInfo) {
		if (!ReadPMXFile(&pmx, filepath.c_str()))
		{
			return false;
		}
		info = pmx.m_info;
		return true;
	}

#if PMX_CACHE_DATA
	if (modelIdStr.empty()) throw;
	if (fcp.forceReload) {
		Sds.removeRes(modelIdStr);
	}
		

	Sd = Sds.findRes(modelIdStr);
	if (!Sd) {
		Sd = Sds.addRes(modelIdStr, std::make_shared<PmxModelShareData>());
		newSd = true;
	}
#else
	Sd = &SdObj;
	bool newSd = true;
#endif



	if (newSd)
	{
		if (!ReadPMXFile(&pmx, filepath.c_str()))
		{
			return false;
		}
		if (onlyGetInfo) {
			info = pmx.m_info;
			return true;
		}
		if (fcp.modelScale) {
			pmx.ScalePmxFile(fcp.modelScaleVec3);
		}
		Sd->pmxFile = pmx;
	}
	else pmx = Sd->pmxFile;


	modelName = pmx.m_info.m_modelName;
	addData();
	realPath = pmx.realPath;
	std::string dirPath = PathUtil::GetDirectoryName(realPath);

	size_t vertexCount = pmx.m_vertices.size();
	if (newSd)
	{
		Sd->positions.reserve(vertexCount);
		Sd->normals.reserve(vertexCount);
		Sd->uvs.reserve(vertexCount);
		Sd->vertexBoneInfos.reserve(vertexCount);
	}
	m_bboxMax = glm::vec3(-std::numeric_limits<float>::max());
	m_bboxMin = glm::vec3(std::numeric_limits<float>::max());

	bool warnSDEF = false;
	bool infoQDEF = false;
	if (newSd)
	{
		for (const auto& v : pmx.m_vertices)
		{
			glm::vec3 pos = v.m_position MUL_VEC3INVZ;
			glm::vec3 nor = v.m_normal MUL_VEC3INVZ;
			glm::vec2 uv = glm::vec2(v.m_uv.x, 1.0f - v.m_uv.y);
			Sd->positions.push_back(pos);
			Sd->normals.push_back(nor);
			Sd->uvs.push_back(uv);
			VertexBoneInfo vtxBoneInfo;
			if (PMXVertexWeight::SDEF != v.m_weightType)
			{
				vtxBoneInfo.m_boneIndex[0] = v.m_boneIndices[0];
				vtxBoneInfo.m_boneIndex[1] = v.m_boneIndices[1];
				vtxBoneInfo.m_boneIndex[2] = v.m_boneIndices[2];
				vtxBoneInfo.m_boneIndex[3] = v.m_boneIndices[3];

				vtxBoneInfo.m_boneWeight[0] = v.m_boneWeights[0];
				vtxBoneInfo.m_boneWeight[1] = v.m_boneWeights[1];
				vtxBoneInfo.m_boneWeight[2] = v.m_boneWeights[2];
				vtxBoneInfo.m_boneWeight[3] = v.m_boneWeights[3];
			}

			switch (v.m_weightType)
			{
			case PMXVertexWeight::BDEF1:
				vtxBoneInfo.m_skinningType = SkinningType::Weight1;
				break;
			case PMXVertexWeight::BDEF2:
				vtxBoneInfo.m_skinningType = SkinningType::Weight2;
				vtxBoneInfo.m_boneWeight[1] = 1.0f - vtxBoneInfo.m_boneWeight[0];
				break;
			case PMXVertexWeight::BDEF4:
				vtxBoneInfo.m_skinningType = SkinningType::Weight4;
				break;
			case PMXVertexWeight::SDEF:
				if (!warnSDEF)
				{
					//warnSDEF = true; SABA_WARN("Use SDEF");					
				}
				vtxBoneInfo.m_skinningType = SkinningType::SDEF;
				{
					auto i0 = v.m_boneIndices[0];
					auto i1 = v.m_boneIndices[1];
					auto w0 = v.m_boneWeights[0];
					auto w1 = 1.0f - w0;

					auto center = v.m_sdefC MUL_VEC3INVZ;
					auto r0 = v.m_sdefR0 MUL_VEC3INVZ;
					auto r1 = v.m_sdefR1 MUL_VEC3INVZ;
					auto rw = r0 * w0 + r1 * w1;
					r0 = center + r0 - rw;
					r1 = center + r1 - rw;
					auto cr0 = (center + r0) * 0.5f;
					auto cr1 = (center + r1) * 0.5f;

					vtxBoneInfo.m_sdef.m_boneIndex[0] = v.m_boneIndices[0];
					vtxBoneInfo.m_sdef.m_boneIndex[1] = v.m_boneIndices[1];
					vtxBoneInfo.m_sdef.m_boneWeight = v.m_boneWeights[0];
					vtxBoneInfo.m_sdef.m_sdefC = center;
					vtxBoneInfo.m_sdef.m_sdefR0 = cr0;
					vtxBoneInfo.m_sdef.m_sdefR1 = cr1;
				}
				break;
			case PMXVertexWeight::QDEF:
				vtxBoneInfo.m_skinningType = SkinningType::DualQuaternion;
				if (!infoQDEF)
				{
					SABA_INFO("Use QDEF");
					infoQDEF = true;
				}
				break;
			default:
				vtxBoneInfo.m_skinningType = SkinningType::Weight1;
				SABA_ERROR("Unknown PMX Vertex Weight Type: {}", (int)v.m_weightType);
				break;
			}
			Sd->vertexBoneInfos.push_back(vtxBoneInfo);

			m_bboxMax = glm::max(m_bboxMax, pos);
			m_bboxMin = glm::min(m_bboxMin, pos);
		}
		Sd->bboxMax = m_bboxMax;
		Sd->bboxMin = m_bboxMin;
		Sd->positions_size = Sd->positions.size();
		m_morphPositions.resize(Sd->positions_size);
		m_morphUVs.resize(Sd->positions_size);
		m_updatePositions.resize(Sd->positions_size);
		m_updateNormals.resize(Sd->normals_size = Sd->normals.size());
		m_updateUVs.resize(Sd->uvs_size = Sd->uvs.size());
	}
	else
	{
		m_bboxMax = Sd->bboxMax;
		m_bboxMin = Sd->bboxMin;

		m_morphPositions.resize(Sd->positions_size);
		m_morphUVs.resize(Sd->positions_size);
		m_updatePositions.resize(Sd->positions_size);
		m_updateNormals.resize(Sd->normals_size);
		m_updateUVs.resize(Sd->uvs_size);
	}



	m_indexElementSize = pmx.m_header.m_vertexIndexSize == 1 ? 2 : pmx.m_header.m_vertexIndexSize;

	if (newSd)
	{
		Sd->indices.resize(pmx.m_faces.size() * 3 * m_indexElementSize);
		m_indexCount = pmx.m_faces.size() * 3;
		switch (pmx.m_header.m_vertexIndexSize)
		{
		case 1:
		{
			int idx = 0;
			uint16_t* indices = (uint16_t*)Sd->indices.data();
			for (const auto& face : pmx.m_faces)
			{
				for (int i = 0; i < 3; i++)
				{
					auto vi = face.vtxId[i];
					indices[idx] = (uint8_t)vi;
					idx++;
				}
			}
			break;
		}
		case 2:
		{
			int idx = 0;
			uint16_t* indices = (uint16_t*)Sd->indices.data();
			for (const auto& face : pmx.m_faces)
			{
				for (int i = 0; i < 3; i++)
				{
					auto vi = face.vtxId[i];
					indices[idx] = (uint16_t)vi;
					idx++;
				}
			}
			break;
		}
		case 4:
		{
			int idx = 0;
			uint32_t* indices = (uint32_t*)Sd->indices.data();
			for (const auto& face : pmx.m_faces)
			{
				for (int i = 0; i < 3; i++)
				{
					auto vi = face.vtxId[i];
					indices[idx] = (uint32_t)vi;
					idx++;
				}
			}
			break;
		}
		default:
			SABA_ERROR("Unsupported Index Size: [{}]", m_indexElementSize);
			return false;
		}
		Sd->indexCount = m_indexCount;
	}
	else
	{
		m_indexCount = Sd->indexCount;
	}

	std::vector<std::string> texturePaths;
	texturePaths.reserve(pmx.m_textures.size());
	for (const auto& pmxTex : pmx.m_textures)
	{
		std::string texPath = PathUtil::Combine(dirPath, pmxTex.m_textureName);
		texturePaths.emplace_back(std::move(texPath));
	}

	// Materialをコピー
	Materials.reserve(pmx.m_materials.size());
	if (newSd)
	{
		Sd->subMeshes.reserve(pmx.m_materials.size());
		uint32_t beginIndex = 0;
		for (const auto& pmxMat : pmx.m_materials)
		{
			MMDMaterial mat;
			mat.m_diffuse = pmxMat.m_diffuse;
			mat.m_alpha = pmxMat.m_diffuse.a;
			mat.m_specularPower = pmxMat.m_specularPower;
			mat.m_specular = pmxMat.m_specular;
			mat.m_ambient = pmxMat.m_ambient;
			mat.m_spTextureMode = MMDMaterial::SphereTextureMode::None;
			mat.m_bothFace = !!((uint8_t)pmxMat.m_drawMode & (uint8_t)PMXDrawModeFlags::BothFace);
			mat.m_edgeFlag = ((uint8_t)pmxMat.m_drawMode & (uint8_t)PMXDrawModeFlags::DrawEdge) == 0 ? 0 : 1;
			mat.m_groundShadow = !!((uint8_t)pmxMat.m_drawMode & (uint8_t)PMXDrawModeFlags::GroundShadow);
			mat.m_shadowCaster = !!((uint8_t)pmxMat.m_drawMode & (uint8_t)PMXDrawModeFlags::CastSelfShadow);
			mat.m_shadowReceiver = !!((uint8_t)pmxMat.m_drawMode & (uint8_t)PMXDrawModeFlags::RecieveSelfShadow);
			mat.m_edgeSize = pmxMat.m_edgeSize;
			mat.m_edgeColor = pmxMat.m_edgeColor;
			mat.m_memo = pmxMat.m_memo;
			mat.m_name = pmxMat.m_name;
			ualib::UaJson js;
			if (mat.m_memo[0]=='{' && js.ParseStr(mat.m_memo)) {
				Json::Value root = js.copyRootValue();
				mat.isPhyMesh = root["isPhyMesh"].asInt();
				mat.isCloth = root["isCloth"].asInt();
				if (mat.isCloth) {
					auto vc = root["cloth"];
					if (vc.isObject()) {						
						ualib::UaJson::getFloatIfExist(vc, "ptcMass", mat.ptcDat.ptcMass);
						ualib::UaJson::getFloatIfExist(vc, "friction", mat.ptcDat.friction);
						ualib::UaJson::getFloatIfExist(vc, "restOffset", mat.ptcDat.restOffset);
					}
				}
			}
			// Texture
			if (pmxMat.m_textureIndex != -1)
			{
				mat.m_texture = PathUtil::Normalize(texturePaths[pmxMat.m_textureIndex]);
			}

			// ToonTexture
			if (pmxMat.m_toonMode == PMXToonMode::Common)
			{
				if (pmxMat.m_toonTextureIndex != -1)
				{
					std::stringstream ss;
					ss << "toon" << std::setfill('0') << std::setw(2) << (pmxMat.m_toonTextureIndex + 1) << ".bmp";
					mat.m_toonTexture = PathUtil::Combine(mmdDataDir, ss.str());
				}
			}
			else if (pmxMat.m_toonMode == PMXToonMode::Separate)
			{
				if (pmxMat.m_toonTextureIndex != -1)
				{
					mat.m_toonTexture = PathUtil::Normalize(texturePaths[pmxMat.m_toonTextureIndex]);
				}
			}

			// SpTexture
			if (pmxMat.m_sphereTextureIndex != -1)
			{
				mat.m_spTexture = PathUtil::Normalize(texturePaths[pmxMat.m_sphereTextureIndex]);
				mat.m_spTextureMode = MMDMaterial::SphereTextureMode::None;
				if (pmxMat.m_sphereMode == PMXSphereMode::Mul)
				{
					mat.m_spTextureMode = MMDMaterial::SphereTextureMode::Mul;
				}
				else if (pmxMat.m_sphereMode == PMXSphereMode::Add)
				{
					mat.m_spTextureMode = MMDMaterial::SphereTextureMode::Add;
				}
				else if (pmxMat.m_sphereMode == PMXSphereMode::SubTexture)
				{
					// TODO: SphareTexture が SubTexture の処理
				}
			}

			Materials.emplace_back(std::move(mat));

			MMDSubMesh subMesh;
			subMesh.m_beginIndex = beginIndex;
			subMesh.m_vertexCount = pmxMat.m_numFaceVertices;
			subMesh.m_materialID = (int)(Materials.size() - 1);
			Sd->subMeshes.push_back(subMesh);

			beginIndex = beginIndex + pmxMat.m_numFaceVertices;
		}

		assert(Sd->subMeshes.size() == Materials.size());//to check
		Sd->initMaterials = Materials;
	}
	else
	{
		Materials = Sd->initMaterials;
	}
	m_mulMaterialFactors.resize(Materials.size());
	m_addMaterialFactors.resize(Materials.size());


	// Node

	{
		//add TempIK
		oriBoneCount = pmx.m_bones.size();
		PMXBone bone{};
		bone.m_name = "tmpIK";
		bone.m_boneFlag = (PMXBoneFlags)(
			PMXBoneFlags::TargetShowMode | PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate
			| PMXBoneFlags::AllowControl | PMXBoneFlags::IK);
		pmx.m_bones.push_back(bone);

		//bone.m_name = "weighter";
		//bone.m_boneFlag = (PMXBoneFlags)(
		//	PMXBoneFlags::TargetShowMode | PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate
		//	| PMXBoneFlags::AllowControl );
		//pmx.m_bones.push_back(bone);

		//pinRbNodes
		bone.m_parentBoneIndex = -777;
#if 0
		bone.m_boneFlag = (PMXBoneFlags)(
			PMXBoneFlags::TargetShowMode | PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate
			| PMXBoneFlags::AllowControl //| PMXBoneFlags::IK | PMXBoneFlags::Visible
			);
		bone.m_name = "pinHandL";			pmx.m_bones.push_back(bone);
		bone.m_name = "pinHandR"; 			pmx.m_bones.push_back(bone);
		bone.m_name = "pinFootL";			pmx.m_bones.push_back(bone);
		bone.m_name = "pinFootR";			pmx.m_bones.push_back(bone);
		bone.m_name = "pinYao";				pmx.m_bones.push_back(bone);
		bone.m_parentBoneIndex = -1;// pmx.rootI;
#endif
	}


	m_nodeMan.GetNodes()->reserve(pmx.m_bones.size()+MAX_EXT_NODE);
	for (const auto& bone : pmx.m_bones)
	{
		auto* node = m_nodeMan.AddNode();
		node->model = this;
		node->SetName(bone.m_name, bone.m_nameU);
		
	}
	ndfwNodes.clear();

	for (size_t i = 0; i < pmx.m_bones.size(); i++)
	{
		createNode(i);
	}

	if (pmx.fgIds[0][0][1] && pmx.fgIds[1][3][3])
	{
		for (int i = 0; i < 2; i++) for (int j = 0; j <5 ; j++) for (int k = 1; k <= 3; k++)
		ndFingers[i][j][k] = m_nodeMan.GetNode(pmx.fgIds[i][j][k]);
	}

	if (!foundRoot)
		m_nodeMan.GetNode(0)->setRoot(true);

	m_transforms.reserve(m_nodeMan.GetNodeCount() + MAX_EXT_NODE);
	m_transforms.resize(m_nodeMan.GetNodeCount());

	m_sortedNodes.clear();
	m_sortedNodes.reserve(m_nodeMan.GetNodeCount() + MAX_EXT_NODE);
	auto* pmxNodes = m_nodeMan.GetNodes();
	for (auto& pmxNode : (*pmxNodes))
	{
		m_sortedNodes.push_back(pmxNode.get());
	}
	std::stable_sort(
		m_sortedNodes.begin(),
		m_sortedNodes.end(),
		[](const PMXNode* x, const PMXNode* y) {return x->GetDeformdepth() < y->GetDeformdepth(); }
	);

	//for (uint32_t i = 0; i < m_sortedNodes.size(); i++)			m_sortedNodes[i]->sortedIdx = i;

	// IK

	for (size_t i = 0; i < pmx.m_bones.size(); i++)
	{
		const auto& bone = pmx.m_bones[i];
		if ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::IK)
		{
			MMDIkSolver* solver = m_ikSolverMan.AddIKSolver();
			auto* ikNode = m_nodeMan.GetNode(i);
			solver->SetIKNode(ikNode);
			ikNode->SetIKSolver(solver);

			auto* targetNode = m_nodeMan.GetNode(bone.m_ikTargetBoneIndex);
			solver->SetTargetNode(targetNode);

			for (const auto& ikLink : bone.m_ikLinks)
			{
				auto* linkNode = m_nodeMan.GetNode(ikLink.m_ikBoneIndex);
				if (ikLink.m_enableLimit)
				{
#ifdef SABA_INVZ
					//glm::vec3 limitMax = ikLink.m_limitMin * glm::vec3(-1, -1, 1);
					//glm::vec3 limitMin = ikLink.m_limitMax * glm::vec3(-1, -1, 1);
					//limitMax.x = -ikLink.m_limitMin.x;
					//limitMax.y = -ikLink.m_limitMin.y;
					//limitMax.z = ikLink.m_limitMax.z;
					//limitMin.y = -ikLink.m_limitMax.y;
					//limitMin.x = -ikLink.m_limitMax.x;
					//limitMin.y = ikLink.m_limitMin.z;
					//solver->AddIKChain(linkNode, true, limitMin, limitMax);

					//https://github.com/benikabocha/saba/issues/30
					glm::vec3 limitMax = ikLink.m_limitMin * glm::vec3(-1);
					glm::vec3 limitMin = ikLink.m_limitMax * glm::vec3(-1);
					solver->AddIKChain(linkNode, true, limitMin, limitMax);
#else

					solver->AddIKChain(linkNode, true, ikLink.m_limitMin, ikLink.m_limitMax);
#endif


				}
				else
				{
					solver->AddIKChain(linkNode);
				}
				linkNode->EnableIK(true);
			}

			solver->SetIterateCount(bone.m_ikIterationCount);
			solver->SetLimitAngle(bone.m_ikLimit);
			if (ikNode->GetName().ends_with("Arm")) {
				solver->initHandDivRat(hasUpper3, fcp.ikHandToArm1);

			}
		}
	}



	mVtxCount = vertexCount;

	// Morph
	
	if (newSd)
	{

		static std::string mA=ualib::WcstoUtf8(L"あ"), mWa = ualib::WcstoUtf8(L"ワ");

		for (const auto& pmxMorph : pmx.m_morphs)
		{
			auto morph = Sd->morphMan.AddMorph();
			assert(pmxMorph.m_controlPanel < 5);
			if (pmxMorph.m_controlPanel==3 && (pmxMorph.m_name == mA || pmxMorph.m_name == mWa))
			{
				morph->isMouthOpen = true;
			}
			morph->SetName(pmxMorph.m_name);
			
			morph->m_morphType = MorphType::None;
			morph->m_controlPanel = pmxMorph.m_controlPanel;
			morph->p_controlPanelAccum = controlPanelAccum;
			morph->SetWeight(0.0f);
			if (pmxMorph.m_morphType == PMXMorphType::Position)
			{
				morph->m_morphType = MorphType::Position;
				morph->m_dataIndex = Sd->positionMorphDatas.size();
				PositionMorphData morphData;
				int mpMin = (int)vertexCount, mpMax = 0;
				for (const auto& vtx : pmxMorph.m_positionMorph)
				{
					PositionMorph morphVtx;
					morphVtx.m_index = vtx.m_vertexIndex;
					morphVtx.m_position = vtx.m_position MUL_VEC3INVZ;
					morphData.m_morphVertices.push_back(morphVtx);

					if (vtx.m_vertexIndex > mpMax) mpMax = vtx.m_vertexIndex;
					if (vtx.m_vertexIndex < mpMin) mpMin = vtx.m_vertexIndex;
				}
				morphData.vtxMin = mpMin; morphData.vtxMax = mpMax;
				Sd->positionMorphDatas.emplace_back(std::move(morphData));

			}
			else if (pmxMorph.m_morphType == PMXMorphType::UV)
			{
				morph->m_morphType = MorphType::UV;
				morph->m_dataIndex = Sd->uvMorphDatas.size();
				UVMorphData morphData;
				for (const auto& uv : pmxMorph.m_uvMorph)
				{
					UVMorph morphUV;
					morphUV.m_index = uv.m_vertexIndex;
					morphUV.m_uv = uv.m_uv;
					morphData.m_morphUVs.push_back(morphUV);
				}
				Sd->uvMorphDatas.emplace_back(std::move(morphData));
			}
			else if (pmxMorph.m_morphType == PMXMorphType::Material)
			{
				morph->m_morphType = MorphType::Material;
				morph->m_dataIndex = Sd->materialMorphDatas.size();

				MaterialMorphData materialMorphData;
				materialMorphData.m_materialMorphs = pmxMorph.m_materialMorph;
				Sd->materialMorphDatas.emplace_back(materialMorphData);
			}
			else if (pmxMorph.m_morphType == PMXMorphType::Bone)
			{
				morph->m_morphType = MorphType::Bone;
				morph->m_dataIndex = Sd->boneMorphDatas.size();

				BoneMorphData boneMorphData;
				for (const auto& pmxBoneMorphElem : pmxMorph.m_boneMorph)
				{
					BoneMorphElement boneMorphElem;
					boneMorphElem.nodeId = pmxBoneMorphElem.m_boneIndex;
					boneMorphElem.m_position = pmxBoneMorphElem.m_position MUL_VEC3INVZ;
					const glm::quat q = pmxBoneMorphElem.m_quaternion;
#ifdef SABA_INVZ
					auto invZ = glm::mat3(glm::scale(glm::mat4(1), glm::vec3(1, 1, -1)));
					auto rot0 = glm::mat3_cast(q);
					auto rot1 = invZ * rot0 * invZ;
#else
					auto rot0 = glm::mat3_cast(q);
					auto rot1 = rot0;
#endif
					boneMorphElem.m_rotate = glm::quat_cast(rot1);
					boneMorphData.m_boneMorphs.push_back(boneMorphElem);
				}
				Sd->boneMorphDatas.emplace_back(boneMorphData);
			}
			else if (pmxMorph.m_morphType == PMXMorphType::Group)
			{
				morph->m_morphType = MorphType::Group;
				morph->m_dataIndex = Sd->groupMorphDatas.size();

				GroupMorphData groupMorphData;
				groupMorphData.m_groupMorphs = pmxMorph.m_groupMorph;
				Sd->groupMorphDatas.emplace_back(groupMorphData);
			}
			else
			{
				SABA_WARN("Not Supported Morp Type({}): [{}]",
					(uint8_t)pmxMorph.m_morphType,
					pmxMorph.m_name
				);
			}
		}



		// Check whether Group Morph infinite loop.
		{
			std::vector<int32_t> groupMorphStack;
			std::function<void(int32_t)> fixInifinitGropuMorph;
			fixInifinitGropuMorph = [this, &fixInifinitGropuMorph, &groupMorphStack](int32_t morphIdx)
				{

					const auto& morphs = (*Sd->morphMan.GetMorphs());
					if (morphIdx >= morphs.size())
						return;
					const auto& morph = morphs[morphIdx];

					if (morph->m_morphType == MorphType::Group)
					{
						auto& groupMorphData = Sd->groupMorphDatas[morph->m_dataIndex];
						for (size_t i = 0; i < groupMorphData.m_groupMorphs.size(); i++)
						{
							auto& groupMorph = groupMorphData.m_groupMorphs[i];

							auto findIt = std::find(
								groupMorphStack.begin(),
								groupMorphStack.end(),
								groupMorph.m_morphIndex
							);
							if (findIt != groupMorphStack.end())
							{
								SABA_WARN("Infinit Group Morph:[{}][{}][{}]",
									morphIdx, morph->GetName(), i
								);
								groupMorph.m_morphIndex = -1;
							}
							else
							{
								groupMorphStack.push_back(morphIdx);
								fixInifinitGropuMorph(groupMorph.m_morphIndex);
								groupMorphStack.pop_back();
							}
						}
					}
				};

			for (int32_t morphIdx = 0; morphIdx < int32_t(Sd->morphMan.GetMorphCount()); morphIdx++)
			{
				fixInifinitGropuMorph(morphIdx);
				groupMorphStack.clear();
			}

		}
	}

	// Physics
	if (!m_physicsMan.Create(fcp.phType))
	{
		SABA_ERROR("Create Physics Fail.");
		return false;
	}


	PMXRigidbody prb{ "yao","yao",2,1,0x8000,PMXRigidbody::Shape::Capsule };
	//if (m_nodeMan.GetNodeCount()>32)
	{
		prb.m_op = PMXRigidbody::Operation::Static;
		prb.m_boneIndex = -1;

		//if (m_nodeMan.GetNodeCount()> 6 && !(m_nodeMan.GetMMDNode(6)->GetNameU() == L"腰"))
		prb.m_boneIndex = GetNodeManager()->FindNodeIndex(L"腰");

		if (prb.m_boneIndex == -1) 	prb.m_boneIndex = GetNodeManager()->FindNodeIndex(L"センター0");
		else isCharacter = true;
		isCharacter = isCharacter && GetNodeManager()->FindNodeIndex(L"頭") != -1;

		allowAllPhysicsNode |= isCharacter;
		//allowAllPhysicsNode = 1;// isCharacter&& allowAllPhysicsNode&& ALL_NODE_PHYSICS;

		if (prb.m_boneIndex == -1) {
			prb.m_boneIndex = -1;
			prb.m_name = "insertDummy";
		}

		initMassMulByVolume *= fcp.modelScaleVec3.x * fcp.modelScaleVec3.y * fcp.modelScaleVec3.z;
		if (pmx.allDynRb)   isCharacter = true;
		
		if (isCharacter)
		{
			rootRbBoneIdx = prb.m_boneIndex;
			yaoPos = prb.m_translate = pmx.m_bones[prb.m_boneIndex].m_position;
			//if (isCharacter) yaoPos += glm::vec3(0, -1, 0);
			float cy = std::max(1.f, prb.m_translate.y) * (allowAllPhysicsNode ?
				(PEI_ZHONG ? SABA_USE_PHYSX ? 1.6 : 1.5 : 0.1) : 1);
			rbRootY = cy;
			if (PEI_ZHONG) prb.m_shapeSize = { cy / (SABA_USE_PHYSX ? (PEI_ZHONG ? 1.6 : 8) : 32) ,PEI_ZHONG ? 1 : cy / 2,cy };// prb.m_shape = PMXRigidbody::Shape::Box;
			else prb.m_shapeSize = vec3{ 1,1,1 }  *fcp.modelScaleVec3;

			prb.m_shape = PMXRigidbody::Shape::Sphere;
			//prb.m_translate.z -= 1; //udu + udd /2
			//prb.m_translate.y *= 0.87f;;
			//prb.m_mass = 1000000;
			prb.m_translateDimmer = 0.05;
			prb.m_rotateDimmer = 0.1;
			prb.m_friction = 1000.0;
			prb.m_repulsion = 0;
			ctrBallSize = prb.m_shapeSize;
			prb.m_mass = (PEI_ZHONG ? 900 : 200) * initMassMulByVolume;
			prb.m_group = 0;
			prb.m_collideMask32 = (PEI_ZHONG ? 0x10000 : 0x10000);
			prb.canInTrigger = true;
			pmx.m_rigidbodies.insert(pmx.m_rigidbodies.begin(), prb); assert(INS_RB_NUM == 1); // add RigidBody Count	

			prb.canInTrigger = false;
			prb.m_mass = 10000 * initMassMulByVolume;
			prb.m_shape = PMXRigidbody::Shape::Sphere;
			prb.m_translateDimmer = 0;
			prb.m_rotateDimmer = 0;
			//prb.m_boneIndex =  prb.m_boneIndex = GetNodeManager()->FindNodeIndex(L"weighter");
			float wr = 10.1f;
			prb.m_translate = { 0, SABA_USE_PHYSX ? -30 : wr - cy * 2   ,0 };
			prb.m_shapeSize = { 3,3,3 };
			prb.m_collideMask32 = 0;
			if (PEI_ZHONG) {
				weightRbIdx = pmx.m_rigidbodies.size(); pmx.m_rigidbodies.push_back(prb);
			}

			for (auto& joint : pmx.m_joints) {
				if (joint.m_rigidbodyAIndex >= 0)	joint.m_rigidbodyAIndex += INS_RB_NUM;
				if (joint.m_rigidbodyBIndex >= 0)	joint.m_rigidbodyBIndex += INS_RB_NUM;
			}
			for (auto& morph : pmx.m_morphs) for (auto& data : morph.m_impulseMorph) if (data.m_rigidbodyIndex >= 0)	data.m_rigidbodyIndex += INS_RB_NUM;
			for (auto& sb : pmx.m_softbodies)for (auto& ar : sb.m_anchorRigidbodies) if (ar.m_rigidBodyIndex >= 0)	ar.m_rigidBodyIndex += INS_RB_NUM;
		}
		else {

			prb.m_shape = PMXRigidbody::Shape::Sphere;
			prb.m_shapeSize = { 0.1,0.1,0.1 };
			prb.m_collideMask32 = 0;

			//pmx.m_rigidbodies.insert(pmx.m_rigidbodies.begin(), prb); assert(INS_RB_NUM == 1); // add RigidBody Count	
		}
	}
	//std::sort(pmx.m_rigidbodies.begin(), pmx.m_rigidbodies.end(), [&](PMXRigidbody& a, PMXRigidbody& b) {
	//	return a.m_boneIndex < b.m_boneIndex;
	//	});


	rbids.resize(pmx.m_rigidbodies.size());
	for (int i = 0; i < rbids.size(); i++) rbids[i] = i;
	std::stable_sort(rbids.begin(), rbids.end(), [&](const int& a, const int& b) {
		auto& rba = pmx.m_rigidbodies[a], rbb = pmx.m_rigidbodies[b];
		//if (rba.m_boneIndex >= 0 && rbb.m_boneIndex >= 0) {
		//	if ((pmx.m_bones[rba.m_boneIndex].m_parentBoneIndex) == (pmx.m_bones[rbb.m_boneIndex].m_parentBoneIndex))
		//		return rba.m_boneIndex < rbb.m_boneIndex;
		//	return (pmx.m_bones[rba.m_boneIndex].m_parentBoneIndex) < (pmx.m_bones[rbb.m_boneIndex].m_parentBoneIndex);
		//}

		return rba.m_boneIndex < rbb.m_boneIndex;
		});

 
	rbs.clear();
	for (size_t i = 0; i < pmx.m_rigidbodies.size(); i++)
	{
		newRb(i);
	}
 

	if (!createRbs()) return false;


	m_physicsMan.sortedRBs.clear(); m_physicsMan.sortedRBs.reserve(rbids.size()+MAX_EXT_NODE);
	for (int i = 0; i < rbids.size(); i++)
		m_physicsMan.sortedRBs.emplace_back(m_physicsMan.m_rigidBodys[rbids[i]].get());
	for (int i = 0; i < rbids.size(); i++) {

		//DPWCS((L"RB %d (%d) %s", i, rbids[i], ualib::Utf8toWcs(m_physicsMan.sortedRBs[i]->Pm.m_name).c_str()));
	}

	if (rootRb) {
		//rbTreeNodes.clear(); 
		//sortedDynRbs(rootRb->node);
		rootRb->onAllRbCreated(this);
	}
	m_physicsMan.addAllBodies();
	//calcSubRbWeight();


	//if(0)

	static int cc = 0;
	for (auto& pmxJoint : pmx.m_joints)
	{
		cc++;
 
		if (pmxJoint.m_rigidbodyAIndex != -1 &&
			pmxJoint.m_rigidbodyBIndex != -1 &&
			pmxJoint.m_rigidbodyAIndex != pmxJoint.m_rigidbodyBIndex)
		{
			auto joint = m_physicsMan.AddJoint();
			if (!joint) break;
			MMDNode* node = nullptr;
			auto rigidBodys = &m_physicsMan.m_rigidBodys;
			auto rba = (*rigidBodys)[pmxJoint.m_rigidbodyAIndex].get();
			auto rbb = (*rigidBodys)[pmxJoint.m_rigidbodyBIndex].get();
			DPWCS((L"rbb %s", ualib::Utf8toWcs(rbb->Pm.m_name).c_str()));
			//assert(rba->mmdNode->rb0 == rba);
			if (rbb->Pm.isOppai) {
				pmxJoint.springR = vec3(100000.f);
				pmxJoint.dampingR = vec3(10000.f);
			}

			bool ret = joint->CreateJointPmx(
				pmxJoint,
				rba,
				rbb
			);
			if (!ret)
			{
				SABA_ERROR("Create Joint Fail.\n");
				return false;
			}
			m_physicsMan.GetMMDPhysics()->AddJoint(joint);

#if 0
			///test Joint
			if (cc > 10) {
				auto joint = m_physicsMan.AddJoint();
				bool ret = joint->CreateJoint(
					pmxJoint,
					touchRB,
					(*rigidBodys)[pmxJoint.m_rigidbodyBIndex].get()
				);
				if (!ret)
				{
					SABA_ERROR("Create Joint Fail.\n");
					return false;
				}
				m_physicsMan.GetMMDPhysics()->AddJoint(joint);
			}
#endif
		}
		else
		{
			SABA_WARN("Illegal Joint [{}]", pmxJoint.m_name.c_str());
		}
	}

	for (const auto& disp : pmx.m_displayFrames)
	{
		auto ws = ualib::Utf8toWcs(disp.m_name);
		mapDIsp[ws] = disp.m_targets;
	}

#if INFLATE_MATERIAL
	createInflate();
#endif
	GetMMDPhysics()->onCreated();
	InitializeAnimation();
	ResetPhysics();

	SetupParallelUpdate();
	if (!warnSDEF && !infoQDEF && m_transforms.size() < SIMPLE_MMD_MAX_GPU_NODE_COUNT) useGPU = true;
	return true;
}

void saba::PMXModel::newRb(size_t i)
{
	auto rb = m_physicsMan.newRigidBody(false);
	rb->pmxRbIdx = i;
	rbs.push_back(rb);
	auto& pmxRB = pmx.m_rigidbodies[i];
	if (pmxRB.m_boneIndex != -1) {
		auto node = GetNodeManager()->GetMMDNode(pmxRB.m_boneIndex);
		if (!node->rb0) {
			node->rb0 = rb; rb->isSubRb = false;
			node->rbAtk = node->rb0;
		}
		else rb->isSubRb = true;
		
		rb->node = (PMXNode*)node;
	}
}

void saba::PMXModel::createNode(size_t i)
{
	int32_t boneIndex = (int32_t)(i);// pmx.m_bones.size() - i - 1);
	const auto& bone = pmx.m_bones[boneIndex];
	auto* node = m_nodeMan.GetNode(boneIndex);
	node->pmxBone = &pmx.m_bones[boneIndex];

	if (!foundRoot && i < pmx.m_bones.size() - 1 && pmx.m_bones[i + 1].m_parentBoneIndex == i) {
		foundRoot = true; rootIdx = i;
		node->setRoot(true);
		nodeRoot = node;
	}
	if (bone.m_parentBoneIndex == -777)
		node->pmxBone->m_parentBoneIndex = rootIdx;
	if (bone.anim && bone.anim->ndFwCount > 0) {
		ndfwNodes.push_back(node);
	}
	// Check if the node is looping
	bool isLooping = false;
	if (bone.m_parentBoneIndex != -1)
	{
		MMDNode* parent = m_nodeMan.GetNode(bone.m_parentBoneIndex);
		while (parent != nullptr)
		{
			if (parent == node)
			{
				isLooping = true;
				SABA_ERROR("This bone hierarchy is a loop: bone={}", boneIndex);
				break;
			}
			parent = parent->GetParent();
		}
	}

	// Check parent node index
	if (bone.m_parentBoneIndex != -1)
	{
		if (bone.m_parentBoneIndex >= boneIndex)
		{
			SABA_WARN("The parent index of this node is big: bone={}", boneIndex);
		}
	}

	if ((bone.m_parentBoneIndex != -1) && !isLooping)
	{
		int pbidx = bone.m_parentBoneIndex;
		//if (pbidx == 1 && i > 2) pbidx = 2;//change to center, not ok
		const auto& parentBone = pmx.m_bones[pbidx];
		auto* parent = m_nodeMan.GetNode(pbidx);
		parent->AddChild(node);
		auto localPos = bone.m_position - parentBone.m_position;
#ifdef SABA_INVZ
		localPos.z *= -1;
#endif
		node->SetTranslate(localPos);

		if (boneIndex < 50) {
			if (node->GetNameU()[0] == L'上' && node->GetNameU() == L"上半身3") hasUpper3 = true;
		}
	}
	else
	{
		auto localPos = bone.m_position;
#ifdef SABA_INVZ
		localPos.z *= -1;
#endif
		node->SetTranslate(localPos);
	}



	glm::mat4 init = glm::translate(
		glm::mat4(1),
		bone.m_position MUL_VEC3INVZ
	);

	node->SetGlobalTransform(init); node->mGlobalInit = init;
	node->CalculateInverseInitTransform();
	node->posOffset = bone.m_positionOffset;
	node->flVisible = ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::Visible);
	node->flMove = ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::AllowTranslate);
	node->flRotate = ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::AllowRotate);
	node->flFixedAxis = ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::FixedAxis);
	if (node->flFixedAxis)
		node->fixedAxis = bone.m_fixedAxis;
	node->SetDeformDepth(bone.m_deformDepth);
	bool deformAfterPhysics = !!((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::DeformAfterPhysics);
	if (bone.anim && bone.anim->aniJellyfish) deformAfterPhysics = true;

	node->EnableDeformAfterPhysics(deformAfterPhysics);
	bool appendRotate = ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::AppendRotate) != 0;
	bool appendTranslate = ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::AppendTranslate) != 0;
	node->EnableAppendRotate(appendRotate);
	node->EnableAppendTranslate(appendTranslate);
	if ((appendRotate || appendTranslate) && (bone.m_appendBoneIndex != -1))
	{
		if (bone.m_appendBoneIndex >= boneIndex)
		{
			SABA_WARN("The parent(morph assignment) index of this node is big: bone={}", boneIndex);
		}
		bool appendLocal = ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::AppendLocal) != 0;
		auto appendNode = m_nodeMan.GetNode(bone.m_appendBoneIndex);
		float appendWeight = bone.m_appendWeight;
		node->EnableAppendLocal(appendLocal);
		node->SetAppendNode(appendNode);
		node->SetAppendWeight(appendWeight);
	}
	//if (i==0) node->SetTranslate(glm::vec3(0, 30, 0));
	node->SaveInitialTRS();

	if (i >= oriBoneCount) {
		if (!tmpIKNode && node->GetName() == "tmpIK") {
			tmpIKNode = node;
		}

	}
}


void PMXModel::Destroy()
{
	Materials.clear();
#if PMX_CACHE_DATA
	Sd = nullptr;
#else
	if (Sd) {
		Sd->subMeshes.clear();

		Sd->positions.clear();
		Sd->normals.clear();
		Sd->uvs.clear();
		Sd->vertexBoneInfos.clear();

		Sd->indices.clear();
	}
#endif
	m_nodeMan.GetNodes()->clear();

	m_updateRanges.clear();
}


void saba::PMXModel::A_CopyBoneWeightFromBtoC(const char* fileName, const char* from, const char* outfile, float maxDis )
{
	PMXFile pmxA,pmxB;
	pmxA.fcp.addRootBone = 0;
	pmxA.fcp.addEyeRb = 0;
	pmxA.fcp.addSubRbBase = 0;
	ReadPMXFile(&pmxA,  fileName);
	pmxB.fcp.addRootBone = 0;
	pmxB.fcp.addEyeRb = 0;
	pmxB.fcp.addSubRbBase = 0;
	ReadPMXFile(&pmxB, from);

	pmxA.copyVertexBoneInfoFrom(&pmxB, maxDis);
	PMXWriter writer;
 
	writer.writeFile(&pmxA, outfile);
 
}

int saba::PMXModel::writeFile(const char* addFileName)
{
	PMXWriter writer;	
	writer.writeFile(&pmx, (pmx.realPath+std::string(addFileName)).c_str());
	return 0;
}

void PMXModel::SetupParallelUpdate()
{
	if (m_parallelUpdateCount == 0)
	{
		m_parallelUpdateCount = std::thread::hardware_concurrency();
	}
	size_t maxParallelCount = std::min(size_t(MAX_Thread), size_t(std::thread::hardware_concurrency()));
	if (m_parallelUpdateCount > maxParallelCount)
	{
		SABA_WARN("PMXModel::SetParallelUpdateCount parallelCount > {}", maxParallelCount);
		m_parallelUpdateCount = MAX_Thread;
	}

	SABA_INFO("Select PMX Parallel Update Count : {}", m_parallelUpdateCount);

	m_updateRanges.resize(m_parallelUpdateCount);
	m_parallelUpdateFutures.resize(m_parallelUpdateCount - 1);

	const size_t vertexCount = Sd->positions.size();
	const size_t LowerVertexCount = 1000;
	if (vertexCount < m_updateRanges.size() * LowerVertexCount)
	{
		size_t numRanges = (vertexCount + LowerVertexCount - 1) / LowerVertexCount;
		for (size_t rangeIdx = 0; rangeIdx < m_updateRanges.size(); rangeIdx++)
		{
			auto& range = m_updateRanges[rangeIdx];
			if (rangeIdx < numRanges)
			{
				range.m_vertexOffset = rangeIdx * LowerVertexCount;
				range.m_vertexCount = std::min(LowerVertexCount, vertexCount - range.m_vertexOffset);
			}
			else
			{
				range.m_vertexOffset = 0;
				range.m_vertexCount = 0;
			}
		}
	}
	else
	{
		size_t numVertexCount = vertexCount / m_updateRanges.size();
		size_t offset = 0;
		for (size_t rangeIdx = 0; rangeIdx < m_updateRanges.size(); rangeIdx++)
		{
			auto& range = m_updateRanges[rangeIdx];
			range.m_vertexOffset = offset;
			range.m_vertexCount = numVertexCount;
			if (rangeIdx == 0)
			{
				range.m_vertexCount += vertexCount % m_updateRanges.size();
			}
			offset = range.m_vertexOffset + range.m_vertexCount;
		}
	}


}

void PMXModel::UpdateRg(const UpdateRange & range)
{
	const auto* position = Sd->positions.data() + range.m_vertexOffset;
	const auto* normal = Sd->normals.data() + range.m_vertexOffset;
	const auto* uv = Sd->uvs.data() + range.m_vertexOffset;
	const auto* morphPos = m_morphPositions.data() + range.m_vertexOffset;
	const auto* morphUV = m_morphUVs.data() + range.m_vertexOffset;
	const auto* vtxInfo = Sd->vertexBoneInfos.data() + range.m_vertexOffset;
	const auto* transforms = m_transforms.data();
	auto* updatePosition = m_updatePositions.data() + range.m_vertexOffset;
	auto* updateNormal = m_updateNormals.data() + range.m_vertexOffset;
	auto* updateUV = m_updateUVs.data() + range.m_vertexOffset;

	//int cc = 0;
	if (gpuVtxPtr)
	{
		char* pb = ((char*)gpuVtxPtr) + range.m_vertexOffset * gpuVtxStride;
		for (size_t i = 0; i < range.m_vertexCount; i++)
		{
			glm::mat4 m;
			switch (vtxInfo->m_skinningType)
			{
			case PMXModel::SkinningType::Weight1:
			{
				const auto i0 = vtxInfo->m_boneIndex[0];
				const auto& m0 = transforms[i0];
				m = m0;
				break;
			}
			case PMXModel::SkinningType::Weight2:
			{
				const auto i0 = vtxInfo->m_boneIndex[0];
				const auto i1 = vtxInfo->m_boneIndex[1];
				const auto w0 = vtxInfo->m_boneWeight[0];
				const auto w1 = vtxInfo->m_boneWeight[1];
				const auto& m0 = transforms[i0];
				const auto& m1 = transforms[i1];
				m = m0 * w0 + m1 * w1;
				break;
			}
			case PMXModel::SkinningType::Weight4:
			{
				const auto i0 = vtxInfo->m_boneIndex[0];
				const auto i1 = vtxInfo->m_boneIndex[1];
				const auto i2 = vtxInfo->m_boneIndex[2];
				const auto i3 = vtxInfo->m_boneIndex[3];
				const auto w0 = vtxInfo->m_boneWeight[0];
				const auto w1 = vtxInfo->m_boneWeight[1];
				const auto w2 = vtxInfo->m_boneWeight[2];
				const auto w3 = vtxInfo->m_boneWeight[3];
				const auto& m0 = transforms[i0];
				const auto& m1 = transforms[i1];
				const auto& m2 = transforms[i2];
				const auto& m3 = transforms[i3];
				m = m0 * w0 + m1 * w1 + m2 * w2 + m3 * w3;
				break;
			}
			case PMXModel::SkinningType::SDEF:
			{
				//cc++;
				// https://github.com/powroupi/blender_mmd_tools/blob/dev_test/mmd_tools/core/sdef.py

				auto& nodes = (*m_nodeMan.GetNodes());
				const auto i0 = vtxInfo->m_sdef.m_boneIndex[0];
				const auto i1 = vtxInfo->m_sdef.m_boneIndex[1];
				const auto w0 = vtxInfo->m_sdef.m_boneWeight;
				const auto w1 = 1.0f - w0;
				const auto center = vtxInfo->m_sdef.m_sdefC;
				const auto cr0 = vtxInfo->m_sdef.m_sdefR0;
				const auto cr1 = vtxInfo->m_sdef.m_sdefR1;
				const auto q0 = glm::quat_cast(nodes[i0]->GetGlobalTransform());
				const auto q1 = glm::quat_cast(nodes[i1]->GetGlobalTransform());
				const auto m0 = transforms[i0];
				const auto m1 = transforms[i1];

				const auto pos = *position + *(glm::vec3*)morphPos;
				const auto rot_mat = glm::mat3_cast(glm::slerp(q0, q1, w1));

				//glm::vec3 scale;		glm::quat rotation;		glm::vec3 translation;	glm::vec3 skew;	glm::vec4 perspective;			glm::decompose(m0, scale, rotation, translation, skew, perspective);
				//const auto rot_mat = glm::mat3(glm::mat4(glm::slerp(q0, q1, w1)) * glm::scale(glm::mat4(1), scale));
				*updatePosition = *(glm::vec3*)pb = glm::mat3(rot_mat) * (pos - center) + glm::vec3(m0 * glm::vec4(cr0, 1)) * w0 + glm::vec3(m1 * glm::vec4(cr1, 1)) * w1;
				*(glm::vec3*)(pb + gpuNormalOfs) = rot_mat * *normal;

				break;
			}
			case PMXModel::SkinningType::DualQuaternion:
			{
				//
				// Skinning with Dual Quaternions
				// https://www.cs.utah.edu/~ladislav/dq/index.html
				//
				glm::dualquat dq[4];
				float w[4] = { 0 };
				for (int bi = 0; bi < 4; bi++)
				{
					auto boneID = vtxInfo->m_boneIndex[bi];
					if (boneID != -1)
					{
						dq[bi] = glm::dualquat_cast(glm::mat3x4(glm::transpose(transforms[boneID])));
						dq[bi] = glm::normalize(dq[bi]);
						w[bi] = vtxInfo->m_boneWeight[bi];
					}
					else
					{
						w[bi] = 0;
					}
				}
				if (glm::dot(dq[0].real, dq[1].real) < 0) { w[1] *= -1.0f; }
				if (glm::dot(dq[0].real, dq[2].real) < 0) { w[2] *= -1.0f; }
				if (glm::dot(dq[0].real, dq[3].real) < 0) { w[3] *= -1.0f; }
				auto blendDQ = w[0] * dq[0]
					+ w[1] * dq[1]
					+ w[2] * dq[2]
					+ w[3] * dq[3];
				blendDQ = glm::normalize(blendDQ);
				m = glm::transpose(glm::mat3x4_cast(blendDQ));
				break;
			}
			default:
				break;
			}

			if (PMXModel::SkinningType::SDEF != vtxInfo->m_skinningType)
			{
				*updatePosition = *(glm::vec3*)pb = glm::vec3(m * glm::vec4(*position + *(glm::vec3*)morphPos, 1));
				*(glm::vec3*)(pb + gpuNormalOfs) = glm::normalize(glm::mat3(m) * *normal);
			}
			*updateUV = *uv + glm::vec2((*morphUV).x, (*morphUV).y);


			pb += gpuVtxStride;

			vtxInfo++;
			position++;
			normal++;
			uv++;

			updatePosition++;
			updateNormal++;
			updateUV++;
			morphPos++;
			morphUV++;
		}
		//DbgPrint("cc %d",cc);

		if (!uvUploaded) {
			auto* updateUV = m_updateUVs.data() + range.m_vertexOffset;
			pb = ((char*)gpuVtxPtr) + range.m_vertexOffset * gpuVtxStride;

			if (!texInvV)
				for (size_t i = 0; i < range.m_vertexCount; i++)
				{
					glm::vec2* vect = (glm::vec2*)(pb + gpuUVOfs);
					glm::vec2* vecs = updateUV;

					*vect = *vecs;

					updateUV++;
					pb += gpuVtxStride;
				}
			else
				for (size_t i = 0; i < range.m_vertexCount; i++)
				{
					glm::vec2* vect = (glm::vec2*)(pb + gpuUVOfs);
					glm::vec2* vecs = updateUV;

					vect->x = vecs->x;
					vect->y = 1 - vecs->y;

					updateUV++;
					pb += gpuVtxStride;
				}
		}
		vtxPosUpdated = true;
	}
	else
		for (size_t i = 0; i < range.m_vertexCount; i++)
		{
			glm::mat4 m;
			switch (vtxInfo->m_skinningType)
			{
			case PMXModel::SkinningType::Weight1:
			{
				const auto i0 = vtxInfo->m_boneIndex[0];
				const auto& m0 = transforms[i0];
				m = m0;
				break;
			}
			case PMXModel::SkinningType::Weight2:
			{
				const auto i0 = vtxInfo->m_boneIndex[0];
				const auto i1 = vtxInfo->m_boneIndex[1];
				const auto w0 = vtxInfo->m_boneWeight[0];
				const auto w1 = vtxInfo->m_boneWeight[1];
				const auto& m0 = transforms[i0];
				const auto& m1 = transforms[i1];
				m = m0 * w0 + m1 * w1;
				break;
			}
			case PMXModel::SkinningType::Weight4:
			{
				const auto i0 = vtxInfo->m_boneIndex[0];
				const auto i1 = vtxInfo->m_boneIndex[1];
				const auto i2 = vtxInfo->m_boneIndex[2];
				const auto i3 = vtxInfo->m_boneIndex[3];
				const auto w0 = vtxInfo->m_boneWeight[0];
				const auto w1 = vtxInfo->m_boneWeight[1];
				const auto w2 = vtxInfo->m_boneWeight[2];
				const auto w3 = vtxInfo->m_boneWeight[3];
				const auto& m0 = transforms[i0];
				const auto& m1 = transforms[i1];
				const auto& m2 = transforms[i2];
				const auto& m3 = transforms[i3];
				m = m0 * w0 + m1 * w1 + m2 * w2 + m3 * w3;
				break;
			}
			case PMXModel::SkinningType::SDEF:
			{
				// https://github.com/powroupi/blender_mmd_tools/blob/dev_test/mmd_tools/core/sdef.py

				auto& nodes = (*m_nodeMan.GetNodes());
				const auto i0 = vtxInfo->m_sdef.m_boneIndex[0];
				const auto i1 = vtxInfo->m_sdef.m_boneIndex[1];
				const auto w0 = vtxInfo->m_sdef.m_boneWeight;
				const auto w1 = 1.0f - w0;
				const auto center = vtxInfo->m_sdef.m_sdefC;
				const auto cr0 = vtxInfo->m_sdef.m_sdefR0;
				const auto cr1 = vtxInfo->m_sdef.m_sdefR1;
				const auto q0 = glm::quat_cast(nodes[i0]->GetGlobalTransform());
				const auto q1 = glm::quat_cast(nodes[i1]->GetGlobalTransform());
				const auto m0 = transforms[i0];
				const auto m1 = transforms[i1];

				const auto pos = *position + *(glm::vec3*)morphPos;
				const auto rot_mat = glm::mat3_cast(glm::slerp(q0, q1, w1));

				*updatePosition = glm::mat3(rot_mat) * (pos - center) + glm::vec3(m0 * glm::vec4(cr0, 1)) * w0 + glm::vec3(m1 * glm::vec4(cr1, 1)) * w1;
				*updateNormal = rot_mat * *normal;

				break;
			}
			case PMXModel::SkinningType::DualQuaternion:
			{
				//
				// Skinning with Dual Quaternions
				// https://www.cs.utah.edu/~ladislav/dq/index.html
				//
				glm::dualquat dq[4];
				float w[4] = { 0 };
				for (int bi = 0; bi < 4; bi++)
				{
					auto boneID = vtxInfo->m_boneIndex[bi];
					if (boneID != -1)
					{
						dq[bi] = glm::dualquat_cast(glm::mat3x4(glm::transpose(transforms[boneID])));
						dq[bi] = glm::normalize(dq[bi]);
						w[bi] = vtxInfo->m_boneWeight[bi];
					}
					else
					{
						w[bi] = 0;
					}
				}
				if (glm::dot(dq[0].real, dq[1].real) < 0) { w[1] *= -1.0f; }
				if (glm::dot(dq[0].real, dq[2].real) < 0) { w[2] *= -1.0f; }
				if (glm::dot(dq[0].real, dq[3].real) < 0) { w[3] *= -1.0f; }
				auto blendDQ = w[0] * dq[0]
					+ w[1] * dq[1]
					+ w[2] * dq[2]
					+ w[3] * dq[3];
				blendDQ = glm::normalize(blendDQ);
				m = glm::transpose(glm::mat3x4_cast(blendDQ));
				break;
			}
			default:
				break;
			}

			if (PMXModel::SkinningType::SDEF != vtxInfo->m_skinningType)
			{
				*updatePosition = glm::vec3(m * glm::vec4(*position + *(glm::vec3*)morphPos, 1));
				*updateNormal = glm::normalize(glm::mat3(m) * *normal);
			}
			*updateUV = *uv + glm::vec2((*morphUV).x, (*morphUV).y);

			vtxInfo++;
			position++;
			normal++;
			uv++;
			updatePosition++;
			updateNormal++;
			updateUV++;
			morphPos++;
			morphUV++;
		}

}

void PMXModel::Morph(PMXMorph * morph, float weight)
{
	switch (morph->m_morphType)
	{
	case MorphType::Position:
		weight = std::min(1.5f - controlPanelAccum[morph->m_controlPanel], weight);
		MorphPosition(
			Sd->positionMorphDatas[morph->m_dataIndex],
			weight
		);
		controlPanelAccum[morph->m_controlPanel] += weight;
		break;
	case MorphType::UV:
		MorphUV(
			Sd->uvMorphDatas[morph->m_dataIndex],
			weight
		);
		break;
	case MorphType::Material:
		MorphMaterial(
			Sd->materialMorphDatas[morph->m_dataIndex],
			weight
		);
		break;
	case MorphType::Bone:
		MorphBone(
			Sd->boneMorphDatas[morph->m_dataIndex],
			weight
		);
		break;
	case MorphType::Group:
	{
		auto& groupMorphData = Sd->groupMorphDatas[morph->m_dataIndex];
		for (const auto& groupMorph : groupMorphData.m_groupMorphs)
		{
			if (groupMorph.m_morphIndex == -1 || groupMorph.m_morphIndex >= Sd->morphMan.GetMorphCount()) { continue; }
			auto& elemMorph = (*Sd->morphMan.GetMorphs())[groupMorph.m_morphIndex];
			Morph(elemMorph.get(), groupMorph.m_weight * weight);
		}
		break;
	}
	default:
		break;
	}
}

void PMXModel::MorphPosition(const PositionMorphData & morphData, float weight)
{
	if (weight == 0)
	{
		return;
	}

	for (const auto& morphVtx : morphData.m_morphVertices)
	{
		*(glm::vec3*)&m_morphPositions[morphVtx.m_index] += morphVtx.m_position * weight;
	}
	if (morphData.vtxMin < morphVtxMin) morphVtxMin = morphData.vtxMin;
	if (morphData.vtxMax > morphVtxMax) morphVtxMax = morphData.vtxMax;
}

void PMXModel::MorphUV(const UVMorphData & morphData, float weight)
{
	if (weight == 0)
	{
		return;
	}

	for (const auto& morphUV : morphData.m_morphUVs)
	{
		m_morphUVs[morphUV.m_index] += morphUV.m_uv * weight;
	}
}

void PMXModel::BeginMorphMaterial()
{
	MaterialFactor initMul;
	initMul.m_diffuse = glm::vec3(1);
	initMul.m_alpha = 1;
	initMul.m_specular = glm::vec3(1);
	initMul.m_specularPower = 1;
	initMul.m_ambient = glm::vec3(1);
	initMul.m_edgeColor = glm::vec4(1);
	initMul.m_edgeSize = 1;
	initMul.m_textureFactor = glm::vec4(1);
	initMul.m_spTextureFactor = glm::vec4(1);
	initMul.m_toonTextureFactor = glm::vec4(1);

	MaterialFactor initAdd;
	initAdd.m_diffuse = glm::vec3(0);
	initAdd.m_alpha = 0;
	initAdd.m_specular = glm::vec3(0);
	initAdd.m_specularPower = 0;
	initAdd.m_ambient = glm::vec3(0);
	initAdd.m_edgeColor = glm::vec4(0);
	initAdd.m_edgeSize = 0;
	initAdd.m_textureFactor = glm::vec4(0);
	initAdd.m_spTextureFactor = glm::vec4(0);
	initAdd.m_toonTextureFactor = glm::vec4(0);

	size_t matCount = Materials.size();
	for (size_t matIdx = 0; matIdx < matCount; matIdx++)
	{
		m_mulMaterialFactors[matIdx] = initMul;
		m_mulMaterialFactors[matIdx].m_diffuse = Sd->initMaterials[matIdx].m_diffuse;
		m_mulMaterialFactors[matIdx].m_alpha = Sd->initMaterials[matIdx].m_alpha;
		m_mulMaterialFactors[matIdx].m_specular = Sd->initMaterials[matIdx].m_specular;
		m_mulMaterialFactors[matIdx].m_specularPower = Sd->initMaterials[matIdx].m_specularPower;
		m_mulMaterialFactors[matIdx].m_ambient = Sd->initMaterials[matIdx].m_ambient;

		m_addMaterialFactors[matIdx] = initAdd;
	}
}

void PMXModel::EndMorphMaterial()
{
	size_t matCount = Materials.size();
	for (size_t matIdx = 0; matIdx < matCount; matIdx++)
	{
		MaterialFactor matFactor = m_mulMaterialFactors[matIdx];
		matFactor.Add(m_addMaterialFactors[matIdx], 1.0f);

		Materials[matIdx].m_diffuse = matFactor.m_diffuse;
		Materials[matIdx].m_alpha = matFactor.m_alpha;
		Materials[matIdx].m_specular = matFactor.m_specular;
		Materials[matIdx].m_specularPower = matFactor.m_specularPower;
		Materials[matIdx].m_ambient = matFactor.m_ambient;
		Materials[matIdx].m_textureMulFactor = m_mulMaterialFactors[matIdx].m_textureFactor;
		Materials[matIdx].m_textureAddFactor = m_addMaterialFactors[matIdx].m_textureFactor;
		Materials[matIdx].m_spTextureMulFactor = m_mulMaterialFactors[matIdx].m_spTextureFactor;
		Materials[matIdx].m_spTextureAddFactor = m_addMaterialFactors[matIdx].m_spTextureFactor;
		Materials[matIdx].m_toonTextureMulFactor = m_mulMaterialFactors[matIdx].m_toonTextureFactor;
		Materials[matIdx].m_toonTextureAddFactor = m_addMaterialFactors[matIdx].m_toonTextureFactor;
	}
}

void PMXModel::MorphMaterial(const MaterialMorphData & morphData, float weight)
{
	for (const auto& matMorph : morphData.m_materialMorphs)
	{
		if (matMorph.m_materialIndex != -1)
		{
			auto mi = matMorph.m_materialIndex;
			if (mi < 0 || mi >= Materials.size()) return;
			auto& mat = Materials[mi];
			switch (matMorph.m_opType)
			{
			case saba::PMXMorph::MaterialMorph::OpType::Mul:
				m_mulMaterialFactors[mi].Mul(
					MaterialFactor(matMorph),
					weight
				);
				break;
			case saba::PMXMorph::MaterialMorph::OpType::Add:
				m_addMaterialFactors[mi].Add(
					MaterialFactor(matMorph),
					weight
				);
				break;
			default:
				break;
			}
		}
		else
		{
			switch (matMorph.m_opType)
			{
			case saba::PMXMorph::MaterialMorph::OpType::Mul:
				for (size_t i = 0; i < Materials.size(); i++)
				{
					m_mulMaterialFactors[i].Mul(
						MaterialFactor(matMorph),
						weight
					);
				}
				break;
			case saba::PMXMorph::MaterialMorph::OpType::Add:
				for (size_t i = 0; i < Materials.size(); i++)
				{
					m_addMaterialFactors[i].Add(
						MaterialFactor(matMorph),
						weight
					);
				}
				break;
			default:
				break;
			}
		}
	}
}

void PMXModel::MorphBone(const BoneMorphData & morphData, float weight)
{
	for (auto& boneMorph : morphData.m_boneMorphs)
	{
		auto node = m_nodeMan.GetMMDNode(boneMorph.nodeId);
		glm::vec3 t = glm::mix(glm::vec3(0), boneMorph.m_position, weight);
		node->SetTranslate(node->GetTranslate() + t);
#if SABA_HAS_BASE_ROTATION
		glm::quat q = glm::slerp(node->GetRotate(), boneMorph.m_rotate, weight);
		node->SetRotate(q);
#endif
	}
}

void saba::PMXModel::createInflate()
{
	for (int i=0;i<Materials.size();i++)
	{
		auto &m=Materials[i];
		if (!m.isCloth) continue;
		DP(("INFLATE"));
		hasInflate = true;
		auto& submesh = Sd->subMeshes;

		assert(submesh[i].m_materialID == i);
		PhyMeshData& pd = m.ptcDat;
		int vi= submesh[i].m_beginIndex,ve=vi+ submesh[i].m_vertexCount;
		auto &vb = pmx.m_vertices ;
		float ptMass = 1.f;
		float invM = 1.f / ptMass;

		std::vector<uint32_t>		idxs;
		if (GetIndexElementSize() == 4) {
			uint32_t* indices = (uint32_t*)GetIndices();
			for (; vi < ve; vi++)  idxs.emplace_back(indices[vi]);
		}
		else if (GetIndexElementSize() == 2) {
			uint16_t* indices = (uint16_t*)GetIndices();
			for (; vi < ve; vi++)  idxs.emplace_back(indices[vi]);
		}
		else throw;

		// Create new vector for picked vertices without duplicates
		auto& pickVtx = pd.v4s;
		pd.restOffset = 0.6f;
		pd.friction = 1000.f;
		pd.idxs.reserve(idxs.size());
		pickVtx.reserve(idxs.size());  // Initial reservation, will be shrunk later
		pd.v4ids.reserve(idxs.size());

		// Use set to track unique indices
 
		std::unordered_map<int, int> indexMap;
		indexMap.reserve(pd.idxs.size());
		// Pick vertices using unique indices only
		for (const auto& idx : idxs) {
			int id = pd.v4ids.size();
			
			if (indexMap.find(idx) == indexMap.end()) {
				// New unique vertex found
				indexMap[idx] = id;  // Map original index to new position
				auto pos = vb[idx].m_position; pos.z = -pos.z; 
				pickVtx.emplace_back(vec4(pos,invM));
				pd.v4ids.emplace_back(idx);
				auto& v = pmx.m_vertices[idx]; auto node = m_nodeMan.GetMMDNode(v.m_boneIndices[0]);
				pd.v4rbs.emplace_back(node->rb0);
			}
			pd.idxs.emplace_back(indexMap[idx]);
			
		}

		// Shrink vectors to fit actual size
		pickVtx.shrink_to_fit(); 
		PhysXMan* pm = dynamic_cast<PhysXMan*>(g_mmdPhysics);
		m.ptcDat.ptcBuf =pm->createInflate(pd);

		for (auto i :pd.v4ids)
		{
			auto & v=pmx.m_vertices[i];
			 
			if (v.m_weightType != PMXVertexWeight::BDEF1 ) {
				
				auto node = m_nodeMan.GetMMDNode(v.m_boneIndices[1]);				//auto & bone = pmx.m_bones[v.m_boneIndices[1]];
				if (!node->rb0) 
					node = m_nodeMan.GetMMDNode(v.m_boneIndices[0]);
				if (!node->rb0) continue;
				node->rb0->pdbPmd = &m.ptcDat;
				auto pos =  Sd->positions[i]; 
				glm::mat4 m = node->rb0->initTransform;
				auto invm = glm::inverse(m);
				pos = invm * glm::vec4(pos,1) ; 
				pos.z = -pos.z;
				ibbArr.emplace_back(node->rb0, indexMap[i],i, pos );
				DPWCS((L"Bind node %d,%d, %s", indexMap[i], i, node->GetNameU().c_str()));
			}
		}
	}
}

void PMXModel::rebuildTmpIK(MMDNode * tgtNode, MMDNode * rootNode, MMDNode * rootNodeBack)
{
	if (!tmpIKNode || !tgtNode) return;

	PMXNode* pn = (PMXNode*)tmpIKNode;
	MMDIkSolver* solver = pn->GetIKSolver();
	solver->SetTargetNode(tgtNode);
	solver->resetChain();
	MMDNode* linkNode = tgtNode;
	bool found = false;
	while (linkNode = linkNode->GetParent()) {
		if (linkNode == rootNode)
		{
			found = true; break;
		}
	}
	if (!found)
	{
		rootNode = rootNodeBack;
		if (!rootNode) {
			linkNode = tgtNode;
			while (linkNode = linkNode->GetParent())
			{
				if (linkNode->flRotate && !linkNode->flFixedAxis) {
					rootNode = linkNode;
					break;
				}
			}
		}
	}

	linkNode = tgtNode;
	while (linkNode = linkNode->GetParent())
	{
		if (linkNode->flRotate && !linkNode->flFixedAxis) {
			solver->AddIKChain(linkNode);
			linkNode->EnableIK(true);
		}
		if (linkNode == rootNode)
			break;
	}

	solver->SetIterateCount(20);
	solver->SetLimitAngle(3.1415926f / 36);
}

void PMXModel::finishTmpIK()
{
	PMXNode* pn = (PMXNode*)tmpIKNode;
	MMDIkSolver* solver = pn->GetIKSolver();
	MMDNode* tgtNode = solver->GetTargetNode();
	auto& chain = solver->getIKChain();
	for (auto& nd : chain) {
		nd.m_node->SetAnimationRotate(nd.m_node->GetIKRotate() * nd.m_node->AnimateRotate());
		nd.m_node->EnableIK(false);
	}
	solver->resetChain();
	//solver->SetTargetNode(nullptr);
}

//float sumChildWeight(MMDNode* node) {
//	auto child = node->GetChild();
//	//if (!child || !child->rb0) return 0;

//	float sum = 0.f;
//	while (child) {
//		if (child->rb0) {
//			child->rb0->sumWeight = child->rb0->getMass() + sumChildWeight(child);
//			sum += child->rb0->sumWeight;
//		}
//		child = child->GetNext();
//	}
//	DPWCS((L"Ms %10s	 %8.f ", node->GetNameU().c_str(),  sum));
//	return sum;
//}

//void PMXModel::calcSubRbWeight()
//{
//	if (rootRbBoneIdx == -1) return;
//	auto nd = m_nodeMan.GetMMDNode(rootRbBoneIdx);
//	if (nd->rb0) nd->rb0->sumWeight=nd->rb0->getMass() + sumChildWeight(nd);
//}



void PMXModel::addData()
{
#if 0
	if (bAddWingBones)
	{

		int lsIdx = pmx.findBoneIdx(ualib::WcstoUtf8(L"左肩P"));
		int rsIdx = pmx.findBoneIdx(ualib::WcstoUtf8(L"右肩P"));
		PMXBone bn = pmx.m_bones[lsIdx];
		float baseh = bn.m_position.y + 2;
		bn.m_name = "WingL";
		bn.m_position = glm::vec3(3.f, baseh, 3.f);
		bn.m_parentBoneIndex = lsIdx;
		pmx.m_bones.push_back(bn);
		bn.m_name = "WingL1";
		bn.m_position = glm::vec3(7.f, baseh + 1, 6.f);
		bn.m_parentBoneIndex = pmx.m_bones.size() - 1;
		pmx.m_bones.push_back(bn);
		bn.m_name = "WingR";
		bn.m_position = glm::vec3(-3.f, baseh, 3.f);
		bn.m_parentBoneIndex = rsIdx;
		pmx.m_bones.push_back(bn);
		bn.m_name = "WingR1";
		bn.m_position = glm::vec3(-7.f, baseh + 1, 6.f);
		bn.m_parentBoneIndex = pmx.m_bones.size() - 1;
		pmx.m_bones.push_back(bn);

	}
#endif
}

PMXNode::PMXNode()
	: m_deformDepth(-1)
	, m_appendNode(nullptr)
	, m_isAppendRotate(false)
	, m_isAppendTranslate(false)
	, m_isAppendLocal(false)
	, m_appendWeight(0)
	, m_ikSolver(nullptr)
{
}

void PMXNode::UpdateAppendTransform()
{
	if (m_appendNode == nullptr)
	{
		return;
	}

	if (m_isAppendRotate)
	{
		float wmadd = 1.f;
		glm::quat appendRotate;
		if (m_isAppendLocal)
		{
			appendRotate = m_appendNode->AnimateRotate();
		}
		else
		{
			if (m_appendNode->GetAppendNode() != nullptr)  //parent append node
			{
				appendRotate = m_appendNode->GetAppendRotate();
			}
			else
			{

				if (m_appendNode->rb0 && model->afterPhysics /*&& m_appendNode->GetParent()->rb0*/) {
					appendRotate = //(quat(glm::inverse(m_appendNode->GetParent()->rb0->GetTransform() * m_appendNode->GetParent()->rb0->getOfsMatInv()) * m_appendNode->rb0->GetTransform())); 
						m_appendNode->m_local;
					wmadd = 0.5f;
				}
				else
					appendRotate = m_appendNode->AnimateRotate();
			}
		}

		if (m_appendNode->m_enableIK)
		{
			appendRotate = m_appendNode->GetIKRotate() * appendRotate;
		}

		glm::quat appendQ = glm::slerp(
			glm::quat(1, 0, 0, 0),
			appendRotate,
			GetAppendWeight() * wmadd
		);
		m_appendRotate = appendQ;
	}

	if (m_isAppendTranslate)
	{
		glm::vec3 appendTranslate(0.0f);
		if (m_isAppendLocal)
		{
			appendTranslate = m_appendNode->GetTranslate() - m_appendNode->GetInitialTranslate();
		}
		else
		{
			if (m_appendNode->GetAppendNode() != nullptr)
			{
				appendTranslate = m_appendNode->GetAppendTranslate();
			}
			else
			{
				appendTranslate = m_appendNode->GetTranslate() - m_appendNode->GetInitialTranslate();
			}
		}

		m_appendTranslate = appendTranslate * GetAppendWeight();
	}

	UpdateLocalTransform();
}

void PMXNode::OnUpdateLocalTransform()
{
	glm::vec3 t = AnimateTranslate();
	if (m_isAppendTranslate)
	{
		t += m_appendTranslate;
	}
	//if (m_parent == nullptr) t += m_mmdModel->nodeRootOfs;

	glm::quat r = AnimateRotate();
	if (exd.enableAnim)
	{
		float dtimeRatio = std::clamp((exd.lastTime - exd.enableTime) / (gd.frameTime * 10), 0.f, 1.0f);
		//DbgPrint(L"ND %s %f", GetNameU().c_str(), dtimeRatio);

		glm::quat finalRtt;
		if (m_enableIK)
			finalRtt = glm::mix(glm::quat(1, 0, 0, 0), GetIKRotate(), dtimeRatio);
		else
			finalRtt = glm::mix(GetIKRotate(), glm::quat(1, 0, 0, 0), dtimeRatio);
		r = ikIgnoreAnim ? finalRtt : finalRtt * r;
	}
	else
	{
		if (m_enableIK)
		{
			r = GetIKRotate() * r;
		}
	}
	if (m_isAppendRotate)
	{
		r = r * m_appendRotate;
	}

	if (addRotation) {
		r = r * (*addRotation);
	}
	localRtt = r;
	//glm::vec3 s = GetScale();
#if USE_CPU_ACC
	auto mt = glm::translate(glm::mat4(1), t);
	auto mr = glm::mat4_cast(r);
	MAT_MUL_ACC(m_local, mt, mr);
#if SABA_HAS_SCALE
	if (hasScale) {
		auto ms = glm::scale(glm::mat4(1), GetScale());
		MAT_MUL_ACC(m_local, m_local, ms);
	}
#endif
#else
	m_local = glm::translate(glm::mat4(1), t)
		* glm::mat4_cast(r)
#if SABA_HAS_SCALE
		* glm::scale(glm::mat4(1), GetScale())
#endif
		;

#endif
}

void PMXNode::OnBeginUpdateTransform()
{
	m_appendTranslate = glm::vec3(0);
	m_appendRotate = glm::quat(1, 0, 0, 0);
}

void PMXNode::OnEndUpdateTransfrom()
{
}



void PMXModel::addChildModel(std::shared_ptr<MMDModel> model, MMDNode * node)
{

}
void PMXModel::clearVtxPickNodes() {
	for (auto& d : pickJTs) {
		m_physicsMan.GetMMDPhysics()->RemoveJoint(d.jt);
		//m_physicsMan.RemoveJoint(touchJT);

		m_physicsMan.GetMMDPhysics()->RemoveRigidBody(d.rb);
		m_physicsMan.RemoveRigidBody(d.rb);
	}
	lastTouchRB = nullptr;
	pickJTs.clear();
}
void PMXModel::addBodyVel(const glm::vec3 & f, bool dynOnly)
{
	auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		if ((!dynOnly || rb->dynRbType == 1) && rb->GetActivation())
			rb->addLinearVel(f);

	}
}
void PMXModel::setBodyVel(const glm::vec3 & f, bool dynOnly)
{
	auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		if ((!dynOnly || rb->dynRbType == 1) && rb->GetActivation())
			rb->setLinearVel(f);
	}
}
void PMXModel::scaleBodyVel(float sc, int what)
{
	auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		if (rb->GetActivation()) {
			rb->scaleVel(sc, what);
		}
	}
}
void PMXModel::scaleBodyVel(const glm::vec3& sc, int what)
{
	auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		if (rb->GetActivation()) {
			rb->scaleVel3(sc, what);
		}
	}
}
 
void PMXModel::scaleDynBodyVel(float sc, int what)
{
	auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		if (rb->dynRbType && rb->GetActivation()) {
			rb->scaleVel(sc, what);
		}
	}
}

void PMXModel::scaleDynBodyVel(const glm::vec3& sc, int what)
{
	auto rigidbodys = GetPhysicsManager()->GetRigidBodys();
	for (auto& rb : (*rigidbodys))
	{
		if (rb->dynRbType && rb->GetActivation()) {
			rb->scaleVel3(sc, what);
		}
	}
}

MMDRigidBody* PMXModel::getRbByVtxIdOrNode(int vid, glm::vec3 * hitPos, MMDNode * nd, MMDNode * ndTouchRb,
	bool lockRtt, uint32_t flag)
{
	MMDRigidBody* pickRb{};
	if (pmx.m_rigidbodies.size() == 0) return nullptr;
	if (vid < 0)
	{
		if (vid == PICK_NODE_CONTINUE)
		{
			assert(lastTouchRB);
			touchRB = lastTouchRB;
			MMDJoint* jt{};
			for (auto& it : pickJTs) { if (it.rb == touchRB) jt = it.jt; }
			assert(jt);
			pickRb = jt->getAnotherRb(touchRB);
			std::erase_if(pickJTs, [=](const VtxPickData& x) { return x.rb == touchRB; });
		}
		else if (touchRB && touchJT)
			if (vid == PICK_NODE_DROP) {
				//	
				m_physicsMan.GetMMDPhysics()->RemoveJoint(touchJT);
				//m_physicsMan.RemoveJoint(touchJT);			
				touchJT = nullptr;

				m_physicsMan.GetMMDPhysics()->RemoveRigidBody(touchRB);
				m_physicsMan.RemoveRigidBody(touchRB);
				touchRB = nullptr;
				return nullptr;
			}

			else  if (vid == PICK_NODE_KEEP) {

				pickJTs.push_back({ touchRB, touchJT }); lastTouchRB = touchRB; touchRB = nullptr;
				return nullptr;
			}
			else throw;
	}
	else {
		//assert(touchRB == nullptr);
	}
	glm::vec3 vtxpos{ 0, 0, 0 };
	if (!touchRB && vid >= 0)
	{
		auto& bif = Sd->vertexBoneInfos[vid];
		auto bi = bif.m_boneIndex[0];
		MMDPhysicsManager* physicsMan = GetPhysicsManager();
		auto rigidbodys = physicsMan->GetRigidBodys();

		int i = 0;
		if (nd && nd->rb0)
		{
			pickRb = nd->rb0;
		}
		else
			for (auto& rb : (*rigidbodys))
			{
				auto node = rb->node;
				if (!node) continue;
				if (bi == node->GetIndex()) {
					nd = node;

					// (rb!=node->rb0)
					pickRb = node->rb0;
					break;
				}
				i++;
			}
		if (!pickRb && !nd) {
			nd = m_nodeMan.GetNode(bi);
			while (!pickRb && nd && nd->GetParent()) {
				nd = nd->GetParent();
				if (nd && nd->rb0)
				{
					pickRb = nd->rb0;
				}
			}
		}
		if (flag&0x10000)
		{
			auto& rb = pickRb; while (!rb->dynRbType && rb->parentRb)
				rb = rb->parentRb;
		}
		DP(("pick VID %d", vid));
		if (hitPos) {
			vtxpos = *hitPos;
		}
		//else if (vid<GetVertexCount())	vtxpos=GetUpdatePositions()[vid]; GPU has no pos buf

		if (pickRb && pickRb->GetActivation()) {
			touchRB = //new MMDRigidBody(); //
				m_physicsMan.newRigidBody(false);
			PMXRigidbody rbd;
			rbd.m_name = "touch";
			rbd.m_boneIndex = ndTouchRb ? ndTouchRb->GetIndex() : -1;
			rbd.m_translate = vtxpos;//;
			rbd.m_rotate = { 0,0,0 };
			//if ( nd)			rbd.m_translate = nd->rb0->getPosition();
			rbd.m_shape = PMXRigidbody::Shape::Box;
			rbd.m_shapeSize = { 5.0,5.0,5.0 };
			rbd.m_group = 16;
			rbd.m_mass = 0;
			rbd.m_collideMask32 = 0;
			rbd.m_op = PMXRigidbody::Operation::Static;
			rbd.pOwner = nullptr;//no col

			//if (!touchRB->CreatePMX(rbd, this, 0))
			//{
			//	SABA_ERROR("Create Rigid Body Fail.\n");
			//	return nullptr;
			//}
			//m_physicsMan.GetMMDPhysics()->AddRigidBody(touchRB);
			if (!touchRB->CreatePMX(rbd, this, ndTouchRb))
			{
				SABA_ERROR("Create Rigid Body Fail.\n");
				return nullptr;
			}
			touchRB->setCollideFilterMask(1,0);
			m_physicsMan.GetMMDPhysics()->AddRigidBody(touchRB);

			pickRbPos = touchRB->getPosition();
			pickRbOfs = glm::inverse(pickRb->GetTransform()) * vec4(pickRbPos, 1);
			DP(("pick Ofs %f,%f,%f", pickRbOfs.x, pickRbOfs.y, pickRbOfs.z));


			DPWCS((L"PICKRB %d node %s", i, pickRb->node->GetNameU().c_str()));
			//touchRB->setGlmMat(pickRb->GetTransform());
#if 0
			MMDNode* cd = pickRb->mmdNode->GetChild();
			while (cd && cd->rb0) {
				pickRb = cd->rb0;
				cd = pickRb->mmdNode->GetChild();
				DP(("PICKNext %S", pickRb->mmdNode->GetNameU().c_str()));
			}
#endif

			PMXJoint pmxJoint{};
			pmxJoint.m_name = "touchJ";
			pmxJoint.m_type = ndTouchRb ? PMXJoint::JointType::SpringDOF6 : PMXJoint::JointType::P2P;
			float ANG = 1;//(ndTouchRb?ndTouchRb->jointAddR:0.f)//3
			float TRL = 1;// (ndTouchRb?ndTouchRb->jointAddT:0.f)
			float ANS = 1;//1
			float TRS = 1;//100
			pmxJoint.translate = SABA_USE_PHYSX ? touchRB->getPosition() :
				glm::vec3{ 0,0,0 };
			pmxJoint.rotate = //SABA_USE_PHYSX ? glm::eulerAngles(touchRB->getRotation()) :
				glm::vec3{ 0,0,0 };
			pmxJoint.limitMinT = { -TRL,-TRL,-TRL };
			pmxJoint.limitMaxT = { TRL,TRL,TRL };
			pmxJoint.limitMinR = { -ANG,-ANG,-ANG };
			pmxJoint.limitMaxR = { ANG,ANG,ANG };

			pmxJoint.springT = { TRS,TRS,TRS };
			pmxJoint.springR = { ANS,ANS,ANS };
			pmxJoint.dampingT = { 1,1,1 };
			pmxJoint.dampingR = { 1,1,1 };
			//touchRB->setContact(false);
			//if (!touchJT)
			touchJT = GetMMDPhysics()->newJoint();
			if (flag & 0x10) {
				pmxJoint.setLocalPos = true;
				pmxJoint.translate = glm::vec3{ 0,0,0 };
			}
			bool ret = touchJT->CreateJoint2pt
				//CreateJointPmx
				(
					pmxJoint,
					touchRB,
					pickRb, lockRtt?2:0, flag != 0?2:0);
			if (!ret)
			{
				assert(0);
				return nullptr;
			}
			m_physicsMan.GetMMDPhysics()->AddJoint(touchJT);
			touchRB->touchRBJoint = touchJT;

		}
	}
	return pickRb;
}

MMDJoint* PMXModel::connectRb(MMDRigidBody * rbA, MMDRigidBody * rbB, bool lockPos, glm::vec3 ofs, bool lockRtt, glm::vec3 rtt)
{
	//DPWCS((L"ConnectRB %s - %s", rbA->node->GetNameU().c_str(), rbB->node->GetNameU().c_str()));
	PMXJoint pmxJoint{};
	pmxJoint.m_name = "touchJ";
	pmxJoint.m_type = //lockPos&&lockRtt? PMXJoint::JointType::Fixed:
		PMXJoint::JointType::P2P;
	float ANG = 0;//(ndTouchRb?ndTouchRb->jointAddR:0.f)//3
	float TRL = 0;// (ndTouchRb?ndTouchRb->jointAddT:0.f)
	float ANS = 0;//1
	float TRS = 10000;//100;
	pmxJoint.translate = ofs;
	pmxJoint.rotate = rtt;//
	pmxJoint.limitMinT = { -TRL,-TRL,-TRL };
	pmxJoint.limitMaxT = { TRL,TRL,TRL };
	pmxJoint.limitMinR = { -ANG,-ANG,-ANG };
	pmxJoint.limitMaxR = { ANG,ANG,ANG };

	pmxJoint.springT = { TRS,TRS,TRS };
	pmxJoint.springR = { ANS,ANS,ANS };
	pmxJoint.dampingT = glm::vec3(1000);
	pmxJoint.dampingR = glm::vec3(10);
	//touchRB->setContact(false);
	//if (!touchJT)
	pmxJoint.setLocalPos = true;
	MMDJoint* touchJT = GetMMDPhysics()->newJoint();
	//rbB->SetCoMTransform(rbA->GetTransform()*rbA->getOfsMat(), 0);
	bool ret = touchJT->CreateJoint2pt(
		pmxJoint,
		rbA,
		rbB, lockRtt?2:0, lockPos ? 2 : 0);
	if (!ret)
	{
		assert(0);
		return nullptr;
	}
	m_physicsMan.GetMMDPhysics()->AddJoint(touchJT);
	return touchJT;
}

MMDJoint* PMXModel::connectRb(MMDRigidBody * rbA, MMDRigidBody * rbB, bool lockPos, bool lockRtt,  saba::PMXJoint  jt, bool movB)
{
	if (movB) {
		jt.useInitRbPos = true;
		glm::vec4 oipos = rbB->initTransform[3];
		rbB->initTransform = glm::translate(mat4(1), jt.translate+ (jt.setLocalPos? rbA->getPosition():vec3(0)));
		if (rbB->m_mmdModel)
			((PMXModel*)rbB->m_mmdModel)->moveAllOffset(rbB->initTransform[3] - oipos);
		else rbB->moveRbOfs(rbB->initTransform[3] - oipos);

		
	}
	//jt.m_type = lockPos && lockRtt ? PMXJoint::JointType::Fixed : PMXJoint::JointType::P2P;
	MMDJoint* touchJT = GetMMDPhysics()->newJoint();
	//rbB->SetCoMTransform(rbA->GetTransform()*rbA->getOfsMat(), 0);
	bool ret = touchJT->CreateJoint2pt(
		jt,
		rbA,
		rbB, lockRtt?2:0, lockPos ? 2 : 0);
	if (!ret)
	{
		assert(0);
		return nullptr;
	}
	m_physicsMan.GetMMDPhysics()->AddJoint(touchJT);

	return touchJT;
}
MMDJoint* PMXModel::connectRbM(MMDRigidBody* rbA, MMDRigidBody* rbB, int lockPos, int lockRtt, saba::PMXJoint  jt, bool movB)
{
	if (movB) {
		jt.useInitRbPos = true;
		glm::vec4 oipos = rbB->initTransform[3];
		rbB->initTransform = glm::translate(mat4(1), jt.translate);
		((PMXModel*)rbB->m_mmdModel)->moveAllOffset(rbB->initTransform[3] - oipos);
	}
	//jt.m_type = lockPos && lockRtt ? PMXJoint::JointType::Fixed : PMXJoint::JointType::P2P;
	MMDJoint* touchJT = GetMMDPhysics()->newJoint();
	//rbB->SetCoMTransform(rbA->GetTransform()*rbA->getOfsMat(), 0);
	bool ret = touchJT->CreateJoint2pt(
		jt,
		rbA,
		rbB, lockRtt , lockPos  );
	if (!ret)
	{
		assert(0);
		return nullptr;
	}
	m_physicsMan.GetMMDPhysics()->AddJoint(touchJT);

	return touchJT;
}
MMDJoint* PMXModel::connectToRootNodeRatio(MMDNode * ndRoot, MMDNode * node, MMDRigidBody * rbB, float ratio, bool lockPos, glm::vec3 ofs, bool lockRtt)
{
	MMDRigidBody* rbRoot = ndRoot->rb0, * rbNode = node->rb0;
	MMDJoint* ret{};
	MMDNode* np = node, * nd = node;
	struct  NdLen {
		MMDNode* nd;
		float len;
	};
	std::vector<NdLen> lens; float len = 0.f;
	lens.push_back({ nd, 0.f });
	while (np != ndRoot && np != nullptr) {
		np = np->GetParent();
		if (np && np->rb0) {
			float dis = glm::length(vec3(nd->mGlobalInit[3]) - vec3(np->mGlobalInit[3]));
			lens.insert(lens.begin(), NdLen{ np, dis });
			len += dis;
			nd = np;
		}
	}
	if (!np) return nullptr;
	float lastDis, rootDis = len * ratio;
	int i = 0; lastDis = rootDis; rootDis -= lens[i].len;
	while (rootDis > 0) { lastDis = rootDis; rootDis -= lens[i].len; i++; }
	MMDRigidBody* rbA = lens[i].nd->rb0;
	float rat = lastDis / lens[i].len;
	glm::vec3 pos = vec3(lens[i].nd->mGlobalInit[3]);
	if (i < lens.size() - 1)
		pos = glm::mix(vec3(lens[i].nd->mGlobalInit[3]), vec3(lens[i + 1].nd->mGlobalInit[3]), rat);


	ret = connectRbMoveB(rbA, pos, rbB, lockPos, ofs, lockRtt);
	return ret;
}

MMDJoint* PMXModel::connectRbMoveB(saba::MMDRigidBody * rbA, glm::vec3 jtpos, saba::MMDRigidBody * rbB, bool lockPos, const glm::vec3 & ofs, bool lockRtt)
{
	saba::PMXJoint j{};
	j.translate = jtpos;

	j.useInitRbPos = true;
	auto oipos = rbB->initTransform;
	rbB->initTransform = glm::translate(mat4(1), j.translate + ofs);
	((PMXModel*)rbB->m_mmdModel)->moveAllOffset(rbB->initTransform[3] - oipos[3]);
	return connectRb(rbA, rbB, lockPos, lockRtt, j);
}

int PMXModel::getMeshIdByVtxId(uint32_t vid)
{
	int smc = GetSubMeshCount();

	for (int i = 0; i < smc; i++)
	{
		auto sm = GetSubMeshes()[i];
		int endIndex = sm.m_beginIndex + sm.m_vertexCount;

		if (GetIndexElementSize() == 2) {
			uint16_t* ids = (uint16_t*)Sd->indices.data();
			for (int j = sm.m_beginIndex; j < endIndex; j++)
				if (vid == ids[j]) {
					return  i;
				}
		}
		else
		{
			assert(GetIndexElementSize() == 4);
			uint32_t* ids = (uint32_t*)Sd->indices.data();
			for (int j = sm.m_beginIndex; j < endIndex; j++)
				if (vid == ids[j]) {
					return  i;
				}
		}
	}
	return 0;
}

MMDNode* saba::PMXModel::addExtNode(PMXBone bone, bool hasRb0)
{
	if (extNodes.size() >= MAX_EXT_NODE) {
		assert(0);
		return nullptr;
	}
	pmx.m_bones.push_back(bone);
	auto* node = m_nodeMan.AddNode();
	node->model = this;
	node->SetName(bone.m_name, bone.m_nameU);
			 
 
	createNode(pmx.m_bones.size()-1);
	m_sortedNodes.push_back(node);
	m_transforms.push_back(mat4(1));
	extNodes.push_back(node);

	if (!hasRb0) return node;
	bool isDynamic = 0;
	PMXRigidbody prb{};
	prb.m_name = bone.m_name;
	prb.m_op = isDynamic?PMXRigidbody::Operation::Dynamic:PMXRigidbody::Operation::Static;
 
	prb.m_boneIndex = pmx.m_bones.size() - 1;;
	prb.m_translate = bone.m_position;
	prb.m_rotate = vec3(0);
	prb.m_shapeSize = vec3 (1)   ;

	prb.m_shape = PMXRigidbody::Shape::Sphere;
 
	prb.m_translateDimmer = 0.5;
	prb.m_rotateDimmer = 0.5;
	prb.m_friction = 0.5;
	prb.m_repulsion = 0;		 
	prb.m_mass = 100;
	prb.m_mass = 100;
	prb.m_group = 15;
	prb.m_collideMask32 = 0;// 0x18000;
	prb.noDynRb = true;
	pmx.m_rigidbodies.push_back(prb); 
	int retFlag = 0;
	int rbid=pmx.m_rigidbodies.size() - 1;
	newRb(rbid);
	rbids.push_back(rbid);
	createRb(rbid, retFlag);
	auto rb = m_physicsMan.m_rigidBodys[rbids[rbid]].get();
	m_physicsMan.sortedRBs.emplace_back(rb);
	GetMMDPhysics()->AddRigidBody(rb);
	if (isDynamic) {
		rb->SetActivation(0); rb->SetActivation(true); //rb->addLinearVel(vec3(0, -10, 0));
	}
	return node;
}

PMXModel::MaterialFactor::MaterialFactor(const saba::PMXMorph::MaterialMorph & pmxMat)
{
	m_diffuse.r = pmxMat.m_diffuse.r;
	m_diffuse.g = pmxMat.m_diffuse.g;
	m_diffuse.b = pmxMat.m_diffuse.b;
	m_alpha = pmxMat.m_diffuse.a;
	m_specular = pmxMat.m_specular;
	m_specularPower = pmxMat.m_specularPower;
	m_ambient = pmxMat.m_ambient;
	m_edgeColor = pmxMat.m_edgeColor;
	m_edgeSize = pmxMat.m_edgeSize;
	m_textureFactor = pmxMat.m_textureFactor;
	m_spTextureFactor = pmxMat.m_sphereTextureFactor;
	m_toonTextureFactor = pmxMat.m_toonTextureFactor;
}

void PMXModel::MaterialFactor::Mul(const MaterialFactor & val, float weight)
{
	m_diffuse = glm::mix(m_diffuse, m_diffuse * val.m_diffuse, weight);
	m_alpha = glm::mix(m_alpha, m_alpha * val.m_alpha, weight);
	m_specular = glm::mix(m_specular, m_specular * val.m_specular, weight);
	m_specularPower = glm::mix(m_specularPower, m_specularPower * val.m_specularPower, weight);
	m_ambient = glm::mix(m_ambient, m_ambient * val.m_ambient, weight);
	m_edgeColor = glm::mix(m_edgeColor, m_edgeColor * val.m_edgeColor, weight);
	m_edgeSize = glm::mix(m_edgeSize, m_edgeSize * val.m_edgeSize, weight);
	m_textureFactor = glm::mix(m_textureFactor, m_textureFactor * val.m_textureFactor, weight);
	m_spTextureFactor = glm::mix(m_spTextureFactor, m_spTextureFactor * val.m_spTextureFactor, weight);
	m_toonTextureFactor = glm::mix(m_toonTextureFactor, m_toonTextureFactor * val.m_toonTextureFactor, weight);
}

void PMXModel::MaterialFactor::Add(const MaterialFactor & val, float weight)
{
	m_diffuse += val.m_diffuse * weight;
	m_alpha += val.m_alpha * weight;
	m_specular += val.m_specular * weight;
	m_specularPower += val.m_specularPower * weight;
	m_ambient += val.m_ambient * weight;
	m_edgeColor += val.m_edgeColor * weight;
	m_edgeSize += val.m_edgeSize * weight;
	m_textureFactor += val.m_textureFactor * weight;
	m_spTextureFactor += val.m_spTextureFactor * weight;
	m_toonTextureFactor += val.m_toonTextureFactor * weight;
}


