﻿#pragma once
#include <irrlicht.h>
#include <map>
#include "irrsaba.h"
#include <vector>
#include <variant>
#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include "UaJsonSetting.h"
#include "external/ImGuizmo/ImSequencer.h"

#define KEY_USE_ONLY_FRAME  1
#define MULTI_DIST_LISTENER 1
#define MMD_PHA_FPS 60
namespace saba {
    class MMDNode;
    class MMDModel;
}
namespace vks {
    class UIOverlay;
}
class MmdRigKeysSequencer;
namespace irr::scene 
{
    class IrrSaba;
    class SnArRoot;

#define KEYPOSE_CMD_TYPES(X) \
    X(None) \
    X(Custom) \
    X(Distance) \
    X(Fw) \
    X(TextFw) \
    X(ConnectRb) \
    X(AddPhyObj) \
    X(CubeGrid) \
    X(VoxGrid) \
    X(Model) \
    X(Motion) \
    X(CameraKey)




#define CUSTOM_CMD_TYPES(X) \
    X(None) \
    X(Kinematic) \
    X(PhyAnim) \
    X(PhyAnimMul) \
    X(PhyToAnim) \
    X(TimeMul) \
    X(PhyTimeMul) \
    X(CtrForce) \
    X(KeyEvent) \
    X(GoToFrame)
    
    // Define the enum
    enum   KeyPoseCmdType : uint32_t {
#define MACRO_X(name) ect##name,
        KEYPOSE_CMD_TYPES(MACRO_X)
#undef MACRO_X
    };
    enum  ECustomCmd {
#define MACRO_X(name) ecc##name,
        CUSTOM_CMD_TYPES(MACRO_X)
#undef MACRO_X
        Count
    };

    struct MMDNodeInfo {
        int sbIdx = 0;
        std::string nodeName;
        int locate = 0;  //  relative to: 0 = world, 1 = node , 2= rb , 3 = rbNode        
        saba::MMDNode* node{};
    };
struct KeyPoseCmd {
    KeyPoseCmdType type = KeyPoseCmdType::ectNone;
    struct KpcNone {};

    struct KpcCustom {
        ECustomCmd cmdType = ECustomCmd::eccNone;
        std::string customCmdName, customCmdStringValue;
        glm::ivec4 iv = glm::ivec4(0);
        glm::vec4 fv = glm::vec4(0);
    };

    struct KpcDistance {
		uint32_t TypeFlag = 1; //0=off, 1 = on, 
		int disType = 0; // 0 = no scale, 1 = base on node scale        
		float distance = 10.f;		
		int contition = 0; // 0 = below, 1 = above
		std::vector< KeyPoseCmd> onCmds; //virtual key, to hold cmds 
        int countMode = 0;  // 0 = one time, 1 = count on ndB
    };

    struct KpcFw // gen fw particle
    {
        uint32_t TypeFlag=1; //1 = show one fw, 2 = turn on node fw, 4 = turn off node fw
        int fwType=1;
        std::string name;
        int rbPos = 0;
        float vMul=1,vAdd=0;
        glm::vec3 fpm = { 0,0,0 };
        glm::vec4 color={1,1,1,1};
    };

    struct KpcConnectRb // connect rigid body
    {
		uint32_t TypeFlag = 1; //1 = connect rb, 2 = connect until next key, 4 = disconnect all , 8 = set base transform
        int  sbA=0;
        std::string nodeNameA;
        int lockT = 0, lockR = 0, localPos = 1;
        glm::vec3 translate={0,0,0}, rotate={0,0,0},t2B={0,0,0},r2B={0,0,0};
        glm::vec3 limMinT={-100,-100,-100}, limMaxT={100,100,100}, limMinR= {-100,-100,-100}, limMaxR={100,100,100};
        float springT=1000.f, springR=1000.f;
		float dampingT=100.f, dampingR=100.f;
        float breakForceMul = 0.f;
    };
    struct KpcAddPhyObj // add physics object 
    {
        uint32_t TypeFlag = 1;  //1 = add one PO, 2 = turn on PO gen, 4 = turn off PO gen  , 16 = modify last rigNode added PO
        uint32_t modFlag = 0;  //1=vel, 2= angVel, 4=pos, 8=rtt, 0x10 = size scale, 0x20 = densityMul,
        float genIntv = 0.1f;    //gen interval (in sec)
        int objNum = 1;      // obj count in one gen
        int maxGen = 1000;   // max count before turn off       
        int poType=1;             //0=cube 1=sphere,100=saba
        float mass = 1.f;
        bool autoRemove = true; float removeTimer = 10.f; 
        float disableHitTime=0.f;
        glm::vec3 size{ 1,1,1 }, src{0,0,0}, tgt{ 0,0,0 }, rtt{ 0,0,0 }, vel{ 0,0,0 };
		float sizeScale = 1.f;
        PhyObjParam opm;
        float emitR = 0.f;
        int srcPosMode = 0;     // 0 = src, 1 = calc by tgt pos and flyTime and speed, 2 = camera pos        
        int srcVelMode = 0;     // 0 = vel, 1 = calc by src to tgt in flyTime, 2 = camera rotation to direction
        float flyTime = 1.f; 
        uint32_t atkFlag = 0;
        MMDNodeInfo srcNd;    
        MMDNodeInfo tgtNd;
    };
    struct KpcTextFw:public KpcAddPhyObj // text fw effect
    {
        std::string name;
        std::wstring text;
        glm::vec4 color1 = { 1,1,1,1 }, color2 = { 1,1,1,1 };
        glm::vec3 velOfs = { 0.f, 0.f, 0.f };
        bool hitCvt = false, hitGroundCvt = false, allInOne = 1;
        float hitVelChgLimit = 100.f;
        int fwSpecular = 0;
        float baseMass = 1.f;
        int connectMode = 1;
        ualib::FT2TextParam tp;
    };
    struct KpcCubeGrid // add physics object 
    {
        uint32_t TypeFlag = 1;  //1 = add    , 2 = turn on , 4 = turn off 

        glm::vec3 pos = glm::vec3(0, 0, 0);
        glm::vec3 rtt = glm::vec3(0);
        glm::vec3 vel = glm::vec3(0);
        glm::ivec3 grid = glm::ivec3(10, 10, 1);
        glm::vec3 brickSize = glm::vec3(1.f, 1, 1.f);
        glm::vec3 brickSpace = glm::vec3(0);
        int reset = 1;  //0=none 1=direct 2=morph
        float density = 1.f;
        float restitution = 0.5f;
        float friction = 0.5f;
        int centerOrigin = 0;
       
        int connect = 0;    // 1=base 2=each other
        std::vector<std::string> imgPaths;
		std::string voxPath; // voxel file
		
        glm::vec4 color = glm::vec4(1.f); 

        MMDNodeInfo srcNd;
        MMDNodeInfo tgtNd;
    };
    struct KpcVoxGrid // add physics object 
    {
        uint32_t TypeFlag = 1;   
        std::string voxPath; // voxel file
        glm::vec3 pos = glm::vec3(0, 0, 0);
        glm::vec3 rtt = glm::vec3(0);
        glm::vec3 vel = glm::vec3(0);
        glm::vec3 brickSize = glm::vec3(1.f, 1, 1.f);
        glm::vec3 brickSpace = glm::vec3(0);
        int reset = 1;  //0=none 1=direct 2=morph
        float density = 1.f;
        float restitution = 0.5f;
        float friction = 0.5f;
        int centerOrigin = 0;
        int connect = 0;    // 1=base 2=each other


        glm::vec4 color = glm::vec4(1.f);

        MMDNodeInfo srcNd;
        MMDNodeInfo tgtNd;
    };

    struct KpcModel {
        unsigned int TypeFlag = 1;
        std::string filePath;
        glm::vec3 pos = glm::vec3(0.0f);
        glm::vec3 rtt = glm::vec3(0.0f);
        float scale =  1.0f;
		float massMul = 1.0f;
		float frictionMul = 1.0f; 
		bool isCharacter = 0;
		bool allRbActive = 0;
		glm::vec3 initVel = glm::vec3(0.0f);
    };
    struct KpcMotion {
        std::string filePath;
        std::string fileType;
		float speedMul = 1.f;
        float startTime = 0.f;  // if < 0 = curTime
        int swapLR = 0;
        glm::ivec4 iv = glm::ivec4(0);
        glm::vec4 fv = glm::vec4(0);
    };
    struct KpcCameraKey {
        saba::VMDCameraAnimationKey vc;
		int anim = 0;
        int local = 0;
    };



    std::variant<KpcNone, KpcCustom, KpcDistance,KpcFw, KpcTextFw, KpcConnectRb, KpcAddPhyObj, KpcCubeGrid, KpcVoxGrid, KpcModel, KpcMotion, KpcCameraKey> cmdPm;

    // Convert enum to string (convert to lowercase)
    static const char* typeToString(KeyPoseCmdType t) {
        switch (t) {
#define MACRO_X(name) case KeyPoseCmdType::ect##name: return #name;
            KEYPOSE_CMD_TYPES(MACRO_X)
#undef MACRO_X
        default: return "unknown";
        }
    }

    // Convert string to enum (case insensitive)
    static KeyPoseCmdType stringToType(const std::string& str) {
#define MACRO_X(name) if (str == #name) return KeyPoseCmdType::ect##name;
        KEYPOSE_CMD_TYPES(MACRO_X)
#undef MACRO_X
            return KeyPoseCmdType::ectNone;
    }

    static const char* CustomCmdToString(ECustomCmd cmd) {
        switch (cmd) {
#define MACRO_X(name) case ECustomCmd::ecc##name: return "cc" #name;
            CUSTOM_CMD_TYPES(MACRO_X)
#undef MACRO_X
        default: return "unknown";
        }
    }
    static ECustomCmd StringToCustomCmd(const std::string& str) {
#define MACRO_X(name) if (str == "cc" #name) return ECustomCmd::ecc##name;
        CUSTOM_CMD_TYPES(MACRO_X)
#undef MACRO_X
            return ECustomCmd::eccNone; // default
    }

    void toJson(Json::Value& jv) const;
    
    void fromJson(const Json::Value& jv);
};

    struct KeyPoseData {

        bool ndTmir = false, ndBmir = false;        
        int sbB{ 1 };
        int baseMode = 0;  //0 = anim, 1 = pos on rbnode, rtt on yao2yao dir, 2 = pos on rbnode, rtt on last, rb , 3 = rb Node, 4 = rb
        int saved = 0;
        glm::vec3 pos = glm::vec3( 0,0,0 );
        glm::quat rtt = glm::quat( 1,0,0,0 );
        float fmul = 1.f;       
        float amul = 1.f; //rtt angle mul
		float fsc = 1.f; //scale
        int rttMode = 0; // 0=none, 1= use rtt rotation, 2 =  rotate front to pos        
        glm::vec3 front = { 0,0,-1 };
        int setRd = 0; float rdRatio = 1.f;
        //float time0 = 1, time1 = 1;                       
        // temp
       
        uu::Ebf bf0 = uu::Ebf::none, bf1 = uu::Ebf::none;   
        std::vector<KeyPoseCmd> cmds;
        glm::mat4 relMat() const { return glm::translate(glm::mat4(1.0f), pos) * glm::mat4_cast(rtt); }
        saba::MMDNode* getB() const;
        glm::vec3 getBPos() const;
        void setB(saba::MMDNode* nd) { ndB = nd; } // saba node
        int dndB = -1;  //PhyObj as ndB
 
        saba::MMDNode* ndB{};
 
    };
    
    struct RigKey {
        int frame = 0;         // Duration in frames to this key from the previous key
        int frameIdx = 0; // Total frames from start to this key
        KeyPoseData pose;    // Single pose per key
        float dragStartPos;
        bool disabled = false;
    };
    
    

    struct RigNode {
        std::vector<RigKey> keys;
		RigKey& curKey() { return keys[curKeyIdx]; }
        saba::MMDNode* node;  // The node this struct represents
        irr::scene::IrrSaba* sb{}, * oTgtSb{};
        int sbT = 0;
        int curKeyIdx = 0, editKeyIdx=0,lastPlayIdx = -1;
        KeyPoseData curPose{}; // last set pose, 
        int moveNodeAnim = 0;
        int disabled = 0;
        int fwOn = 0, tfwOn = 0, apoOn = 0;
 
        int lastPoUid[2] = {0};
		bool isCameraNode = false;
#if MULTI_DIST_LISTENER
        struct DistListener {
            KeyPoseCmd::KpcDistance distListener;
            bool distListenerOn = false;
            saba::MMDNode* curDistNdB{}; 
        };
		std::vector<DistListener> curDistListeners;
        int swapLRCC = 0;
#else
        KeyPoseCmd::KpcDistance curDistListener;  bool distListenerOn = false; saba::MMDNode* curDistNdB{}; int swapLRCC = 0;
#endif
        KeyPoseCmd::KpcFw curFw;
        KeyPoseCmd::KpcTextFw curTFW;
        KeyPoseCmd::KpcAddPhyObj curAPO;
        
        int curCubeGrid = -1, curTexId = 0;
        float lastTimeAPO = 0.f, lastTimeTFW = 0.f;
        int apoGenCount = 0,tfwGenCount=0;
        std::string alias;
        std::vector<saba::MMDJoint*> connectedJoints;
        irr::scene::IrrSaba* curModelSb{};

        float vmdStartTime = 0, vmdSpeedMul = 1;
        void breakAllJoints() {
            for (auto j : connectedJoints) j->destroy();            
        }
        int getKeyIdAtFrame(int frame) {
            int cf = 0;
            for (size_t i = 0; i < keys.size(); ++i) {
                cf += keys[i].frame;
                if (cf == frame) return i;
                else if (cf > frame) return i - 1;
            }
            return  keys.size() - 1;
        }
        PhyObjParam popmt;
    };

 

    class RigNodeMap {
    private:
        std::vector<RigNode> nodes;

        // Custom comparator for sorting
        static bool CompareNodes(const RigNode& a, const RigNode& b) {
            if (a.sbT != b.sbT)
                return a.sbT < b.sbT;
            return a.node->GetIndex() < b.node->GetIndex();
        }

    public:
        using iterator = std::vector<RigNode>::iterator;
        using const_iterator = std::vector<RigNode>::const_iterator;

        // Iterator support
        iterator begin() { return nodes.begin(); }
        iterator end() { return nodes.end(); }
        const_iterator begin() const { return nodes.begin(); }
        const_iterator end() const { return nodes.end(); }

        // Map-like operations
        std::pair<iterator, bool> emplace( RigNode& value) {
             
            auto it = find(value.node);
            if (it != end()) {
                return { it, false };
            }

            value.node = value.node;
            auto insertPos = std::lower_bound(nodes.begin(), nodes.end(), value, CompareNodes);
            auto newIt = nodes.insert(insertPos, value);
            return { newIt, true };
        }

        iterator find(saba::MMDNode* key) {
            return std::find_if(nodes.begin(), nodes.end(),
                [key](const RigNode& node) { return node.node == key; });
        }

        bool contains(saba::MMDNode* key) {
            return find(key) != end();
        }

        RigNode& operator[](saba::MMDNode* key) {
            auto it = find(key);
            if (it != end()) {
                return *it;
            }

            RigNode newNode;
            newNode.node = key;
            auto [newIt, _] = emplace( newNode);
            return *newIt;
        }

        iterator erase(iterator it) {
            return nodes.erase(it);
        }

        bool empty() const { return nodes.empty(); }
        size_t size() const { return nodes.size(); }

        void clear() { nodes.clear(); }

        // Additional helper methods
        static saba::MMDNode* GetNodeFromIt(const iterator& it) {
            return it->node;
        }

        static RigNode& GetRigNodeFromIt(const iterator& it) {
            return *it;
        }
    };;

    class MmdRigKeysSequencer;
    class MmdPhyAnimator {
    public:
        friend class MmdRigKeysSequencer;
        struct MmdPhyAnimatorParam
        {
            IrrMMD* mmd{};            
			std::vector<int> sbs;
            bool isEditor = false;     
            IrrSaba* setSb0{}, * setSb1{};
        };

        // Constructors and basic operations
        MmdPhyAnimator(MmdPhyAnimatorParam pm);        
        ~MmdPhyAnimator();
        void resetSabas();
        MmdPhyAnimatorParam Pm;
        std::string mpaName,curFilePath, lastSavedFile;
        void createUI();
         
        RigNodeMap nodeMap;// order by 1st rigNode.sbT ,2nd rigNode.node->GetIndex()
        RigNodeMap::iterator nodeIt = nodeMap.end();
        RigNode* curRigNode = nullptr;
        saba::MMDNode* curCameraRigNode = nullptr;
        // Helper functions for NEW_DATA_STRUCT
        void createNode(saba::MMDNode* node);

        int maxCumFrame = 1;
        void updateCumulativeTimes();
        struct KeyState {
            RigNodeMap nodeMap;
            saba::MMDNode* currentNode;
            float defaultTime;
        };

        void uiMenu();
        void uiSequencer();
        void uiOtherTabs();
        bool setUiSequencerSelected = false, setTabAddObjSelected = false;

        saba::MMDNode* addExtNode(saba::MMDNode* parentNode=nullptr, std::string idstr="");
        bool toggleCamera();
 
        const int framePerSecond = MMD_PHA_FPS;
        // Key management
        void newKeyIf(int mode = 1, int copyLast = 0, int frame=MMD_PHA_FPS);     //create key at curFrame if not exists     
 
        void toNextKey();
        void toPrevKey();
        void toKey(int id);
        void toClosestKeyTime(float currentTime);
        void toClosestKeyFrame(int currentFrame);
        size_t keyCount() const
        {
            if (!curRigNode) return 0;
            return curRigNode->keys.size();
        }

        RigKey* curKey()
        {
            if (!curRigNode || curRigNode->keys.empty()) {                
                return nullptr;
            }
            curMmdNode = curRigNode->node;
            return &curRigNode->keys[curRigNode->curKeyIdx];
        }
        RigKey* curEditKey()
        {
            if (!curRigNode || curRigNode->keys.empty()) {
                return nullptr;
            }
            curMmdNode = curRigNode->node;
            return &curRigNode->keys[curRigNode->editKeyIdx];
        }
        int currentKeyIndex() const { return curRigNode?curRigNode->curKeyIdx:-2; }
        int curEditKeyIndex() const { return curRigNode?curRigNode->editKeyIdx : -2; }
       

        // Node manipulation
        KeyPoseData& curNodePose() { return nodeIt->curPose; }
        KeyPoseData* keyPose()        {            if (!curKey()) return nullptr;            return &curKey()->pose;        }
        KeyPoseData* editKeyPose(){            if (!curEditKey()) return nullptr;            return &curEditKey()->pose;    }

        void setNode(saba::MMDNode* nd);
        void setBase(saba::MMDNode* baseNd);
        void removeNode(saba::MMDNode* nd);
        void switchNodes(int ofs);
        void switchToNode(saba::MMDNode* node);
         
        // Pose management
        void setPose(int mode = 0);
        void clearPoses();
        bool delCurFrame();
 
        // Playback control
        void update();
		bool isPlaying() const { return playState; }
        bool playStart(int mode, int fromWhat =0);
		bool playStartFromFrame(int frame,float speed=1.f);
        void playStop();
        void onPlayKeyChanged(RigNode& rigNode, std::vector<KeyPoseCmd> &cmds,  bool editChange=false);
        void genTFW(  irr::scene::RigNode& rigNode);
        PhyObjParam genPhyObj(RigNode& rigNode, int  idx, bool mirror,
            int frameCurKeyIdx=-1, float keyRatio=0.f, int mode=0);
        void setPPNdB(int ndbid, int uid);
        void updatePlayback(float time);

        // Node references
        saba::MMDNode* rigBaseNd{};
        saba::MMDNode* curMmdNode{};

        // Undo/Redo system
        bool saveState();
        void undo();
        void redo();
        bool canUndo() const { return !undoStack.empty(); }
        bool canRedo() const { return !redoStack.empty(); }

        std::function<void(vks::UIOverlay*)> OnUpdateUIOverlay;
        void onSbNodePicked(MMDNode* node);


        MmdRigKeysSequencer* rigSequencer = nullptr;
        bool seqSelectionHasCurKey();
        void seqClearSelection();


        
        // Enhanced seeking method
        int getCurFrame() { return curFrame; }
        void setCurFrame(int frame) {
            curFrame = frame; 
        }
        void seekToTime(float time) {
            playStop();
             
            setCurFrame(static_cast<int>(time / timePerFrame ));
        }

 
        // Save and load
        void saveToFile(const std::string& filepath);

        bool loadFromFile(const MpaLoadJsonParam& lf, bool clear=true);

        void extendKeysWithRotation(float angle, const glm::vec3& center = glm::vec3(0.0f));

        inline static KeyPoseData copyedPose;
        void copyPose() { 
            copyedPose = curRigNode->keys[curRigNode->editKeyIdx].pose; 
        }
        void pastePose() {
            newKeyIf(0, 0); setPose(); 
            curRigNode->keys[curRigNode->editKeyIdx].pose = copyedPose;            
        }
        std::vector<std::pair<saba::MMDNode*, RigKey>> copiedKeys;
        void copySelectedKeys();
        void pasteKeysToCurrentFrame(bool insertMode);
        void deleteSelectedKeys();
        bool toogleCamera()
        {
            if (playState) {
				
				return false;
            }
        }
        int playState = 0; // 0:stopped 1:playing 2:pause 10:repeat
        int seqSelectedIdx = 0;
        bool camPreview = false ;
        float playSpeed = 1.f;
        bool inputingKey = false;
    private:
        ualib::UaLibContext* Ctx;
        irr::scene::SnArRoot* arRoot;
        irr::video::IVideoDriver* Driver;
        int curRigKeyIdx = -1;
        float defaultKeyDurationTime = 1.0f;
       
        float playTime = 0;
        int lastPlayFrame = -1;
        float curTime = 0, timePerFrame = 1.f / framePerSecond;
        int curFrame = 0;

        // Cache for node keys to avoid rebuilding during playback
        std::map<saba::MMDNode*, std::vector<std::pair<size_t, const KeyPoseData*>>> nodeKeysCache;
        void rebuildNodeKeysCache();

        inline static IrrMMD* mmd{};
        IrrSaba* Sb0{}; 
        std::vector<IrrSaba*> sabas{};
        irr::scene::ISceneManager* SceneManager;
        int rigState = 0; // 0:stopped 1:playing 2:pause
        int nextFrameTo = -1;
        void loadRigNode();
        bool showGizmo = true, showNodePath = true;
        float defTime = 1.f;

        //range playback
        float customStartTime = 0.0f;  // Start time for ranged playback
        float customEndTime = 0.0f;    // End time for ranged playback
        bool useRangePlayback = false; // Whether to use ranged playback
 

        int timeToFrame(float time) const {
            return static_cast<int>(time / timePerFrame + 0.5f);
        }
        float frameToTime(int frame) const {
            return frame * timePerFrame;
        }
        int getCurrentFrame() const {
            return curFrame;
        }
        void saveWorld();
        void restoreWorld();

        // Undo/Redo stacks
        std::vector<KeyState> undoStack;
        std::vector<KeyState> redoStack;

        void clearRedoStack() { redoStack.clear(); }
        void restoreState(const KeyState& state);
        float lastSaveTime = -1.f;


   
        // Utility methods (you'll need to implement these)
        float getCurrentPlaybackTime() {
            // Return current playback time
            return curTime; // From your existing playback system
        }

        void onNodeFw(irr::scene::RigNode& rigNode, MMDNode* node);
       

 

        // Integration with key management
        void onSequencerKeyAdded(int type) {
            // Create a new key at current time
            newKeyIf();
        }

        void onSequencerKeyDeleted(int index) {
            // Remove the key at specific index
            if (curRigNode && index >= 0 && index < curRigNode->keys.size()) {
                curRigNode->keys.erase(
                    curRigNode->keys.begin() + index
                );
            }
        }


        //editor
        MMDNode* lastEditingNode = nullptr;
        RigNode* lastEditingRigNode = nullptr;
        RigKey* lastEditingKey = nullptr;
        bool imguiCommonAddPhyObjProperties(KeyPoseCmd::KpcAddPhyObj& apo);
        void showCmdEditor();
        void editKeyCmd(std::vector<KeyPoseCmd>& cmds, int id);
        bool InputNode(int& sbIdx,std::string& name, const std::string& label);
        bool InputMMDNodeInfo(MMDNodeInfo &info, std::string label);
        int selectedCmdIdx[16]{};


        std::vector<std::string> fwNames[3];
        void doUpdateNode(saba::MMDNode*& nd, saba::MMDNode* ndB, irr::scene::KeyPoseData& p0, int mtt, bool isMir, irr::scene::KeyPoseData& p1, float ratio, float& fmul, irr::scene::RigKey& currKey, irr::scene::RigKey& nextKey, irr::scene::RigNode& rigNode, bool& genApo);

        void irrCamToRttCam(irr::scene::IrrSaba* sb);


        saba::MMDCamera mmdCamDat;
        int editViewId = 0, animViewId = 0;

        int frameBreak = -1;
        std::vector<KeyPoseCmd> copiedCmds;

        using UpdateCallback = std::function<void(KeyPoseData& pose, const KeyPoseData& sourcePose)>;
        void sOtSl(UpdateCallback callback); //set other selected key pose to editing key pose field

        float framePixelWidthTarget = 10.f;
};

class MmdRigKeysSequencer : public ImSequencer::SequenceInterface {

    //selection
    bool mIsSelecting = false;
    ImVec2 mSelectionStart;
    ImVec2 mSelectionEnd;

    std::unordered_map<saba::MMDNode*, ImRect> mNodeRects;
    saba::MMDNode* lastSelectNode{}; int lastKeyIdx;
public:
    std::vector<std::pair<saba::MMDNode*, size_t>> mSelectedKeys;


    MmdRigKeysSequencer(MmdPhyAnimator* animator)
        : mAnimator(animator),
        mFrameMin(0),
        mFrameMax(1000),
        mCurrentFrame(0) {

    }

    // Key method for synchronization
    void syncPlaybackFromFrame(int frame);

    // ImSequencer interface methods
    virtual int GetFrameMin() const override {
        return mFrameMin;
    }

    virtual int GetFrameMax() const override {

        return std::max(1000, mAnimator ? mAnimator->maxCumFrame : 1000);
    }

    virtual int GetItemCount() const override {
        return static_cast<int>(mAnimator->nodeMap.size());
    }
    virtual const char* GetItemLabel(int index) const override;
    virtual bool IsItemEnabled(int index) const override {
        auto it = std::next(mAnimator->nodeMap.begin(), index);
        return !it->disabled;
    }
    virtual void SetItemEnabled(int index, bool enabled) {
        auto it = std::next(mAnimator->nodeMap.begin(), index);
        it->disabled = !enabled;
    }
    virtual void Get(int index, int** start, int** end, int* type, unsigned int* color) override;
    virtual void BeginEdit(int index) override {
        auto it = std::next(mAnimator->nodeMap.begin(), index);
        if (it != mAnimator->nodeMap.end()) {
            mAnimator->switchToNode(it->node);
        }
    }
    virtual void EndEdit() override {
        DP(("END EDIT"));
    }

    void clearSelection() {
        mSelectedKeys.clear();
    }
    void processKeySelection(const ImRect& rc);

    bool clickedOnKey = false ;
    virtual void CustomDrawCompact(int index, ImDrawList* draw_list, const ImRect& rc, const ImRect& clippingRect) override;
    // Method to get current frame
    int getCurrentFrame() const {
        return mCurrentFrame;
    }

private:
    // Conversion utility
    float convertFrameToTime(int frame) const {
        return frame * mAnimator->timePerFrame;
    }

    MmdPhyAnimator* mAnimator;
    int mFrameMin;
    int mFrameMax;
    int mCurrentFrame;

    bool mDraggingKey = false;
    saba::MMDNode* mDragKeyNode = nullptr;
    int mDragKeyIndex = -1;
    float mDragStartX = 0.0f;
    float mDragStartPos = 0.0f;
    bool lastDrawFinished = true;
};



class MpaManager {
public:
    using MpaFinishCallback = std::function<void(const std::string&)>;
    MpaManager(IrrMMD* _mmd) :mmd(_mmd) {}
    void playMPA(MpaLoadJsonParam lf, std::vector<int> sbs,bool forceReload = false);
    void stopMPA(MpaLoadJsonParam lf);
 
    void update();

    void setFinishCallback(MpaFinishCallback callback) {
        onMpaFinish = callback;
    }

    void clear() {
        mpaMap.clear();
    }
 
    IrrMMD* mmd;

    struct MpaInfo {
        std::shared_ptr<MmdPhyAnimator> mpa;
        std::string filepath;
        bool isPlaying = false;
    };

    std::map<std::string, MpaInfo> mpaMap;

private:
    MpaFinishCallback onMpaFinish;
};

 






}
