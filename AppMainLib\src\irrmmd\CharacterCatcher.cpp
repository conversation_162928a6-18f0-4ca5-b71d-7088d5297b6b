﻿#include "AppGlobal.h"
#include "CharacterCatcher.h"
//AMP  single thread render  31fps 2ms 10 xl.zip  paused=770fps 
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtx/euler_angles.hpp>
#include <stlUtils.h>
#include <Saba/Model/MMD/MMDNode.h>
#include <Saba/Model/MMD/MMDModel.h>
#include <Saba/Model/MMD/MMDPhysics.h>
#include <Saba/Base/File.h>
#include <UaUtils.h>
#include <helpers/UpUtils.h>
#include <irrfw/eqv/EQV.h>
#include "IrrMMD.h"
#include "irrSaba.h"
#include "vulkanrenderer/VkMr2D.h"
#include "../../AppMainLib/app/ArMmPLayer/SnArItem.h"
#include "PhyObjMan.h"
using namespace glm;
using namespace uu;
using namespace irr::scene;
using namespace irr::core;
using namespace EQVisual;
using namespace saba;
using namespace ualib;
namespace {

}
#define DBG_CATCHER IS_WIN_DBG
#if DBG_CATCHER 
#define MMDFWD(T,N,P, V, C) 		do{ Sb->mmdFw((T),(N),(P),(V),(C));}while(0) //FID will not change at runtime
#define MMDFWLineD(T,N,P, V, C,S) 		do{ Sb->mmdFwLine((T),(N),(P),(V),(C),(S));}while(0) //FID will not change at runtime
#else
#define MMDFWD( ) 
#define MMDFWLineD( )
#endif
#define THROW_TIME 2.f
void irr::scene::CharacterCatcher::init(const CharacterCatcherParam& pm)
{
	Sb = pm.sb;
	mmd = Sb->mmd;
  
	Rcv = Sb->Eqv->Ctx->getEvtRcv();
	Pmx = Sb->Pmx;	Pom = Sb->Pom; Eqv = Sb->Eqv;	 
}
void irr::scene::CharacterCatcher::catchStart(IrrSaba* tsb, int setpose)
{
	startCC++;
	if (setpose) pose=setpose;
	Sb->tsb = tsb;
	s0 = this->Sb; s1 = Sb->tsb;
	type = 0; tsb->cat.type = 1;
	setStage(1);
	catchStartTime = gSceneTime;


	if (startCC > 1 && Sb->tsb->ndYao->getGlobalPos().y< Sb->ndYao->getGlobalPos().y)
	{
	Sb->tsb->Pmx->addBodyVel(vec3(0, 100, 0), true);

	}
	Sb->ndArmR->forEachSubNodes([=](saba::MMDNode* nd) { nd->phyAnimRatT = 0; nd->phyAnimRatR = 0; });
	Sb->ndArmL->forEachSubNodes([=](saba::MMDNode* nd) { nd->phyAnimRatT = 0; nd->phyAnimRatR = 0; });

	
	switch (pose)
	{
	case 20:
		npa0HR = std::make_unique<MmdNodePhyAnimator>();
		npa0HR->setSabas(s0, s1);
		npa0HR->loadAnimJson("data/mmd/nodePhyAnim/20R.json5");
		npa0HL = std::make_unique<MmdNodePhyAnimator>();
		npa0HL->setSabas(s0, s1);
		npa0HL->loadFromMirror(npa0HR.get());
		npa1LR = std::make_unique<MmdNodePhyAnimator>();
		npa1LR->setSabas(s1, s0);
		npa1LR->loadAnimJson("data/mmd/nodePhyAnim/20_1_zuan.json5");//    20_1_LR.json5");
		npa1LL = std::make_unique<MmdNodePhyAnimator>();
		npa1LL->setSabas(s1, s0);
		npa1LL->loadFromMirror(npa1LR.get());
		break;
	}
}
void irr::scene::CharacterCatcher::catchEnd()
{
	 
	s0->disableAnim(0, 0);
	s1->disableAnim(0, 0);
	for (auto& jt : jts)
	{
		jt->destroy();
	}
	jts.clear();
	Sb->resetPhyAnim();

	Sb->tsb->resetPhyAnim();
 
	Sb->lastTsb = Sb->tsb;
	Sb->lookAt_mmdLookAt = false;
	//
	FRAMEWAITER_CALL_BN(1,ArmThrowUp) {
		Sb->Pmx->scaleBodyVel(0.1f, 3);
		Sb->ndArm1L->rb0->addLinearVel(vec3(0, 60 + task.repeatCountDown*10, 0));
		Sb->ndArm1R->rb0->addLinearVel(vec3(0, 60 + task.repeatCountDown * 10, 0));
		Sb->lastTsb->Pmx->addBodyVel(vec3(0,22.f* task.repeatCountDown/15, 0), true);
	},15,1);
	FRAMEWAITER_CALL_BN(15,Reset) {
		Sb->setPhyAnim(0, 1, true);
		vec3 vel = // glh::calcVelocityP2PinTimeGuess(s1->ndYao->getGlobalPos(), s0->ndUpper2->getGlobalPos(), 			glm::vec3(0, -9.81 * GRAVITY_MUL, 0), 1-st,0.3f, 60 * SABA_PHYSICS_FRAMESTEP,10);
			glh::calcVelocityP2PinT(s1->ndYao->getGlobalPos(), s0->ndUpper2->getGlobalPos(), THROW_TIME, glm::vec3(0, -9.81 * GRAVITY_MUL, 0));
		s1->Pmx->setBodyVel(vel, true);
	});
	Sb->tsb = nullptr;
	
	setStage(-1);
}

void irr::scene::CharacterCatcher::catcherUpdate(float steptime, int step, int stepCount)
{
	curPhyStep = step;
	s0 = this->Sb; s1 = Sb->tsb;
	vec3 dt = s1->ndYao->rbPos() - s0->ndYao->rbPos();
	float dis = s0->ndYao->disTo(s1->ndYao);
	fm = startCC == startCC == 1 ? 0.3f:1.f;
	fmv=vec3(1, dt.y>1 ? 0.f:1.f, 1);
	float st = gSceneTime - stageTime[stage];
	s0sc = s0->ndYao->absScale.x;	s1sc = s1->ndYao->absScale.x;
	if (stage >= 0 && pose==10)
		LookAtTSB();
	switch (stage) {
	case 1: {
		stage1_started();
	}break;
	case 2: {
		stage2_approching();
	}break;
	case 3: {
		stage3_closedAnimation();
	}break;
	case 4: {
		
		stage_final();

	}break;
	}

	for (int nid=0;nid<MMD_NID_MAX;nid++)	if (ndAct[nid])
	{
		if (pose==10) 
		switch (nid) {
		case ehR: 			actionUpdate_handR( );	break;
		case ehL: 			actionUpdate_handL();	break;
		}
		else if (pose == 20)
			actionUpdate20(nid);
	}


#if 0
	FrameWaiter::getLastTask().onOtherFrame = [=](FWTask& t) {
		if (s0->ndYao->disTo(s1->ndYao) < 10.f)
			t.targetFrame = FrameWaiter::currentFrameCount();
		};
#endif
}

void irr::scene::CharacterCatcher::stage_final()
{
	vec3 dt = s1->ndYao->rbPos() - s0->ndYao->rbPos();
	float dis = s0->ndYao->disTo(s1->ndYao);
	float st = gSceneTime - stageTime[stage];

	MMDFWD(2, "sw2", vec3(0, 0, 0), vec3(0), 0xffffFFFF);
	if (pose == 10)
	{
		s1->ndLegR->rb0->addLinearVelToPos(s0->ndArm1L->rbPos() + vec3(0, 2, 0), 100);
	}
}

void irr::scene::CharacterCatcher::stage1_started()
{
	vec3 dt = s1->ndYao->rbPos() - s0->ndYao->rbPos();
	float dis = s0->ndYao->disTo(s1->ndYao);
	float st = gSceneTime - stageTime[stage];
	if (st < 0.1f) return;
	MMDFWD(2, "sw2", vec3( 0,0,0 ), vec3(0), 0xFF00ff00);
	

	if (pose == 10)
	{

		if (  s1->ndYao->rb0->vel.y < 0) {
			if (st < THROW_TIME) {
				vec3 vel = // glh::calcVelocityP2PinTimeGuess(s1->ndYao->getGlobalPos(), s0->ndUpper2->getGlobalPos(), 			glm::vec3(0, -9.81 * GRAVITY_MUL, 0), 1-st,0.3f, 60 * SABA_PHYSICS_FRAMESTEP,10);
					glh::calcVelocityP2PinT(s1->ndYao->getGlobalPos(), s0->ndUpper2->getGlobalPos(), std::max(0.01f, THROW_TIME - st), glm::vec3(0, -9.81 * GRAVITY_MUL, 0));
				s1->Pmx->setBodyVel(vel, true);
			}
			else s1->ndYao->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 0,0,-3 }), 30.f, 0.95);
			//s1->Pmx->scaleBodyVel(0.1f, 1);
			float s1r = s1->Pmx->yaoPos.y * 0.35f;

			auto rm = s0->ndUpper2->GetGlobalTransform() * glh::rttDegZXY(60, -90, 0);
			s1->ndYao->rb0->addRotationToMatOnNode(rm, 10000);
			s1->ndLower->rb0->addRotationToMatOnNode(rm, 5000);
			s1->ndUpper->rb0->addRotationToMatOnNode(rm, 5000);
			s0->ndArm1R->rb0->addLinearVelToPosScXZ(s1->ndUpper2->transformVec({ 0,0,2 }), 10.f, 0.9);
			s0->ndArm1L->rb0->addLinearVelToPosScXZ(s1->ndLegL->rb0->rbTransformVec({ 0,0,1 }), 10.f, 0.9);


			//s0->ndArm1R->rb0->addLinearVelToPosLimitDis(s1->ndUpper2->transformVec({ 0,0,2 }), 10, 0, 5);
			//s0->ndArm1L->rb0->addLinearVelToPosLimitDis(s1->ndLegL->rb0->rbTransformVec({ 0,0,1 }), 10, 0, 5);
			pose10ArmUnderS1(0, 0.75);

		}
		if (dt.y < 16 && s1->ndYao->rb0->vel.y < 0)
		{
			setStage(2); locked = false;
			s1->resetPhyAnim();
			s0->loadBaseAnimation(1, L"data/mmd/base1vmd/10LrhH.vmd"); s0->disableAnim(2, 1);
			s0->ndYao->forEachSubNodes([=](saba::MMDNode* nd) {	nd->phyAnim = 1; }, false);
		}
	}
	else if (pose == 20)
	{
		if (s1->ndYao->rb0->vel.y < 0) {
			if (st < THROW_TIME) {
				vec3 vel = // glh::calcVelocityP2PinTimeGuess(s1->ndYao->getGlobalPos(), s0->ndUpper2->getGlobalPos(), 			glm::vec3(0, -9.81 * GRAVITY_MUL, 0), 1-st,0.3f, 60 * SABA_PHYSICS_FRAMESTEP,10);
					glh::calcVelocityP2PinT(s1->ndYao->getGlobalPos(), s0->ndUpper2->getGlobalPos(), std::max(0.01f, THROW_TIME - st), glm::vec3(0, -9.81 * GRAVITY_MUL, 0));
				s1->Pmx->setBodyVel(vel, true);
			}
			else s1->ndYao->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ 0,0,-2 }), 30.f, 0.95);

			auto rm = s0->ndUpper2->GetGlobalTransform();
			s1->ndYao->rb0->addRotationToMatOnNode(rm, 10000);

			s0->ndArm1R->rb0->addLinearVelToPosScXZ(s1->ndLeg1R->transformVec({ 0,0,2 }), 10.f, 0.9);
			s0->ndArm1L->rb0->addLinearVelToPosScXZ(s1->ndLeg1L->rb0->rbTransformVec({ 0,0,2 }), 10.f, 0.9);
			s1->ndLeg1R->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ -2,-3,dis > 7 ? -3 : 0 }), 20, 0.9);
			s1->ndLeg1L->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ 2,-3, dis > 7 ? -3 : 0 }), 20, 0.9);
			pose20ArmCrabS1(0, 0.75);

		}

		if (dt.y < 20 && s1->ndYao->rb0->vel.y < 0)
		{
			setStage(2); locked = false; s0->ndArmL->setTreePhyAnimRat(0.2); s0->ndArmR->setTreePhyAnimRat(0.2);
			s1->resetPhyAnim();
			s0->loadBaseAnimation(1, L"data/mmd/base1vmd/20L.vmd"); s0->disableAnim(2, 1);  
			s0->ndYao->forEachSubNodes([=](saba::MMDNode* nd) {	nd->phyAnim = 1; }, false);

			//FRAMEWAITER_CALL_B(30) {
			//	s1->localPhyAnimToPos = true;		s1->localPhyAnim = true;
			//	s1->setPhyAnim(nullptr, 0, true); s1->setSubNodePhyAnim(s1->ndUpper, 1);
			//	s1->ndUpper->forEachSubNodes([=](saba::MMDNode* nd) {	nd->phyAnimRatT =  nd->phyAnimRatR = 0.9f;	}, true);
			//	s1->animCycleStart = 0;
			//	s1->loadAnimation(s0->Vmd->filePath.c_str());
			//	s1->setPlaying(true); s1->setCurTime(s0->getCurTime());
			//});
		}
	}
	else {
		s1->ndArm1R->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ -10,10,3 }), 16 * fm, 0.6);
		s1->ndArm1L->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ 10,10,3 }), 16 * fm, 0.6);
		s1->ndLeg1R->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ -16,-3,dis > 7 ? -20 : 0 }), 3 * fm, 0.9);
		s1->ndLeg1L->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 16,-3,dis > 7 ? -20 : 0 }), 3 * fm, 0.9);
		if (dt.y < 20 && s1->ndYao->rb0->vel.y < 0)
		{
			
			setStage(2);
			s0->ndUpper->forEachSubNodes([=](saba::MMDNode* nd) {			nd->phyAnimRatT = 0; nd->phyAnimRatR = 0;			}, false);
			s1->ndYao->forEachSubNodes([=](saba::MMDNode* nd) {
				nd->phyAnimRatT = 1; nd->phyAnimRatR = 20;
				}, false);
			s1->ndYao->rb0->SetActivation(true);
			//s1->ndUpper->EnableDeformAfterPhysics(true); s1->setBonePhsAnime(s1->ndUpper, true, true);
				//sb0->ndUpper->rb0->SetActivation(false);
			s0->ndYao->rb0->SetActivation(false);
			s1->ndYao->forEachSubNodes([=](saba::MMDNode* nd) {nd->phyAnim = 0; });
		}
	}
		
	
 
}
// ============================================================================================================================================================================================================================================================================================================

// ============================================================================================================================================================================================================================================================================================================

// ============================================================================================================================================================================================================================================================================================================
void irr::scene::CharacterCatcher::stage2_approching()  
{
	vec3 dt= s1->ndYao->rbPos()-s0->ndYao->rbPos();
	float dis = s0->ndYao->disTo(s1->ndYao);
	float st = gSceneTime - stageTime[stage], st1= std::min(st,1.f), st2= std::min(st, 2.f);
	MMDFWD(2, "sw2", vec3(0, 0, 0), vec3(0), 0xffffff00);

 

	if (pose < 10)
	{
		//s1->ndYao->rb0->SetCoMTranslate(sb0->ndYao->transformVec({ 0,0,-3 }), 6);
		s1->ndYao->rb0->addLinearVelToPosScVec(s0->ndYao->transformVec({ 0,-3,-6 }), fmv * 10.f, 0.95);
		s1->ndUpper2->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ 0,3,-1 }), 20 * fm, 0.99);
		s1->ndYao->rb0->setRotateTo(glm::mat4_cast(s0->ndYao->rb0->getRotation()));
		vec3 hlR = (s1->ndHandR->rbPos() + s1->ndLegR->rbPos()) / 2.f; //s1->ndYao->transformVec({ 2,-1,-3 })
		vec3 hlL = (s1->ndHandL->rbPos() + s1->ndLegL->rbPos()) / 2.f; //  s1->ndYao->transformVec({ -2,-1,-3 });
		float rat = (10 - dis) / 10;
		s0->ndHandR->rb0->addLinearVelToPosSc(hlR + s1->ndYao->rotateVec({ 0,0, 3 - 6 * rat }), 30 * fm, 0.8);
		s0->ndHandL->rb0->addLinearVelToPosSc(hlL + s1->ndYao->rotateVec({ 0,0,3 - 6 * rat }), 30 * fm, 0.8);

		s1->ndArm1R->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ 0,10,1 }), 16 * fm, 0.6);
		s1->ndArm1L->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ 0,10,1 }), 16 * fm, 0.6);
		{
			s1->ndLeg1R->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ -16,-3, 3 }), 3 * fm, 0.9);
			s1->ndLeg1L->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 16,-3,  3 }), 3 * fm, 0.9);
		}
	}
	else if (pose==10)
	{
		float s1r = s1->Pmx->yaoPos.y * 0.35f;

		float armf = dis<20?glm::clamp(100.f - st * st*10, 10.f, 100.f):10.f;
		auto up2tgt = s0->ndUpper2->transformVec({ -3,1,-2.6 });

		if (s1->ndUpper2->rb0->getPosition().y < up2tgt.y) {
			s1->ndUpper2->rb0->addLinearVelToPosSc(up2tgt, 50.f, 0.8f);
		}
		else s1->ndYao->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 0,0,-4 }), glm::clamp(50.f + st2 * 50, 20.f, 100.f), 0.987f);


		if (dis > 7.f * s0sc) {		

			s1->ndLeg1R->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 16, 0, 0 }), 10, 0.9);
		}
		else s1->ndLegR->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 16, 0, 0 }), 1, 0.9);
		auto rm = s0->ndUpper2->GetGlobalTransform() * glh::rttDegZXY(75, -90, 00) ;
		if (1) rm = rm * glh::rttDegZYX(0, 15, 00);
		s1->ndYao->rb0->addRotationToMatOnNode(rm,  5000 * (1 + st1));
		s1->ndLower->rb0->addRotationToMatOnNode(rm,2500 * (1 + st1));
		s1->ndUpper->rb0->addRotationToMatOnNode(rm, 2500 * (1 + st1));

			pose10ArmUnderS1(0,st<1.f?1.5f:1.0f);

		if (st > 0.5f && ( 0  //||s1->ndUpper2->rbPos().y - s0->ndArm1R->rbPos().y < -0.1f 
			//glh::invMatRotateVec(s0->ndUpper2->rb0->GetTransform(), s1->ndHead->rbPos() - s0->ndArm1R->rbPos()).y < -1.f
			||  s1->ndHead->rbPos().y - s0->ndArm1R->rbPos().y < -1.f  
			||  s1->ndHead->rbPos().y - s1->ndUpper2->rbPos().y < -2.0f)			
			&& ndStage[ehR]==0   && gSceneTime>ndStageTime[ehR]+0.1f  ) 
		{
			startNdAct(ehR, 1); 
		}
		if (st > 0.5f && (s1->ndLegL->rbPos().y - s0->ndArm1L->rbPos().y < -1.0f || s1->ndLegR->rbPos().y - s0->ndArm1L->rbPos().y < -1.0f)
			 && ndStage[ehL] == 0 && gSceneTime > ndStageTime[ehL] + 0.2f) 
		{
			startNdAct(ehL, s1->ndLegL->rbPos().y< s1->ndLegR->rbPos().y?1:2);
		}

	}
	else 		if (pose == 20)
	{
		
		float hdR = 2.735f*s0sc;
		auto up2tgt = s0->ndNeck->transformVec({ 0,0,hdR });

		if (s1->ndYao->rb0->getPosition().y < up2tgt.y || locked) {
			locked = true;
			s1->ndYao->rb0->addLinearVelToPosSc(up2tgt, 30.f, 0.9f);

		}
		else s1->ndYao->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 0,0,hdR }), glm::clamp(50.f + st2 * 50, 20.f, 100.f), 0.987f);
		s1->ndLeg1R->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ -1,0,dis > 7 ? -3 : 0 }), 10, 0.9);
		s1->ndLeg1L->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ 1,0, dis > 7 ? -3 : 0 }), 10, 0.9); 
		
#if BODY_FORCE_RTT
		auto rm = s0->ndUpper2->GetGlobalTransform()  ;
		//if (1) rm = rm * glh::rttDegZYX(0, 15, 00);
		s1->ndYao->rb0->addRotationToMatOnNode(rm, 8000 * (1 + st1));
 
		s1->ndUpper->rb0->addRotationToMatOnNode(rm, 5000 * (1 + st1));
		s1->ndUpper2->rb0->addRotationToMatOnNode(rm, 5000 * (1 + st1));
		s1->ndUpper2->rb0->addRotationToMatOnNode(s0->ndHead->GetGlobalTransform(), 1000 * (1 + st1));
#endif

		if (curPhyStep==0 && s0->ndHead->disTo(s1->ndYao) < 7.5f*s0sc && st>0.5f) {
			vec3 pNeck = s0->ndNeck->getGlobalPos(), 
				pArmL = s0->ndArmL->getGlobalPos(), pArmR = s0->ndArmR->getGlobalPos(), pHandL = s0->ndHandL->getGlobalPos(), pHandR = s0->ndHandR->getGlobalPos(), pArm1L = s0->ndArm1L->getGlobalPos(), pArm1R = s0->ndArm1R->getGlobalPos(),
				pLegL = s1->ndLegL->getGlobalPos(), pLegR = s1->ndLegR->getGlobalPos(), pLeg1L = s1->ndLeg1L->getGlobalPos(), pLeg1R = s1->ndLeg1R->getGlobalPos(), pFootL = s1->ndFootL->getGlobalPos(), pFootR = s1->ndFootR->getGlobalPos();


			auto vl = glh::relativePosInMat(s0->ndUpper2->rb0->GetTransform(), s1->ndLeg1L->rbPos() , s0->ndArm1L->rbPos());
			auto vr = glh::relativePosInMat(s0->ndUpper2->rb0->GetTransform(), s1->ndLeg1R->rbPos() , s0->ndArm1R->rbPos());
			auto vlu = glh::posToMat(s0->ndUpper2->rb0->GetTransform(), s1->ndLeg1L->rbPos());
			auto vru = glh::posToMat(s0->ndUpper2->rb0->GetTransform(), s1->ndLeg1R->rbPos());
			//auto vhl = glh::invMatTransformVec(s0->ndUpper2->rb0->GetTransform(), s1->ndLegL->rb0->getNodePos() - s0->ndHead->rbPos());
			//auto vhl1 = glh::invMatTransformVec(s0->ndUpper2->rb0->GetTransform(), s1->ndLeg1L->rb0->getNodePos() - s0->ndHead->rbPos());
			//auto bvhl = vhl.x < -0.6f && vhl1.x < -0.1f;
			//auto vhr = glh::invMatTransformVec(s0->ndUpper2->rb0->GetTransform(), s1->ndLegR->rb0->getNodePos() - s0->ndHead->rbPos());
			//auto vhr1 = glh::invMatTransformVec(s0->ndUpper2->rb0->GetTransform(), s1->ndLeg1R->rb0->getNodePos() - s0->ndHead->rbPos());
			//auto bvhr = vhr.x > 0.6f && vhr1.x > 0.1f;
#define S1s  "1s"
			vec3 intptL{}, intptR{};
			s1->ndYao->scaleVel(0.9f, 1);
			if (  (
				//s1->ndLeg1L->rbPos().y - s0->ndArm1L->rbPos().y > 2.0f
				////|| vl.x > 2.f || glh::invMatRotateVec(s0->ndUpper2->rb0->GetTransform(), s1->ndLegL->rbPos() - s0->ndArmL->rbPos()).z > 2.f
				
				!glh::isStickInTriangle(pNeck,  pArm1L, pHandL, pLeg1L, pFootL,1.f,&intptL)
				&& !glh::isStickInTriangle(pNeck, pArm1L, pHandL, pLegL, pLeg1L, 1.f, &intptL)
				|| vlu.x < -0.96f   || vlu.z > 0.96f
				)
				&& ndStage[ehL] == 0 && (gSceneTime > ndStageTime[ehL] + 0.1f || gSceneTime > npa0HL->startTime+0.75f) )
			{	 
				MMDFWD(2, "sw2" S1s, intptL, vec3(0), 0xFFffFFFF);
				MMDFWLineD(2, "pt" S1s, pNeck, pArm1L, 0xFF800000, 0.1f); MMDFWLineD(2, "pt" S1s, pArm1L, pHandL, 0xFFc00000, 0.1f);
				MMDFWLineD(2, "pt" S1s, pLeg1L, pFootL, 0xFF00FF00,0.1f);
				MMDFWLineD(2, "pt" S1s, pLegL, pLeg1L, 0xFF00FF00, 0.1f);
				
				s0->ndArmL->setTreePhyAnimRat(0.0);				startPoseAnim(ehL, npa0HL.get(), npa1LL.get());
			}
			if ( (//s1->ndLeg1R->rbPos().y - s0->ndArm1R->rbPos().y > 2.0f  	|| vru.x>0.6f || vru.y > 2.0f || vru.z > 0.6f
				!glh::isStickInTriangle(pNeck,  pArm1R, pHandR, pLeg1R,	 pFootR, 1.1f, &intptR)
				&& !glh::isStickInTriangle(pNeck, pArm1R, pHandR, pLegR, pLeg1R, 1.f, &intptR)
				|| vru.x > 0.96f   || vru.z > 0.96f
				)
				&& ndStage[ehR] == 0 && (gSceneTime > ndStageTime[ehR] + 0.1f || gSceneTime > npa0HR->startTime + 0.75f))
			{
				MMDFWD(2, "sw2" S1s, intptR, vec3(0), 0xFFffFF80);
				MMDFWLineD(2, "pt" S1s, pNeck, pArm1R, 0xFF800000, 0.1f); MMDFWLineD(2, "pt" S1s, pArm1R, pHandR, 0xFFc00000, 0.1f);
				MMDFWLineD(2, "pt" S1s, pLeg1R, pFootR, 0xFFffFF00, 0.1f);
				MMDFWLineD(2, "pt" S1s, pLegR, pLeg1R, 0xFFffFF00, 0.1f);
				s0->ndArmR->setTreePhyAnimRat(0.0);				startPoseAnim(ehR, npa0HR.get(), npa1LR.get());
			}
		}
		
		bool animated = false;
		if (npa0HL && npa0HL->stage && 1) {
			//ndAniHR->posMul = std::max(0.75f,1-st*0.5f);
			//npa0HL->revForce = 0.1f;
			auto d = npa0HL->getCurData();
			if (d.time >= 0) {
 				MMDFWLineD(2, "pt" , d.pos, d.pos+ glh::matRotateVec(mat4_cast(d.rtt), vec3(3, -3, 0)), 0xFFff00FF, 0.1f);
				MMDFWLineD(2, "pt" , d.pos, d.pos + glh::matRotateVec(mat4_cast(d.rtt), vec3(-2, -2, 0)), 0xFF00FFFF, 0.1f);

				ndStageTime[ehL] = gSceneTime;
				animated = true;
			}
			else {
				s0->ndArmL->setTreePhyAnimRat(0.2);
			}

			d = npa1LL->getCurData();
			if (d.time >= 0) {
	 
			}
			
			
		}
		if (npa0HR && npa0HR->stage && 1) {
			//ndAniHR->posMul = std::max(0.75f,1-st*0.5f);
			auto d = npa0HR->getCurData();
			if (d.time >= 0) {
				MMDFWLineD(2, "pt" , d.pos, d.pos + glh::matRotateVec(mat4_cast(d.rtt), vec3(-3, -3, 0)), 0xFFff00FF, 0.1f);
				MMDFWLineD(2, "pt" , d.pos, d.pos + glh::matRotateVec(mat4_cast(d.rtt), vec3(2, -2, 0)), 0xFF00FFFF, 0.1f);

				ndStageTime[ehR] = gSceneTime;
				animated = true;
			}
			else {
				s0->ndArmR->setTreePhyAnimRat(0.2);
			}

			d = npa1LR->getCurData();
			if (d.time >= 0) {
				MMDFWD(2, "pt1s", d.pos, glh::matRotateVec(mat4_cast(d.rtt), vec3(0, 0, -10)), 0xFFff0000);
				MMDFWD(2, "pt1s", d.pos, glh::matRotateVec(mat4_cast(d.rtt), vec3(0, -10, 0)), 0xFF00FF00);

			}
			
			
		}
		if (!animated)
		  pose20ArmCrabS1(0, st < 1.f ? 1.5f : 1.0f);
	}
		
	if (pose >= 10)
	{
		if (st > 0.f && dt.y < 1 && 0  )
		{
			setStage(3);
			s0->ndUpper->forEachSubNodes([=](saba::MMDNode* nd) {
				nd->phyAnimRatT = 1; nd->phyAnimRatR = 1;
				}, true);		

		}
	}
	else
	{
		if (st > 0.3f && (dt.y < 3 || st>11.f))
		{
			setStage(3);
			s1->localPhyAnimToPos = true;		s1->localPhyAnim = true;
			s1->ndYao->forEachSubNodes([=](saba::MMDNode* nd) {nd->phyAnim = 1; });
			s1->loadBaseAnimation(1, pose == 1 ? L"data/mmd/base1vmd/1U.vmd" : L"data/mmd/base1vmd/2U.vmd"); s1->disableAnim(1, 1);
			s1->loadAnimation(s0->Vmd->filePath.c_str());
			s1->setPlaying(true); s1->setCurTime(s0->getCurTime());
		}
	}
	
}

void irr::scene::CharacterCatcher::startPoseAnim(int nid, MmdNodePhyAnimator* s0pa, MmdNodePhyAnimator* s1pa)
{
#if 0
	startNdAct(nid, 1);
#else
	s1pa->fmul = 1 + ndStageRecent[nid] * 1.0f;
	s0pa->startAnim();
	if (gSceneTime - ndStageTime[nid] < 1.f)  ndStageRecent[nid]++; else ndStageRecent[nid] = 0;
	s1pa->fmul = 0.5 + ndStageRecent[nid] * 1.0f;
	s1pa->startAnim();
#endif
	ndStageTime[nid] = gSceneTime;
}

void irr::scene::CharacterCatcher::stage3_closedAnimation()
{
	float dis = s0->ndYao->disTo(s1->ndYao);
	float st = gSceneTime - stageTime[stage];
	auto Pmx = s0->Pmx;
	MMDFWD(2, "sw2", vec3( 0,0,0 ), vec3(0), 0xffff0000);
	//s1->ndYao->rb0->SetCoMTranslate(sb0->ndYao->transformVec({ 0,0,-3 }), 6);
	vec3 s1ArmLTgt, s0Arm1LTgt;
	bool disOK = false;
	if (pose < 10)
	{
		s1->ndYao->rb0->addLinearVelToPosScVec(s0->ndYao->transformVec({ 0,0,-3 }), vec3(1, 1, 1) * 200.f, 0.75);
		s1->ndUpper2->rb0->addLinearVelToPosScXZ(s0->ndUpper2->transformVec({ 0,0,-2 }), 10 * fm, 0.75);
		s1->ndYao->rb0->setRotateTo(glm::mat4_cast(s0->ndYao->rb0->getRotation()));
		s1->ndLeg1R->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ -10,-3, 19 }), 66 * fm, 0.9);
		s1->ndLeg1L->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 10,-3,  19 }), 66 * fm, 0.9);
		disOK = true;
	}
	else if (pose == 10)
	{
		s1ArmLTgt = s0->ndUpper2->rb0->rbTransformVec(vec3(-2, 2, 1));
		s0Arm1LTgt = s1->ndLegR->rb0->rbTransformVec(vec3(0, 0, 2));
		pose10ArmUnderS1();

		s1->Pmx->scaleBodyVel(0.99f, 1);
		float s1r = s1->Pmx->yaoPos.y * 0.35f;
		s1->ndUpper2->rb0->addLinearVelToPosScVec(s0->ndYao->transformVec({ -s1r,4,-3 }), vec3(1, 1, 1) * 100.f, 0.75);
		s1->ndLeg1R->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 16,4, -2 }), 3+st*3 , 0.7);
		s1->ndLeg1L->rb0->addLinearVelToPosScXZ(s0->ndYao->transformVec({ 16,4, -1 }), 3+st*2 , 0.7);

		s1->ndArm1L->rb0->addLinearVelToPosLimitDis(s1ArmLTgt, 60, 0, 3);
		auto rm = s0->ndUpper2->GetGlobalTransform() * glh::rttDegZXY(90, -90, 0);
		s1->ndYao->rb0->addRotationToMatOnNode(rm, 10000+st*10000);
 		s1->ndUpper2->rb0->addRotationToMatOnNode(rm, 25000 + st * 10000);

		float armLDis = s1->ndArm1L->rb0->dis2To(s1ArmLTgt);
		float legLDis = s0->ndArm1L->rb0->dis2To(s0Arm1LTgt);
		if (armLDis<2.f && legLDis<2.f)
			disOK = true;
	}
	

	if (st > 0.01f && disOK || st>0.5f)
	{
		setStage(4);

		 
		//s1->localPhyAnimToPos = true;		
		s1->localPhyAnim = true;

		if (pose == 1)
		{
			//s1->attachPhyNodeToParent(s1->ndYao, s0->ndYao, { 0,  2,-4 });
			//s1->attachPhyNodeToParent(s1->ndUpper, s0->ndUpper, { 0,  2,-3.2f });
			PMXJoint jt{};
			jt.translate = { 0, 2,-3 };
			jt.limitMinT = vec3(0, -1, 0);
			jt.limitMaxT = vec3(0, 1, 0);
			jt.setLocalPos = true;
			jt.springT = vec3(1000.f);
			jt.springR = vec3(2000.f);
			jt.dampingT = vec3(1000.f);
			jt.dampingR = vec3(1000.f);
			jts.insert(Pmx->connectRb(s0->ndYao->rb0, s1->ndYao->rb0, 0, 1, jt));
			jt.translate = { 0, 2,-1 };
			jts.insert(Pmx->connectRb(s0->ndUpper2->rb0, s1->ndUpper2->rb0, 0, 0, jt));

			jt.translate = { 0, 2,0.9 }; jt.t2B = vec3(0, -0.3, 0);;
			jts.insert(Pmx->connectRb(s1->ndLegR->rb0, s0->ndHandR->rb0, 0, 0, jt));
			jt.t2B = vec3(0, -0.6, 0);;
			jts.insert(Pmx->connectRb(s1->ndLegL->rb0, s0->ndHandL->rb0, 0, 0, jt));
			//Pmx->connectRb(s0->ndYao->rb0, s1->ndLegR->rb0, 0, { -3,1,-6 }, false);
			//Pmx->connectRb(s0->ndYao->rb0, s1->ndLegL->rb0, 0, { 3,1,-6 }, false);
			s1->ndLegR->phyAnimRatR = 0; s1->ndLegL->phyAnimRatR = 0;
			s1->ndLegR->forEachSubNodes([=](saba::MMDNode* nd) {nd->phyAnimReset(); });
			s1->ndLegL->forEachSubNodes([=](saba::MMDNode* nd) {nd->phyAnimReset(); });

		}
		else if (pose==2)
		{
			s1->connectPhyNodeToParent(s1->ndYao, s0->ndYao, { 0,  0,-3 });
			s1->connectPhyNodeToParent(s1->ndUpper, s0->ndUpper, { 0,  0,-2.2f });
			jts.insert(Pmx->connectRb(s1->ndUpper->rb0, s0->ndHandR->rb0, 0, { 0,0,-1 }, false));
			jts.insert(Pmx->connectRb(s1->ndUpper->rb0, s0->ndHandL->rb0, 0, { 0,-0.3,-1.33 }, false));
			jts.insert(Pmx->connectRb(s0->ndLegR->rb0, s1->ndLeg1R->rb0, 0, { -2,1,2 }, false));
			jts.insert(Pmx->connectRb(s0->ndLegL->rb0, s1->ndLeg1L->rb0, 0, { 2,1,2 }, false));
		}
		else if (pose == 10)
		{
			float s1r = s1->Pmx->yaoPos.y*0.35f;
			PMXJoint jt{};

			jt.limitMinT = vec3(-3);
			jt.limitMaxT = vec3( 3);
			jt.setLocalPos = true;
			jt.springT = vec3(10.f);
			jt.springR = vec3(0.f);
			jt.dampingT = vec3(100.f);
			jt.dampingR = vec3(0.f);
			
			jt.visualize = true;
			jt.translate = { -s1r, 6,-2 }; jt.t2B = vec3(0, 0, -2);;
			auto jup2 = Pmx->connectRb(s0->ndYao->rb0, s1->ndUpper2->rb0, 0, 0, jt);
			jts.insert(jup2);

			jt.limitMinT = vec3(-1);
			jt.limitMaxT = vec3(1);

			s1->ndLegR->phyAnimRatR = 0; s1->ndLegL->phyAnimRatR = 0;
			s1->ndLegR->forEachSubNodes([=](saba::MMDNode* nd) {nd->phyAnimReset(); });
			s1->ndLegL->forEachSubNodes([=](saba::MMDNode* nd) {nd->phyAnimReset(); });

			jt.translate = { -2, 2,1}; jt.t2B = vec3(0, 0, -1);
			auto jArmL = Pmx->connectRb(s0->ndUpper2->rb0, s1->ndArm1L->rb0, 0, 0, jt);
			jts.insert(jArmL);
			s1->ndArm1L->forEachSubNodes([=](saba::MMDNode* nd) {nd->phyAnimReset(); });

			jt.translate = {0,0,1 }; jt.t2B = vec3(-2, -2, 0);
			jt.springR = vec3(0.f);
			auto jArmR = Pmx->connectRb( s1->ndArmR->rb0, s0->ndArmR->rb0, 0, 0, jt);
			jts.insert(jArmR);
			s1->ndArm1R->forEachSubNodes([=](saba::MMDNode* nd) {nd->phyAnimReset(); });

			jt.translate = { 0,0,2 };  jt.t2B = vec3(1, -1, 0);
			auto jlr = Pmx->connectRb(s1->ndLegR->rb0, s0->ndArm1L->rb0, 0, 0, jt);
			jts.insert(jlr);
			jt.translate = { 0,0,2 }; jt.t2B = vec3(-1, 1, 0);
			auto jll = Pmx->connectRb(s1->ndLegL->rb0, s0->ndArm1L->rb0, 0, 0, jt);
			jts.insert(jll);

			s1->ndArmR->rb0->SetCoMTranslate(s0->ndUpper2->rb0->rbTransformVec({ -6, 2, -3 }));

			jt.springT = vec3(10000.f); jt.translate = { -10, 3,-3 }; jt.t2B = vec3(0, 0, -1);
			auto jArmRUp2 = Pmx->connectRb(s0->ndUpper2->rb0, s1->ndArmR->rb0, 0, 0, jt);
			jts.insert(jArmRUp2);

			jt.translate = { 0,1,-3 }; jt.t2B = vec3(0, 0, 1);
			auto jYao = Pmx->connectRb(s0->ndYao->rb0, s1->ndYao->rb0, 0, 0, jt);
			jts.insert(jYao);
			FRAMEWAITER_CALL_B(10) {				 
				//removeJoint(jArmR);//removeJoint(jArmL);
				removeJoint(jll);
				removeJoint(jup2);  removeJoint(jArmRUp2);
				
				//s1->resetPhyAnim();
			});
		}

	}
}


void irr::scene::CharacterCatcher::setStage(int s) {
	stage = s; stageTime[stage] = gSceneTime; 

}

void irr::scene::CharacterCatcher::startNdAct(int nid, int act)
{
	ndAct[nid] = act;
	ndStage[nid] = 1;
	ndStageTime[nid] = gSceneTime;
}

void irr::scene::CharacterCatcher::setNdStage(int nid, int stg)
{
	ndStage[nid] = stg;
	ndStageTime[nid] = gSceneTime;
}

void irr::scene::CharacterCatcher::stopNdAct(int nid)
{
	ndStage[nid] = 0;  ndAct[nid] = 0;
	ndStageTime[nid] = gSceneTime;
}

void irr::scene::CharacterCatcher::LookAtTSB()
{
	Sb->mmdLookAt = Sb->tsb->ndHead->rbPos();
	Sb->lookAt_mmdLookAt = true;//				:sb->mmdLookAt.y-sb->ndYao->getGlobalPos().y<20.f*sb->ndYao->absScale.y &&  !notes[i].fake ;
}

void irr::scene::CharacterCatcher::pose10ArmUnderS1(int mode, float fmul)
{

	if (mode == 0) {
	 	Sb->ndArm1R->rb0->addLinearVelToPosLimitDis(glh::BaddAtoB(Sb->ndArmR->rb0->parentRb->getPosition(), Sb->ndArmR->rb0->parentRb->getPosition() * vec3(0, 1, 0) + 
			(Sb->tsb->ndHead->GetParent()->getGlobalPos()+ Sb->tsb->ndUpper2->GetParent()->getGlobalPos()) * vec3(0.5f, 0,0.5f) + vec3(0, 0, 0), 
			//  Sb->tsb->ndUpper2->GetParent()->getGlobalPos() * vec3(1, 0,1) + vec3(0, 0, 0),
			1.5f * Sb->ndYao->absScale.x), fmul * 20, 0, 2);
	 	Sb->ndArm1L->rb0->addLinearVelToPosLimitDis(glh::BaddAtoB(Sb->ndArmL->rb0->parentRb->getPosition(), Sb->ndArmL->rb0->parentRb->getPosition() * vec3(0, 1, 0) +
			Sb->tsb->ndLegL->getGlobalPos() * vec3(1, 0, 1) + vec3(0, 0, 0), 
			2 * Sb->ndYao->absScale.x), fmul*10, 0, 2);
	}
	else {
		Sb->ndArm1R->rb0->addLinearVelToPosLimitDis(Sb->tsb->ndUpper2->transformVec({ 0,0,2 }), 10, 0, 5);
		Sb->ndArm1L->rb0->addLinearVelToPosLimitDis(Sb->tsb->ndLegL->rb0->rbTransformVec({ 0,0,1 }), 10, 0, 5);
	}

	auto rm = Sb->ndUpper2->GetGlobalTransform();
	Sb->ndArm1R->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(45, -60, 90), 10000 * fmul);
	Sb->ndArm1L->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(-45, 60, 90), 10000 * fmul);
	Sb->ndHandR->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(45, -60, 90), 20000 * fmul);
	Sb->ndHandL->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(-45, 60, 90), 20000 * fmul);
}

void irr::scene::CharacterCatcher::pose20ArmCrabS1(int mode, float fmul)
{
	float st = gSceneTime - stageTime[stage];
	float st1m2s = std::min((st)/1,1.f);
	if (mode == 0) {
		//Sb->ndArm1L->rb0->addLinearVelToPosLimitDis(s1->ndLeg1L->rbPos(), fmul * 30, 0, 2);
		//Sb->ndArm1R->rb0->addLinearVelToPosLimitDis( s1->ndLeg1R->rbPos(), fmul * 30, 0, 2);
		//Sb->ndHandL->rb0->addLinearVelToPosLimitDis(s1->ndUpper->rbPos(), fmul * 10, 0, 2);
		//Sb->ndHandR->rb0->addLinearVelToPosLimitDis(s1->ndUpper->rbPos(), fmul * 10, 0, 2);
	}
	else {
 
	}

	auto rm = Sb->ndUpper2->GetGlobalTransform();
	//Sb->ndArmR->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(-30,0, 0), 10000 * fmul);
	//Sb->ndArmL->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(30, 0, 0), 10000 * fmul);
	Sb->ndArm1R->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(60, -60, 90), 10000 * fmul);
	Sb->ndArm1L->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(-60, 60, 90), 10000 * fmul);
	Sb->ndHandR->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(45, -90, 90), 10000 * fmul);
	Sb->ndHandL->rb0->addRotationToMatOnNode(rm * glh::rttRadZYX(-45, 90, 90), 10000 * fmul);
	s1->ndLegL->rb0->addLinearVelToPosLimitDis(s0->ndNeck->transformVec({1,0,-1}), 30* st1m2s,0,1);
	s1->ndLegR->rb0->addLinearVelToPosLimitDis(s0->ndNeck->transformVec({ -1,0,-1 }),30* st1m2s, 0, 1);
	//s1->ndLeg1L->rb0->addLinearVelToPosLimitDis(s0->ndYao->transformVec({ 1,1,-2 }), 30 * st1m2s, 0, 1);
	//s1->ndLeg1R->rb0->addLinearVelToPosLimitDis(s0->ndYao->transformVec({ -1,1,-2 }), 30 * st1m2s, 0, 1);

}


void irr::scene::CharacterCatcher::actionUpdate_handR()
{
	int nid = ehR;
	auto& stage = ndStage[nid];
	float st=gSceneTime - ndStageTime[nid];
	vec3 ofs = glh::invMatRotateVec(s0->ndYao->rb0->GetTransform(), s0->ndArm1R->rbPos() - s1->ndUpper2->rbPos());
	switch (ndAct[nid])
	{
	case 1: {
		auto pShdR = Sb->ndArmR->rb0->parentRb->rbTransformVec({ -1,0,1 }), pS1Up2 = s1->ndUpper2->transformVec({ 0,0,2 });	 
		
		switch (stage) {
		case 1: {
			s0->ndArmR->rb0->addLinearVelToPosLimitDis(pShdR  + s0->ndUpper2->rotateVec({ -1,-1,5 }), 100, 0, 1);
			s1->ndUpper2->rb0->addLinearVel(s0->ndUpper2->rotateVec({ 0,5,-1 }));
			s0->ndArmR->setTreePhyAnimRat(0.f);
			vec3 ofs = glh::invMatRotateVec(s0->ndYao->rb0->GetTransform(),s0->ndHandR->rbPos()- s1->ndHead->rbPos());
			bool isFar =  ofs.z>2;
			if (isFar) { s0->ndArm1R->scaleVel(0.0f, 3); s0->ndArmR->scaleVel(0.0f, 3);}
			else { MMDFWD(2, "sw", vec3(0, 2, 0), vec3(0), 0xff00ff00); }
			if (st > 1.0f || isFar && st > 0.1f) {
				setNdStage(nid, 2);
			}
		}break;
		case 2: {
			s0->ndArm1R->scaleVel(0.1f, 3);
			s0->ndArm1R->rb0->addLinearVelToPosLimitDis(pShdR + s0->ndUpper->rotateVec({ -1,-2,2 }), 70, 0, 1);
			if (s0->ndHandR->rbPos().y - s1->ndHead->rbPos().y > -2.f) s0->ndArm1R->rb0->addLinearVel({ 0,-3,0 });
			if (st > 0.1f)
				setNdStage(nid, 3);
		}break;
		case 3: {
			 
			s0->ndArm1R->scaleVel(0.5f, 3);
			auto tpos = glh::BaddAtoB(pShdR, pS1Up2 + vec3(0, -3, 0), 5 * s0sc);
			MMDFWD(2, "sw", tpos, vec3(0), 0xffFF8000);
			s0->ndArm1R->rb0->addLinearVelToPosLimitDis(tpos, 50, 0, 1);
			s1->ndHead->rb0->addLinearVelToPosLimitDis(pShdR+vec3(0,3,0), 10, 0, 2);

			if (st > 0.3f || ofs.z<0 && ofs.y<0 ) {
				//auto jt=Pmx->connectRb(s0->ndArm1R->rb0, s1->ndUpper2->rb0, 0, { 0,1,0 }, 1);				FRAMEWAITER_CALL_B(30) { jt->destroy(); });
				//stopNdAct(nid);
				
				setNdStage(nid, 4);
			}
		}break;
		case 4: { 
			s0->ndArm1R->scaleVel(0.1f, 3);

			MMDFWD(2, "sw", vec3(0,2,0), vec3(0), 0xffFF0000);
			if (st > 0.3f 
				 || ofs.z<1  || ofs.y>1
				) {
				s0->ndArmR->setTreePhyAnimRat(0.1);
				stopNdAct(nid);
 
			}
		}break;

		}
		
	}break;
	case 2: {
	}break;
	}
 
}

void irr::scene::CharacterCatcher::actionUpdate_handL()
{
	int nid = ehL;
	auto& stage = ndStage[nid];
	float st = gSceneTime - ndStageTime[nid];
	
	switch (ndAct[nid])
	{
	case 1:  case 2:{
		bool isR = ndAct[nid] == 2; auto s1Leg = (isR ? s1->ndLegR : s1->ndLegL), s1Leg1 = (isR ? s1->ndLeg1R : s1->ndLeg1L);
		auto pShdL = s0->ndArmL->rb0->parentRb->rbTransformVec({ 2,0,2 }), pS1LegL = s1Leg->transformVec({ 0,0,1 });
		s0->mmdLookAt = s1Leg->rbPos();
		switch (stage) {
		case 1: {
			s0->ndArmL->setTreePhyAnimRat(0.0f);
			s0->ndArm1L->rb0->addLinearVel({ 0,200,0 });
			setNdStage(nid, 2);
		}break;
		case 2: {
			s0->ndArm1L->scaleVel(0.5f, 3);
			vec3 ofs = glh::invMatRotateVec(s0->ndYao->rb0->GetTransform(), s0->ndHandL->rbPos() - s1Leg->rbPos());
			bool isFar = ofs.z > 2;
			if (isFar) { s0->ndArm1R->scaleVel(0.0f, 3); s0->ndArmR->scaleVel(0.0f, 3); }
			auto tpos = pShdL * vec3(1, 0, 1) + pS1LegL * vec3(0, 1, 0) + vec3(0, 3-st*6.f, 0);
			MMDFWD(2, "sw", tpos, vec3(0), 0xff00FF00);
			s0->ndArm1L->rb0->addLinearVelToPosLimitDis(tpos, 30, 0, 3);
			s1Leg1->rb0->scaleVel(0.5f, 3); s1Leg1->rb0->addLinearVel(s0->ndUpper->rotateVec({20,10,0}));
			if (st > 0.5f || isFar && st>0.2f)
				setNdStage(nid, 3);
		}break;
		case 3: {
			s0->ndArm1L->scaleVel(0.2f, 3);
			auto tpos = s1Leg->rbPos() + s0->ndUpper2->rotateVec({ -2,-1,-0 }); //glh::BaddAtoB(pShdL, pS1LegL + vec3(0, -1, 0), 3 * s0sc);
			MMDFWD(2, "sw", tpos, vec3(0), 0xffFFFF00);
			s0->ndArm1L->rb0->addLinearVelToPosLimitDis(tpos, 30, 0, 2);
			s1Leg1->rb0->scaleVel(0.5f, 3); s1Leg1->rb0->addLinearVel(s0->ndUpper->rotateVec({ 20,10,s1Leg1->rb0->getPosition().y > s0->ndHandL->rbPos().y ? 20 : 0 }));
			if (st > 0.5f || s0->ndArm1L->rbPos().y < s1Leg->rbPos().y -1.0f
				) {				
				setNdStage(nid, 4);
				s0->ndArmL->setTreePhyAnimRat(0.7);
			}
		}break;
		case 4: { 
			auto tpos = s1Leg->rbPos() + s0->ndUpper->rotateVec({ -2,-1,-0 });
			MMDFWD(2, "sw", tpos, vec3(0), 0xffFF0000);
			Sb->ndArm1L->rb0->addLinearVelToPosLimitDis(tpos, 10, 0, 2);
			s1Leg->rb0->addLinearVelToPosLimitDis(Sb->ndArmL->rbPos(), 6, 0, 1);
			if (st > 0.5f 
				) {
				s0->ndArmL->setTreePhyAnimRat(0.1);
				stopNdAct(nid);
			}
		}break;
		}

	}break;
 
	}
 
}

void irr::scene::CharacterCatcher::actionUpdate20(int nid  )
{
	
	auto& stage = ndStage[nid];
	float st = gSceneTime - ndStageTime[nid];

	switch (ndAct[nid])
	{
	case 1:  case 2: {

		bool isR = ndAct[nid] == 2; auto s1Leg = (isR ? s1->ndLegR : s1->ndLegL), s1Leg1 = (isR ? s1->ndLeg1R : s1->ndLeg1L);
		auto s0arm = isR ? s0->ndArmR : s0->ndArmL, s0arm1 = isR ? s0->ndArm1R : s0->ndArm1L;
		auto pShd = s0arm->rb0->parentRb->rbTransformVec({ isR?-2:2,0,2 }), pS1LegL = s1Leg->transformVec({ isR ? -2 : 2,0,1 });
		s0->mmdLookAt = s1Leg->rbPos();
		switch (stage) {
		case 1: {
			s0arm->setTreePhyAnimRat(0.0f); s0arm1->rb0->addLinearVel({ 0,200,0 });
			setNdStage(nid, 2);
		}break;
		case 2: {
			s0arm1->scaleVel(0.5f, 3);
			vec3 ofs = glh::invMatRotateVec(s0->ndYao->rb0->GetTransform(), s0->ndHandL->rbPos() - s1Leg->rbPos());
			bool isFar = ofs.z > 2;
			if (isFar) { s0->ndArm1R->scaleVel(0.0f, 3); s0->ndArmR->scaleVel(0.0f, 3); }
			auto tpos = pShd * vec3(1, 0, 1) + pS1LegL * vec3(0, 2, 0) + vec3(0, 10 - st * 10.f, 0)+ s0->ndUpper2->rotateVec(vec3(isR?-2:2,0, 6));
			MMDFWD(2, "sw2", tpos, vec3(0), 0xFF00FF00);
			s0arm1->rb0->addLinearVelToPosLimitDis(tpos, 30, 0, 3);
			s1Leg1->rb0->scaleVel(0.5f, 3); s1Leg1->rb0->addLinearVel(s0->ndUpper2->rotateVec({ isR ?  0 : 0,0,-20 }));
			if (st > 0.2f || isFar && st > 0.2f)
				setNdStage(nid, 3);
		}break;
		case 3: {
			s0arm1->scaleVel(0.2f, 3);
			auto tpos = s1Leg->rbPos() + s0->ndUpper2->rotateVec({ -2,1,-1 }); //glh::BaddAtoB(pShdL, pS1LegL + vec3(0, -1, 0), 3 * s0sc);
			MMDFWD(2, "sw2", tpos, vec3(0), 0xFFFFFF00);
			s0arm1->rb0->addLinearVelToPosLimitDis(tpos, 30, 0, 2);
			s1Leg1->rb0->scaleVel(0.5f, 3); s1Leg1->rb0->addLinearVel(s0->ndUpper2->rotateVec({ isR ? -10 : 10,10,-10 }));
			s1Leg->rb0->addLinearVel(s0->ndUpper2->rotateVec(vec3( 0,-30,-36 )));
			if (st > 0.2f || s0arm1->rbPos().y < s1Leg->rbPos().y - 1.0f
				) {
				setNdStage(nid, 4);
				s0arm->setTreePhyAnimRat(0.3);
			}
		}break;
		case 4: {
			auto tpos = s1Leg->rbPos() + s0->ndUpper->rotateVec({ 0,-1,-1 });
			MMDFWD(2, "sw2", tpos, vec3(0), 0xFFFF0000);
			Sb->ndArm1L->rb0->addLinearVelToPosLimitDis(tpos, 10, 0, 2);
			s1Leg->rb0->addLinearVel(s0->ndUpper2->rotateVec(vec3(isR ? 10 : -10, 0, -10)));
			s1Leg1->rb0->addLinearVel(s0->ndUpper2->rotateVec( vec3(isR ? -10 : 10,  -30, 0)));
			s1Leg->rb0->addLinearVelToPosLimitDis(Sb->ndArmL->rbPos(), 6, 0, 1);
			if (st > 0.5f
				) {
				s0arm->setTreePhyAnimRat(0.75);
				stopNdAct(nid);
			}
		}break;
		}

	}break;

	}
}



void irr::scene::CharacterCatcher::removeJoint(MMDJoint* jt)
{
	jts.erase(jt);
	jt->destroy();
}
