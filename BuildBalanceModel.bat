@echo off
echo Building PMXGenerator_Test...

mkdir build 2>nul
cd build

cmake -G "Visual Studio 16 2019" -A x64 ..
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed
    exit /b %ERRORLEVEL%
)

cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed
    exit /b %ERRORLEVEL%
)

echo Build succeeded
echo Running PMXGenerator_Test...
Release\PMXGenerator_Test.exe

echo Done
cd ..