# PMXFile_Generator Framework

A framework for generating PMX models programmatically. This framework allows you to create simple 3D models with physics properties that can be used in MMD (MikuMikuDance) or other applications that support the PMX format.

## Features

- Create basic geometric shapes (box, chain links)
- Create compound models (chain, multi-box)
- Add physics properties (rigid bodies, joints)
- Set up proper bone hierarchies
- Save generated models to PMX files

## Usage

### Basic Example: Creating a Box

```cpp
#include <Saba/Model/MMD/PMXFile_Generator.h>

using namespace saba;
using namespace glm;

int main()
{
    PMXFile_Generator generator;
    
    // Define a box
    BoxShape box;
    box.size = vec3(2.0f, 1.0f, 3.0f);
    box.position = vec3(0.0f, 1.0f, 0.0f);
    box.rotation = vec3(0.0f, 0.0f, 0.0f);
    
    // Create the box model
    generator.createBox(box);
    
    // Save to file
    generator.saveToFile("box_model.pmx");
    
    return 0;
}
```

### Creating a Chain

```cpp
#include <Saba/Model/MMD/PMXFile_Generator.h>
#include <vector>

using namespace saba;
using namespace glm;

int main()
{
    PMXFile_Generator generator;
    
    // Define chain links
    std::vector<ChainLink> links;
    
    // Create 5 links in a vertical chain
    for (int i = 0; i < 5; i++) {
        ChainLink link;
        link.radius = 0.5f;
        link.length = 1.0f;
        link.position = vec3(0.0f, 5.0f - i * 1.2f, 0.0f);
        link.rotation = vec3(0.0f, 0.0f, half_pi<float>());
        links.push_back(link);
    }
    
    // Create the chain model
    generator.createChain(links);
    
    // Save to file
    generator.saveToFile("chain_model.pmx");
    
    return 0;
}
```

### Creating a Cube (Multi-Box)

```cpp
#include <Saba/Model/MMD/PMXFile_Generator.h>
#include <vector>

using namespace saba;
using namespace glm;

int main()
{
    PMXFile_Generator generator;
    
    // Define boxes for each face of the cube
    std::vector<BoxShape> boxes;
    float size = 5.0f;
    float thickness = 0.2f;
    
    // Front face
    BoxShape front;
    front.size = vec3(size, size, thickness);
    front.position = vec3(0.0f, 0.0f, size/2);
    boxes.push_back(front);
    
    // Back face
    BoxShape back;
    back.size = vec3(size, size, thickness);
    back.position = vec3(0.0f, 0.0f, -size/2);
    boxes.push_back(back);
    
    // Left face
    BoxShape left;
    left.size = vec3(thickness, size, size);
    left.position = vec3(-size/2, 0.0f, 0.0f);
    boxes.push_back(left);
    
    // Right face
    BoxShape right;
    right.size = vec3(thickness, size, size);
    right.position = vec3(size/2, 0.0f, 0.0f);
    boxes.push_back(right);
    
    // Top face
    BoxShape top;
    top.size = vec3(size, thickness, size);
    top.position = vec3(0.0f, size/2, 0.0f);
    boxes.push_back(top);
    
    // Bottom face
    BoxShape bottom;
    bottom.size = vec3(size, thickness, size);
    bottom.position = vec3(0.0f, -size/2, 0.0f);
    boxes.push_back(bottom);
    
    // Create the multi-box model
    generator.createMultiBox(boxes);
    
    // Save to file
    generator.saveToFile("cube_model.pmx");
    
    return 0;
}
```

### Advanced Usage: Manual Model Creation

For more complex models, you can use the low-level API to manually create vertices, faces, bones, rigid bodies, and joints:

```cpp
#include <Saba/Model/MMD/PMXFile_Generator.h>

using namespace saba;
using namespace glm;

int main()
{
    PMXFile_Generator generator;
    
    // Initialize a new model
    generator.initialize("CustomModel", "A custom model created programmatically");
    
    // Add vertices
    int32_t v0 = generator.addVertex(vec3(-1.0f, 0.0f, 0.0f), vec3(-1.0f, 0.0f, 0.0f), vec2(0.0f, 0.0f));
    int32_t v1 = generator.addVertex(vec3(1.0f, 0.0f, 0.0f), vec3(1.0f, 0.0f, 0.0f), vec2(1.0f, 0.0f));
    int32_t v2 = generator.addVertex(vec3(0.0f, 1.0f, 0.0f), vec3(0.0f, 1.0f, 0.0f), vec2(0.5f, 1.0f));
    
    // Add a face (triangle)
    generator.addFace(v0, v1, v2);
    
    // Add a material
    generator.createDefaultMaterial(1); // 1 face
    
    // Add bones
    int32_t rootBone = generator.addBone("Root", vec3(0.0f, 0.0f, 0.0f), -1);
    int32_t childBone = generator.addBone("Child", vec3(0.0f, 1.0f, 0.0f), rootBone);
    
    // Add rigid bodies
    RigidBodyDef rbDef;
    rbDef.name = "Body";
    rbDef.shape = PMXRigidbody::Shape::Box;
    rbDef.size = vec3(2.0f, 1.0f, 1.0f);
    rbDef.position = vec3(0.0f, 0.5f, 0.0f);
    rbDef.boneIndex = rootBone;
    int32_t rbIndex = generator.addRigidBody(rbDef);
    
    // Save to file
    generator.saveToFile("custom_model.pmx");
    
    return 0;
}
```

## Model Types

### Box

A simple box with a single rigid body.

### Chain

A series of connected links with rigid bodies and joints between them. The first link is static, and the rest are dynamic.

### Multi-Box

Multiple boxes connected with joints. Useful for creating compound objects like a cube with separate faces.

## Bone Weighting

### Understanding Bone Weight Types

PMX models support several types of bone weighting which determine how vertices are influenced by different bones:

1. **BDEF1**: Single bone influence. Each vertex is controlled by exactly one bone.
2. **BDEF2**: Two bone influence with blending. Each vertex can be smoothly blended between two bones.
3. **BDEF4**: Four bone influence with blending. Each vertex can be influenced by up to four bones.
4. **SDEF**: Spherical Deformation (advanced).
5. **QDEF**: Dual Quaternion Deformation (advanced).

### Best Practices for Bone Weighting

#### When to Use Each Weight Type

- **BDEF1**: Use for rigid objects that should not deform (e.g., static props).
- **BDEF2**: Use for most deformable objects, including chains, ropes, and articulated parts.
- **BDEF4**: Use for complex joints like shoulders or hips where more blending is needed.

#### Common Issues and Solutions

**Issue**: All vertices assigned to a single bone (bone 0) using BDEF1, causing stiff, unrealistic deformation.
- **Solution**: When creating meshes that span multiple bones (like chains or ropes), always use a function that supports proper bone weighting between adjacent bones, such as `createCylinderMesh()` or `createChainLinkMesh()` with `prevBoneIndex` and `nextBoneIndex` parameters.

**Example: Creating a Chain with Proper Bone Weighting**
```cpp
// Create a chain with proper bone weighting
PMXFile_Generator generator;

// Define chain links
std::vector<ChainLink> links;
for (int i = 0; i < 5; i++) {
    ChainLink link;
    link.radius = 0.3f;
    link.length = 1.0f;
    link.position = vec3(0.0f, i * 1.5f, 0.0f);
    
    // Alternate the orientation for a more natural chain appearance
    if (i % 2 == 0) {
        link.rotation = vec3(0.0f, 0.0f, 0.0f);
    } else {
        link.rotation = vec3(0.0f, glm::half_pi<float>(), 0.0f);
    }
    
    links.push_back(link);
}

// Create the chain model - this automatically handles proper bone weighting between links
generator.createChain(links, false);
```

## Physics Properties

The framework supports the following physics properties:

- **Rigid Bodies**: Define the physical shape, mass, and behavior of objects
- **Joints**: Connect rigid bodies with constraints on movement and rotation
- **Bone Hierarchy**: Define the skeletal structure of the model

## Requirements

- GLM (OpenGL Mathematics) library
- Saba library (for PMX file format support)

## License

This framework is distributed under the MIT License.
