// Copyright (C) 2002-2012 <PERSON><PERSON>
// This file is part of the "Irrlicht Engine".
// For conditions of distribution and use, see copyright notice in irrlicht.h

#pragma once
#include "saba/Model/MMD/MMDNode.h"
#include "saba/Model/MMD/MMDPhysics.h"

#include "MmdNodeHandler.h"
#include <vector>
#define MAX_KICK 3600

#define PHY_CATCH_BALL  0
#define MAX_OBJ_LOCKABLE_AGE    3



namespace EQVisual {
	class EQV;

}
namespace irr::scene
{
	class PhyObjManager;
	extern PhyObjManager* gPoMan[8];



	struct ObjSinger;
	class IrrMMD;
	class IrrSaba;

	class ObjHandler;

	struct PhyObj :public saba::MMDNode 
	{
		PhyObj(int cc, PhyObjParam _pm);
		virtual ~PhyObj() {
			//DP(("del obj %d",id));
		}
		
		virtual const glm::mat4& GetGlobalTransform() const  {			
			return mGlobalTransform;		}
		virtual const glm::mat4 getMatT(int type) const {
			return mGlobalTransform;
		}
		//operator saba::MMDRigidBody*() const { return rb; }
		int uid=-1; //record id , unique
		int id=0; //ball id
		PhyObjParam pm;
		saba::MMDRigidBody* rb{};		
		float timer = 6,timerBack=0.f, age = 0.f,ageLast=0.f, lockableAge = MAX_OBJ_LOCKABLE_AGE, lockPower = 1;
		float startPhyTime = 0;
		IrrSaba* sb{}, * sbLastHit{}, * oTgtSb{}, * throwingBySb{}, * lastGrabSb{};
		std::vector<saba::MMDNode*> ndLock; 
		saba::MMDJoint* jtB2A{};			saba::MMDRigidBody* vtxFwRb{};
		std::vector < irr::scene::IrrSaba* > subSns;
		ENdId lockNdId = eNdIdNone, lockNdIdLast = eNdIdNone;
		int stage = 0; float stageTime = 0;
		int singerId = -1;
		int frame = 0;
		int fwC = 0, hitGroundCC=0;

		int grabbed = 0;  saba::AtkObjData* grabBy{}; 
		int grabStage = 0;

		bool fwcvt = false,fwcvtForce=false; int cvted = 0;
		irr::video::SColorf scf;
		float3 vel, pos, lvel, llvel, lpos, dtvel, dtpos, acceleration, velFromDtPos;
		float accLen=0;
		float resetCldFltCD = 0.f;
		int mmdHitCount=0, impactCount = 0;
		std::map<void*, int> leaveCount;
		float killY = -100.f;
		std::function<void()> onKillY;
		std::function<void()> onHitBySb;
		float centerForceMul = 0.f;
		int voiceFxCount = 0; irr::scene::IrrSaba* lastVoiceFxSb{};
		int hitSbCC = 0;
		bool isflyingToSb = false;int flyingToFlag = 0; saba::MMDNode* flyingToSbNode{};

		void setScale(glm::vec3 sc);


 		void updateFrame(float deltaTime);
		
		void resetStatus() {
			age = 0;  singerId = -1; leaveCount.clear(); mmdHitCount = 0; oTgtSb = nullptr; 
		}
		bool canAtkBy(int nid) {
			
			return nid< MMD_NID_MAX && (pm.atkFlag & (1 << nid));
		}
		bool ageLockable() {
			return age < lockableAge;
		}
		void ndLock_remove(saba::MMDNode*nd){
			for (auto it = ndLock.begin(); it != ndLock.end();)			
				if (*it == nd)	it = ndLock.erase(it);	else ++it;			
		}
		void disableHitFor(float time) { if (time <= 0.0001f) return; rb->setCollideFilterMask(1, 0); resetCldFltCD = time; }
 
	//private:
		glm::mat4 mGlobalTransform = glm::mat4(1);
	};

	struct ObjSinger
	{
		int objId = -1;
	};

	struct PhyObjManagerParam {
		irr::scene::ISceneManager *sm;
		saba::MMDPhysics* phy;
		EQVisual::EQV* eqv;
		irr::scene::IrrMMD* mmd;
 
	};

	class PhyObjManager
	{
		friend class ObjHandler;
	public:		
		PhyObjManager(PhyObjManagerParam pm);
		~PhyObjManager();
		std::vector<PhyObj*> phyObjs, phyObjsS,toDelPhyObjs;

		PhyObj* getLastObj(); PhyObj* getLastVisibleObj();
		void reflectPhyToSn();
		void updateBegin(float deltaTime, int step, int stepCount);
		void updateObjScale(irr::scene::PhyObj& o);
		void updateEnd(float deltaTime, int step, int stepCount);
		void freeSceneNodes();
		void updateHitAndVtxMat(float stepTime, int stepCount);

		void calculatePositionAndSpeed(PhyObjParam& pm, saba::PMXRigidbody& ci, ISceneManager* sm, IrrSaba* sb0, irr::core::vector3df& spd);

		void removeObjRes(irr::scene::PhyObj& o);
		void removeObj(PhyObj *o);
		void removeAllObjs();
		void removeTagObj(uint32_t tag);
		void clearAndResetRec();
		void initSetSb0(IrrSaba* sb);
		
		PhyObj* addObj(PhyObjParam& pm);
		void modObj(PhyObj* o, PhyObjParam& pm, uint32_t modFlag); //1=vel, 2= angVel, 4=pos, 8=rtt, 0x10 = size scale, 0x20 = densityMul,
		saba::MMDRigidBody* addAnchor(glm::vec3 pos);
		//void onSabaRelease(IrrSaba* sb);
		irr::core::matrix4 mmdBaseMat, matAbsInv;
		struct ObjRecord {
			PhyObj* ifObj;
		} *objRec{};
		
		float ballTime = 1.f;
		ObjHandler* ohl[8]={};
		EQVisual::EQV* Eqv{};
		PhyObj* groundPo{};
		saba::MMDRigidBody* groundRb{};
	private:
		
		saba::MMDPhysics* Phy{}; 
		irr::scene::IrrMMD* mmd;
		IrrSaba* sb0{};
		static inline int objCC = 0;
		std::array<ObjSinger, 8> singers;
		int curSinger = 0;
		PhyObjManagerParam Pm;
		int objIdCC = 1;
		irr::u32 fixedObjColor = 0;// 0xFFFFFFFF; //0;// 0xFFFF0000;
	};


	class ObjHandler
	{
	public:
		ObjHandler(PhyObjManager* pom) :Pom(pom) {}
		virtual bool onHitSabaNodeRb(PhyObj& o, saba::MMDRigidBody* rb) = 0;
		virtual bool onUpdate(irr::scene::PhyObj& o, float stepTime) = 0;

		PhyObjManager* Pom{};
	};

	class ObjHandler_Conn:public ObjHandler
	{
	public:
		ObjHandler_Conn(PhyObjManager* pom) :ObjHandler(pom) {}
		bool onHitSabaNodeRb(PhyObj& o, saba::MMDRigidBody* nodeRb) override;
		bool onUpdate(irr::scene::PhyObj& o, float stepTime) override;
		void setMode(int m);
	private:
		int mode = 0;  // 0:yao tail  1:head 2tails
		int nodeC = 1, tgtNd=0, connCC=0;
		saba::MMDNode* nodes[MAX_PHO_CONN];
		glm::vec3 initOfs[MAX_PHO_CONN]{};
		glm::vec3 rootLockRttVal[MAX_PHO_CONN]{};
		uint32_t colors[MAX_PHO_CONN] = { 0xFFf5d59f,0xFF0080FF,0xFFFF0000,0xFFFF0000,0xFFFF0000,0xFFFF0000,0xFFFF0000,0xFFFF0000,0xFFFF0000,0xFFFF0000 };
		bool rootLockRtt= false;

	};
	
	class ObjHandler_OppaiSaver :public ObjHandler
	{
	public:
		ObjHandler_OppaiSaver(PhyObjManager* pom) :ObjHandler(pom) {}
		bool onHitSabaNodeRb(PhyObj& o, saba::MMDRigidBody* nodeRb) override;
		bool onUpdate(irr::scene::PhyObj& o, float stepTime) override;
	private:
		int mode = 0;  
 

	};


	void actAnimPm(irr::scene::IrrSaba* sb, bool setActId, int actId, irr::scene::PhyObjParam& pm);

}

