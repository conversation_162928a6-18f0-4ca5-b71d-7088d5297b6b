<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|Win32">
      <Configuration>Profile</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\CommonStaticLib\winUtils.h" />
    <ClInclude Include="..\UaIrrlicht\source\Irrlicht\VulkanRenderer\shader\GsParticleShared.h" />
    <ClInclude Include="app\ArMmPLayer\AppMainAMP.h" />
    <ClInclude Include="app\ArMmPLayer\FL_ArEditor.h" />
    <ClInclude Include="app\ArMmPLayer\SnArItem.h" />
    <ClInclude Include="app\ArMmPLayer\SnArRoot.h" />
    <ClInclude Include="app\MusicFirework\AppBase.h" />
    <ClInclude Include="app\MusicFirework\AppMain.h" />
    <ClInclude Include="app\MusicFirework\AppMainTextFw.h" />
    <ClInclude Include="app\MusicFirework\DbgHelpers.h" />
    <ClInclude Include="app\MusicFirework\UndoList.h" />
    <ClInclude Include="src\AppTypes.h" />
    <ClInclude Include="src\cppIncDefine.h" />
    <ClInclude Include="src\DataRecorderBase.h" />
    <ClInclude Include="src\dsp\EQMan.h" />
    <ClInclude Include="src\dsp\EqTypes.h" />
    <ClInclude Include="src\dsp\FFT.hpp" />
    <ClInclude Include="src\FFHelper\AssHelper.h" />
    <ClInclude Include="src\FFHelper\UaFfmpeg.h" />
    <ClInclude Include="src\FFHelper\UaFfmpegFile.h" />
    <ClInclude Include="src\ffi_shared_header.h" />
    <ClInclude Include="src\FlutterDartFFI.h" />
    <ClInclude Include="src\FT\FMAndroid.h" />
    <ClInclude Include="src\FT\FT2Man.h" />
    <ClInclude Include="src\FT\UaFontMetric.h" />
    <ClInclude Include="src\FwClock.h" />
    <ClInclude Include="src\FwCommon.h" />
    <ClInclude Include="src\FwManager.h" />
    <ClInclude Include="src\FwShaderEmu.h" />
    <ClInclude Include="src\IAppBase.h" />
    <ClInclude Include="src\ImgVideoEncoder.h" />
    <ClInclude Include="src\IrrFw\eqv\EQV.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvFwNodeHelper.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvHelpers.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvItemBar2DSn.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvItemBar3D.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvItemBar3DObj.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvItemSn.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvItemWave.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvItemWaveFw.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvItemWaveLine.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvItemWaveMesh.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvItemWaveStrip.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvLoader.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvNode.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvNodeBand.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvFwNode.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvNodeWave.h" />
    <ClInclude Include="src\IrrFw\eqv\EqvTouchActionManager.h" />
    <ClInclude Include="src\IrrFw\MrCsParticle.h" />
    <ClInclude Include="src\IrrFw\SnCsParticle.h" />
    <ClInclude Include="src\AppGlobal.h" />
    <ClInclude Include="src\IrrFw\SnGuQin.h" />
    <ClInclude Include="src\IrrFw\SnLevelWheel.h" />
    <ClInclude Include="src\IrrFw\SnPiano.h" />
    <ClInclude Include="src\IrrFw\SnWater.h" />
    <ClInclude Include="src\IrrFw\SvgMan.h" />
    <ClInclude Include="src\irrmmd\CCubeGridSceneNode.h" />
    <ClInclude Include="src\irrmmd\CharacterAttacker.h" />
    <ClInclude Include="src\irrmmd\CharacterCatcher.h" />
    <ClInclude Include="src\irrmmd\CInstancedMeshSceneNode.h" />
    <ClInclude Include="src\irrmmd\CLabelSceneNode.h" />
    <ClInclude Include="src\irrmmd\CLineGridSceneNode.h" />
    <ClInclude Include="src\irrmmd\CMidiPlateSceneNode.h" />
    <ClInclude Include="src\irrmmd\CVoxelMeshSceneNode.h" />
    <ClInclude Include="src\irrmmd\ImGuiMmdHelper.h" />
    <ClInclude Include="src\irrmmd\IrrMMD.h" />
    <ClInclude Include="src\irrmmd\irrSaba.h" />
    <ClInclude Include="src\irrmmd\MmdMidiPlayer.h" />
    <ClInclude Include="src\irrmmd\MmdNodeHandler.h" />
    <ClInclude Include="src\irrmmd\MmdNodePhyAnimator.h" />
    <ClInclude Include="src\irrmmd\MmdPhyAnimator.h" />
    <ClInclude Include="src\irrmmd\MmdPhysicsHelper.h" />
    <ClInclude Include="src\irrmmd\PhyObjMan.h" />
    <ClInclude Include="src\irrmmd\PhysicsHelper.h" />
    <ClInclude Include="src\irrmmd\sabaCloth.h" />
    <ClInclude Include="src\irrmmd\SbFwLauncher.h" />
    <ClInclude Include="src\irrmmd\SnPhyCloth.h" />
    <ClInclude Include="src\irrmmd\SnPhyFluid.h" />
    <ClInclude Include="src\irrmmd\SnPhyInflatable.h" />
    <ClInclude Include="src\irrmmd\SnPhyMesh.h" />
    <ClInclude Include="src\irrmmd\SnTestAI.h" />
    <ClInclude Include="src\irrmmd\sv\KawaiiLyricGenerator.h" />
    <ClInclude Include="src\irrmmd\VmdEventExt.h" />
    <ClInclude Include="src\LeapMan.h" />
    <ClInclude Include="src\MatrixRecorder.h" />
    <ClInclude Include="src\NetMan.h" />
    <ClInclude Include="src\PhysicsMan.h" />
    <ClInclude Include="src\PythonMan.h" />
    <ClInclude Include="src\ScopeGuard.h" />
    <ClInclude Include="src\ShaderToy.h" />
    <ClInclude Include="src\targetver.h" />
    <ClInclude Include="src\ThreadPool\ThreadPool.h" />
    <ClInclude Include="src\UaCommon.h" />
    <ClInclude Include="src\UaJsonSetting.h" />
    <ClInclude Include="src\UaLib.h" />
    <ClInclude Include="src\UaLibContext.h" />
    <ClInclude Include="src\UaLibEvtRcv.h" />
    <ClInclude Include="src\UaLibMain.h" />
    <ClInclude Include="src\UaLibStage.h" />
    <ClInclude Include="src\UaUtils.h" />
    <ClInclude Include="src\ulMedia\MediaProcessorAndroid.h" />
    <ClInclude Include="src\ulMedia\rgb2yuv.h" />
    <ClInclude Include="src\ulMedia\yuv2rgb.h" />
    <ClInclude Include="src\VideoFrameProcessor.h" />
    <ClInclude Include="src\VideoHelpers.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\SDK\LeapSDK\samples\ExampleConnection.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\CommonStaticLib\winUtils.cpp" />
    <ClCompile Include="app\ArMmPLayer\AppMainAMP.cpp" />
    <ClCompile Include="app\ArMmPLayer\AppMainAMP_P2.cpp" />
    <ClCompile Include="app\ArMmPLayer\SnArItem.cpp" />
    <ClCompile Include="app\ArMmPLayer\SnArRoot.cpp" />
    <ClCompile Include="app\MusicFirework\AppBase.cpp" />
    <ClCompile Include="app\MusicFirework\AppMainTextFw.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="app\MusicFirework\AppMainTextFwP2.cpp" />
    <ClCompile Include="src\AppGlobal.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\dsp\EQMan.cpp" />
    <ClCompile Include="src\FFHelper\AssHelper.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass_library.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass_parse.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass_strtod.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass_utils.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\FFHelper\UaFfmpeg.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\FFHelper\UaFfmpegFile.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\FlutterDartFFI.cpp" />
    <ClCompile Include="src\FT\FMAndroid.cpp" />
    <ClCompile Include="src\FT\FT2Man.cpp" />
    <ClCompile Include="src\FwClock.cpp" />
    <ClCompile Include="src\FwCommon.cpp" />
    <ClCompile Include="src\FwManager.cpp" />
    <ClCompile Include="src\FwShaderEmu.cpp" />
    <ClCompile Include="src\ImgVideoEncoder.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EQV.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvFw.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvHelpers.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemBar.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemBar2DSn.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemBar3D.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemBar3DObj.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemSn.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemWave.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemWaveFw.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemWaveLine.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemWaveMesh.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvItemWaveStrip.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvLoader.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvNode.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvNodeBand.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvFwNode.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvNodeWave.cpp" />
    <ClCompile Include="src\IrrFw\eqv\EqvTouchActionManager.cpp" />
    <ClCompile Include="src\IrrFw\MrCsParticle.cpp" />
    <ClCompile Include="src\IrrFw\SnCsParticle.cpp" />
    <ClCompile Include="src\IrrFw\SnGuQin.cpp" />
    <ClCompile Include="src\IrrFw\SnLevelWheel.cpp" />
    <ClCompile Include="src\IrrFw\SnPiano.cpp" />
    <ClCompile Include="src\IrrFw\SnWater.cpp" />
    <ClCompile Include="src\IrrFw\SvgMan.cpp" />
    <ClCompile Include="src\irrmmd\CCubeGridSceneNode.cpp" />
    <ClCompile Include="src\irrmmd\CharacterAttacker.cpp" />
    <ClCompile Include="src\irrmmd\CharacterCatcher.cpp" />
    <ClCompile Include="src\irrmmd\CInstancedMeshSceneNode.cpp" />
    <ClCompile Include="src\irrmmd\CLabelSceneNode.cpp" />
    <ClCompile Include="src\irrmmd\CLineGridSceneNode.cpp" />
    <ClCompile Include="src\irrmmd\CMidiPlateSceneNode.cpp" />
    <ClCompile Include="src\irrmmd\CVoxelMeshSceneNode.cpp" />
    <ClCompile Include="src\irrmmd\ImGuiMmdHelper.cpp" />
    <ClCompile Include="src\irrmmd\IrrMMD.cpp" />
    <ClCompile Include="src\irrmmd\irrSaba.cpp" />
    <ClCompile Include="src\irrmmd\irrSabaAnimation.cpp" />
    <ClCompile Include="src\irrmmd\irrSabaPhysics.cpp" />
    <ClCompile Include="src\irrmmd\irrSabaWalk.cpp" />
    <ClCompile Include="src\irrmmd\MmdMidiPlayer.cpp" />
    <ClCompile Include="src\irrmmd\MmdNodeHandler.cpp" />
    <ClCompile Include="src\irrmmd\MmdNodePhyAnimator.cpp" />
    <ClCompile Include="src\irrmmd\MmdPhyAnimator.cpp" />
    <ClCompile Include="src\irrmmd\MmdPhyAnimator_part2.cpp" />
    <ClCompile Include="src\irrmmd\MmdPhysicsHelper.cpp" />
    <ClCompile Include="src\irrmmd\PhyObjMan.cpp" />
    <ClCompile Include="src\irrmmd\PhysicsHelper.cpp" />
    <ClCompile Include="src\irrmmd\sabaCloth.cpp" />
    <ClCompile Include="src\irrmmd\SbFwLauncher.cpp" />
    <ClCompile Include="src\irrmmd\SnPhyCloth.cpp" />
    <ClCompile Include="src\irrmmd\SnPhyFluid.cpp" />
    <ClCompile Include="src\irrmmd\SnPhyInflatable.cpp" />
    <ClCompile Include="src\irrmmd\SnPhyMesh.cpp" />
    <ClCompile Include="src\irrmmd\SnTestAI.cpp" />
    <ClCompile Include="src\irrmmd\sv\KawaiiLyricGenerator.cpp" />
    <ClCompile Include="src\irrmmd\VmdEventExt.cpp" />
    <ClCompile Include="src\LeapMan.cpp" />
    <ClCompile Include="src\MatrixRecorder.cpp" />
    <ClCompile Include="src\NetMan.cpp" />
    <ClCompile Include="src\PythonMan.cpp" />
    <ClCompile Include="src\ShaderToy.cpp" />
    <ClCompile Include="src\UaJsonSetting.cpp" />
    <ClCompile Include="src\UaLibContext.cpp" />
    <ClCompile Include="src\UaLibEvtRcv.cpp" />
    <ClCompile Include="src\UaLibMain.cpp" />
    <ClCompile Include="src\UaLibStage.cpp" />
    <ClCompile Include="src\UaUtils.cpp" />
    <ClCompile Include="src\ulMedia\MediaProcessorAndroid.cpp" />
    <ClCompile Include="src\ulMedia\rgb2yuv.cpp" />
    <ClCompile Include="src\ulMedia\yuv2rgb.cpp" />
    <ClCompile Include="src\VideoFrameProcessor.cpp" />
    <ClCompile Include="src\VideoHelpers.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="cpp.hint" />
    <None Include="packages.config" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>15.0</VCProjectVersion>
    <ProjectGuid>{CDFC48BC-35A2-4ADE-AAE2-B0822229C300}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>AppMainLib</RootNamespace>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Profile|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <LibraryPath>D:\SDK\ffmpeg-dev\lib;$(LibraryPath)</LibraryPath>
    <IntDir>$(SolutionDir)$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IntDir>$(SolutionDir)$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IntDir>$(SolutionDir)$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;APP_TEXTFW;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>src;include;../UaIrrlicht/include;../UaIrrlicht/source/Irrlicht;../CommonStaticLib;D:\SDK\LeapSDK\include;../UaIrrlicht/external/glm;../UaIrrlicht/external/imgui;D:\sdk\VulkanSDK\Include;D:\SDK\freetype\include;../CommonStaticLib/src;../CommonStaticLib/saba/src;D:\SDK\ffmpeg-dev\include;d:\sdk\bullet3\src;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\include;D:\SDK\PhysX\physx\include;D:\SDK\PhysX\physx\include\cudamanager;D:\SDK\PhysX\physx\source\physxextensions\src;D:\SDK\PhysX\physx\source\physxgpuextensions\src;D:\SDK\PhysX\physx\source\foundation\include;D:\SDK\PhysX\physx\source\geomutils\include;D:\SDK\PhysX\physx\snippets\graphics\include;../UaIrrlicht/;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <PrecompiledHeaderFile>appGlobal.h</PrecompiledHeaderFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <AdditionalOptions>/d1reportSingleClassLayout:IrrSaba %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>src;include;../UaIrrlicht/include;../UaIrrlicht/source/Irrlicht;../CommonStaticLib;D:\SDK\LeapSDK\include;../UaIrrlicht/external/glm;../UaIrrlicht/external/imgui;D:\sdk\VulkanSDK\Include;D:\SDK\freetype\include;../CommonStaticLib/src;../CommonStaticLib/saba/src;D:\SDK\ffmpeg-dev\include;d:\sdk\bullet3\src;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\include;D:\SDK\PhysX\physx\include;D:\SDK\PhysX\physx\include\cudamanager;D:\SDK\PhysX\physx\source\physxextensions\src;D:\SDK\PhysX\physx\source\physxgpuextensions\src;D:\SDK\PhysX\physx\source\foundation\include;D:\SDK\PhysX\physx\source\geomutils\include;D:\SDK\PhysX\physx\snippets\graphics\include;../UaIrrlicht/;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Full</Optimization>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <PrecompiledHeaderFile>appGlobal.h</PrecompiledHeaderFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>src;include;../UaIrrlicht/include;../UaIrrlicht/source/Irrlicht;../CommonStaticLib;../SDK/DGEngine;../UaIrrlicht/external/glm;D:\sdk\VulkanSDK\Include;../CommonStaticLib/src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>src;include;../UaIrrlicht/include;../UaIrrlicht/source/Irrlicht;../CommonStaticLib;../SDK/DGEngine;../UaIrrlicht/external/glm;D:\sdk\VulkanSDK\Include;../CommonStaticLib/src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>src;include;../UaIrrlicht/include;../UaIrrlicht/source/Irrlicht;../CommonStaticLib;../SDK/DGEngine;../UaIrrlicht/external/glm;D:\sdk\VulkanSDK\Include;../CommonStaticLib/src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;APP_TEXTFW;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>src;include;../UaIrrlicht/include;../UaIrrlicht/source/Irrlicht;../CommonStaticLib;D:\SDK\LeapSDK\include;../UaIrrlicht/external/glm;../UaIrrlicht/external/imgui;D:\sdk\VulkanSDK\Include;D:\SDK\freetype\include;../CommonStaticLib/src;../CommonStaticLib/saba/src;D:\SDK\ffmpeg-dev\include;d:\sdk\bullet3\src;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\include;D:\SDK\PhysX\physx\include;D:\SDK\PhysX\physx\include\cudamanager;D:\SDK\PhysX\physx\source\physxextensions\src;D:\SDK\PhysX\physx\source\physxgpuextensions\src;D:\SDK\PhysX\physx\source\foundation\include;D:\SDK\PhysX\physx\source\geomutils\include;D:\SDK\PhysX\physx\snippets\graphics\include;../UaIrrlicht/;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <PrecompiledHeaderFile>appGlobal.h</PrecompiledHeaderFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="..\VkUpApp\packages\Microsoft.Windows.ImplementationLibrary.1.0.240122.1\build\native\Microsoft.Windows.ImplementationLibrary.targets" Condition="Exists('..\VkUpApp\packages\Microsoft.Windows.ImplementationLibrary.1.0.240122.1\build\native\Microsoft.Windows.ImplementationLibrary.targets')" />
  </ImportGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\VkUpApp\packages\Microsoft.Windows.ImplementationLibrary.1.0.240122.1\build\native\Microsoft.Windows.ImplementationLibrary.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\VkUpApp\packages\Microsoft.Windows.ImplementationLibrary.1.0.240122.1\build\native\Microsoft.Windows.ImplementationLibrary.targets'))" />
  </Target>
</Project>