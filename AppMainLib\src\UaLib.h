﻿#pragma once
#include "Helpers/UPCOMMON.h"
#include "VulkanRenderer/shader/GsParticleShared.h"  //MultiRenderQueue
#include "VulkanRenderer/VkHeader.h"
#ifdef _WIN32
#define IS_WIN 1
#ifdef _DEBUG
#define IS_WIN_DBG 1
#else
#define IS_WIN_DBG 0
#endif
#define APP_FPS  60  //game time FPS, system always 60fps
#else
#define IS_WIN 0
#define APP_FPS 30  
#define IS_WIN_DBG 0
#endif

#define MULTI_VIEW_COUNT			1

#define HAS_AR_EDITOR				1


#define FIX_FRAMETIME 1  //VIDEO input output must 1


#define USE_LEAP					1//LEAP MOTION
#define USE_JOYSTICK		1
#define JOYSTICK_CAMERA		1

#define USE_OXR						0//IS_WIN
#define OXR_CONTROL_OBJ				0 // 0:CAM, 0x10 xrMode, 0x11 rt1
#define UE_PLUGIN					0// 0:mmd plugin 1:UE plugin
#define MULTI_THREAD_RENDERING		0

#define HDR_ON			1	  //VkDriver.cpp SWAPCHAIN_HDR_IDX
#define HDR_FRAMEBUF_FORMAT  1 //  1:ECF_A2B10G10R10UN 2:ECF_A16B16G16R16F
#define HDR_USE_MIDRT	1

#define IS_VMD_CONVERTER  0
#if IS_VMD_CONVERTER
#undef APP_FPS 
#define APP_FPS 30
#endif
#define RELEASE_APP IS_VMD_CONVERTER
#define USE_FFMPEG !RELEASE_APP
#define USE_PYTHON 0//(IS_WIN && !RELEASE_APP)

//		IRR_VSYNC 
 
//#define APP_THREAD_POOL_NEED_WAIT   0   //not important buffer data like normal calc
#define HAS_MMD  (!UE_PLUGIN &&	  1)//MMD
#define MMD_CONTROL_SD				0
#define SAVE_OTHER_IMAGES			MMD_CONTROL_SD // depth, mask. For AI process

#define FW_WATER_MIRROR		IS_WIN
#define KALEIDO_ART			0 
#define USE_IFPPT_TXT	0//KALEIDO_ART
#define ARROOT_SCALE	1

#define MMD_GPU_TRANSFORM 1
#define MMD_OVERRIDE_TIME 0

#define MIDI_DELAY 3 
#define MMD_MIDI 1
#define MMD_MIDI_MODE 10
#define MMD_AR_BALL		0
#define MMD_ACTION_ANIM 	1
#define MMD_ACTION_TIME		1
#define MMD_PLAY_GUQIN  0		// midiEventToEmb choose chanel
#define USE_SVG				1//!MMD_PLAY_GUQIN//IS_WIN

#define CAM_MMD_SELFIE	0
#define MMD_PMX_HAND		1
#define MMD_FINGERS		1

#define MMD_LEAP_FINGER  1   //1:hand 2:foot

#define MMD_HAND_PTCTRL  USE_LEAP//  
#define MMD_ARM1_IK      (USE_LEAP && 0)

#define MMD_POSE_VIEWER 0
#define MMD_MTG_HANDSEAL 0 //�Yӡ
#define MMD_MONTAGE (MMD_PLAY_GUQIN || MMD_ACTION_ANIM)  //��϶���
#define PTR_EVT_TO_MMD	1//HAS_MMD
#define MMD_OBJ_CONNECT 0
#define MMD_LOOKAT_SB0  0
#define MMD_PICK_CHARACTER_ONLY 0
#define EGGBALL 1

#define MMD_HAND_OXR (USE_OXR && 0 )
#define MMD_HAND_CAM (MMD_HAND_OXR &&0 )
#define MMD_REVERSE_PLAY     false

#define SVG_MMD_WRITE				0
#define SVG_MMD_RB					0//SVG_MMD_WRITE
#define SVG_MMD_RB_ON_MMDBASE		0
#define SVG_MMD_RB_HAS_SUBMMD		0
#define SVG_MMD_ONLY_FIRST_WRITE	0
#define SVG_MMD_WRITE_WITH_MOUTH 0
#define SVG_MMD_WRITE_PEN_LIGHT			0
#define SVG_MMD_WRITE_SAVE_VMD	0
#define SVG_MMD_WRITE_USING_LEG 0
#define SVG_MMD_WRITE_ON_LAND  0//SVG_MMD_WRITE_USING_LEG

#define SBATK_CATCHER				0 //mask arm:1,leg:2

#define MMD_ADD_WING		0
#define MMD_MOUTH_FROM_LYRIC			0//HAS_MMD
#define SBT_DELAY   0//(MMD_MOUTH_FROM_LYRIC?2:0)  //0
#define MMD_CTRL_ARM_IK_R (MMD_PLAY_GUQIN || PTR_EVT_TO_MMD) 
#define MMD_HAND_FW_MODE  0//HAS_MMD
#define MMD_LOOKAT_CAMERA	1 //!PTR_EVT_TO_MMD
#define MMD_SAVE_VMD_MORPH (HAS_MMD&& (MMD_MOUTH_FROM_LYRIC|| SVG_MMD_WRITE_SAVE_VMD)&& 0 )
#define MMD_SAVE_VMD_MOTION (HAS_MMD&&(MMD_SAVE_VMD_MORPH||IS_VMD_CONVERTER || SVG_MMD_WRITE_SAVE_VMD ||  0 ) )  // must MMD_SAVE_CAMVMD=0
#define MMD_SAVE_VMD_MOTION_ALLNODE 1
#define MMD_SAVE_CAMVMD		0//use saveCameraVmd() directly now:G - B - N ,  // (IS_WIN&&!MMD_SAVE_VMD_MOTION)//(HAS_MMD && 0)
#define MMD_JOYSTICK_HEAD	0
#define MMD_JOYSTICK_BODY	  USE_JOYSTICK
#define MMD_JOYSTICK_GAMECAST   0//USE_JOYSTICK
#define MMD_SAVE_PUNCH_MIDI (HAS_MMD && 0)
#define MMD_VIRTUAL_SINGER	(!MMD_CONTROL_SD && 1)
#define MMD_ATTACK_OBJ		1//		MMD_VIRTUAL_SINGER
#define MMD_COMBINE_CATCHER		1 //combine 2mmd dance
#define MMD_BAND_EQV		0
#define MMD_TREE_FW			1

#define MOUTH_DELAY_MS		-350
#define GEN_MIDI_ASS		0
#define MMD_CENTER_CTRL		0//SVG_MMD_WRITE
#define ASS_KARAOKE_REMOVE__ true // 

#define USE_BULLET_PHYSICS	1//HAS_MMD
#define PHS_HAS_SOFTBODY	0//FW_SUB_MATRIX // open FW_SUB_MATRIX



#define HAS_FW_FX			1


#define LOADBANDLIST_OFS 0


#define APP_HAS_CLOCK  0 //clock

#define APP_CUBE_SPHERE  0
#define IRR_GEN_CUBE_TO_SPHERE  0
#define IRR_GEN_CUBE_SPHERE_STEREO_3D 0 // seam

#define USE_TTS 0

#define FW_HAS_LAND			1
#define IRR_DRAW_MIRROR (FW_WATER_MIRROR&&!UE_PLUGIN&&IS_WIN&& !MMD_CONTROL_SD &&     1	 )
#define IRR_MIRROR_BLUR		1

#if KALEIDO_ART
#define FW_HAS_OIT  1
#define FW_DRAW_WATER  1
#else
#ifdef VKUPDLL
#define FW_HAS_OIT 0
#else
#define FW_HAS_OIT	( !MMD_CONTROL_SD && 1)// (!HAS_AR_EDITOR  && IS_WIN)//IS_WIN//
#endif
#define FW_DRAW_WATER (IRR_DRAW_MIRROR &&   0	)
#endif
#define FW_OCM_MUL (FW_HAS_OIT?1:0.5)
//==================================================================================================

#define DRAW_SHADER_TOY 0
#define DRAW_SHADER_TOY_BG	(DRAW_SHADER_TOY&&0)
#define DRAW_SHADER_TOY_SCENE_BG	(DRAW_SHADER_TOY&&1)
#define DRAW_SHADER_TOY_MMDTEX (DRAW_SHADER_TOY&&	0)
#define DRAW_SHADER_TOY_MMD_IRRTEX (DRAW_SHADER_TOY&&	0)


#define FW_USE_GS 0
#define FW_NO_GS (!FW_USE_GS)

#define HAS_ART_CANVAS 0
#define FW_USE_SSBO 1

#define CAM_RTT_VEL -0.25
#define CAM_MOV_VEL -1

#ifdef DBG_ON_PC
#define TEST_STROKE 0
#else
#define TEST_STROKE 0
#endif

#ifdef _WIN32
#define HAS_FT2MAN  1

#define HAS_FM_Android 0
#define HAS_MIDI (!UE_PLUGIN &&	1)
#define HAS_SHADOWMAP	(!UE_PLUGIN &&	 1  )
#elif defined(__ANDROID__)
#define HAS_FT2MAN  0
#define HAS_FM_Android 1
#define HAS_SHADOWMAP		0
#else
#define HAS_FT2MAN  0
#define HAS_FM_Android 0

#endif
#define USE_WIN_FONT (USE_XAML_ISLAND || 0)

//#include "AntTweakBar.h"
#define FW_BASE_VY 1000
#include "UaJsonSetting.h"
#include "UaCommon.h"
#include "Helpers/glmUtils.h"

#include "UaLibContext.h"
#include "UaLibStage.h"
#include "stlUtils.h"

#define MMD_SAVE_VMD (MMD_SAVE_VMD_MORPH || MMD_SAVE_VMD_MOTION || MMD_SAVE_CAMVMD)

#if DRONE_AR
#define MMD_SABA_SCALE 1.f//800.f//ar mmd real
#elif USE_AR_DATA
#define MMD_SABA_SCALE 100.f			// AR better use 100 to fit camera ruler
#else
#define MMD_SABA_SCALE 100.f//ar mmd real
#endif
#define MMD_SCALE_OLD  (MMD_SABA_SCALE / 100.f)
#define MMD_ZY_DUR 0.2f


#define AR_DATA_FPS 30


#define POLE_DANCE 0

#define GAMESCENE_PLATE 1
#define PLATE_PLAYER_SKIP			0