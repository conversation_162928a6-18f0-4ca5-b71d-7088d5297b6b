#pragma once
#include "AppTypes.h"
#include <Saba/Base/Path.h>
#include <Saba/Base/File.h>
#include <Saba/Base/UnicodeUtil.h>
#include <Saba/Base/Time.h>

//#include <Saba/Model/MMD/PMDModel.h>
#include <Saba/Model/MMD/PMXModel.h>
#include <Saba/Model/MMD/VMDFile.h>
#include <Saba/Model/MMD/VMDAnimation.h>
#include <Saba/Model/MMD/VMDCameraAnimation.h>
#include <Saba/Model/MMD/MMDPhysics.h>
#include <irrlicht.h>
#include <CEmptySceneNode.h>
#include <VulkanRenderer/VkFixedFunctionMaterialRenderer.h>
#include "MmdNodeHandler.h"
#include <UaJsonSetting.h>

#include "irrmmd/CLabelSceneNode.h"


#include <mmdFormats/vmd.h>
#include <mmdFormats/EncodingHelper.h>
#include <btBulletCollisionCommon.h>
#include <btBulletDynamicsCommon.h>
#define SMALL_CHARACTER_SCALE	0
#define SPRING_MODE_COUNT 4
#include "IrrFw/SvgMan.h"
#include "IrrFw/SnPiano.h"
#include "SnPhyCloth.h"
#include "SnPhyInflatable.h"
#include "Helpers/UpStrIdxRes.h"

#include "CharacterAttacker.h"
#include "CharacterCatcher.h"
//SABA COMPONENTS
#include "SbFwLauncher.h"
#include "VmdEventExt.h"

#define MMDFWS(T,N,P, V, C) 		do{ static int fid = Eqv->getFwIdxByFwIdStr((T),(N)); mmdFw((P), (fid), (V) , (C));}while(0) //FID will not change at runtime


class LeapMan;
namespace irr::video {
	class VkMaterialRenderer_MMD;
}
namespace EQVisual {
	class EQV;
}
namespace saba {
	class MmdPhysicsController;
}
namespace irr::scene
{
	class PhyObjManager;
	class MpaManager;

	class SabaCloth;
	enum class ESabaClass {
		SabaBase=0,SimpleItem,Cloth,
	};
	class UaBtDebugDraw;
	class SnArItem;
	class MmdNodeHandler_JoyGameCast;

	class SnArRoot;

	struct irrSabaParam
	{
		ESabaClass clsType= ESabaClass::SabaBase;
		int idx=-1;
		irr::io::path pmxFile,vmdFile,camVmdFile;
		saba::PMXFileCreateParam fcp;
		EQVisual::EQV* eqv{};
		ualib::UaLibContext* ctx{};
		//midilib::MidiMan* midi=nullptr;
		irr::scene::ISceneNode* snMmdRoot;
		float scale = 1.f;
		bool allowGPU = true;
		IrrMMD* mmd{};
		irr::core::vector3df relToParent;
		SnArItem* snArItem{};

		u32 pickData=0;

		bool needSaveCamVmd = false;
		bool activeNodeCam = false;  // setCurCamera
		bool skipLastKey = false;
		bool hasFW = false;
		bool receiveShadow = HAS_SHADOWMAP;
		bool drawCam=false;
		bool freeAllVtxData = false;
		bool handWrite = SVG_MMD_WRITE;
		bool allowAllNodePhysics = true;


	};
	struct SabaEmb {
		float timer,life, lastTimer;
		irr::core::vector3df tgt,tgL,curpos;
#if HAS_MIDI
		midilib::MidiEventStruct me;
		bool mePlayed ;
		int key,ch,vel;
#endif
	};
	struct SvNoteData
	{
		double time, dur;
		int key;
		bool rap;
		std::wstring lyric;
	};
	struct MpaLoadJsonParam {
		std::string  filepath;		
		bool swapLR = false;
		float playSpeed = 1.f;
		IrrSaba* setSb0{},*setSb1{};
	};

	struct PmxCB {

		int vtxCount, nodeCount, test1, test2;		float4 pad, pad2, pad3; //64 bytes;

		glm::mat4 transforms[SIMPLE_MMD_MAX_GPU_NODE_COUNT -1]; //-1 mat size for prev data
	};
	const int szofcb=sizeof(PmxCB);

	struct SabaShareData {
		irr::video::IHardwareBuffer* drawVertices{}, * drawIndexs{};
		irr::video::VkHardwareBuffer* mVbVtxBoneInfo{}, * mVbOrigPN{}; //gpu transform
		~SabaShareData() {
			if (drawIndexs) drawIndexs->drop();
			if (drawVertices) drawVertices->drop();
			if (mVbVtxBoneInfo) mVbVtxBoneInfo->drop();
			if (mVbOrigPN) mVbOrigPN->drop();
		}
	};

	struct MmdCompute {
		MmdCompute(video::IVideoDriver* driver);
		~MmdCompute();
		video::VkDriver* Driver;
		VkDevice Device;
		irr::video::VkMaterialRenderer_MMD* mrMMD{};
		bool gpuCamTrnasform = false;

		std::shared_ptr<VkFxUtil::VkFxBase> mFxCsMmdVtx;
		VkDescriptorSet  dsMMD=VK_NULL_HANDLE;
		VkCommandBuffer vkCmb;
		VkFence fence;
		uint32_t vtxCount = 0, nodeCount=0;
		vks::Buffer	mCbMats,mVbMorph;
		int last_morphVtxMin = 0, last_morphVtxMax = 0;
		struct PosNorm {
			float3 pos; float u;
			float3 norm; float v;
			float3 dtPos; float pad;

		};
		PosNorm* pn{};
		SabaShareData* sbd{};
		irr::video::VkHardwareBuffer* mVbPosNorm{};// , * mVbMorph{};
		void prepareGPUTransform(saba::MMDModel* model);
        void fillCmdBUf(int vtxCount);
		void runCS(saba::MMDModel* model);
    };

	class IrrSaba : public CEmptySceneNode, public EQVisual::SvgDrawData
	{
		friend class SbFwLauncher;
		friend class IrrMMD;
		friend class MmdNodeHandler_Writer;
		friend class MmdNodeHandler_JoyDance;
		friend class MmdNodeHandler_JoyGameCast;

	protected:
		int itemIdx = -1;
		std::string strModelID;
	public:

		IrrSaba(ISceneNode* parent, ISceneManager* mgr, s32 id, irrSabaParam pm);
		virtual ~IrrSaba();
		void loadCamera(irr::io::path p, bool drawOnly);

		virtual void OnRegisterSceneNode() override;
		virtual void updateAbsolutePosition() override;
		virtual void OnAnimate(u32 timeMs) override;
		virtual void updatePhysicsStep(int step, int stepCount);

		void actionOnPose();

		void phyAnimationUpdate(float stepTime, int step, int stepCount);

		virtual void preRender() override;
		virtual void render() override;
		void renderPhysxDbgVisualLines(saba::MMDPhysics* ph);
		virtual const core::aabbox3d<f32>& getBoundingBox() const _IRR_OVERRIDE_;
		virtual video::SMaterial& getMaterial(u32 num) override
		{
			return MatBase;
		}
		//ckadd
		virtual void updateTransform();

		bool loadModel(irr::io::path file);

		void launchNodeFw(saba::MMDNode& node);
		void launchCbNodeFw(saba::MMDNode& node, float countMul = 1, float spdMul=1);
		void onClear();
		struct phyForceParam {
			saba::MMDNode* node; float fY; std::string fw; int fwSubNode; float hueAdd = 0.f;
		};
		void phyForceOnNode(const phyForceParam& pm);



		struct phyLAParam {
			const glm::vec3& pos;
			float powHead=10.f,powHeadMax=20.f;
			float powEye=20.f;
			float powCatEar = 0.f;
			float angleMul=1.f;
			float disMin = 5.f;
			float disMax = 20.f;
			float disRatioPowMul = 0.25f;
			float disRatioAngMul = 0.5f;
		};
		void phyLookAt(phyLAParam pm);
		int lastLookObjId = 0;

		void parseMmdMatMemo(saba::MMDMaterial& mmdMat);
		void createIkLabels(int labelFlag, saba::MMDNode* rootNode = nullptr, bool isRight = false);

		void nodeToFwSrc(saba::NodeExtData& ne, irr::core::matrix4& matAbs);

		void onNodeFirework(irr::core::vector3df& dtPos, saba::MMDNode& node);
		saba::MMDNode* setNodeFlag(const wchar_t* name, saba::EId eid, uint32_t flag, bool addFlag = true);
		saba::MMDNode* findNode(const std::wstring name);
		saba::MMDNode* setNodeFlag(int nodeIdx, saba::EId eid, uint32_t flag, bool addFlag = true);

		saba::MMDMorph* setMorphFlag(const wchar_t* name, int mid, uint32_t flag, bool addFlag = true);
		void setMorphValue(const wchar_t* name, float val);

		//animation
		bool loadMotion(io::path filepath, uint32_t addKeyFrame=0); //load VPD VMD
		bool loadAnimation(irr::io::path file, bool isCamAni = false, bool resetPhy = true); //load VMD
		void clearAnimation();
		bool hasAnimation() { return Vmd.get() != nullptr; }
		void ensureNodeControl(saba::MMDNode* node);
		void addAnimationKeyFrame(uint32_t frame);
		void aniSetNodeKeyFrame(saba::MMDNode* node, uint32_t frame);

		void loadActionAnimations(irr::io::path file);
		bool loadAppendAnimation(irr::io::path file);
		bool loadBaseAnimation(int id, irr::io::path file); // now only id=1
		bool loadPose(irr::io::path file, int slot);	//load VPD
		bool loadPoseFromJson(Json::Value jsv);
		void onJoyEvent(const irr::SEvent::SJoystickEvent& je);
		void resetAnimation(float startTime = 0.f, bool reverse = false, int phySyncFrame = 30);
		//bool togglePlaying() {	return mPlaying = !mPlaying; 		}
		void toggleLnv(int mode, int add = 1);
		void setPlaying(bool pl);
		bool getPlaying() { return mPlaying; }
		bool getAnimationPlaying() { return mPlaying && animeTime * 30.f < Vmd->GetMaxMotionTimeFrame(); }
		void setCameraPlaying(bool cpl) { mCamPlaying = cpl; }
		void setAnimationSpeed(float s) { mmdSpeedMul = s; Pmx->animSpdMul = s; }
		float getAnimationSpeed() { return mmdSpeedMul; }
		float getAnimationRatio() { return std::min(1.f, animeTime * 30.f / (Vmd ? Vmd->GetMaxMotionTimeFrame() : 60)); }
		void setSpeechId(int sid, float dur, float startRatio = 0.f);  // mouth action id
		void setPhysics(bool isOn) {
			mModel->needPhysics = isOn;
		}
		bool attachToParentNode(const wchar_t* name);

		bool connectPhyNodeToParent(const saba::MMDNode* node, const saba::MMDNode* ndParent, float3 ofs );

		saba::MMDRigidBody* Rb0() { return ndRbRoot?ndRbRoot->rb0:nullptr; }

		void copyPoseTo(int pose, IrrSaba* sb2, int pose2);
		void recordCamBegin();
		void recordCamFrame(int id);
		void recordCamEnd();
		void recordVmdEnd();
		void saveVmd(irr::io::path fp);
		void saveMidi();
		void setCurTime(float t){	animeTime = t;}
		float getCurTime() { return animeTime; }
		void setWriting(bool wr);

		void mmdToIrrPos(irr::core::vector3df& pos);
		irr::core::vector3df mmd2irr(irr::core::vector3df pos);
		virtual irr::core::vector3df realTranslation() const override {
 			return   ndRoot->getGlobalPos()*MMD_SABA_SCALE;
			//getAbsoluteTransformation().getTranslation();
		}
		void irrToMmdPos(irr::core::vector3df& pos);
		irr::core::vector3df irr2mmd(irr::core::vector3df pos);
		irr::core::matrix4 mmd2irr(irr::core::matrix4 m) { return mmdBaseMat * m; }
		inline glm::mat4 mmd2irr(glm::mat4 m) { return mmdBaseMat * m; }
		inline glm::mat4 irr2mmd(glm::mat4 m) { return mmdBaseInv * m; }



		void saveCam(irr::scene::ICameraSceneNode* cam);
		Json::Value poseToJson();

#if HAS_MIDI
		void midiEventToEmb(int mode, irr::scene::SabaEmb& emb, midilib::MidiEventStruct& evt, float time);
		void onMidiEvent(midilib::MidiEventStruct evt);
		std::vector<smf::MidiEvent> midiArr;
#endif


		void UpdateEqvData(const EQVisual::EqvUpdateParam& pm);

		void attackObjUpdate(float steptime, int step, int stepCount);

		bool pinOnRoot(glm::mat4 mRoot);
		void processNodeJPV(saba::MMDNode* nd);
		void setAllDynRbActive(bool active, int actCD = 0);
		void pinWriteNodes();
		struct phyObjForceParam {
			int rst = 0; float3 ofs{ 0 };
			float mul = 1;
			int objId = -1;
			float maxDis = 999999.f,maxY=999999.f;
			uint32_t tag=0;
		};
		void phyObjForceAttack(phyObjForceParam pm);
		void spring(uint32_t flag, int sprMode, float mul = 1);
		void setVtxFw(int f) { vtxFrameFw = f; }
		void disableAnim(int disablePartId, int newVmdId = -1);


	public:

		irrSabaParam Pm;
		IrrMMD* mmd{};
		SbFwLauncher* fwLch{};

		int lauchAllVtxFw = 0;

		u32 CenterForce = 3;

		int MmdMode = MMD_MIDI_MODE;  //1:midi punch    10:piano  11:guqin
		core::vector3df drawAbsPos, mmdRhSpeed;
		glm::vec3 mmdLookAt, * lookAtPos{}; bool lookAt_mmdLookAt = false;
		float phyLookMul = 1.f, phyLookHeadAngMul = 1.f, phyLookHeadMul = 1.f;
		glm::quat mmdCamRtt;

		core::matrix4 mtCam;
		irr::scene::ICameraSceneNode* irrCam{}, * camSave{};
		EQVisual::SnGuQin* snGuqin{};
		void showGuqin(bool show);
		saba::MMDNode* ndOpCtr{}, * ndRoot{}, *ndAllParent{},*ndRbRoot{}, * ndCenter{}, * ndGroup{},
			* ndFootIKL{}, * ndFootIKR{}, * ndUpper{}, * ndArmL{}, * ndArmR{}, * ndArm1L{}, * ndArm1R{}, * ndHead{}, * ndNeck{}, * ndHeadIK{}, * ndHeadTgt{},
			* ndLegL{}, * ndLegR{}, * ndLeg1L{}, * ndLeg1R{}, * ndFootL{}, * ndFootR{}, * ndToeL{}, * ndToeR{},
			* ndRFinger2{}, * ndLFinger2{}, * ndHandL{}, * ndHandR{}, * ndIKArmL{}, * ndIKArmR{}, * ndUpper2{}, * ndUpper3{}, * ndIkFingerR{}, * ndFingerR{}, * ndOpaiL{}, * ndOpaiR{},
			* ndEyes{}, //* ndEyeR{}, * ndEyeL{},
			* ndYao{}, * ndLower{}, * ndManco{}, * ndZushouR{}, * ndPinHandL{}, * ndPinHandR{}, * ndPinFootL{}, * ndPinFootR{}, * ndPinYao{}, * ndShoulderCR{}, * ndOppaiC{},
			* ndCatEarL{}, * ndCatEarR{},* ndTail{},
			* ndCamPos{}, * ndSubRbBase{},
			* ndJoy2Rtt{};
		float absScale() { return ndRbRoot->absScale.x; }
		saba::MMDNode* ndLookAt{}, * ndHitBall{}; float ndLookAtMul = 1.f;
		saba::MMDMorph* mpA{}, * mpI{}, * mpU{}, * mpE{}, * mpO{}, * mpWa{}, * mpN{}, * mpBlink{}, * mpXiao{}, * mpPsy{}, * mpStarEye{}, * mpSpr{}, * mpFw1{};


		float centerForceMul = 1.f, cfMulTimer = 1.f;
		glm::vec3 centerForceVec{ 1,1,1 }, centerForceMinusUnit{ 1,0.1,1 };
		float modelAlpha=1.f;
#if MMD_ATTACK_OBJ
		saba::MMDNode* ndLockLast{};
		int lastKickObjId=0;
		CharacterAttacker charAtk;
#endif
#if MMD_COMBINE_CATCHER

		CharacterCatcher cat;
#endif


		PhyObj* phyObj{};// saba phy obj
#if MMD_SAVE_VMD
		std::shared_ptr<vmd::VmdMotion> vmdWriteFile;
		oguna::EncodingConverter ecv;
#endif

		MpaManager* sbMpa{};
		void startSbMPA(MpaLoadJsonParam lf);

		void onMotionFinish(saba::MMDNode& node);
		int rhAniIdAdd=0, maxrhanim=0;
		void setAdAnim(int id, float dur,float delay=0.f,float anmOfs=0.f);
		int setRndAdAnim(float& TdelayS,u32 group = 0);
		void setIkPos(int id, irr::core::vector3df pos){
			mmdBaseInv.transformVect(pos);
			if (id==0)
			curSTP[0].pos = pos;
		}

		int isCamJump(float ts);
		bool isAiCharacter() {return (Pmx->isCharacter || localPhyAnim) && Pm.clsType ==ESabaClass::SabaBase; }
		bool isHumanModel() { return Pmx->isCharacter; }
		int updateCameraAtOffsetTime(float timeOfs,bool fromStart=false);
		irr::scene::ISceneNode* camTgtDmy{}, * camUpDmy{};
		bool camWarped = false;
		void addCameraFrameToFitChildBase(IrrSaba *sb);

		std::function<void(IrrSaba* sb, int evt) > onSabaEvent;

		irr::core::vector3df fwtSrc,fwtAbsVel, headPos,centerPos;// , fwtRtt
		irr::core::matrix4 fwtMat, matToParentNode;
		bool frameRtt = false; 	glm::vec3 frameRttVec{}; saba::MMDNode* ndFrameRtt{};

		bool lookAtCam = false, canLookAtCam = 1, canEyeLookAtCam = false;
		bool nearKiss = false;
		int bursting = false;
		float phyFwHue = 0.f;
		int fist = 0;
		void setHandPointCam(bool b);
		void syncToFrame(float frame, int count = 60,int mode =1) {
			animeTime = frame/30;
			lastOwTime = 0.f;
			mModel->syncPhyToFrame(frame, count,mode);
		}
		void animeTimeAdd(float t) { animeTime= core::clamp(animeTime += t,0.f, Vmd? std::max(1/30.f,Vmd->GetMaxKeyTimeFrameDeprecated() / 30.f):1); }
		void switchToEyeCam(bool toEye);


		irr::scene::ISceneNode* camTgt{}, * camTgt1{}, * camTgt2{};
		struct DrawStruct {
			bool drawing; float drawRat;
			core::vector3df drawPos, mmdPos, vel;
		};
		DrawStruct dsLast;
		saba::MMDPhysics* mmdPhysics{};
		bool hasVtxData();
		irr::core::vector3df ctrMph;

		saba::MMDNode* getPickNodeByVtxId(int vid,int  byNode,glm::vec3 *hitPos=nullptr, bool onlyDynRb=false);
		saba::MMDNode* selectedNode{}, * curPickingNode{};
		core::vector3df pickNodeIrrPos;
		float pickDisMul = 1.f;
		int pickLastPtNum = 0;
		void pickVtxMove(saba::MMDNode* pickingNode, core::vector2df xy, bool swapYZ);
		//
		saba::MMDNode* getPickNodeByNodeId(int nid);
		saba::MMDNode* getPickNodeByMarker(core::vector2df xy,u32 filter);
		void pickNodeMove(saba::MMDNode* node,core::vector2df xy,bool swapYZ,bool forceRtt =false);
		bool ifPickTmpIK(saba::MMDNode*& pickingNode, core::vector2df xy, saba::MMDNode* tmpIkRoot );
		bool setTmpIK(saba::MMDNode*& pickingNode, saba::MMDNode* tmpIkRoot);
		void setNodeRealPos(saba::MMDNode* node, core::vector3df pos);
		void translateNodeInCamSpace(saba::MMDNode* node, int space, core::vector3df ofs);
		void rotateNodeInCamSpace(saba::MMDNode* node, int space, core::vector3df ofs);
		saba::PMXModel* Pmx{};
		std::shared_ptr<saba::VMDAnimation>	Vmd;
		IrrSaba* sbTracker{};
		bool useOIT = 0;

		void globlToBaseAnime();

		inline static uu::UpStrIdxRes<std::shared_ptr<SabaShareData>> Sbds;//Same pmx VERTEX DATA CACHE
		std::shared_ptr<SabaShareData> sbd;


		EQVisual::SnPiano* piano;
		float lastPATime = 0.f;
		//void copyNodeStateFrom(irr::scene::IrrSaba* sb);

		//cs
		int mdApproachingMorph = 0;
		int mdApproachingMotion = 0;
		int mdViewNode=1;
		int mdViewNodeMode=1; //0:ik,  1:ik mov 2: ik mov rtt, 3:all
		int getUseGPU() { return useGPU ? 1 : 0; }

		void onSvFwCenterPt(const EQVisual::SvTimePt& pt, int fromId, float deltaS);
		void stkLineAdd(std::wstring txt);
		void stkLineDel(int i);
		void writeWithLeg();
		void stkDrawFirstPt();
		void mmdFw(float3 pos, int fwIdx, float3 vec, irr::video::SColorf col = { 1,1,1,1 }, EQVisual::LfwParam* pm = nullptr, float3* tcl = nullptr);

		void mmdFw(int ptrType, std::string_view idStr, float3 pos, float3 vel, video::SColor color);

		void mmdFwLine(int ptrType, std::string_view idStr, float3 pos, float3 pos2, video::SColor color, float step,float startStepRate=0.f);

		void setBonePhsActive(saba::MMDNode* node, bool active,bool setSubNodes=true);

		void setPhyAnim(saba::MMDNode* node, int phAni, bool setSubNodes);

		void setSubNodePhyAnim(saba::MMDNode* node, int phAni);

		void togglePhyDebugVisual();

		void reloadTex(io::path tex);
		int animCycleStart = -1, modelCollisionId = -1;

		void enableIK(bool en);
		void toggleIKEnable() { enableIK(ikEnable = !ikEnable); }
		bool ikEnable = true;

		// Walk motion control methods
		void startWalking(const glm::vec3& direction, float speed = 1.0f);
		void stopWalking();
		void updateWalkMotion(float deltaTime);
		bool isWalking() const { return walkingActive; }
		void setWalkDirection(const glm::vec3& direction);
		void setWalkSpeed(float speed);


		irr::scene::CLabelSceneNode* ikVRoot{};
		std::string writeFrameTxtStr;

		bool to_rootLockMat = false;
		bool rootLocked = false, onArPath=false;
		int xrMode = 0;// 0=rootTr , 1=rt1, 0x10000=node,
		ualib::OxmUpdateData  oud;
		MmdPhysicsController* mmdPhyCtr{};
		bool phyStand = true;
#if MMD_HAND_PTCTRL || MMD_FINGERS
		saba::MMDNode* ndFgrs[2][5][5]{};  //fingers MMD_LEAP_FINGER 1=Hand 2=Foot

#endif
#if MMD_OPEN_POSE
		void openPoseDraw(PITex rt=0);
#endif
		bool allowCalcLookAtCam = true;
		const int& getItemIdx() { return itemIdx; }
		virtual void setItemIdx(int idx);

		LeapMan* leap{};
		void enableLeap(bool isOn);
		void actVoiceFx(int chId, int fx, int objKey=60, std::wstring  txt=L"",float wordMul=1.f);

		void setFxMpCallback(int ch);

		void appendLipMorph(std::string txt, float dur);

		void lookOnMousePos(int viewId=0);
		bool canLookOnVelocity = false;
		void lookOnVelocity();
		irr::scene::IrrSaba* mic{}, * tsb{}, * lastTsb{}, * wheel{};
		SabaCloth* sbCloth{};
		virtual void drawReset() override;
		virtual void drawEnd() override;
		PhyObjManager* Pom{};
		EQVisual::EQV* Eqv{};
		ualib::UaLibContext* Ctx;
		int rcvEqvData = 0;  // 10:rb dance
		std::shared_ptr<MmdNodeHandler_JoyGameCast> hdlJgc;
		std::shared_ptr<MmdNodeHandler_JoyDance> hdlJbd;
		int pickFilter = 0;
		bool hasVtxFw = false;
		float handFwSpeed = 100.f;

		bool localPhyAnim = false, localPhyAnimToPos = false; 
		uint32_t localPhyAnimFlag = 0;  //1:yao rtt , 
		
		float3 phyRtt{};
		uint32_t poType = 0;
		int objId = 0;
		float zyTimer = 0.f, zyPast=10, lastHitFxTime=0;
		bool lockFloatY = false; float mFloatY = 0.f,mFloatYadd = 0.f;
		int phyStep=0, phyStepCount=3; float phyStepTime=1;
		//material alpha control
		int rbActCD = 0;

		int pickSubMesh = 0;

		saba::MMDRigidBody* lastConRb[32] = {};
		int  lastConnId = 0;

		//beat
		bool hasBeat = false;
		float beatVal = 0;

		int springDefMode=0,springMode = -1;
		float springMul = 1.f;
		bool hitDisabled = false;
		bool forcePhyAnim = false;
		//ragdoll
		RagDollParam curRdp;
		struct RagDollVars {
			int stage = -1;
			float timer = 0, stageTime=0, totalTime=10;
			float ratio=1;
			float upt0, upt1, fint;
			float phyAniMul = 1;
			float phyVelScaleOnAnim = 1.f;
			float scRatioMin = 0.2f;
			int moveLeg = 0;
		} rd;
		void startRagDoll(RagDollParam pm);


		int phyMatAniTreeLevel = 0;
		float ctrForce = 0.f;
		float lastTimeSetVelP2P=0;
		//watchRoot;
		saba::MMDRigidBody* attachObjLast[32]={};

		std::function<void(int ch, SvNoteData& note)> onSvNoteCb;

		void updateAnimationToFrame(float f) {
			Pmx->UpdateAllAnimation(Vmd.get(), 1.f, 0);
		};
		void createInflate();

		bool isCamerer = false; //bind on cam
		void resetPhyAnim();



		void updateCameraOnMmdCamera(const MMDCamera& mmdcam, bool onlyCalc=false);
	private:
		bool hasModel=false;
		float phyForceMul = 0.f, phyTurnMul = 1.f;
		bool kickSwitchFoot = false;;
		int phyFrame = 0, phyFrameCD = 0;
		saba::MMDNode* ndKickLeg{};
		int vtxFrameFw = 1;
		void addRb2NdPosForce(saba::MMDNode* nd, float xm = 1.f, float ym = 1.f, float zm = 1.f, float rttmul = 1);;

		void footOnGround(saba::MMDNode* footNode);
		void adjustFootPositionAndRotation(saba::MMDNode* footNode, bool isHighFall);
	protected:

		irr::video::IVideoDriver* Driver;


		MmdCompute* mcs{};
		void updateMMD(float deltaTime);

		void updateVtxMat();



		void updateCamera(float camframe, bool onlyCalc = false);
		void updateMmdMode1(float deltaTime);

		void leapUpdate();

		void mmdSynthVSingNote();

		void leapNode(irr::scene::ISceneNode* ndSrc, saba::MMDNode* node);
		void leapMat(irr::core::matrix4 m0, irr::core::matrix4 m, saba::MMDNode* node, irr::core::vector3df mul, irr::core::vector3df ofs);



		void phyLeadTo(saba::MMDRigidBody* rb,float3 pmin,float3 pmax,float fmul);

		void onMorph(int reason, saba::MMDMorph& morph);
		void freeRes();


		std::shared_ptr<saba::MMDModel>	mModel;


		std::shared_ptr<saba::VMDCameraAnimation>	m_vmdCameraAnim;
		core::aabbox3d<f32> Box;
		bool drawCam = false;
		irr::scene::ISceneNode* snCam{}, * snCamCube{}, * snBall{}, * snBox{}, *snPickBall{};


		saba::MMDNodeManager* mNodeMan;
		float animeTime = 0.f;
		bool mPlaying = false, mCamPlaying = true;

		bool fwPrelaunch = true;
		//irr
		std::vector<irr::video::SMaterial>	mtrs, hlMtrs;
		std::vector<irr::video::MMDCbCp>  cbcps,hlCbcps;
		void UploadCbData(irr::video::MMDCbCp* pb,int count);
		vks::Buffer cbDynVkBuf;
		uint32_t dynBufAlignment=0;



		irr::video::SColorf curDiffuse=0xFFFFFFFF;


		size_t lastUpdateVtx = 0;
		float stepTime = 0.f;
		core::vector3df lookPosC, drawPosAdd, drawMmdPos, lookTgtMmdPos{ 0,10,10 }, lookTgtMmdPosAdd{}, headIkTgtPos{}, pinYaoTgt{ 0,17,0 }, leapCtrTgt{0,5,0};
		core::vector3df curCharPos;
		std::queue<DrawStruct> drQue;

		irr::core::vector3df lastWritePos;

		EQVisual::SvTimePt curSTP[8];


		int lnvId = 1, specId = 3;

		std::vector<float4> lnvSets  = {{1,0,0,1}, {0.5,0.5,0,1 },{0.5,0,0,1},  {0.5,0.25,0,1},{0.75,0.375,0,1},{0.5,0.25,0.5,0.5},{0.75,0.375,0.5,0.5},{1,0.5,0.5,0.5 }, { 1,0.0,1,0 },{0.5,0.5,1,0},{0.5,0,1,0} };
		std::vector<float4> specSets = { {0,0,0,0},{0.25,0.25,0.25,0},{0.5,0.5,0.5,0}, {0,0,0,32},{0.25,0.25,0.25,32},{0.5,0.5,0.5,32}, };

		std::vector<SabaEmb> embs;
		core::vector3df posIKArmR,lastEmbPos, headVector;
		int adAnmId = -1, adAnmIdLast=0, rhAnimCC[8] = { 0 };
		float aniRateR=0;
		float lastTimer[8] = {}, motionMul = 1.f, lastRbHitTime=0.f;
		float adAniDur = 0.f,adAniTime=0.f;
		core::vector3df lastCamPos, eyePos; bool eyePosSet = false;
		float lastMidiTime[8] = {}, lastHitTime[8] = {};
		u32 midiNodeFlag = 2; //1 hand 2 foot  3 upper2
		float handUpRatR = 0.f, xiaoRat=0.f;
		saba::MMDMorph* lastMp{};
		saba::MMDRigidBody* rbt{};

		int curSid = 0, nextSid = 0;
		float sidTimer = 0.1f, sidTimerMax = 0.1f, sidTimerDur = 0.f, sidTimerDurMax = 0.f, sidRatio = 1.0f;
		irr::io::path modelFile,motionFile,writeVmdFile;

		float mmdSpeedMul = 1.0f;
		int camId = 0;
		bool reversePlay = false;
		IrrSaba* parentMMD{};
		const saba::MMDNode* parentAttechNode{};
		std::vector< saba::MMDNode*> ikNodes;
		saba::MMDJoint* jtWrite{};
		PITex texCoat{}, texBott{},  texFuCl{}, texBtCl{}, texFuRt{}, texSdtoy, texQun{};
		float curPeak = 0.f;
		float lastOwTime=0.f;
		float laughMorph = 0.f;
		float headDis = 10000.f, headCamAngle=180.f;
		float lastFrame=-1.f;
		float lastTs = 0, lastTsFrame = -1;

		float catEarMorph = 1.f;
		irr::core::vector3df hdIkSrcPos,hdIkTgtPos,hdIkPos;
		float hdIkPosRatio=0.f;
		bool lastLookAtCam = false;
		bool useGPU =false;
		bool hasBloom = false , pinedYao=0; 

#if MMD_ADD_WING
		saba::PMXModel* mdLWing{}, *mdRWing{};
		void initWing();
#endif
		bool HandPointCam = false;
		bool aniUpdated = false;
		bool isLookAtCam = false; // or look ndLock
		bool firstUpdate = true;
        void vtxProcessCPU();


		bool isReplaceMat(std::string name);
		bool needControl(saba::MMDNode* nd,int flag);
		irr::video::ITexture* getModelTexture(irr::io::path fp);

		std::vector<std::shared_ptr<IMmdNodeHandler>> nodeHandlers;
		std::shared_ptr<IMmdNodeHandler> ophdl{};

		irr::video::FFDrawCP smEdgeCP;
		irr::video::SMaterial smEdge;
		irr::video::SMaterial MatBase;


		struct PickNodesInfo {
			saba::MMDNode* nd;
			int lbId;
			core::vector3df pos;
			float dis, dis2d, angle,sizeR;
			video::SColor color;
		};
		std::map<int , PickNodesInfo> snMarks ;
		std::vector<CLabelSceneNode::Item> labelItems;
		int lbId = 0;
		int8_t curLabelCreateMark[32] = {0};
		size_t ieSize = 0;
		float springAng=0;
		bool lastPhyLook = false;
		bool allShadowCastMat = true;
		UaBtDebugDraw* Dg{};
		float bandv[EQ_MAX_BANDS]{};
		int eqvBandCount = 1;
		bool frameRestore = false;

		//void mmdFwIdStr(int typeId, std::string_view name, float3 pos, float3 vec, irr::video::SColorf col={1,1,1,1}, EQVisual::LfwParam* pm = nullptr, float3* tcl = nullptr);
		glm::mat4 rootLockMat;
		/// Add Node Rotation
		public:
			void setAddRttNode(size_t& ndAddRttIdx, const glm::quat& ndAddRttQt, size_t idx, core::vector3df rtt, bool reset);

		private:
			size_t setNodeAddRotation(const wchar_t* name, size_t idx, const glm::quat* rtt);

		//GPU transform matrix
#if MMD_GPU_TRANSFORM

#endif



		float curDeltaTime = 0.f, toConnect=0;
		bool leapOn = false;

		struct {
			float lastTime = 0;
		} ivm;


		irr::scene::SnInflatable* snInflate{};
		virtual irr::core::vector3df mapOnCloth(irr::core::vector3df pos, irr::video::SColorf* scf) override;


		bool isOIT=false;

		struct LipData {
			std::string txt;
			float timeS;
		};
		std::vector<LipData> lipArr; int lipId = 0; float lipStartS = 0;


		//VMDEventExt
		public:
			VmdEventExt *vee = nullptr;
			std::function<void(const SVmdEvent& e	)> cbVmdEvent;
		private:
			void onVmdEvent(const SVmdEvent& e);


		public://tempvar
			int adsbcc = -1;
			PhyObjParam pmt{};
		private:
			// Walk motion variables
			bool walkingActive = false;
			glm::vec3 walkDirection = {0.0f, 0.0f, -1.0f};
			float walkSpeed = 1.0f;
			float walkCycleTime = 0.0f;
			float stepHeight = 0.0f;
			float stepLength = 0.0f;
			bool leftFootForward = false;
			float lastStepTime = 0.0f;
			float stepDuration = 0.5f; // Time for a complete step cycle

    };
#if !USE_PHYSX
	class UaBtDebugDraw : public btIDebugDraw
	{
		int m_debugMode = 1;
	public:
		UaBtDebugDraw() {	}
		virtual ~UaBtDebugDraw() {	}
		virtual void    drawLine(const btVector3& from, const btVector3& to, const btVector3& fromColor, const btVector3& toColor) { DP(("UaBtDebugDraw::drawLine")); }
		virtual void    drawLine(const btVector3& from, const btVector3& to, const btVector3& color);
		virtual void    drawSphere(const btVector3& p, btScalar radius, const btVector3& color) { DP(("UaBtDebugDraw::drawSphere")); }
		virtual void    drawTriangle(const btVector3& a, const btVector3& b, const btVector3& c, const btVector3& color, btScalar alpha) { DP(("UaBtDebugDraw::drawTriangle")); }
		virtual void    drawContactPoint(const btVector3& PointOnB, const btVector3& normalOnB, btScalar distance, int lifeTime, const btVector3& color) { DP(("UaBtDebugDraw::drawContactPoint")); }
		virtual void    reportErrorWarning(const char* warningString) { DP(("UaBtDebugDraw::reportErrorWarning")); }
		virtual void    draw3dText(const btVector3& location, const char* textString) { DP(("UaBtDebugDraw::draw3dText")); }
		virtual void    setDebugMode(int debugMode) { m_debugMode = debugMode; }
		virtual int     getDebugMode() const { return m_debugMode; }
		//
		void begin() { lineVec.clear(); }
		void end() {}
		std::vector<irr::video::S3DVertex> lineVec;
	};
#endif


	class SabaHolder {
	private:
		IrrSaba* ptr;

	public:
		// Constructor
		SabaHolder(IrrSaba* p = nullptr) : ptr(p) {
			if (ptr) {
				ptr->grab();
			}
		}

		// Copy Constructor
		SabaHolder(const SabaHolder& other) : ptr(other.ptr) {
			if (ptr) {
				ptr->grab();
			}
		}

		// IrrSabassignment Operator for IrrSaba*
		SabaHolder& operator=(IrrSaba* newPtr) {
			if (ptr != newPtr) {
				if (ptr) {
					ptr->drop();
				}
				ptr = newPtr;
				if (ptr) {
					ptr->grab();
				}
			}
			return *this;
		}

		// IrrSabassignment Operator for Holder
		SabaHolder& operator=(const SabaHolder& other) {
			if (this != &other) {
				if (ptr) {
					ptr->drop();
				}
				ptr = other.ptr;
				if (ptr) {
					ptr->grab();
				}
			}
			return *this;
		}

		// Destructor
		~SabaHolder() {
			if (ptr) {
				ptr->drop();
			}
		}

		// IrrSabaccess the underlying pointer
		IrrSaba* get() const {
			return ptr;
		}

		// Dereference operators
		IrrSaba& operator*() const {
			return *ptr;
		}

		IrrSaba* operator->() const {
			return ptr;
		}
	};


	void rotateRbDir(saba::MMDRigidBody* rb, const glm::vec3 front, glm::vec3 dirTgtNorm, float mul);

}

irr::scene::SnArItem* gLoadSabaModel(irr::scene::PMXFileCreateParam  fcp, uint32_t aiFlag);