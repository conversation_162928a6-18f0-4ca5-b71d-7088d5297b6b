#pragma once
 
#include <ualib.h>
#include "AppTypes.h"

#include "FL_ArEditor.h"

#include "irrmmd/irrSaba.h"
#include "irrmmd/sabaCloth.h"

#define PS_TO_MOVE_ROOT_NODE  1//SABA_ONE_WORLD

namespace irr::scene {
	class SnArRoot;
	
	
	struct ArItemParam {
		EQVisual::EQV* eqv;
		ualib::UaLibContext* ctx;
		CsEditorItem* cei;
		SnArRoot* arRoot;
		IrrMMD* mmd;
		int pickId;

	};
	struct irrSabaParam;



	class SnArItem : public irr::scene::ISceneNode, public EQVisual::SvgDrawData
	{
		friend class SnArRoot;
	public:
		SnArItem(irr::scene::ISceneNode* parent, irr::scene::ISceneManager* mgr, int id, const ArItemParam& pm);
		virtual ~SnArItem();
		uint32_t AIFlag=0;			//0x01: IrrSaba phyiscUpdate, 0x10 :main   0x100: item,  0x1000: allPhysics

		virtual void OnRegisterSceneNode() _IRR_OVERRIDE_;
		virtual void render() _IRR_OVERRIDE_  {};
		virtual void OnAnimate(u32 timeMs) _IRR_OVERRIDE_;
		virtual core::matrix4 getRelativeTransformation() const;
		virtual const core::aabbox3d<f32>& getBoundingBox() const
		{
			return Box;
		}

		virtual u32 getMaterialCount() const
		{
			return 1;
		}

		virtual video::SMaterial& getMaterial(u32 i)
		{
			return mtr;
		}
		
		void onCreate();
		void onSelected();
		bool isCur();
		void onMediaStarted(int mode);
		void onMediaStopped();
		void updateMediaFrame(float timeS,float dtime);
		void setFwIdx(int id) { fwIdx = id; };
		void onUpdatedState(uint64_t flag);
		void rotateByItemRttType();
		void redrawOldStroke();
		void createModel(irrSabaParam* pm);
		bool loadMotion(io::path filepath);
		bool loadPose(Json::Value jsv);
		bool loadMeshSN(io::path filepath);
		int getItemCount() {
			return snSubArItemsRoot ? snSubArItemsRoot->getChildren().size() : 0;
		}
		void forEachArChild(std::function<void(irr::scene::SnArItem* childSn)> cb,u32 flag=0x10)
		{
			snSubArItemsRoot->forEachChild([=](irr::scene::ISceneNode* it) {
				auto ai = (irr::scene::SnArItem*)it;
				if (ai->AIFlag & flag)
					cb(ai);
			});
		}
		void translateInCamSpace(int space, core::vector3df ofs);
		void rotateInCamSpace(int space, core::vector3df ofs);
		void itemTypeChanged();
		void setValue(int64_t pm1, int64_t pm2);
		int64_t getValue(int64_t pm1, int64_t pm2);
		struct LoadItemStruct {
			CsEditorItem cei{};
			std::string label, text;
			std::string modelFile, motionFile;
			Json::Value jsToLoadPose;
			void setStringPtr() {
				cei.text = text.c_str();
				cei.label = label.c_str();
			}
		} leiBk;
		static void jsonToCei(const Json::Value& vr, SnArItem::LoadItemStruct &lei);
		Json::Value itemToJson();


		bool toLoadModel = false;
 
		CsEditorItem* Cei;
		void resetCei();
		bool isText = false;
		bool isTextStroke = false;
 
		IrrSaba* sb{};		
		io::path modelFilePath;
		core::matrix4 getModelAbsCenter();
		void createSvg();

		bool addRotation(core::vector3df rttDeg);
		void setAddRttNode(size_t idx, core::vector3df rtt, bool reset);

	protected:
		irr::scene::ISceneNode* snSubArItemsRoot{};
		EQVisual::EQV* Eqv{};
		ualib::UaLibContext* Ctx{};
		core::vector3df ptSpd;
		std::string textBack;
		
	private:
		core::aabbox3d<f32> Box;
		video::SMaterial mtr;
		SnArRoot* arRoot{}; //root's is self
		ArItemParam Pm;

		IMeshSceneNode* snMesh{};
		irr::scene::ISceneNode* snAnchor{}, * snMarker{}, *snPickBox{};
		irr::scene::ICameraSceneNode* camSave{};
		irr::video::IVideoDriver* Driver{};
		
		
		int fwIdx = 0;
		float hAdd = 0.f;
		uint32_t toStartText = 0;
		int itemStarted = 0;
		bool mediaPlaying = false;
		bool forcePhysics = false;
		int selectTimeCdMs = -1;
		int pickId = 0;
		const CsEditorState& Cs();
		void saveCam(irr::scene::ICameraSceneNode* cam);
		void startStkText(bool gen);

		size_t ndAddRttIdx = 0;
		core::vector3df ptAddRtt;
		glm::quat ndAddRttQt;

		std::vector<const char*>morphNames;



	};

	void initCsEditorItem(CsEditorItem* ei);
 
}