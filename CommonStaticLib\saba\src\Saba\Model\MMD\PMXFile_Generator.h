//
// PMXFile_Generator.h - Framework for generating PMX models programmatically
//
//
// PMXFile_Generator.cpp - Implementation of PMX model generator
//

/*
===============================================================================
PMX FILE GENERATION INSTRUCTIONS FOR LLM AI
===============================================================================

PMX (Polygon Model eXtended) is a 3D model format used by the MikuMikuDance software.
This guide helps LLM AI generate valid PMX files programmatically.

STRUCTURE OVERVIEW for Generator: 
------------------
A PMX file consists of the following key components that need to be created:
1. Header & Model Info: Basic file information and model metadata
2. Vertices: 3D points with normals, UV coordinates, and bone weights
3. Faces: Triangles defined by vertex indices
4. Materials: Surface properties like color, shininess, opacity
5. Bones: Skeletal hierarchy for animation and deformation
6. Rigid Bodies: Physics simulation objects
7. Joints: Constraints between rigid bodies

GENERATION STEPS:
----------------
1. Initialize a PMX file with basic header and metadata
2. Create a basic bone structure (at minimum, a root bone)
3. Add vertices with positions, normals, UVs, and bone weights
4. Define faces (triangles) using vertex indices
5. Create materials with appropriate properties
6. Set up physics with rigid bodies and joints

GENERATION APPROACHES:
--------------------
1. High-level approach: Use built-in generators for common shapes
   - createBox() - Create a single box with physics
   - createChain() - Create a chain of connected links
   - createRope() - Create a flexible rope with physics
   - createMultiBox() - Create a compound object from multiple boxes
   - createBrickWall() - Create a wall of bricks

2. Low-level approach: Build custom models using primitives
   - initialize() - Set up a new model
   - addVertex() - Add a vertex with position, normal, and UV
   - addFace() - Add a triangle face using vertex indices
   - addBone() - Add a bone to the skeleton hierarchy
   - addRigidBody() - Add a physics rigid body
   - addJoint() - Add a physics constraint between rigid bodies
   - addMaterial() - Add material properties
   - saveToFile() - Save the completed model

BASIC USAGE EXAMPLES:
-------------------
1. Create a simple box:
```cpp
// Initialize a generator and model
PMXFile_Generator generator;
generator.initialize("BoxModel", "A simple box model");

// Create a box
Gpm_BoxShape box;
box.size = glm::vec3(2.0f, 1.0f, 3.0f);        // Width, height, depth
box.position = glm::vec3(0.0f, 5.0f, 0.0f);    // Position in 3D space
box.rotation = glm::vec3(0.0f, glm::radians(45.0f), 0.0f); // Rotation in radians
generator.createBox(box);

// Save to file
generator.saveToFile("box_model.pmx");
```

2. Create a chain:
```cpp
// Initialize a generator and model
PMXFile_Generator generator;
generator.initialize("ChainModel", "A chain of links");

// Define chain links
std::vector<Gpm_ChainLink> links;
for (int i = 0; i < 5; i++) {
    Gpm_ChainLink link;
    link.radius = 0.5f;                        // Radius of the link
    link.length = 1.0f;                        // Length of the link
    link.position = glm::vec3(0.0f, 5.0f - i * 1.2f, 0.0f); // Vertical positioning
    link.rotation = glm::vec3(0.0f, 0.0f, glm::half_pi<float>()); // Rotation
    links.push_back(link);
}

// Create the chain with physics (true = enable self-collision)
generator.createChain(links, true);

// Save to file
generator.saveToFile("chain_model.pmx");
```

3. Create a cube from multiple boxes:
```cpp
// Initialize a generator and model
PMXFile_Generator generator;
generator.initialize("CubeModel", "A cube made of 6 faces");

// Define boxes for each face
std::vector<Gpm_BoxShape> boxes;
float size = 5.0f;
float thickness = 0.2f;

// Add front, back, left, right, top, and bottom faces
Gpm_BoxShape front;
front.size = glm::vec3(size, size, thickness);
front.position = glm::vec3(0.0f, 0.0f, size/2);
boxes.push_back(front);

// Add other faces similarly...

// Create the multi-box model
generator.createMultiBox(boxes);

// Save to file
generator.saveToFile("cube_model.pmx");
```

4. Create a brick wall:
```cpp
// Initialize a generator and model
PMXFile_Generator generator;
generator.initialize("WallModel", "A brick wall");

// Create a brick wall with 5 rows and 10 columns
generator.createBrickWall(
    5,                          // Number of rows
    10,                         // Number of columns
    1.0f,                       // Brick width
    0.5f,                       // Brick height
    0.3f,                       // Brick depth
    0.1f,                       // Mortar thickness
    glm::vec3(0.0f, 0.0f, 0.0f) // Origin position
);

// Save to file
generator.saveToFile("brick_wall_model.pmx");
```

5. Custom model creation:
```cpp
// Initialize a generator and model
PMXFile_Generator generator;
generator.initialize("CustomModel", "A custom model");

// Manually add vertices
int32_t v1 = generator.addVertex(
    glm::vec3(-1.0f, 0.0f, 0.0f),  // Position
    glm::vec3(0.0f, 1.0f, 0.0f),   // Normal
    glm::vec2(0.0f, 0.0f)          // UV coordinates
);
// Add more vertices...

// Add faces (triangles)
generator.addFace(v1, v2, v3);

// Add materials
generator.createDefaultMaterial(1); // For 1 face

// Add bones
int32_t rootBone = generator.addBone("Root", glm::vec3(0.0f), -1,
    (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
               PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));

// Add rigid bodies
RigidBodyDef rbDef;
rbDef.name = "Body";
rbDef.engName = "Body";
rbDef.shape = PMXRigidbody::Shape::Box;
rbDef.size = glm::vec3(2.0f, 1.0f, 1.0f);
rbDef.position = glm::vec3(0.0f, 0.5f, 0.0f);
rbDef.boneIndex = rootBone;
rbDef.mass = 1.0f;
rbDef.linearDamping = 0.9f;
rbDef.angularDamping = 0.9f;
int32_t rbIndex = generator.addRigidBody(rbDef);

// Save to file
generator.saveToFile("custom_model.pmx");
```

PHYSICS PROPERTIES:
-----------------
When creating physical objects, configure these properties:

1. Rigid Bodies:
   - Shape: Box, Sphere, Capsule, Cylinder, Cone
   - Size: Dimensions of the shape
   - Position & Rotation: Transform in 3D space
   - Mass: Weight of the object (0 = immovable)
   - Damping: How quickly movement slows down (linear and angular)
   - Friction: Resistance when sliding against surfaces
   - Restitution: Bounciness (0 = no bounce, 1 = perfect bounce)
   - Operation: Static (bone-following), Dynamic (physics-driven), or Combined

2. Joints:
   - Type: Various constraint types (SpringDOF6, Hinge, Slider, etc.)
   - Connected Bodies: Which rigid bodies are linked
   - Position & Rotation: Joint anchor point and orientation
   - Limits: Movement and rotation constraints (min/max values)
   - Springs: Force parameters that pull the joint to rest position

BEST PRACTICES:
-------------
1. Start with simple primitives before creating complex models
2. Use reasonable mass values (0.1-10.0) for dynamic objects
3. Set appropriate damping (0.1-0.9) to prevent unstable simulation
4. Create a logical bone hierarchy for proper animation
5. Keep polygon count reasonable for performance
6. Place pivot points (bone positions) at logical centers of objects
7. Test physics settings incrementally to find stable configurations

For more detailed examples, refer to the PMXFile.h PMXFile.cpp PMXGenerator_Example.cpp and PMXGenerator_Test.cpp files.
 
*/
#ifndef SABA_MODEL_PMXFILE_GENERATOR_H_
#define SABA_MODEL_PMXFILE_GENERATOR_H_

#include "PMXFile.h"
#include "PMXWriter.h"
#include <string>
#include <vector>
#include <memory>
#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>

namespace saba {

// Struct to define a box shape
struct Gpm_BoxShape {
    glm::vec3 size = glm::vec3(1.0f); // Width, height, depth
    glm::vec3 position = glm::vec3(0.0f); // Center position
    glm::vec3 rotation = glm::vec3(0.0f); // Rotation in radians
};

// Struct to define a chain link
struct Gpm_ChainLink {
    float radius = 0.5f;
    float length = 2.0f;
    glm::vec3 position = glm::vec3(0.0f);
    glm::vec3 rotation = glm::vec3(0.0f);
};

// Struct to define a rigid body
struct RigidBodyDef {
    std::string name,engName;
    PMXRigidbody::Shape shape = PMXRigidbody::Shape::Box;
    glm::vec3 size = glm::vec3(1.0f);
    glm::vec3 position = glm::vec3(0.0f);
    glm::vec3 rotation = glm::vec3(0.0f);
    float mass = 1.0f;
    float linearDamping = 0.9f;
    float angularDamping = 1.0f;
    float restitution = 0.0f;
    float friction = 0.5f;
    uint8_t group = 0;
    uint32_t collisionMask = 0xFFFFFFFF;
    PMXRigidbody::Operation operation = PMXRigidbody::Operation::Dynamic;
    int32_t boneIndex = -1;
};

// Struct to define a joint
struct JointDef {
    std::string name;
    PMXJoint::JointType type = PMXJoint::JointType::SpringDOF6;
    int32_t rigidBodyAIndex = -1;
    int32_t rigidBodyBIndex = -1;
    glm::vec3 position = glm::vec3(0.0f);
    glm::vec3 rotation = glm::vec3(0.0f);
    glm::vec3 linearLimitMin = glm::vec3(-1.0f);
    glm::vec3 linearLimitMax = glm::vec3(1.0f);
    glm::vec3 angularLimitMin = glm::vec3(-glm::pi<float>() / 4);
    glm::vec3 angularLimitMax = glm::vec3(glm::pi<float>() / 4);
    glm::vec3 springLinear = glm::vec3(0.0f);
    glm::vec3 springAngular = glm::vec3(0.0f);
};

class PMXFile_Generator {
public:
    PMXFile_Generator();
    ~PMXFile_Generator();

    // Initialize a new PMX file with basic settings
    void initialize(const std::string& modelName, const std::string& comment = "");

    // Create a box model
    void createBox(const Gpm_BoxShape& boxDef);

    // Create a chain model with multiple links
    void createChain(const std::vector<Gpm_ChainLink>& links, bool selfCollision=false);


    // Create a rope model with multiple cylindrical segments
    void createRope(const std::vector<Gpm_ChainLink>& links, bool selfCollision=false, int boneMeshSegments=1, bool connectBoneMesh=false);

    // Create a model with multiple boxes (like a cube with 6 faces)
    void createMultiBox(const std::vector<Gpm_BoxShape>& boxes);

    // Generate a brick wall model
    void createBrickWall(
        int rows,
        int columns,
        float brickWidth,
        float brickHeight,
        float brickDepth,
        float mortar = 0.2f,
        const glm::vec3& origin = glm::vec3(0.0f)
    );

    void createCone(float radius, float height, const glm::vec3& position, const glm::vec3& rotation, const std::string& boneName="coneBone", const std::string& rbName="coneRb");

    // Add a bone to the model
    int32_t addBone(const std::string& name, const glm::vec3& position, int32_t parentBoneIndex = -1, uint16_t boneFlags = 0, bool isChainBone = false, int32_t nextBoneIndex = -1);

    // Add a rigid body to the model
    int32_t addRigidBody(const RigidBodyDef& rbDef);

    // Add a joint between two rigid bodies
    int32_t addJoint(const JointDef& jointDef);

    // Save the generated model to a PMX file
    bool saveToFile(const std::string& filename);

    // Get the generated PMX file
    PMXFile* getPMXFile() { return &m_pmxFile; }

 
    // Initialize PMX header with default values
    void initializeHeader();

    // Add a vertex to the model
    int32_t addVertex(const glm::vec3& position, const glm::vec3& normal, const glm::vec2& uv);

    // Add a face (triangle) to the model
    void addFace(uint32_t v1, uint32_t v2, uint32_t v3);

    // Add a material to the model
    int32_t addMaterial(const std::string& name, const glm::vec4& diffuse, int32_t faceCount);

    // Create a default material
    int32_t createDefaultMaterial(int32_t faceCount);

    // Helper method to create a box mesh
    void createBoxMesh(const glm::vec3& size, const glm::vec3& position, const glm::vec3& rotation);    // Helper method to create a chain link mesh
    void createChainLinkMesh(float radius, float length, const glm::vec3& position, const glm::vec3& rotation, int32_t boneIndex);

    // Helper method to create a cylinder mesh for rope segments
    void createCylinderMesh(float radius, float length, const glm::vec3& position, const glm::vec3& rotation, int32_t boneIndex, int32_t prevBoneIndex, int32_t nextBoneIndex, int boneMeshSegments);

    PMXFile m_pmxFile;
    int32_t m_currentMaterialFaceCount;
};

} // namespace saba

#endif // !SABA_MODEL_PMXFILE_GENERATOR_H_
