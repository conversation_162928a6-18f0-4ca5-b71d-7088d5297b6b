# SnPhyFluid Class Documentation

## Overview
The `SnPhyFluid` class is a fluid simulation scene node based on PhysX PBD (Position Based Dynamics) particle system. It's designed to be similar to the existing `SnPhyCloth` class but specifically for fluid simulation.

## Key Differences from SnPhyCloth

### 1. Particle System Type
- **SnPhyCloth**: Uses `PxParticleClothBuffer` for cloth simulation
- **SnPhyFluid**: Uses `PxParticleAndDiffuseBuffer` for fluid simulation with foam/bubble effects

### 2. Particle Initialization
- **SnPhyCloth**: 2D grid (numX, numZ) for cloth surface
- **SnPhyFluid**: 3D grid (numX, numY, numZ) for fluid volume

### 3. Material Properties
- **SnPhyCloth**: Spring stiffness, damping for cloth behavior
- **SnPhyFluid**: Viscosity, surface tension, cohesion, vorticity confinement for fluid behavior

### 4. Particle Phase Flags
- **SnPhyCloth**: `PxParticlePhaseFlag::eParticlePhaseSelfCollideFilter | PxParticlePhaseFlag::eParticlePhaseSelfCollide`
- **SnPhyFluid**: `PxParticlePhaseFlag::eParticlePhaseFluid | PxParticlePhaseFlag::eParticlePhaseSelfCollide`

### 5. Rendering
- **SnPhyCloth**: Triangulated mesh surface
- **SnPhyFluid**: Billboard quad rendering (2 triangles per particle, camera-facing)

### 6. Additional Features
- **SnPhyFluid** includes diffuse particle support for foam and bubble effects
- Configurable diffuse particle parameters (threshold, drag, buoyancy, etc.)
- Separate rendering system for diffuse particles with lifetime-based alpha fading

## Usage Example

```cpp
// Create fluid simulation
SnPhyFluid* fluid = new SnPhyFluid(
    1.0f,                           // size parameter
    sceneManager->getRootSceneNode(), // parent
    sceneManager,                   // scene manager
    -1,                            // id
    core::vector3df(0, 10, 0)      // position
);

// Configure parameters
fluid->numPointsX = 50;
fluid->numPointsY = 60;
fluid->numPointsZ = 30;
fluid->particleSpacing = 0.1f;
fluid->maxDiffuseParticles = 100000;
fluid->showDiffuseParticles = true;
fluid->particleSize = 0.5f;

// Optional: Create custom container
fluid->createFluidContainer(float3(20.0f, 15.0f, 20.0f), float3(0, 0, 0));
```

## Key Parameters

### Fluid Properties
- `fluidDensity`: Density of the fluid (default: 1000.0f for water)
- `particleSpacing`: Distance between particles (default: 0.1f)

### Grid Dimensions
- `numPointsX`, `numPointsY`, `numPointsZ`: Number of particles in each dimension

### Rendering Options
- `showDiffuseParticles`: Enable foam/bubble rendering
- `particleSize`: Size of billboard quads (camera-facing)

### Diffuse Particles
- `maxDiffuseParticles`: Maximum number of foam/bubble particles
- Automatically generated based on fluid motion and turbulence

### Container Setup
- `createFluidContainer()`: Creates collision planes for fluid containment
- Automatically called in constructor with default 15x20x15 container
- Can be called manually with custom size and position

## Implementation Notes

1. **Physics Integration**: Uses the same PhysX scene and context as SnPhyCloth
2. **Memory Management**: Properly handles CUDA pinned memory allocation/deallocation
3. **Thread Safety**: Includes thread synchronization for mesh updates
4. **Performance**: Optimized for GPU-based particle simulation
5. **Diffuse Particles**: Uses `getDiffusePositionLifeTime()` method (not `getDiffusePositions()`)
6. **Dual Rendering**: Separate meshes for main fluid particles and diffuse particles

## Files Created

1. **SnPhyFluid.h**: Header file with class declaration
2. **SnPhyFluid.cpp**: Implementation file with fluid simulation logic
3. **SnPhyFluidExample.cpp**: Usage examples and helper functions
4. **SnPhyFluid_README.md**: This documentation file

## Dependencies

- PhysX 5.x with GPU particle support
- CUDA runtime
- Irrlicht engine
- Same dependencies as SnPhyCloth

## Rendering Details

The fluid uses **billboard quad rendering** where each particle is represented by a camera-facing quad made of 2 triangles. This approach provides:

- **Better visual quality** than point rendering
- **Proper transparency blending** with Irrlicht's material system
- **Scalable particle size** through the `particleSize` parameter
- **Camera-facing orientation** for consistent appearance from any angle

Each particle generates:
- **4 vertices** forming a quad
- **6 indices** forming 2 triangles
- **Texture coordinates** for potential texture mapping

## Future Enhancements

1. **Texture Mapping**: Add particle textures for better visual effects
2. **Surface Reconstruction**: Add marching cubes or similar for smooth fluid surface
3. **Interaction**: Add methods for applying forces, adding/removing particles
4. **Optimization**: Further GPU optimization for large particle counts
5. **Shader Integration**: Custom shaders for fluid rendering effects
6. **Diffuse Particle Rendering**: Implement separate rendering for foam/bubbles
