﻿#include "AppGlobal.h"
#include "irrmmd.h"
#include <cppIncDefine.h>
#include <filesystem>
#include <regex>
#include <string>
#include <iostream>
#include <chrono>
#include <VulkanRenderer/VkDriver.h>
#include "stlUtils.h"
#include "../../app/armmplayer/snarRoot.h"
#include <glm/gtx/matrix_decompose.hpp>
#include "SnPhyFluid.h"
using namespace irr;
using namespace irr::scene;
using namespace ualib;
using namespace glm;
void testcode1();

#if IS_WIN_DBG
#define MMDFWD(T,N,P, V, C) 		do{ sb0->mmdFw((T),(N),(P),(V),(C));}while(0) //FID will not change at runtime
#define MMDFWLineD(T,N,P, V, C,S) 		do{ sb0->mmdFwLine((T),(N),(P),(V),(C),(S));}while(0) //FID will not change at runtime
#else
#define MMDFWD( )
#define MMDFWLineD( )
#endif

#define MMD_HAS_FW 1
#ifdef _WIN32
#if			1
#if			1
#define MMD_NUM 1
#define PMX_FILE "D:/MMD/PMX/ld/wrt.pmx"//xbr/xbrWrt.pmx"///ganyu/gyWrt.pmx"//mikuA/wrt.pmx"/zzA/1.pmx"//zz/hnd.pmx"//shenhe/s.pmx"//angela/1.pmx"//ld/gc.pmx"//keli/s.pmx"//a/a.pmx"//ld/wrt.pmx"//ganyu/gc.pmx"//diona/gc.pmx"//zz/wrtlh.pmx"
//yelan/vf.pmx"//8ch/en.pmx"// shenli/slIkAr.pmx"//  mikuc/h.pmx"//zz/vf.pmx"//97r/e.pmx"//ganyu/e.pmx"//lilya/1.pmx"//mikuc/h.pmx"//
//fumo/1.pmx"//miya/1.pmx"//angela/1.pmx"//ouro/1.pmx"//
//shenli/slIkAr.pmx"//ld/rdm.pmx"//miku/m2.pmx" // mikuO2/miku.pmx"//ganyu/s.pmx"//8ch/s.pmx"//ld/ld.pmx"// ganyu/s.pmx"//zz/1.pmx"////shenli/sl.pmx"
//mikuo/miku.pmx"//zy/zy.pmx"//  xg/ik.pmx" //D:/MMD/PMX/
#define VMD_NAME "write"//cycStand2"//StandHandSeals"//layOfs"//"layft"//gamecast1"//laym2"//jl"//s1"//fcs"//dance"//rdBlade"//yao1"//f2b"//layPoses"
//runAR"//ai_bj"//rdBlade"//dance"//stdRHU"// layPoses"
//StandHandSeals" //vmd/sit stands"  //stands //standOph"//
#else
#define MMD_NUM  2
#define PMX_FILE "D:/MMD/PMX/97r/e.pmx"//mikuA/vf.pmx"//neruB/1.pmx"//zz/h.pmx"//yelan/s.pmx"//shenhe/headIk.pmx"//shenli/slIkAr.pmx"//keli/s.pmx"//zl/headIk.pmx"//ld/rdhik.pmx"//shenhe/s.pmx"//
#define VMD_NAME "f2a"//jl"//laym2"//layl"//dive/r1"//yao1"//tak/1k"//"d:/tmp/palette/1.vmd"//
#endif
#define PMX_FILE1 "D:/MMD/PMX/ld/wrt.pmx"//8ch/s.pmx"//zzA/1.pmx"//angela/1.pmx"//ganyu/s.pmx"//ganyu/gy1ikAR.pmx"//diona/s.pmx"//lnren/headIk.pmx"//windy/s.pmx"//kq2/s.pmx" ganyu/s.pmx"//
#define VMD_NAME1 "cycStand2"//jl"//laym3"//layr"//dive/r2"//yao2"//rdBlade"//tak/3km"//"f2b"//rdBlade1"//standOph1"//layPoses1"//balei"//dance"//
#if 0
#define VMD_FILE  "D:/tmp/ps2/1.vmd"//moon/1.vmd"//"D:/tmp/echo/sitmouth.vmd"// "G:/MMD/v/shakeIt/1.vmd"//"d:/mmd/vmd/" VMD_NAME ".vmd"
#define VMD_FILE1 "G:/MMD/v/shakeIt/1.vmd"
#else
#define VMD_FILE  "d:/mmd/vmd/" VMD_NAME ".vmd"
#define VMD_FILE1 "d:/mmd/vmd/" VMD_NAME1 ".vmd"
#endif
#define PMX_FILE2  "D:/MMD/PMX/ganyu/gyWrt.pmx"//youla/s.pmx"//zzA/1.pmx"
#define VMD_FILE2 "d:/mmd/vmd/cycStand2Sub.vmd"


//#define VMD_CAN_FILE "d:/mmd/vmd/writekm30cam.vmd"//rdBladeCam.vmd"//"d:/tmp/echo/cam.vmd"//"d:/mmd/vmd/sithdcam.vmd"//s1cam.vmd"//"mmd/vmd/f2cam.vmd"//DTBcam.vmd"//
#elif 0
#define MMD_NUM  3
#define PMX_FILE  "D:/MMD/PMX/ganyu/gy1ikAR.pmx"//""mmd/pmx/s/1.pmx"//miku/gy.pmx"
#define VMD_FILE  "D:/MMD/VMD/f3a.vmd"//rdBlade.vmd"   //"mmd/vmd/yao1.vmd"

#define PMX_FILE1 "D:/MMD/PMX/shenhe/headIk.pmx"//ld/rdhik.pmx"//"mmd/pmx/s/1.pmx"//
#define VMD_FILE1 "D:/MMD/VMD/f3b.vmd"//rdBlade.vmd"   //"mmd/vmd/yao2.vmd"
#define PMX_FILE2 "D:/MMD/PMX/youla/s.pmx" //ld/rdhik.pmx"//"mmd/pmx/s/1.pmx"//miku/miku.pmx"
#define VMD_FILE2 "D:/MMD/VMD/f3c.vmd"//rdBlade1.vmd" //"mmd/vmd/tak/2k.vmd"
#elif 1
#define MMD_NUM  3
#define CYCPMX "youla/s"//shenli/sl"//ld/fix"
#define PMX_FILE   "D:/MMD/PMX/" CYCPMX ".pmx"//""mmd/pmx/s/1.pmx"//miku/gy.pmx"
#define PMX_FILE1  "D:/MMD/PMX/bb/bbL.pmx"//ld/fix.pmx"//"D:/MMD/PMX/" CYCPMX ".pmx"//zzA/1.pmx"
#define PMX_FILE2  "D:/MMD/PMX/8ch/s.pmx"//zzA/1.pmx"
#define CYCNAME "Stand"//HS3"//runBackTi"//
#define VMD_FILE  "d:/mmd/vmd/cyc" CYCNAME ".vmd"
#define VMD_FILE1 "d:/mmd/vmd/cyc" CYCNAME ".vmd"
#define VMD_FILE2 "d:/mmd/vmd/cyc" CYCNAME "Sub.vmd"

#define INF_CAM  "d:/mmd/vmd/cyc" CYCNAME "Cam.vmd" //rdBladeCam1.vmd";//standOphcam.vmd";//layPosesCam2.vmd"
#else
#define MMD_NUM  3
#define PMX_FILE  "D:/MMD/PMX/shenhe/headIk.pmx"//diona/s.pmx"//yor/1.pmx"//"mmd/pmx/miku/gy.pmx"
#define VMD_FILE  "mmd/vmd/f3c.vmd"//tak/1k.vmd"//f2a.vmd"//

#define PMX_FILE1 "D:/MMD/PMX/ganyu/gy1ikAR.pmx"//"mmd/pmx/zz/1.pmx"
#define VMD_FILE1 "mmd/vmd/f3a.vmd"//tak/3k.vmd" //f2b.vmd"//
#define PMX_FILE2 "D:/MMD/PMX/youla/s.pmx"//keli/s.pmx"//yor/2.pmx"//"mmd/pmx/miku/miku.pmx"
#define VMD_FILE2 "mmd/vmd/f3b.vmd"//tak/2k.vmd"

#define VMD_CAN_FILE "mmd/vmd/f3cam.vmd"//f2cam.vmd"//
#endif

 //#define VMD_FILE "d:/mmd/vmd/" VMD_NAME ".vmd" //

#else
#define MMD_NUM  1
#define PMX_FILE	"mmd/pmx/miku/gy.pmx"//angela/1.pmx"//"mmd/pmx/shenhe/1.pmx"
#define VMD_FILE	"mmd/vmd/standS.vmd"
#define PMX_FILE1	"mmd/pmx/miku/gy.pmx"//shenhe/1.pmx"//"mmd/pmx/zz/1.pmx"
#define VMD_FILE1	"mmd/vmd/yao2.vmd"
#define PMX_FILE2	"mmd/pmx/miku/gy.pmx"//miku/miku.pmx"
#define VMD_FILE2	"mmd/vmd/laym2.vmd"
#endif


// hmu r 180 p 5, -1, 0 |  -90 0, 0, 0
#if 0
#define SBCRTT 90, 190, 135 //blade
#define SBCOFS - 1.6,- 1, 1
#elif 0
#define SBCRTT 180, 250, 0 //cycStand
#define SBCOFS  0, 0, 0
#elif 0
#define SBCRTT -180, 30, 45
#define SBCOFS  0, 0, 0
#else 1
#define SBCRTT  0, 0, 0
#define SBCOFS  0, 0, 0
#endif


namespace fs = std::filesystem;

// Convert std::filesystem::file_time_type to std::chrono::system_clock::time_point
std::chrono::system_clock::time_point to_time_point(const std::filesystem::file_time_type& file_time) {
	return std::chrono::system_clock::time_point(file_time - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
}

// Usage

std::string findLatestAutosaveFile(const std::string& directory) {
	std::regex autosavePattern(R"(autosave(\d+)\.json)");
	std::string latestFile;
	std::chrono::system_clock::time_point latestTime;

	for (const auto& entry : fs::directory_iterator(directory)) {
		if (entry.is_regular_file()) {
			std::smatch match;
			std::string filename = entry.path().filename().string();
			if (std::regex_match(filename, match, autosavePattern)) {
				auto fileTime =   to_time_point(fs::last_write_time(entry));
				if (latestFile.empty() || fileTime > latestTime) {
					latestTime = fileTime;
					latestFile = entry.path().string();
				}
			}
		}
	}

	return latestFile;
}

IrrMMD::IrrMMD(IrrMmdParam pm):Pm(pm)
{
	//testcode1();

	Ctx = Pm.spm.ctx;
	arRoot = pm.arRoot;
	SceneManager = Ctx->getSceneManager();
	mdplr.Ctx = Ctx;
	baseMatrix = glm::scale(glm::mat4(1), glm::vec3(MMD_SABA_SCALE));
	Pm.spm.snMmdRoot =     Ctx->getSceneManager()->addEmptySceneNode(Pm.spm.snMmdRoot);
	saba::Fs = Ctx->getFileSystem();
	//Pm.spm.snMmdRoot->setScale(0.5f);
	//Pm.spm.snMmdRoot->setVisible(false);
#if !IS_WIN
	Pm.spm.snMmdRoot->paraChildrenAnimate = true;
#endif

#if                         1
	pmxPaths.push_back(PMX_FILE);
	pmxPaths.push_back(PMX_FILE1);
	pmxPaths.push_back(PMX_FILE2);
	pmxPaths.push_back("D:/MMD/PMX/nilu/o.pmx");
	pmxPaths.push_back("D:/MMD/PMX/sl2/sf.pmx");
	pmxPaths.push_back("D:/MMD/PMX/ganyu/s.pmx");
	pmxPaths.push_back("D:/MMD/PMX/shenhe/s.pmx");
	//pmxPaths.push_back("D:/MMD/PMX/97r/e.pmx");
	pmxPaths.push_back(PMX_FILE);
	pmxPaths.push_back(PMX_FILE1);
#else

	pmxPaths.push_back(PMX_FILE);
	pmxPaths.push_back(PMX_FILE1);
	pmxPaths.push_back(PMX_FILE2);

#endif
	shdpm.shadowRat = 0.1f;
	shdpm.whiteRat = 0.78f;
	shdpm.NdotLRat = 0.08f;
	shdpm.NdotEyeRat = 0.1f;
	shdpm.toonMulCount = 2;



	saba::gd.frameTime = 1.f / APP_FPS;
	//test
	//Pm.spm.rootOfs = float3(-20, 0, 0);

}

IrrMMD::~IrrMMD()
{

}

void irr::scene::IrrMMD::createSabas(int num)
{
	bRecreate = false;
	mmdMaxId = std::max(num-1, MMD_NUM-1);
	Pm.spm.mmd = this;
	Pm.spm.hasFW = MMD_HAS_FW;
	Pm.spm.scale = MMD_SABA_SCALE ;
#ifdef INF_CAM
	if (Pm.mmdInfinity) Pm.spm.camVmdFile = INF_CAM;
#endif
	//RootNode->getSceneManager()->addCubeSceneNode(100, RootNode);
	auto spm = Pm.spm;

	IrrSaba* saba{};
	if (sabas.size() == 0)
	{
		spm.idx = 0;
		spm.pmxFile = pmxPaths[0];
		spm.vmdFile = vmdPaths[0].size() > 0 ? vmdPaths[0] : io::path(VMD_FILE);
		//spm.vmdFile = "d:/mmd/vmd/std.vmd";
		spm.needSaveCamVmd = MMD_SAVE_CAMVMD;
#if defined(VMD_CAN_FILE)  && IS_WIN
		camPath = VMD_CAN_FILE;
		spm.camVmdFile = camPath;
#endif
		spm.drawCam = MMD_DRAW_CAM;

#if AR_PATH_ON_STEP

#else
		//spm.rootOfs = float3(-10,0,0);
#endif
		spm.activeNodeCam = !Pm.mmdInfinity;
		//spm.scale = 1000;
		saba = new irr::scene::IrrSaba(spm.snMmdRoot, spm.ctx->getSceneManager(), -1, spm);
		saba->drop();
		saba->nearKiss = 1;
		saba->resetAnimation(0, MMD_REVERSE_PLAY);
		saba->passTypeFlags |= IrrPassType_ShadowMap;

		loadMiscs(saba);
		sabas.push_back(saba);
		sb0 = saba;
	}

	if (Pm.mmdInfinity)
	{
		auto spm = Pm.spm;
		spm.idx = 1;
		spm.pmxFile = pmxPaths[1];
		spm.vmdFile = vmdPaths[1].size() > 0 ? vmdPaths[1] : io::path(VMD_FILE1);
		spm.needSaveCamVmd = false;
		saba->setAnimationSpeed(0.0f); //saba->setPlaying(false);
		spm.scale = (float)INFMMD_CHILD_SCALE;
	 	spm.relToParent = float3(SBCOFS);
		auto sbc = new irr::scene::IrrSaba(saba, spm.ctx->getSceneManager(), -1, spm);

		//sbc->Pmx->rootTr= float3(SBCOFS);
		sbc->setRotation({ SBCRTT });
		sbc->setPosition(float3(SBCOFS) * float(INFMMD_CHILD_SCALE));
		//sbc->setAnimationSpeed(0.f);//sbc->setPlaying(false);
		sbc->drop();
		doAttach0(sbc);
		sabas.push_back(sbc);

		spm.idx = 2;
		spm.pmxFile = pmxPaths[2];
		spm.vmdFile = VMD_FILE2;
		spm.needSaveCamVmd = false;
		//spm.skipLastKey = true;
		//spm.scale = 1;// 0.05f;
		spm.relToParent = float3(SBCOFS);
		sbc = new irr::scene::IrrSaba(sbc, spm.ctx->getSceneManager(), -1, spm);
		//sbc->Pmx->rootTr = float3(SBCOFS);
		sbc->setRotation({ SBCRTT });
		sbc->setPosition(float3(SBCOFS)* float(INFMMD_CHILD_SCALE));
		//sbc->setAnimationSpeed(0.f);//sbc->setPlaying(false);
		sbc->drop();
		doAttach0(sbc);
		sabas.push_back(sbc);

		//sabas[1]->copyPoseTo(0, sabas[2], -1);
		return;
	}

	spm = Pm.spm;
	if (mmdMaxId >= 1 && sabas.size() <= 1) {
		spm.idx = 1;
		spm.pmxFile = pmxPaths[1];
		spm.vmdFile = vmdPaths[1].size() > 0 ? vmdPaths[1] : io::path(VMD_FILE1);
		//spm.scale *= 1.1;
		auto saba = new irr::scene::IrrSaba(spm.snMmdRoot, spm.ctx->getSceneManager(), -1, spm);
		saba->drop();
		saba->nearKiss = 1;
		saba->passTypeFlags |= IrrPassType_ShadowMap;
		sabas.push_back(saba);
	}

	spm = Pm.spm;
	if (mmdMaxId >= 2 && sabas.size() <= 2) {
		spm.idx = 2;
		spm.pmxFile = pmxPaths[2];
		spm.vmdFile = vmdPaths[2].size() > 0 ? vmdPaths[2] : io::path(VMD_FILE2);
		auto saba = new irr::scene::IrrSaba(spm.snMmdRoot, spm.ctx->getSceneManager(), -1, spm);
		saba->drop();
		saba->passTypeFlags |= IrrPassType_ShadowMap;
		sabas.push_back(saba);
	}
}

void irr::scene::IrrMMD::releaseSabas()
{
if (Pm.mmdInfinity)
	sabas[0]->remove();
else
	for (auto& saba : sabas)
	{
		saba->remove();
	}

	sabas.clear();
}

void irr::scene::IrrMMD::preUpdate()
{
	if (!sb0) return;
	if (bRecreate)
	{
		releaseSabas();
		createSabas(mmdMaxId+1);
	}
	auto cam = Ctx->getViewCamera(Ctx->gd.CamFpv?ev_fpv:Ctx->mainViewId);
	if (!cam) cam = Ctx->gd.CamRtt;
	mat4 mc = sb0->mmdBaseInv * cam->getAbsoluteTransformation();

	glm::vec3 scale;
	glm::vec3 skew; glm::quat mmdCamRtt;
	glm::vec4 perspective;
	//camPos = vec3(0);
	glm::decompose(mc, scale, mmdCamRtt, camPos, skew, perspective);
	static float phyTimeLast = 0;
	camMat = glm::translate(glm::mat4(1), camPos) * glm::toMat4(mmdCamRtt);
	camVel = (camPos - camPosLast)/(gPhyTime-phyTimeLast);
	camPosLast = camPos;
	phyTimeLast = gPhyTime;

	hasBloom = 0;
}

void irr::scene::IrrMMD::postUpdate()
{
	if (MPA) MPA->update();
	if (MPA != mainMPA) mainMPA->update();
}


void irr::scene::IrrMMD::reloadMMD()
{
	{
		auto saba = sabas[0];

		saba->loadModel(pmxPaths[0]);
		saba->loadAnimation("");
		saba->resetAnimation(0, MMD_REVERSE_PLAY);
		//if (saba->Pm.camVmdFile.size() > 0) saba->loadAnimation(saba->Pm.camVmdFile);
		//doAttach0();
		loadMiscs(saba);
		//saba->setPosition(0,100,0);
		//saba->setRotation({ 0, 180, 0 });
		//saba->setVisible(false);
		// create light
		//snLight->setParent(Ctx->gd.CamRtt);		snLight->setPosition(0, 10, -10); //RootSn->setPosition(0, 0, 20);

	}

	if (sabas.size()>1)
	{
		auto saba = sabas[1];

		saba->loadModel(pmxPaths[1]);
		saba->loadAnimation("");
		if (saba->Pm.camVmdFile.size() > 0) saba->loadAnimation(saba->Pm.camVmdFile);
		doAttach0(saba);
	}
	if (sabas.size() > 2)
	{
		auto saba = sabas[2];

		saba->loadModel(pmxPaths[2]);
		saba->loadAnimation("");
		if (saba->Pm.camVmdFile.size() > 0) saba->loadAnimation(saba->Pm.camVmdFile);
		doAttach0(saba);
	}

	if (Pm.mmdInfinity)
	{
		sabas[1]->copyPoseTo(0, sabas[2], -1);

	}
}

void irr::scene::IrrMMD::loadPoseId(int id, int frameNum)
{
#if IS_WIN
	std::wstring s = ualib::wstrFmt(L"mmd/vpd/%d.vpd",id);
	sabas[0]->syncToFrame(99999, frameNum,2);
	sabas[0]->clearAnimation();
	sabas[0]->loadPose(s.c_str(), id);
#endif
}

void irr::scene::IrrMMD::loadMiscs(irr::scene::IrrSaba* saba)
{
#if MMD_PLAY_GUQIN
	saba::MMDPhysics *phy=saba->mModel->GetPhysicsManager()->GetMMDPhysics();
	phy->addBox(float3(10, 0.1, 1.5), float3(0, 9.0+0.5 + GUQIN_YADD, -0.7), float3(0, 10*core::DEGTORAD, 0));
#endif

#if MMD_MONTAGE

#if MMD_ACTION_ANIM

#elif MMD_PLAY_GUQIN
	saba->loadAppendAnimation("d:/mmd/vmd/BACKUP/guqin/tanRH00.vmd");
	saba->loadAppendAnimation("d:/mmd/vmd/BACKUP/guqin/tanRH01.vmd");
	saba->loadAppendAnimation("d:/mmd/vmd/BACKUP/guqin/tanRH02.vmd");
	saba->loadAppendAnimation("d:/mmd/vmd/BACKUP/guqin/tanRH10.vmd");
	saba->loadAppendAnimation("d:/mmd/vmd/BACKUP/guqin/tanRH11.vmd");
	saba->loadAppendAnimation("d:/mmd/vmd/BACKUP/guqin/tanRH12.vmd");
#elif MMD_MTG_HANDSEAL
	saba->loadAppendAnimation("d:/mmd/vmd/handseals.vmd");
	saba->loadAppendAnimation("d:/mmd/vmd/handseals.vmd");
#else
	//saba->loadAppendAnimation("d:/mmd/vmd/d0.vmd");
	//......
	////saba->loadAppendAnimation("d:/mmd/vmd/d9.vmd");
#endif
#endif
}

void irr::scene::IrrMMD::setAnimating(bool aning)
{
	animating = aning;
	for (auto& s : sabas)
	{
		s->setPlaying(animating);
	}
}

void irr::scene::IrrMMD::doAttach0(IrrSaba*p)
{
	if (Pm.mmdInfinity)
	if (!p->attachToParentNode(INF_MMD_ATTACH))
		p->attachToParentNode(L"右手首");

}

void irr::scene::IrrMMD::toggleLnv(int mode)
{
	for (auto sb : sabas) sb->toggleLnv(mode);
}

void irr::scene::IrrMMD::setOverrideTIme(int64_t us, float setGameTime, int64_t deltaUs)
{
#if MMD_OVERRIDE_TIME
	owMmdTimeUs = us;
	owDeltaUs = deltaUs;
	if (owMmdTimeUsLast == us && us >= 0)
		owMmdTimeUs += deltaUs / 2;
	owMmdTimeUsLast = us;
	owSetTime = setGameTime;
#endif
}

void irr::scene::IrrMMD::freeAllRes()
{
	if (mainMPA) {
		delete mainMPA; mainMPA = nullptr;
	}
	for (int i = 0; i < 8; i++) if (gPoMan[i])
		gPoMan[i]->removeAllObjs();
	IrrSaba::Sbds.freeAllRes();
}

IrrSaba* irr::scene::IrrMMD::getSaba(int id)
{
	if (sabas.size() == 0) return nullptr;
	//if (id >= sabas.size()) return sabas[0];
	if (id < 0) return sabas[UaRand(sabas.size())];

	return sabas[id% sabas.size()];
}

IrrSaba* irr::scene::IrrMMD::getAnmGlbNearestSaba(glm::vec3 tgt)
{
	if (sabas.empty())
		return nullptr;



	// Find closest saba based on distance to camera
	IrrSaba* nearest = nullptr;
	float minDist = FLT_MAX;

	for (auto saba : sabas) {
		if (!saba->IsVisible)
			continue;

		// Get MMD model center position
		core::vector3df pos;
		if (saba->ndYao) {
			// Use waist node position if available
			pos = saba->ndYao->mGlobalAnim[3];
			//saba->mmdToIrrPos(pos); // Convert from MMD to Irrlicht coordinates
		}
		else {
			// Fallback to node position
			pos = saba->ndRoot->mGlobalAnim[3];
		}

		const float dist = pos.getDistanceFromSQ(tgt);
		if (dist < minDist) {
			minDist = dist;
			nearest = saba;
		}
	}

	return nearest;
}

IrrSaba* irr::scene::IrrMMD::nextSaba(int id,int ofs)
{

	return sabas[(id + ofs) % sabas.size()];
}

IrrSaba* irr::scene::IrrMMD::nextSaba(IrrSaba* cursb)
{
	for (size_t i = 0; i < sabas.size(); i++)
		if (sabas[i] == cursb) return sabas[(i + 1) % sabas.size()];

	return nullptr;
}



void irr::scene::IrrMMD::resetAni(float startS) {

	for (size_t i=0;i<sabas.size();i++) sabas[i]->resetAnimation(startS, MMD_REVERSE_PLAY);

}

bool irr::scene::IrrMMD::switchSabas(int idx)
{
#if 0
	if (idx + 3 >= pmxPaths.size()) return false;
	auto parent = sabas[0]->getParent();
	auto t = sabas[0];
	sabas[0] = sabas[1];
	sabas[1] = sabas[2];
	sabas[2] = t;
	//sabas[1]->setParent(sabas[0]);
	//sabas[2]->setParent(sabas[1]);
	//doAttach0(sabas[1]);
	//doAttach0(sabas[2]);
	if (INFMMD_REMOVE_OLD) {
		core::matrix4 mNewAbs0 = sabas[0]->getAbsoluteTransformation();
		sabas[0]->setScale(MMD_SABA_SCALE);
		if (INFMMD_KEEP_POS) {
			auto m1 = sabas[0]->getRelativeTransformation();
			core::matrix4 m1i; m1.getInverse(m1i);
			parent->setAbsMatrix(mNewAbs0 * m1i);
		}
		sabas[0]->setParent(parent); sabas[0]->attachToParentNode(nullptr);
		sabas[1]->setScale(INFMMD_CHILD_SCALE);
	}
	//sabas[2]->setScale(INFMMD_CHILD_SCALE);
#if 0
	auto ta = std::move(sabas[0]->Vmd);
	auto tc= std::move(sabas[0]->m_vmdCameraAnim);
	sabas[0]->Vmd = std::move(sabas[1]->Vmd);
	sabas[0]->m_vmdCameraAnim = std::move(sabas[1]->m_vmdCameraAnim);
	sabas[1]->Vmd = std::move(sabas[2]->Vmd);
	sabas[1]->m_vmdCameraAnim = std::move(sabas[2]->m_vmdCameraAnim);
#else
	sabas[0]->loadAnimation(VMD_FILE,false,false);

	sabas[1]->loadAnimation(VMD_FILE1, false, false);
#ifdef INF_CAM
	sabas[0]->loadAnimation(INF_CAM, true);
	sabas[1]->loadAnimation(INF_CAM, true);
#endif
	//sabas[2]->loadAnimation(VMD_FILE2, false, true);
	//sabas[2]->loadAnimation(INF_CAM, true);
#endif
	auto spm = Pm.spm;
	spm.scale = INFMMD_CHILD_SCALE;
	spm.idx = idx + 3;
	spm.pmxFile = pmxPaths[idx+3];
	spm.vmdFile = VMD_FILE2;
	spm.needSaveCamVmd = false;
	//spm.skipLastKey = true;
	//spm.scale = 1;// 0.05f;
	spm.relToParent = float3(SBCOFS);
	if (INFMMD_REMOVE_OLD)
		sabas[2]->remove();
	else {
		sabas[2]->setPhysics(false); sabas[2]->setAnimationSpeed(0);
	}
	auto sbc = sabas[2] = new irr::scene::IrrSaba(sabas[1], spm.ctx->getSceneManager(), -1, spm);
#ifdef INF_CAM
	sabas[2]->loadAnimation(INF_CAM, true);
#endif
	//sbc->Pmx->rootTr = float3(SBCOFS);
	sbc->setRotation({ SBCRTT });
	sbc->setPosition(float3(SBCOFS) * float(INFMMD_CHILD_SCALE));
	//sbc->setAnimationSpeed(0.f);//sbc->setPlaying(false);
	sbc->drop();
	doAttach0(sbc);
	//sabas.push_back(sbc);
#endif
	return true;
}

IrrSaba* irr::scene::IrrMMD::curAiSaba()
{
	return arRoot->curAiSb();
}

void irr::scene::IrrMMD::setPmxFile(irr::io::path fp) {
	for (auto& pmxfp : pmxPaths)
		pmxfp = fp;
	bRecreate = true;

}

void irr::scene::IrrMMD::resetPmxFiles(int count)
{
	if (count > 0)
		mmdMaxId = count - 1;
	pmxPaths[0]=PMX_FILE;
	pmxPaths[1]=PMX_FILE1;
	pmxPaths[2]=PMX_FILE2;
	bRecreate = true;
}


void irr::scene::IrrMMD::getInfo(io::path fp, MMDInfo& mi)
{
	auto pmxModel = std::make_shared<saba::PMXModel>();
	pmxModel->setTexInvV(true);
	//pmxModel->bAddWingBones = true;
	bool useGPU;
	if (!pmxModel->Load(fp.c_strA(), "", useGPU,true))
	{
		return ;
	}
	mi.info = pmxModel->info;
	pmxModel = nullptr;
}

bool irr::scene::IrrMMD::drawPauseDemo(irr::video::ITexture* texDemo)
{
	if (   !RootNode->isVisible())
	return false;

	//Ctx->getSceneManager()->setActiveCamera(sabas[0]->irrCam);
	irr::video::VkDriver* driver=(irr::video::VkDriver*)Ctx->getDriver();

	driver->setRenderTarget(texDemo, 1, 1, 0);// 1, 1, 0xFF000000 | std::min(255, 8 * pauseDemoFr) << 16);
	driver->resetFrameCache();
	Ctx->getSceneManager()->drawPassType(IrrPassType_Models);
	driver->UploadSharedBuffers();
	driver->flush();

	//Ctx->getSceneManager()->setActiveCamera(Ctx->gd.CamNormal);

	return true;
}

void irr::scene::IrrMMD::createMrk()
{
	if (!MPA && sabas.size() >= 1) {
		auto sbs = { 0,1,2,3,4,5,6,7 };
		//sbs.insert(sbs.begin(), sabas.back()); sbs.erase(sbs.end()-1);
		mainMPA =  MPA = new MmdPhyAnimator({ this,sbs,true,nullptr });
		//mrk->newKeyIf(1, 0);
	}

}

bool irr::scene::IrrMMD::onKeyEvent(bool pressed, const irr::SEvent::SKeyInput& ke)
{
	if (!pressed) return false; if (!MPA) return false;
	if (SceneManager->WSADMode != ISceneManager::EWSADModes::eRig || MPA->inputingKey)
		return false;

	switch (ke.Key)
	{
	case irr::KEY_LEFT:	case irr::KEY_RIGHT:
	{
		int inc = (ke.Key == irr::KEY_RIGHT ? 1 : -1);
		if (inc && ke.Control)
		{
			MPA->copySelectedKeys();

			// Move cursor to next frame after copying
			int nextFrame = MPA->getCurFrame() + inc;
			MPA->setCurFrame(nextFrame);

			MPA->pasteKeysToCurrentFrame(1);
		}
		else if (inc==1) MPA->toNextKey(); else MPA->toPrevKey();

		return true;
	}
	case irr::KEY_DOWN:	case irr::KEY_UP:
	{
		if (ke.Control)
		{
			rigPlaybackTimeMul *= ke.Key == irr::KEY_UP ? 2.f : 0.5f;
		}
		else MPA->switchNodes(ke.Key == irr::KEY_UP ? 1 : -1);
		return true;
	}
	case irr::KEY_KEY_A:
	{
		//mrk->toClosestKeyTime(mrk->currentTime);
		if (ke.Control && ke.Shift)
			MPA->extendKeysWithRotation(glm::radians( - 30.f));
		else
		if (ke.Shift)
		{
			MPA->newKeyIf(0, 0, 1); MPA->setPose();
			MPA->setCurFrame(MPA->getCurFrame() - 3);
			MPA->newKeyIf(0, 1, 1); MPA->setPose();
			MPA->curKey()->pose.fmul = 0;
			MPA->setCurFrame(MPA->getCurFrame() + 6);
			MPA->newKeyIf(0, 1, 1); MPA->setPose();
			MPA->curKey()->pose.fmul = 0;
		}
		else if (MPA->currentKeyIndex() >= 0 && MPA->currentKeyIndex() == MPA->keyCount() - 1)
		{
			MPA->newKeyIf(1, 1); MPA->setPose();
		}
		else MPA->newKeyIf(  0, 1), MPA->setPose();;

		return true;
	}
	case irr::KEY_KEY_D:
	{
		//SceneManager->WSADMode = ISceneManager::EWSADModes::eMove;
		if (ke.Control)
			MPA->removeNode(0);
		else
			MPA->deleteSelectedKeys();
		return true;
	}
	case irr::KEY_KEY_E:
	{
		if (ke.Control)
		rigSaveFile();
		else {
			auto filename = "data/rigFrSave.json";
			std::string directory = "data/tmp";
			std::string latestFile = Ctx->jss["mpa"]["lastSavedFile"].asString();// findLatestAutosaveFile(directory);
			if (!latestFile.empty()) {
				MPA->loadFromFile({ latestFile });
			}
			else {
				MPA->loadFromFile({ filename });
			}

		}
		return true;
	}
	case irr::KEY_KEY_F:
	{
		if (ke.Alt) return false;
		return true;
	}
	case irr::KEY_KEY_G:
	{

		return  MPA->toggleCamera();
	}
	case irr::KEY_SPACE:
	{
		if (MPA->isPlaying())
			MPA->playStop();
		else if (MPA->playStart(10, 0)) {
			static int cc = 0; cc = (cc + 1) % 10;
			if (MPA->maxCumFrame>1)
			MPA->saveToFile("data/tmp/autosave" + std::to_string(cc) + ".json");
		}

		return true;
	}
	case irr::KEY_KEY_J:
	{
		if (ke.Control)
		{
			sb0->Pmx->saveState();
		}
		  sb0->frameRestore ^= 1;
		  sb0->setPhyAnim(0, !sb0->frameRestore, true);
		  sb0->rd.phyAniMul = sb0->frameRestore ? 0.1f : 1.f;
		return true;
	}
	case irr::KEY_KEY_Q:
	{
		if (ikVisable)
			arRoot->switchGizmoMode();
		else
			rigForceMul*=ke.Shift?0.5f:2.0f;
		return true;
	}
	case irr::KEY_KEY_R:
	{
		mainMPA->playStop(  );
		mainMPA->playStart(1, 0);
		{

			if (sb0->sbMpa && sb0->sbMpa->mpaMap.size()) {
				auto mpa = sb0->sbMpa->mpaMap.begin()->second.mpa;
				static float lastTime = gEditorTime;
				if (mpa->maxCumFrame > 1 && gEditorTime-lastTime>10.f) {
					static int cc = 0; cc = (cc + 1) % 10;
					lastTime = gEditorTime;
					mpa->saveToFile("R:/img/sb0_autosave" + std::to_string(cc) + ".json");
				}
			}
		}
		return false;
	}
	case irr::KEY_KEY_S:
	{
		MPA->setPose();
		return true;
	}
	case irr::KEY_KEY_C: if (ke.Control) { MPA->copySelectedKeys(); return true; }		break;
	case irr::KEY_KEY_V: if (ke.Control) { MPA->pasteKeysToCurrentFrame(ke.Shift); return true; }		break;
	case irr::KEY_KEY_W:
	{
		if (ikVisable)
		{
			arRoot->switchGizmoGL();
			break;
		}
		if (sabas.size() < 2) Ctx->getLib()->CurStage()->sendKeyEvent(KEY_KEY_M, 1, 0, 0, 0);
		rigSetInitNodes();
		return true;
	}

	case irr::KEY_KEY_X:  
	{
		if (ke.Control)
		MPA->setBase(arRoot->curPickNode.sbNode);		//set node base
		else {
			if (arRoot->curPickNode.sbNode)
				MPA->setNode(arRoot->curPickNode.sbNode);  //createNode

		}
		return true;
	}


	case irr::KEY_KEY_Z:
	{
		if (ke.Control && ke.Shift) MPA->redo();
		else if (ke.Control) MPA->undo();
		else if (Ctx->getEvtRcv()->isMouseKeyDown(0) && ke.Alt)
		{
			Ctx->gd.ignoreNextMouseUp[0] = true;
			break;
		}
		return true;
	}

	}
	return false;
}

bool irr::scene::IrrMMD::onPointerEvent(const irr::SEvent::SPointInput& pe)
{
	if (SceneManager->WSADMode != ISceneManager::EWSADModes::eRig)
		return false;
	if (pe.act == EPA_MouseWheel)
	{
		if (pe.Control) {

			//mrk->switchNodes(pe.Wheel > 0 ? 1 : -1);

		}
		else
		{


		}
		return true;
	}
	else if (pe.act == EPA_Down) {

	}
	return false;
}

void irr::scene::IrrMMD::curCtrlSb(std::function<void(irr::scene::IrrSaba*)> cb)
{
	if (!syncMovNode) {
		if (auto sb = arRoot->curAiSb())
			cb(sb);
	}
	else for (auto sb : sabas)
		cb(sb);
}



void irr::scene::IrrMMD::rigSetInitNodes()
{
	auto s0=sabas[0], s1=sabas[1];
	MPA->setNode(s1->ndHandR);
	MPA->setBase(s0->ndLegR);
	MPA->setNode(s1->ndFootR);
	MPA->setBase(s0->ndLower);
	MPA->setNode(s1->ndYao);
	MPA->setBase(s0->ndYao);
}

Json::Value irr::scene::mmdPoseToJson(saba::MMDModel* model)
{
	Json::Value root;
	auto mgr= model->GetNodeManager();
	for (int i = 0; i < mgr->GetNodeCount(); i++)
	{
		saba::MMDNode* nd = mgr->GetMMDNode(i);
		if ((nd->flMove || nd->flRotate) && !nd->IsPhysicsActive || nd->isRoot()) {
			auto trs = nd->GetAnimationTranslate();
			auto rtt = nd->GetAnimationRotate();
			auto scl = nd->GetInitialScale();
			bool hasScl = scl != glm::vec3(1, 1, 1);
			if (trs != glm::vec3(0, 0, 0) || rtt != glm::quat(1, 0, 0, 0) || hasScl)
			{
				Json::Value bone;
				bone.append(nd->GetName());
				bone.append(trs.x);
				bone.append(trs.y);
				bone.append(trs.z);
				bone.append(rtt.w);
				bone.append(rtt.x);
				bone.append(rtt.y);
				bone.append(rtt.z);
				if (hasScl) {
					bone.append(scl.x);
					bone.append(scl.y);
					bone.append(scl.z);
				}
				root.append(bone);
			}
		}
	}
	return root;
}

int irr::scene::mmdPoseFromJson(saba::MMDModel* model,const Json::Value& jsv)
{
	Json::Value root;
	int bc = 0;
	auto mgr = model->GetNodeManager();
	for (auto& v : jsv DOT_AS_ARRAY) if (v.isArray() && v[0].isString() && v.size()>=8) {
		std::string bname = v[0].asString();
		saba::MMDNode* nd = mgr->GetMMDNode(bname);
		if (nd)
		{
			nd->SetAnimationTranslate({ v[1].asFloat(),v[2].asFloat(),v[3].asFloat()});
			nd->SetAnimationRotate(glm::quat(v[4].asFloat(), v[5].asFloat(), v[6].asFloat(), v[7].asFloat()));
			if (v.size() >= 11) {
				nd->SetInitialScale({ v[8].asFloat(),v[9].asFloat(),v[10].asFloat() });
			}
		}
	}
	return bc;
}

void irr::scene::IrrMMD::connectSabas(int type, int nextInc)
{
	size_t sbc = sabas.size();
	for (size_t i = 0; i < sbc; i++)
	{
		auto sb = sabas[i];
		auto sbN = sabas[(i + sbc + nextInc) % sbc];
		auto sbP = sabas[(i + sbc - nextInc) % sbc];
		switch (type)
		{
		default:
		{
			sb->Pmx->connectRb(sb->ndHandL->rb0, sbN->ndHandR->rb0, 1, vec3(0), 0, vec3(0));
			sb->Pmx->connectRb(sbP->ndHandL->rb0, sb->ndHandR->rb0, 1, vec3(0), 0, vec3(0));
		}
			break;
		}
	}
}

 
 
void irr::scene::IrrMMD::createFluid()
{
	core::vector3di size(5, 6, 3);

		// Create the fluid scene node
	fluidNode = new SnPhyFluid(
		1.0f,                           // size parameter
		Ctx->getSceneManager()->getRootSceneNode(), // parent node
		Ctx->getSceneManager(),                   // scene manager
		-1,                            // id
		{ 0,0,0 }                       // position
	);
	fluidNode->setScale(MMD_SABA_SCALE);
	// Configure fluid parameters
	fluidNode->numPointsX = static_cast<u32>(size.X * 10);  // 50 particles in X
	fluidNode->numPointsY = static_cast<u32>(size.Y * 10);  // 60 particles in Y
	fluidNode->numPointsZ = static_cast<u32>(size.Z * 10);  // 30 particles in Z
	fluidNode->particleSpacing = 0.1f;                      // Distance between particles
	fluidNode->maxDiffuseParticles = 100000;                // Max foam/bubble particles

	// Configure rendering
	fluidNode->showDiffuseParticles = true;                 // Show foam/bubbles
	fluidNode->particleSize = 0.5f;                         // Size of billboard quads

 
}

void irr::scene::IrrMMD::resetFluid()
{
	fluidNode->resetFluid();
}

#if 0
#include <Eigen/Dense>
#include <unsupported/Eigen/Splines>

void testcode1()
{
	using namespace Eigen;
	typedef Spline2d::PointType PointType;
	typedef Spline2d::KnotVectorType KnotVectorType;
	typedef Spline2d::ControlPointVectorType ControlPointVectorType;

	ControlPointVectorType points = ControlPointVectorType::Random(2, 100);

	KnotVectorType chord_lengths; // knot parameters
	Eigen::ChordLengths(points, chord_lengths);

	// interpolation without knot parameters
	{
		const Spline2d spline = SplineFitting<Spline2d>::Interpolate(points, 3);
		DP(("CTRLS %d,", spline.ctrls().size()));
		for (Eigen::DenseIndex i = 0; i < points.cols(); ++i)
		{
			PointType pt = spline(chord_lengths(i));
			PointType ref = points.col(i);
			DP((" %f,%f    %f,%f", pt.x(), pt.y(), ref.x(), ref.y()));
			assert((pt - ref).matrix().norm() < 1e-14);
		}

	}

	// interpolation with given knot parameters
	{
		const Spline2d spline = SplineFitting<Spline2d>::Interpolate(points, 3, chord_lengths);
		DP(("CTRLS %d,", spline.ctrls().size()));
		for (Eigen::DenseIndex i = 0; i < points.cols(); ++i)
		{
			PointType pt = spline(chord_lengths(i));
			PointType ref = points.col(i);
			assert((pt - ref).matrix().norm() < 1e-14);
		}
	}


	{
		int size = 300;
		Eigen::RowVectorXd times(size), sins(size);
		times << 0, 0.0200668896321070, 0.0401337792642141, 0.0602006688963211, 0.0802675585284281, 0.100334448160535, 0.120401337792642, 0.140468227424749, 0.160535117056856, 0.180602006688963, 0.200668896321070, 0.220735785953177, 0.240802675585284, 0.260869565217391, 0.280936454849498, 0.301003344481605, 0.321070234113712, 0.341137123745819, 0.361204013377926, 0.381270903010033, 0.401337792642141, 0.421404682274248, 0.441471571906355, 0.461538461538462, 0.481605351170569, 0.501672240802676, 0.521739130434783, 0.541806020066890, 0.561872909698997, 0.581939799331104, 0.602006688963211, 0.622073578595318, 0.642140468227425, 0.662207357859532, 0.682274247491639, 0.702341137123746, 0.722408026755853, 0.742474916387960, 0.762541806020067, 0.782608695652174, 0.802675585284281, 0.822742474916388, 0.842809364548495, 0.862876254180602, 0.882943143812709, 0.903010033444816, 0.923076923076923, 0.943143812709030, 0.963210702341137, 0.983277591973244, 1.00334448160535, 1.02341137123746, 1.04347826086957, 1.06354515050167, 1.08361204013378, 1.10367892976589, 1.12374581939799, 1.14381270903010, 1.16387959866221, 1.18394648829431, 1.20401337792642, 1.22408026755853, 1.24414715719064, 1.26421404682274, 1.28428093645485, 1.30434782608696, 1.32441471571906, 1.34448160535117, 1.36454849498328, 1.38461538461538, 1.40468227424749, 1.42474916387960, 1.44481605351171, 1.46488294314381, 1.48494983277592, 1.50501672240803, 1.52508361204013, 1.54515050167224, 1.56521739130435, 1.58528428093646, 1.60535117056856, 1.62541806020067, 1.64548494983278, 1.66555183946488, 1.68561872909699, 1.70568561872910, 1.72575250836120, 1.74581939799331, 1.76588628762542, 1.78595317725753, 1.80602006688963, 1.82608695652174, 1.84615384615385, 1.86622073578595, 1.88628762541806, 1.90635451505017, 1.92642140468227, 1.94648829431438, 1.96655518394649, 1.98662207357860, 2.00668896321070, 2.02675585284281, 2.04682274247492, 2.06688963210702, 2.08695652173913, 2.10702341137124, 2.12709030100334, 2.14715719063545, 2.16722408026756, 2.18729096989967, 2.20735785953177, 2.22742474916388, 2.24749163879599, 2.26755852842809, 2.28762541806020, 2.30769230769231, 2.32775919732441, 2.34782608695652, 2.36789297658863, 2.38795986622074, 2.40802675585284, 2.42809364548495, 2.44816053511706, 2.46822742474916, 2.48829431438127, 2.50836120401338, 2.52842809364549, 2.54849498327759, 2.56856187290970, 2.58862876254181, 2.60869565217391, 2.62876254180602, 2.64882943143813, 2.66889632107023, 2.68896321070234, 2.70903010033445, 2.72909698996656, 2.74916387959866, 2.76923076923077, 2.78929765886288, 2.80936454849498, 2.82943143812709, 2.84949832775920, 2.86956521739130, 2.88963210702341, 2.90969899665552, 2.92976588628763, 2.94983277591973, 2.96989966555184, 2.98996655518395, 3.01003344481605, 3.03010033444816, 3.05016722408027, 3.07023411371237, 3.09030100334448, 3.11036789297659, 3.13043478260870, 3.15050167224080, 3.17056856187291, 3.19063545150502, 3.21070234113712, 3.23076923076923, 3.25083612040134, 3.27090301003345, 3.29096989966555, 3.31103678929766, 3.33110367892977, 3.35117056856187, 3.37123745819398, 3.39130434782609, 3.41137123745819, 3.43143812709030, 3.45150501672241, 3.47157190635452, 3.49163879598662, 3.51170568561873, 3.53177257525084, 3.55183946488294, 3.57190635451505, 3.59197324414716, 3.61204013377926, 3.63210702341137, 3.65217391304348, 3.67224080267559, 3.69230769230769, 3.71237458193980, 3.73244147157191, 3.75250836120401, 3.77257525083612, 3.79264214046823, 3.81270903010033, 3.83277591973244, 3.85284280936455, 3.87290969899666, 3.89297658862876, 3.91304347826087, 3.93311036789298, 3.95317725752508, 3.97324414715719, 3.99331103678930, 4.01337792642141, 4.03344481605351, 4.05351170568562, 4.07357859531773, 4.09364548494983, 4.11371237458194, 4.13377926421405, 4.15384615384615, 4.17391304347826, 4.19397993311037, 4.21404682274248, 4.23411371237458, 4.25418060200669, 4.27424749163880, 4.29431438127090, 4.31438127090301, 4.33444816053512, 4.35451505016722, 4.37458193979933, 4.39464882943144, 4.41471571906355, 4.43478260869565, 4.45484949832776, 4.47491638795987, 4.49498327759197, 4.51505016722408, 4.53511705685619, 4.55518394648829, 4.57525083612040, 4.59531772575251, 4.61538461538462, 4.63545150501672, 4.65551839464883, 4.67558528428094, 4.69565217391304, 4.71571906354515, 4.73578595317726, 4.75585284280936, 4.77591973244147, 4.79598662207358, 4.81605351170569, 4.83612040133779, 4.85618729096990, 4.87625418060201, 4.89632107023411, 4.91638795986622, 4.93645484949833, 4.95652173913044, 4.97658862876254, 4.99665551839465, 5.01672240802676, 5.03678929765886, 5.05685618729097, 5.07692307692308, 5.09698996655518, 5.11705685618729, 5.13712374581940, 5.15719063545151, 5.17725752508361, 5.19732441471572, 5.21739130434783, 5.23745819397993, 5.25752508361204, 5.27759197324415, 5.29765886287625, 5.31772575250836, 5.33779264214047, 5.35785953177258, 5.37792642140468, 5.39799331103679, 5.41806020066890, 5.43812709030100, 5.45819397993311, 5.47826086956522, 5.49832775919732, 5.51839464882943, 5.53846153846154, 5.55852842809365, 5.57859531772575, 5.59866220735786, 5.61872909698997, 5.63879598662207, 5.65886287625418, 5.67892976588629, 5.69899665551839, 5.71906354515050, 5.73913043478261, 5.75919732441472, 5.77926421404682, 5.79933110367893, 5.81939799331104, 5.83946488294314, 5.85953177257525, 5.87959866220736, 5.89966555183946, 5.91973244147157, 5.93979933110368, 5.95986622073579, 5.97993311036789, 6.;
		sins << 0., 0.0205663230027132, 0.0390160433647213, 0.0590891330065624, 0.0875596988696830, 0.0959822118249474, 0.121373632487893, 0.144185779407900, 0.168823702943980, 0.195714802779603, 0.182086468335945, 0.208556236727609, 0.225094826864952, 0.239997837196716, 0.261856373004331, 0.285616850959075, 0.307675255212350, 0.368369766783931, 0.323959067443884, 0.384881639934677, 0.363815465411856, 0.447469201284049, 0.453774250363027, 0.430583512112867, 0.443099359428671, 0.509029695458685, 0.475835951758427, 0.535054453990577, 0.517313698480048, 0.540962965727346, 0.545515801814871, 0.549079158057189, 0.608171721462356, 0.633254366558289, 0.661681941754734, 0.621837761755506, 0.716087740139810, 0.626367576826244, 0.655836894152803, 0.686777150859095, 0.783641511917343, 0.740251957156445, 0.687992462634859, 0.784761727801862, 0.812689567708724, 0.723454299040675, 0.867775567606339, 0.834921353384008, 0.886473449480867, 0.829994027552906, 0.936539227083496, 0.878125359050542, 0.857422602795660, 0.797532129862921, 0.943697646147127, 0.931398718818189, 0.831954959636186, 0.990429183436099, 0.937267902704322, 1.03366330075745, 0.934498869672776, 1.03339624571952, 0.997468793041113, 1.06835135036732, 0.935065588934669, 1.05824035228952, 0.900356286901882, 1.10344335276178, 1.08109260151034, 1.00924107228951, 0.894536706478818, 1.05842262698500, 0.869469062376110, 1.10339186007555, 1.12123314842733, 0.898684459599225, 1.07266484712897, 1.08181878008800, 0.859772954875467, 1.05813887444340, 0.927536817088291, 1.05875873937114, 1.05513797051990, 0.881028526179104, 0.940789571986141, 0.939650398904516, 0.895333225658922, 1.15791888887766, 0.819811378153096, 0.872483401229913, 0.913929786309735, 1.07742711882579, 0.948836532262710, 1.01695652247989, 1.07892353697484, 1.07951455459521, 0.926555376069836, 0.796683097955963, 0.971180186596668, 1.08404809595985, 0.938788138021134, 0.723978021690113, 0.845028175059507, 0.827345363692465, 0.963611479800734, 0.784913093396594, 0.902260395754080, 0.754290064829049, 0.640991769001049, 0.840961782568416, 0.884688925819984, 0.945578335759086, 0.734722787877624, 0.730891994457772, 0.560468510656587, 0.888009033487127, 0.588779454127353, 0.507860660213622, 0.723119729832821, 0.706654621382516, 0.852209479056328, 0.441125081583452, 0.703532575135291, 0.649072175609160, 0.768270742631807, 0.821436286437389, 0.812472042863249, 0.310112243913094, 0.290088866876053, 0.784062286903034, 0.724847602437022, 0.382946261736111, 0.333365214123254, 0.255326531995527, 0.510563540394420, 0.546504484804774, 0.561758001362831, 0.193398150326653, 0.211822141863330, 0.337944053003746, 0.0491697943148871, 0.506909715390791, 0.394250155597078, 0.344556489656333, 0.220319484594126, 0.466870975203072, 0.352811824041645, -0.0203204635966194, 0.000187284360740975, 0.390929784556070, -0.0636185847381180, 0.215749967807034, 0.391783875224768, 0.0131507101596210, 0.214743651863588, 0.313117360143471, -0.213347879945078, -0.152700491575586, 0.284183282760694, 0.230258633151635, -0.170326153940235, -0.138941908793679, -0.361493597493055, 0.0225982276008491, 0.136956358471678, -0.0134364651061402, -0.354908737138856, -0.338680251031658, -0.324487377118159, -0.250495042710380, -0.207682989113764, -0.201777395597538, -0.603650467101997, -0.0570224603490521, -0.494298935449787, -0.701565642803869, -0.199839524611218, -0.548524440885001, -0.336774711215616, -0.663082614278816, -0.665924584060438, -0.558180481080407, -0.709519401338939, -0.550362969544220, -0.317669574094212, -0.215362655162646, -0.557314553299032, -0.476358803662329, -0.618538714302365, -0.857936707662719, -0.650422788916676, -0.450608748642427, -1.03675737413944, -0.458466487973903, -0.435238631393051, -0.612515366468904, -1.02282034118212, -1.00591774855133, -0.754300354448578, -0.811106583146996, -0.977243563786221, -1.15873641827749, -0.967899906565075, -0.603952304631890, -0.614897411289103, -0.506405816692878, -1.08323303849733, -0.878993651876167, -1.21339204205580, -0.926163267157209, -1.14708966655702, -0.702523099754653, -0.997607886250364, -1.05378691425895, -0.520207754254763, -1.22657313553732, -0.972570592572648, -1.16907062433655, -0.906452454632314, -1.03946558253614, -0.550998692611392, -1.08167304585878, -0.897951496093406, -0.780087992998209, -1.05605506303820, -0.726866824733939, -1.09042965985860, -1.38018514904770, -0.950070929813976, -1.05770071443760, -1.07268971722784, -0.543996855503508, -0.606425043418392, -0.945835692115167, -0.669056893514066, -1.16728466807845, -0.938678055549470, -1.01236119569040, -0.725637220253413, -0.878964985274675, -0.938002625674076, -1.29352679841118, -1.12194428508496, -1.36126581275738, -0.535406897512086, -1.11461313522307, -1.26469342269406, -1.35376389844295, -0.853393422301156, -1.23411956192447, -0.889103354650984, -0.453241720722455, -0.674642312393962, -0.485956820045976, -1.34342137994975, -0.522858292122314, -1.28347238567162, -0.991285795882105, -0.837456034504287, -1.02889729768886, -0.547777267737865, -0.385836799097512, -0.618013718429130, -1.03722116318478, -0.872749491150546, -1.10717663074987, -0.974662603291053, -1.11073176801917, -0.356460205615645, -0.954131536713498, -0.953578387057411, -0.408819629096933, -1.20503187990326, -0.823728061276806, -0.639337429057833, -0.865717404705197, -0.431884787539002, -1.01026779136354, -0.756675282804455, -1.10586000122177, -0.662224314038648, -0.111456991937247, -0.769480559944682, -0.0502235743017712, -0.584109125124463, -0.902892919532128, -0.466296466268305, -0.357583159307505, -0.472303230829483, -0.482252751675765, -0.497157767695055, 0.0271725486596654, -0.370289805051389, -0.403480529070726, 0.205775238849162, -0.736735702304588, -0.668465354498537, -0.0640652586715190, -0.284538180097004, -0.156691852132801;

		Eigen::Spline<double, 1, 4> spline(Eigen::SplineFitting<Eigen::Spline<double, 1, 4>>::Interpolate(sins, 4, times));

		for (int i = 0 + 4; i < size - 4; i++)
		{
			std::cout << times(i) << "\t" << spline(times(i)) << "\t" << sins(i) << "\n";
		}





		return ;
	}
}
#endif