﻿#include "AppGlobal.h"
#include "MmdPhysicsHelper.h"
#include "irrSaba.h"
static const float FAR_MOV_TIME = 0.2f, POST_MOVE_TIME = 0.1f;

void saba::MmdPhysicsController::FootAdjustStrategy::apply(MmdPhysicsController& con, float deltaTime) {


    const auto& config = con.m_config;
    auto& ndt = con.ndt[con.curId];
    const auto& state = ndt.curState;
    const float scale = state.scaleFactor;
    const float scale2 = scale * scale;
	auto* footNode = ndt.node, * anotherFootNode = con.ndt[con.curId == 0 ? 1 : 0].node;
	const float footY = footNode->rb0->pos.y;
    const float baseFootY = con.baseFootY;
    bool yOnGround = footY < baseFootY * 0.5f;
    auto sb = con.Sb;
	const float legLen = sb->ndLegL->mGlobalInit[3].y;
    // Get parent node and validate
    auto parentNode = footNode->GetParent(); assert(parentNode);
    float lastCD = ndt.farCD;
    ndt.farCD = glm::max(ndt.farCD - deltaTime, 0.f);	
    ndt.farPostCD -= deltaTime;
    bool farPost = ndt.farPostCD > 0;
    if (ndt.node->atkLocking) return;
    if (lastCD > 0 && ndt.farCD <= 0) ndt.farPostCD = POST_MOVE_TIME; // far stage finished, start farPost stage
	//if (ndt.farPostCD > 0.f) return;
    bool tooFar = ndt.farCD > 0.f ;
    if (tooFar) {
        DP(("toofar %f",ndt.farCD));
    }
	const float MAXDIS = legLen*1.5f* scale, MINDIS = legLen * 0.3f * scale, MIDDIS = legLen * 0.6f * scale, MAXDIS2 = MAXDIS * MAXDIS, MINDIS2 = MINDIS * MINDIS, MIDDIS2 = MIDDIS * MIDDIS;
	// Get body transform
	glm::mat4 bodyTfm = sb->ndUpper2->rb0->getNodeTransform();
	irr::core::matrix4 m = con.body->rb0->getNodeTransform();
    
	m.setRotationDegrees(m.getRotationDegrees() * irr::core::vector3df(0, 1, 0));
    bodyTfm = m;
	glm::mat4 bodyTfmInv = glm::inverse(glm::mat4(m));

    //feet moving
    if (state.groundAngle < 90.0f &&
        ((footY < con.baseFootY * 3.f && state.bodyVel.y < -0.2f) 
            ||        (state.bodyPosition.y > con.baseBodyPos.y * 0.5f && footY <  con.baseFootY)) 
       // ||       state.isHighFall
        )
    {
        bool movR = ndt.movR;


        // Get positions and calculate distances
        const glm::vec3 bodyPos = vec3(0, 0, 0);// state.bodyPosition;
        const glm::vec3 localPosL = glh::matTransformVec(bodyTfmInv, con.footL->rb0->pos) * vec3(1,0,1);
		const glm::vec3 localPosR = glh::matTransformVec(bodyTfmInv, con.footR->rb0->pos) * vec3(1, 0, 1);
        const float mulBySpd = std::abs(state.bodyVel.y);
        // Determine far/near feet using projection distance
        const auto projDistSq = [](const glm::vec3& a, const glm::vec3& b) {
            return glm::dot(glm::vec2(a.x - b.x, a.z - b.z),
                glm::vec2(a.x - b.x, a.z - b.z));
            };
		const auto AngleProjectOnXY = [](glm::vec3 a, glm::vec3 c, glm::vec3 b) { 
			//angle a->center->b
            c.y = 0; a.y = 0; b.y = 0;
			glm::vec3 va = glm::fastNormalize((a - c) );
			glm::vec3 vb = glm::fastNormalize((b - c) );
			sbFw2LineD("pt", c, c+va*3.f, 0xFF808080, 30); 		sbFw2LineD("pt", c, c + vb * 3.f, 0xFFC0C0C0, 30);
			return glm::acos(glm::dot(va, vb));
			};
		float mirrorFactor = state.isHighFall?0.99f: 0.8f; 
        if (!tooFar) {

            float legsAngXY=AngleProjectOnXY(con.footL->rb0->pos, sb->ndUpper2->rb0->pos, con.footR->rb0->pos) ;
            float legsDis2 = projDistSq(con.footL->rb0->pos, con.footR->rb0->pos);
			float disL = projDistSq(bodyPos, localPosL), disR = projDistSq(bodyPos, localPosR);
			//if (localPosL.z > 0 && state.bodyVel.z<-1 )                movR = false;
			//else if (localPosR.z > 0 && state.bodyVel.z < -1) 			movR = true;			else 
            if (legsDis2 < MINDIS2) {
				movR = ndt.movR = disL < disR;
			}
			else
            if (legsDis2 > MAXDIS2 && legsAngXY > piFloat / 2)
                movR = ndt.movR = disL < disR, mirrorFactor=1.25f;
            else //if (disLegs < MAXDIS2)
                movR = ndt.movR = disL > disR;

           

        }
        saba::MMDNode* stayFoot = movR ? con.footL : con.footR;
        saba::MMDNode* moveFoot = movR ? con.footR : con.footL;

        if (tooFar || footNode == moveFoot) {

            if (state.isHighFall) {
                //con.m_bodyNode->rb0->setAngVelToPos(con.m_bodyNode->rb0->pos + vec3(0, -10, 0), std::min(100.f, mulBySpd / 2.f));
                //con.body->rb0->addRotationToMatOnNode_MatRttResetXZ(ndt.vMulInc * 200.f, -piFloat / 6, 0);

            }
            else {

            }
            parentNode->rb0->addRotationToMatOnNode_MatRttResetXZ(parentNode->rb0->getNodeTransform(), state.isHighFall ? 10 : 30, 0, 0);

            int ndId = movR ? 1 : 0;
            auto& dt = con.ndt[ndId];
            // Calculate mirror position and adjust for high fall
            vec3 mirrorDir = (bodyPos - (movR ? localPosL : localPosR));
            float mirLen = std::min(con.baseBodyPos.y * scale / 3.f, glm::length(mirrorDir) * mirrorFactor);

            glm::vec3 tgtPos = ndt.tgtPos;
            if (!tooFar)
            tgtPos = ndt.tgtPos = bodyPos + glm::fastNormalize(mirrorDir) * mirLen;
			//DP(("tgtPos.x %c = %f",movR?'R':'L', tgtPos.x));

            //DETECT  FOOT CROSSING
            if (gSceneTime-ndt.lastFarTime>0.9f) 
            if (movR && tgtPos.x > -1 * scale && ndt.farCD<0.01f) { 
                tgtPos.x = ndt.tgtPos.x = -3 * scale; ndt.farCD = FAR_MOV_TIME; ndt.lastFarTime = gSceneTime; ndt.farMul = 1 + abs(tgtPos.x + scale) / scale;
                DP(("FAR %d R>", con.curId));
            }
            else if (!movR && tgtPos.x < 1 * scale && ndt.farCD < 0.01f) {
                tgtPos.x = ndt.tgtPos.x = 3 * scale; ndt.farCD = FAR_MOV_TIME; ndt.lastFarTime = gSceneTime; ndt.farMul = 1 + abs(tgtPos.x - scale)/scale;
                DP(("FAR %d L<", con.curId));
            }
            float fy = moveFoot->rb0->pos.y;
			tgtPos = bodyTfm * vec4(tgtPos, 1);
            tgtPos.y = fy;

            //DETECT  FOOT  FAR
            const float farDist = config.farDistance * scale;
            bool disTooFar = footY < con.baseFootY && projDistSq(moveFoot->rb0->pos, tgtPos) > farDist* farDist;
            if (gSceneTime - ndt.lastFarTime > 0.2f)if (disTooFar && ndt.farCD < 0.01f)
            { ndt.farCD = FAR_MOV_TIME; ndt.lastFarTime = gSceneTime; ndt.farMul = 1.f+ farDist/scale; DP(("FAR %d F", con.curId));           }

            // Debug visualization for foot positions
            sbFw2D("sw", moveFoot->rb0->pos, glm::vec3(0),
                tooFar ? SColor(255, 255, 0, 128) :
                SColor(255, 128, 128, 128));

            sbFw2D("sw", tgtPos, glm::vec3(scale * 0.5f),
                state.isHighFall ? SColor(255, 255, 0, 0) :
                SColor(255, 0, 255, 128));

            const float groundBaseMul = 10.f;
            const float applySpeed = state.isHighFall ? ndt.vMulInc * 5.f :
                tooFar && yOnGround ? groundBaseMul * ndt.farMul : 5.f;
            vec3 farNdToBody = glh::posToMat(con.body->rb0->getNodeTransform(), stayFoot->rb0->getNodePos());
            // Handle high fall distance adjustment
            if (state.isHighFall && projDistSq(stayFoot->rb0->pos, tgtPos) > 100.0f * scale2
                ) {
                const glm::vec3 midPoint = (stayFoot->rb0->pos + tgtPos) * 0.5f;
                const glm::vec2 dir = glm::normalize(glm::vec2(
                    stayFoot->rb0->pos.x - tgtPos.x,
                    stayFoot->rb0->pos.z - tgtPos.z
                ));
                glm::vec3 newFar, newNear;

                {
                    const float targetDist = 10.0f * scale;
                    newFar = midPoint + glm::vec3(dir.x * targetDist * 0.5f, tgtPos.y, dir.y * targetDist * 0.5f);

                    glm::vec3 newNear = midPoint - glm::vec3(dir.x * targetDist * 0.5f, tgtPos.y, dir.y * targetDist * 0.5f);
                    tgtPos = newNear; tgtPos.y = fy;
                }

                newFar.y = stayFoot->rb0->pos.y;
                stayFoot->rb0->scaleVel3(glm::vec3(0.75f, 1.0f, 0.75f), 1);
                stayFoot->rb0->addLinearVelToPos(newFar, applySpeed);

                // Debug visualization for distance adjustment //YELLOW
                sbFw2LineD("pt", stayFoot->rb0->pos, newFar, SColor(255, 255, 255, 0), 20);

            }

            // Apply height adjustment

            const float heightAdjust = state.isHighFall ? -0.3f :
                tooFar && yOnGround ?  0.1f+ndt.farCD*0.2f : 0.1f;
            tgtPos.y += heightAdjust * scale;
           
            moveFoot->rb0->scaleVel3(glm::vec3(0.75f, 1.0f, 0.75f), 1);
            moveFoot->rb0->addLinearVelToPos(tgtPos, tooFar ? applySpeed * 2.f : applySpeed);
            //DP(("F%d toofar %d,  %f", movR, tooFar, applySpeed));
            // Debug line for movement vector //BLUE
            sbFw2LineD("pt", moveFoot->rb0->pos, tgtPos, tooFar ? SColor(255, 255, 0, 255) : SColor(255, 255, 255, 0), 20);

            //move body  
            auto movToCtr = [=](MMDNode* node, float mul) {
                vec3 ctrTgtXZ = node->rb0->pos * vec3(1, 0, 1);//     glm::mix(stayFoot->rb0->pos, tgtPos, 0.5f)* vec3(1, 0, 1);
                node->rb0->addLinearVelToPos(ctrTgtXZ + vec3(0, (node->rb0->initPos.y) * scale, 0), groundBaseMul* mul);
                //node->rb0->addLinearVelToPosLimitDis(ctrTgtXZ + vec3(0, (node->rb0->initPos.y) * scale, 0), groundBaseMul * mul,0,scale*7.f);
                };
            movToCtr(sb->ndYao,0.1f);//     glm::mix(stayFoot->rb0->pos, tgtPos, 0.5f)* vec3(1, 0, 1);
            movToCtr(sb->ndUpper2, 0.25f);
            moveStage = 1;
        }
        else if (moveStage == 1)
        {
            moveStage = 2;
        }
    }

    
    // Handle rotation
    if (state.groundAngle < 75.0f && (footNode->rb0->vel.y < -0.2f && footY < baseFootY || yOnGround) || tooFar  || farPost)
    {
        const glm::mat4 nodeMat = footNode->rb0->getNodeTransform();
        const glm::quat currentRot(nodeMat);
        const glm::vec3 currentUp = currentRot * glm::vec3(0, 1, 0);


        // After rotation application
        if (tooFar) {
            footNode->scaleVel(0.8f, 3); footNode->GetParent()->scaleVel(0.9f, 1);
          
        }
        else if ( farPost) {
            footNode->scaleVel(0.5f, 3); footNode->GetParent()->scaleVel(0.7f, 1);
             
            // sb->Pmx->scaleDynBodyVel(0.9f, 1);
        }
         // sb->ndYao->rb0->addLinearVel({ 0, 3*scale, 0 });
        const glm::quat newRot = footNode->rb0->addRotationToMatOnNode_MatRttResetXZ(
            nodeMat,
            tooFar ? config.baseRttMul * 100.f   : farPost? config.baseRttMul * 300.f : config.baseRttMul * (yOnGround?2.f:1.f)
        );
        //DP((" far %d %d",tooFar,farPost));
        if (1) {
            const glm::vec3 targetUp = newRot * glm::vec3(0, 1, 0);
            float mul = 200 - state.bodyVel.y*2 + state.groundAngle *10.f; mul *= tooFar||farPost?2.0f:1;
            if (yOnGround && mul > 0.f) if (//state.groundAngle < 37.5f && 
                !state.isHighFall && !tooFar) {
                float standMulLeg = std::max(0.f, (30 - state.groundAngle) / 30.f) * 1.f + 1.f;
                float standMul = std::max(0.f, (30 - state.groundAngle) / 30.f) * 5.f + 1.f;
                if (farPost) parentNode->scaleVel(0.5f, 2);
                parentNode->rb0->addRotationToMatOnNode_MatRttResetXZ(parentNode->rb0->getNodeTransform(), mul * 0.85f * standMulLeg, 0, 0);
                parentNode = parentNode->GetParent();
                if (farPost) parentNode->scaleVel(0.5f, 2);
                parentNode->rb0->addRotationToMatOnNode_MatRttResetXZ(parentNode->rb0->getNodeTransform(), mul * 0.75f * standMulLeg, 0, 0);
                sb->ndUpper->rb0->addRotationToMatOnNode_MatRttResetXZ(con.body->rb0->getNodeTransform(), mul * standMul, 0, 0);
               // sb->ndUpper->rb0->addRotationToMatOnNode_MatRttResetXZ(mul * standMul/3, 0, 0);
                if (sb->ndUpper3 && sb->ndUpper3->rb0) sb->ndUpper3->rb0->addRotationToMatOnNode_MatRttResetXZ(mul * standMul, 0, 0);
                if (sb->ndUpper2->rb0) sb->ndUpper2->rb0->addRotationToMatOnNode_MatRttResetXZ(mul * standMul, 0, 0);
                sb->ndYao->rb0->addRotationToMatOnNode_MatRttResetXZ(mul* standMul*2, 0, 0);
            }
            else if (tooFar) {
                parentNode->scaleVel(0.2f, 2);
                parentNode->rb0->addRotationToMatOnNode_MatRttResetXZ(parentNode->rb0->getNodeTransform(), mul * 0.6f, 0, 0);
                parentNode = parentNode->GetParent();
                parentNode->scaleVel(0.1f, 2);
                parentNode->rb0->addRotationToMatOnNode_MatRttResetXZ(parentNode->rb0->getNodeTransform(), mul * 0.5f, 0, 0);
                float mul1 = mul * 3.f + 5.f * std::clamp((state.groundAngle) / 60, 0.0f, 1.f);
                con.body->rb0->addRotationToMatOnNode_MatRttResetXZ(mul1 * 1.5f, 0, 0);
                con.body->rb0->addLinearVelToPos(con.body->rb0->getPosition() * vec3(1, 0, 1) + vec3(0, con.baseBodyPos.y * scale, 0), 3 * scale);
                sb->ndUpper->rb0->addRotationToMatOnNode_MatRttResetXZ(mul1 * 1.5f, 0, 0);
                if (sb->ndUpper3 && sb->ndUpper3->rb0) sb->ndUpper3->rb0->addRotationToMatOnNode_MatRttResetXZ(mul1 * 1.25f, 0, 0);
                sb->ndUpper2->rb0->addRotationToMatOnNode_MatRttResetXZ(mul1 * 1.f, 0, 0);
                sb->ndHead->rb0->addRotationToMatOnNode_MatRttResetXZ(mul1 * 0.57f, 0, 0);

                if (!ndt.movR)sb->ndLegL->rb0->addTorqueLocal(vec3(0, 0, -3), true);
                else sb->ndLegR->rb0->addTorqueLocal(vec3(0, 0, 3), true);

                footNode->rb0->setRotation_MatRttResetXZ();
                if (yOnGround && ndt.farCD > 0 && ndt.farCD < 0.1f) anotherFootNode->rb0->setRotation_MatRttResetXZ();
                else anotherFootNode->rb0->addRotationToMatOnNode_MatRttResetXZ(1000);
                anotherFootNode->rb0->parentRb->addRotationToMatOnNode_MatRttResetXZ(200);
            }

            auto fp = footNode->rb0->pos;
            // Visual debug lines
            sbFw2LineD("pt", fp, fp + newRot * vec3(0, -6, 0),  0xFFFF0000, 60);
            sbFw2LineD("pt", fp, fp + quat(nodeMat) * vec3(0, -3, 0), tooFar? 0xFFFFFF00: farPost ? 0xFF00FF00 : 0xFFFFFFFF, 30);
        }
        
    }
    //else  //not on ground
    //{
    //    sb->ndUpper2->rb0->addRotationToMatOnNode_MatRttResetXZ(parentNode->rb0->getNodeTransform(), 50, 0, 0);
    //    sb->ndUpper->rb0->addRotationToMatOnNode_MatRttResetXZ(parentNode->rb0->getNodeTransform(), 100, 0, 0);
    //    sb->ndYao->rb0->addRotationToMatOnNode_MatRttResetXZ(parentNode->rb0->getNodeTransform(), 200, 0, 0);
    //}
}
