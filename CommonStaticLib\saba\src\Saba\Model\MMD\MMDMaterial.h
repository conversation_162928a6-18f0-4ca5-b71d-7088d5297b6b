﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//

#ifndef SABA_MODEL_MMD_MMDMATERIAL_H_
#define SABA_MODEL_MMD_MMDMATERIAL_H_

#include <string>
#include <cstdint>
#include <glm/vec3.hpp>
#include <glm/vec4.hpp>
#include "PMXFile.h"
namespace saba
{
	struct MMDMaterial
	{
		MMDMaterial();
		enum class SphereTextureMode
		{
			None=0,
			Mul=1,
			Add=2,
		};

		glm::vec3		m_diffuse;
		float			m_alpha;
		glm::vec3		m_specular;
		float			m_specularPower;
		glm::vec3		m_ambient;
		uint8_t			m_edgeFlag;
		float			m_edgeSize;
		glm::vec4		m_edgeColor;
		std::string		m_texture;
		std::string		m_spTexture;
		SphereTextureMode	m_spTextureMode;
		std::string		m_toonTexture;
		glm::vec4		m_textureMulFactor;
		glm::vec4		m_spTextureMulFactor;
		glm::vec4		m_toonTextureMulFactor;
		glm::vec4		m_textureAddFactor;
		glm::vec4		m_spTextureAddFactor;
		glm::vec4		m_toonTextureAddFactor;
		bool			m_bothFace;
		bool			m_groundShadow;
		bool			m_shadowCaster;
		bool			m_shadowReceiver;
		std::string     m_memo;
		std::string		m_name;

		
		//ckadd
		uint32_t id = -1;
		bool isPhyMesh = false, isCloth = false;
		int spaIsNormalMap = 0;
		uint32_t Bloom=0;
		int	rainbow = 0; float rainbowSpeed = 1.f, rainbowSbOfs=0.f, rainbowMtrOfs = 0.f;
		int centerTransparent = 0;
		float alphaMul = 1.f;
		glm::vec4 diffuseMul{ 1,1,1,1 };

		int subMeshAlphaMod=0;
		float subMeshAlphaMul = 1.f;
		float subMeshAlphaDec=0;	
		PhyMeshData ptcDat;
		
		void smaSetDec(float v=1.f){ if (subMeshAlphaMod) subMeshAlphaDec = v; }
	};
}

#endif // !SABA_MODEL_MMD_MMDMATERIAL_H_
