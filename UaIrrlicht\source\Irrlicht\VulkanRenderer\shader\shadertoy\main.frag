#version 460

#extension GL_ARB_separate_shader_objects: enable
#extension GL_ARB_shading_language_420pack: enable
#extension GL_GOOGLE_include_directive : enable

// layout (location = 0) in vec2 frag_pos;

// #define OFFSCREEN_BUFFERS 4
// #define IMAGE_TEXTURES 4

// layout (set = 0, binding = 0) uniform sampler2D iTextures[IMAGE_TEXTURES]; //textures
// layout (set = 0, binding = IMAGE_TEXTURES) uniform sampler2D iChannels[OFFSCREEN_BUFFERS]; //buffers
// layout (set = 0, binding = IMAGE_TEXTURES+OFFSCREEN_BUFFERS) uniform sampler2D iKeyboard; //keyboard

// //define in any order
// #define iChannel0 iChannels[0]
// #define iChannel1 iChannels[1]
// #define iChannel2 iChannels[2]
// #define iChannel3 iChannels[3]





layout (binding = 2)  uniform sampler2D iChannel0;
layout (binding = 3)  uniform sampler2D iChannel1;

layout(location = 0) in vec4 iColorD;
layout(location = 1) in vec2 iTex0;

#include "common.glsl"
// struct DataStruct{
//         vec4 a;
//         //vec4 b;
//     };
// layout (std430, binding = 10) buffer frameBuffer {
// 	DataStruct data[];
// };

layout (location = 0) out vec4 out_color;


#include "st/glassbox.glsl"
//#include "st/main_rainGlass.glsl"
//#include "st/liquid.glsl"
void main()
{
    vec4 uFragColor=vec4(0.);
    vec2 fragCoord=gl_FragCoord.xy;
    fragCoord.y=iResolution.y-fragCoord.y; // shadertoy v(y)-flip main_image
    
    mainImage(uFragColor,fragCoord);
    out_color=uFragColor;
    //if(is_pause)out_color=vec4(vec3(dot(clamp(out_color.rgb,0.,1.),vec3(1.))/3.),1.);
    if(main_image_srgb)out_color.rgb = ((exp2(out_color.rgb)-1.0)-out_color.rgb*0.693147)*3.258891;
}
