﻿  mmdPCH.cpp
  SnippetImmUtils.cpp
  SnippetUtils.cpp
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(256,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(256,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(256,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(257,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(257,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(257,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(258,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(258,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(258,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(259,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(259,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(259,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(260,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(260,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(260,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(261,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(261,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(261,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(262,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(262,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(262,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(263,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(263,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(263,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(264,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(264,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(264,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(265,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(265,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(265,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(266,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(266,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(266,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(267,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(267,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(267,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(268,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(268,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(268,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(269,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(269,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(269,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(270,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(270,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(270,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(271,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(271,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(271,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(272,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(272,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(272,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(273,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(273,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(273,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(274,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(274,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(274,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(275,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(275,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(275,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(276,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(276,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(276,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(277,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(277,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(277,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(278,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(278,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(278,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(279,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(279,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(279,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(280,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(280,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(280,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(281,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(281,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(281,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(282,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(282,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(282,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(283,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(283,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(283,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(284,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(284,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(284,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(285,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(285,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(285,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(286,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(286,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(286,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(287,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(287,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(287,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(288,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(288,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(288,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(289,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(289,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(289,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(290,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(290,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(290,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(291,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(291,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(291,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(292,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(292,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(292,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(293,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(293,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(293,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(294,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(294,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(294,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(295,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(295,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(295,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(296,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(296,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(296,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(297,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(297,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(297,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(298,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(298,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(298,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(299,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(299,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(299,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(300,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(300,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(300,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(301,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(301,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(301,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(302,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(302,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(302,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(303,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(303,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(303,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(304,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(304,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(304,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(305,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(305,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(305,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(306,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(306,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(306,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(307,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(307,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(307,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(308,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(308,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(308,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(309,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(309,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(309,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(310,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(310,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(310,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(311,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(311,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(311,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(312,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(312,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(312,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(313,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(313,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(313,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(314,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(314,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(314,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(315,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(315,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(315,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(316,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(316,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(316,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(317,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(317,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(317,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(318,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(318,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(318,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(319,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(319,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(319,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(320,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(320,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(320,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(321,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(321,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(321,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(322,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(322,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(322,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(323,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(323,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(323,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(324,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(324,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(324,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(325,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(325,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(325,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(326,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(326,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(326,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(327,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(327,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(327,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(328,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(328,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(328,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(329,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(329,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(329,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(330,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(330,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(330,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(331,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(331,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(331,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(332,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(332,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(332,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(333,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(333,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(333,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(334,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(334,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(334,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(335,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(335,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(335,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(336,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(336,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(336,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(337,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(337,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(337,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(338,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(338,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(338,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(339,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(339,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(339,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(340,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(340,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(340,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(341,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(341,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(341,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(342,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(342,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(342,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(343,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(343,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(343,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(344,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(344,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(344,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(345,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(345,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(345,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(346,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(346,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(346,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(347,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(347,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(347,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(348,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(348,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(348,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(349,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(349,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(349,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(350,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(350,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(350,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(351,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(351,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(351,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(352,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(352,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(352,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(353,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(353,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(353,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(354,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(354,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(354,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(355,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(355,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(355,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(356,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(356,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(356,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(357,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(357,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(357,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(358,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(358,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(358,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(359,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(359,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(359,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(360,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(360,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(360,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(361,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(361,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(361,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(362,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(362,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(362,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(363,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(363,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(363,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(364,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(364,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(364,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(365,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(365,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(365,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(366,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(366,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(366,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(367,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(367,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(367,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(368,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(368,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(368,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(369,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(369,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(369,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(370,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(370,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(370,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(371,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(371,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(371,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(372,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(372,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(372,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(373,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(373,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(373,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(374,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(374,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(374,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(375,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(375,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(375,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(376,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(376,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(376,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(377,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(377,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(377,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(378,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(378,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(378,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(379,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(379,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(379,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(380,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(380,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(380,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(381,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(381,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(381,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(382,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(382,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(382,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(383,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(383,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(383,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(384,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(384,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(384,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(385,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(385,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(385,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(386,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(386,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(386,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(387,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(387,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(387,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(388,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(388,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(388,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(389,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(389,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(389,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(390,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(390,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(390,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(391,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(391,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(391,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(392,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(392,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(392,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(393,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(393,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(393,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(394,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(394,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(394,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(395,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(395,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(395,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(396,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(396,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(396,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(397,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(397,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(397,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(398,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(398,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(398,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(399,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(399,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(399,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(400,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(400,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(400,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(401,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(401,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(401,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(402,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(402,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(402,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(403,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(403,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(403,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(404,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(404,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(404,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(405,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(405,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(405,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(406,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(406,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(406,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(407,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(407,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(407,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(408,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(408,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(408,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(409,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(409,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(409,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(410,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(410,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(410,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(411,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(411,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(411,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(412,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(412,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(412,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(413,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(413,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(413,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(414,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(414,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(414,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(415,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(415,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(415,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(416,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(416,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(416,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(417,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(417,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(417,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(418,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(418,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(418,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(419,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(419,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(419,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(420,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(420,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(420,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(421,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(421,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(421,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(422,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(422,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(422,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(423,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(423,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(423,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(424,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(424,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(424,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(425,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(425,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(425,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(426,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(426,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(426,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(427,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(427,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(427,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(428,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(428,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(428,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(429,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(429,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(429,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(430,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(430,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(430,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(431,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(431,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(431,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(432,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(432,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(432,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(433,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(433,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(433,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(434,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(434,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(434,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(435,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(435,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(435,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(436,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(436,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(436,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(437,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(437,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(437,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(438,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(438,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(438,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(439,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(439,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(439,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(440,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(440,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(440,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(441,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(441,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(441,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(442,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(442,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(442,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(443,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(443,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(443,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(444,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(444,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(444,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(445,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(445,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(445,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(446,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(446,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(446,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(447,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(447,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(447,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(448,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(448,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(448,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(449,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(449,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(449,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(450,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(450,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(450,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(451,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(451,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(451,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(452,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(452,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(452,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(453,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(453,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(453,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(454,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(454,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(454,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(455,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(455,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(455,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(456,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(456,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(456,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(457,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(457,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(457,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(458,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(458,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(458,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(459,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(459,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(459,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(460,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(460,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(460,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(461,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(461,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(461,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(462,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(462,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(462,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(463,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(463,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(463,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(464,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(464,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(464,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(465,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(465,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(465,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(466,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(466,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(466,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(467,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(467,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(467,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(468,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(468,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(468,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(469,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(469,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(469,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(470,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(470,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(470,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(471,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(471,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(471,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(472,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(472,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(472,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(473,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(473,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(473,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(474,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(474,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(474,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(475,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(475,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(475,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(476,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(476,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(476,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(477,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(477,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(477,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(478,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(478,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(478,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(479,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(479,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(479,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(480,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(480,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(480,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(481,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(481,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(481,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(482,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(482,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(482,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(483,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(483,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(483,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(484,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(484,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(484,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(485,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(485,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(485,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(486,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(486,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(486,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(487,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(487,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(487,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(488,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(488,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(488,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(489,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(489,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(489,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(490,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(490,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(490,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(491,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(491,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(491,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(492,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(492,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(492,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(493,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(493,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(493,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(494,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(494,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(494,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(495,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(495,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(495,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(496,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(496,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(496,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(497,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(497,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(497,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(498,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(498,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(498,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(499,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(499,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(499,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(500,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(500,13): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(500,23): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(501,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(501,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(501,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(502,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(502,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(502,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(503,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(503,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(503,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(504,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(504,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(504,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(505,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(505,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(505,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(506,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(506,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(506,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(507,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(507,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(507,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(508,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(508,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(508,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(509,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(509,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(509,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(510,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(510,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(510,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(511,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(511,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(511,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(512,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(512,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(512,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(513,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(513,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(513,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(514,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(514,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(514,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(515,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(515,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(515,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(516,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(516,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(516,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(517,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(517,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(517,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(518,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(518,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(518,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(519,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(519,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(519,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(520,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(520,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(520,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(521,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(521,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(521,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(522,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(522,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(522,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(523,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(523,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(523,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(524,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(524,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(524,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(525,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(525,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(525,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(526,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(526,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(526,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(527,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(527,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(527,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(528,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(528,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(528,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(529,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(529,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(529,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(530,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(530,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(530,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(531,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(531,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(531,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(532,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(532,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(532,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(533,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(533,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(533,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(534,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(534,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(534,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(535,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(535,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(535,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(536,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(536,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(536,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(537,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(537,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(537,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(538,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(538,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(538,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(539,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(539,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(539,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(540,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(540,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(540,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(541,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(541,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(541,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(542,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(542,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(542,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(543,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(543,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(543,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(544,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(544,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(544,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(545,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(545,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(545,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(546,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(546,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(546,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(547,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(547,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(547,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(548,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(548,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(548,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(549,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(549,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(549,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(550,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(550,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(550,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(551,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(551,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(551,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(552,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(552,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(552,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(553,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(553,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(553,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(554,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(554,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(554,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(555,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(555,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(555,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(556,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(556,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(556,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(557,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(557,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(557,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(558,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(558,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(558,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(559,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(559,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(559,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(560,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(560,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(560,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(561,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(561,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(561,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(562,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(562,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(562,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(563,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(563,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(563,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(564,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(564,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(564,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(565,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(565,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(565,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(566,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(566,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(566,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(567,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(567,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(567,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(568,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(568,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(568,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(569,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(569,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(569,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(570,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(570,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(570,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(571,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(571,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(571,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(572,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(572,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(572,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(573,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(573,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(573,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(574,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(574,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(574,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(575,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(575,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(575,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(576,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(576,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(576,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(577,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(577,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(577,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(578,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(578,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(578,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(579,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(579,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(579,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(580,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(580,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(580,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(581,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(581,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(581,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(582,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(582,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(582,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(583,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(583,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(583,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(584,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(584,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(584,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(585,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(585,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(585,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(586,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(586,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(586,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(587,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(587,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(587,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(588,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(588,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(588,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(589,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(589,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(589,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(590,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(590,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(590,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(591,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(591,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(591,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(592,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(592,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(592,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(593,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(593,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(593,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(594,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(594,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(594,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(595,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(595,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(595,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(596,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(596,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(596,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(597,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(597,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(597,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(598,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(598,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(598,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(599,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(599,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(599,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(600,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(600,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(600,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(601,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(601,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(601,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(602,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(602,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(602,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(603,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(603,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(603,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(604,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(604,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(604,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(605,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(605,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(605,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(606,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(606,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(606,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(607,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(607,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(607,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(608,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(608,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(608,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(609,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(609,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(609,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(610,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(610,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(610,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(611,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(611,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(611,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(612,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(612,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(612,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(613,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(613,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(613,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(614,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(614,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(614,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(615,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(615,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(615,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(616,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(616,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(616,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(617,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(617,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(617,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(618,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(618,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(618,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(619,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(619,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(619,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(620,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(620,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(620,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(621,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(621,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(621,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(622,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(622,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(622,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(623,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(623,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(623,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(624,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(624,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(624,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(625,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(625,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(625,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(626,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(626,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(626,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(627,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(627,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(627,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(628,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(628,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(628,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(629,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(629,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(629,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(630,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(630,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(630,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(631,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(631,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(631,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(632,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(632,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(632,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(633,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(633,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(633,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(634,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(634,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(634,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(635,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(635,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(635,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(636,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(636,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(636,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(637,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(637,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(637,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(638,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(638,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(638,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(639,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(639,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(639,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(640,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(640,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(640,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(641,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(641,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(641,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(642,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(642,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(642,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(643,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(643,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(643,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(644,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(644,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(644,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(645,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(645,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(645,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(646,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(646,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(646,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(647,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(647,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(647,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(648,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(648,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(648,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(649,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(649,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(649,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(650,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(650,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(650,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(651,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(651,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(651,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(652,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(652,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(652,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(653,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(653,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(653,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(654,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(654,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(654,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(655,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(655,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(655,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(656,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(656,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(656,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(657,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(657,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(657,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(658,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(658,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(658,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(659,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(659,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(659,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(660,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(660,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(660,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(661,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(661,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(661,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(662,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(662,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(662,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(663,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(663,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(663,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(664,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(664,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(664,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(665,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(665,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(665,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(666,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(666,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(666,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(667,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(667,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(667,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(668,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(668,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(668,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(669,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(669,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(669,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(670,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(670,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(670,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(671,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(671,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(671,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(672,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(672,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(672,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(673,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(673,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(673,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(674,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(674,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(674,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(675,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(675,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(675,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(676,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(676,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(676,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(677,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(677,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(677,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(678,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(678,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(678,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(679,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(679,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(679,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(680,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(680,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(680,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(681,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(681,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(681,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(682,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(682,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(682,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(683,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(683,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(683,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(684,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(684,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(684,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(685,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(685,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(685,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(686,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(686,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(686,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(687,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(687,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(687,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(688,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(688,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(688,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(689,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(689,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(689,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(690,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(690,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(690,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(691,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(691,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(691,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(692,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(692,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(692,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(693,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(693,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(693,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(694,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(694,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(694,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(695,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(695,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(695,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(696,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(696,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(696,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(697,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(697,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(697,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(698,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(698,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(698,22): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(699,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(699,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(699,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(700,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(700,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(700,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(701,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(701,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(701,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(702,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(702,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(702,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(703,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(703,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(703,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(704,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(704,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(704,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(705,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(705,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(705,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(706,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(706,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(706,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(707,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(707,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(707,21): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(708,3): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(708,12): warning C4305: 'initializing': truncation from 'double' to 'float'
D:\SDK\PhysX\physx\snippets\snippetutils\SnippetUtils.cpp(708,21): warning C4305: 'initializing': truncation from 'double' to 'float'
  apngEnc.cpp
D:\AProj\CommonStaticLib\apng\apngEnc.cpp(947,46): warning C4267: 'argument': conversion from 'size_t' to 'unsigned int', possible loss of data
D:\AProj\CommonStaticLib\apng\apngEnc.cpp(953,18): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\apng\apngEnc.cpp(955,57): warning C4267: 'argument': conversion from 'size_t' to 'unsigned int', possible loss of data
D:\AProj\CommonStaticLib\apng\apngEnc.cpp(963,18): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\apng\apngEnc.cpp(965,57): warning C4267: 'argument': conversion from 'size_t' to 'unsigned int', possible loss of data
  imageApng.cpp
  CXAudio2.cpp
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'Audio/CXAudio2.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_math_defines.h(23,9): warning C4005: 'M_E': macro redefinition
  (compiling source file 'Audio/CXAudio2.cpp')
      D:\SDK\ffmpeg-dev\include\libavutil\mathematics.h(37,9):
      see previous definition of 'M_E'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_math_defines.h(26,9): warning C4005: 'M_LN2': macro redefinition
  (compiling source file 'Audio/CXAudio2.cpp')
      D:\SDK\ffmpeg-dev\include\libavutil\mathematics.h(43,9):
      see previous definition of 'M_LN2'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_math_defines.h(30,9): warning C4005: 'M_PI_4': macro redefinition
  (compiling source file 'Audio/CXAudio2.cpp')
      D:\SDK\ffmpeg-dev\include\libavutil\mathematics.h(79,9):
      see previous definition of 'M_PI_4'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_math_defines.h(31,9): warning C4005: 'M_1_PI': macro redefinition
  (compiling source file 'Audio/CXAudio2.cpp')
      D:\SDK\ffmpeg-dev\include\libavutil\mathematics.h(85,9):
      see previous definition of 'M_1_PI'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_math_defines.h(32,9): warning C4005: 'M_2_PI': macro redefinition
  (compiling source file 'Audio/CXAudio2.cpp')
      D:\SDK\ffmpeg-dev\include\libavutil\mathematics.h(91,9):
      see previous definition of 'M_2_PI'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_math_defines.h(35,9): warning C4005: 'M_SQRT1_2': macro redefinition
  (compiling source file 'Audio/CXAudio2.cpp')
      D:\SDK\ffmpeg-dev\include\libavutil\mathematics.h(103,9):
      see previous definition of 'M_SQRT1_2'
  
D:\AProj\CommonStaticLib\Audio\CXAudio2.cpp(1151,25): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
D:\AProj\CommonStaticLib\Audio\CXAudio2.cpp(1154,34): warning C4267: '+=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\Audio\CXAudio2.cpp(1396,11): warning C5055: operator '<=': deprecated between enumerations and floating-point types
D:\AProj\CommonStaticLib\Audio\CXAudio2.cpp(1401,16): warning C5055: operator '>=': deprecated between enumerations and floating-point types
  CXAudioManager.cpp
D:\AProj\CommonStaticLib\Audio\CXAudioManager.cpp(83,13): warning C4101: 'e': unreferenced local variable
D:\AProj\CommonStaticLib\Audio\CXAudioManager.cpp(479,10): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  WavFile.cpp
  cpu_acc_matrix.cpp
  DebugLogLib.cpp
D:\AProj\CommonStaticLib\Helpers\DebugLogLib.cpp(414,20): warning C4838: conversion from 'int' to 'TCHAR' requires a narrowing conversion
  glmUtils.cpp
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(184,27): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(184,27): warning C4244:         with
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(184,27): warning C4244:         [
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(184,27): warning C4244:             T=float
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(184,27): warning C4244:         ]
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(185,22): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(185,22): warning C4244:         with
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(185,22): warning C4244:         [
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(185,22): warning C4244:             T=float
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(185,22): warning C4244:         ]
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(186,28): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(186,28): warning C4244:         with
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(186,28): warning C4244:         [
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(186,28): warning C4244:             T=float
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(186,28): warning C4244:         ]
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(188,32): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(188,32): warning C4244:         with
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(188,32): warning C4244:         [
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(188,32): warning C4244:             T=float
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(188,32): warning C4244:         ]
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(189,22): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(189,22): warning C4244:         with
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(189,22): warning C4244:         [
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(189,22): warning C4244:             T=float
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(189,22): warning C4244:         ]
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(190,32): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(190,32): warning C4244:         with
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(190,32): warning C4244:         [
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(190,32): warning C4244:             T=float
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(190,32): warning C4244:         ]
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(192,22): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(192,22): warning C4244:         with
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(192,22): warning C4244:         [
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(192,22): warning C4244:             T=float
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(192,22): warning C4244:         ]
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(193,19): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(193,19): warning C4244:         with
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(193,19): warning C4244:         [
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(193,19): warning C4244:             T=float
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(193,19): warning C4244:         ]
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(194,22): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(194,22): warning C4244:         with
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(194,22): warning C4244:         [
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(194,22): warning C4244:             T=float
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(194,22): warning C4244:         ]
D:\AProj\CommonStaticLib\Helpers\glmUtils.cpp(702,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  UpByteCache.cpp
D:\AProj\CommonStaticLib\Helpers\UpByteCache.cpp(61,28): warning C4244: '=': conversion from '__int64' to 'int', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpByteCache.cpp(62,16): warning C4244: '=': conversion from '__int64' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpByteCache.cpp(93,31): warning C4244: '=': conversion from '__int64' to 'int', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpByteCache.cpp(94,17): warning C4244: '=': conversion from '__int64' to 'uint32_t', possible loss of data
  UpMemoryBuffer.cpp
  UpUtils.cpp
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(418,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(420,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(421,37): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(422,38): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(423,56): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(425,37): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(426,37): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(427,46): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(429,41): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(430,38): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(431,47): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(434,19): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(441,5): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(446,12): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(457,14): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpUtils.cpp(460,14): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  UpVtxIdxCache.cpp
D:\AProj\CommonStaticLib\Helpers\UpVtxIdxCache.cpp(31,27): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpVtxIdxCache.cpp(32,15): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpVtxIdxCache.cpp(33,27): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\Helpers\UpVtxIdxCache.cpp(34,15): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  json_reader.cpp
  json_value.cpp
  json_writer.cpp
  Binasc.cpp
  MidiEvent.cpp
  MidiEventList.cpp
  Compiling...
  MidiFile.cpp
  MidiMessage.cpp
  MidiMan.cpp
D:\AProj\CommonStaticLib\MidiMan.h(91,31): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'MidiMan.cpp')
  
D:\AProj\CommonStaticLib\MidiMan.cpp(97,27): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(98,38): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(99,44): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(100,41): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(217,14): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(225,14): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(244,36): warning C4244: '=': conversion from 'double' to 'int64_t', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(324,16): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(351,27): warning C4244: '=': conversion from 'double' to 'int64_t', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(507,28): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(558,31): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(566,13): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(575,38): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(589,13): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(654,13): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(658,29): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(662,13): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(684,42): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(734,43): warning C4244: '=': conversion from 'double' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(808,10): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(815,31): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(818,37): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(825,38): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(827,32): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(829,22): warning C4066: characters beyond first in wide-character constant ignored
D:\AProj\CommonStaticLib\MidiMan.cpp(842,18): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(849,32): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(850,43): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
D:\AProj\CommonStaticLib\MidiMan.cpp(865,53): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  VoxLoader.cpp
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(64,65): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(64,74): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(64,83): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(95,27): warning C4018: '<': signed/unsigned mismatch
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(96,31): warning C4018: '<': signed/unsigned mismatch
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(97,35): warning C4018: '<': signed/unsigned mismatch
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(109,31): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(110,31): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(111,31): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(118,97): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(118,124): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(118,151): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(140,117): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(140,154): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(140,191): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(167,52): warning C4018: '<': signed/unsigned mismatch
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(462,85): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'ogt_vox/VoxLoader.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(462,85):
      the template instantiation context (the oldest one first) is
          D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(124,40):
          see reference to function template instantiation 'VoxOutData &std::vector<VoxOutData,std::allocator<VoxOutData>>::emplace_back<glm::ivec3,uint32_t&,int&,bool&,size_t&,int,int,int,bool&>(glm::ivec3 &&,uint32_t &,int &,bool &,size_t &,int &&,int &&,int &&,bool &)' being compiled
              D:\AProj\CommonStaticLib\ogt_vox\VoxLoader.cpp(124,52):
              see the first reference to 'std::vector<VoxOutData,std::allocator<VoxOutData>>::emplace_back' in 'VoxLoader::loadFromFileBuffer::<lambda_1>::operator ()'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(924,24):
          see reference to function template instantiation '_Ty &std::vector<_Ty,std::allocator<_Ty>>::_Emplace_one_at_back<glm::vec<3,int,glm::packed_highp>,uint32_t&,int&,bool&,size_t&,int,int,int,bool&>(glm::vec<3,int,glm::packed_highp> &&,uint32_t &,int &,bool &,size_t &,int &&,int &&,int &&,bool &)' being compiled
          with
          [
              _Ty=VoxOutData
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(845,20):
          see reference to function template instantiation '_Ty &std::vector<_Ty,std::allocator<_Ty>>::_Emplace_back_with_unused_capacity<glm::vec<3,int,glm::packed_highp>,uint32_t&,int&,bool&,size_t&,int,int,int,bool&>(glm::vec<3,int,glm::packed_highp> &&,uint32_t &,int &,bool &,size_t &,int &&,int &&,int &&,bool &)' being compiled
          with
          [
              _Ty=VoxOutData
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(860,18):
          see reference to function template instantiation 'void std::_Construct_in_place<VoxOutData,_Ty,uint32_t&,int&,bool&,size_t&,int,int,int,bool&>(VoxOutData &,_Ty &&,uint32_t &,int &,bool &,size_t &,int &&,int &&,int &&,bool &) noexcept' being compiled
          with
          [
              _Ty=glm::ivec3
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(472,14):
          see reference to function template instantiation '_Ty *std::construct_at<_Ty,glm::vec<3,int,glm::packed_highp>,uint32_t&,int&,bool&,size_t&,int,int,int,bool&>(_Ty *const ,glm::vec<3,int,glm::packed_highp> &&,uint32_t &,int &,bool &,size_t &,int &&,int &&,int &&,bool &) noexcept(<expr>)' being compiled
          with
          [
              _Ty=VoxOutData
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(476,82): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'ogt_vox/VoxLoader.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(463,90): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'ogt_vox/VoxLoader.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(463,90):
      the template instantiation context (the oldest one first) is
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(860,18):
          see reference to function template instantiation 'void std::_Construct_in_place<VoxOutData,_Ty,uint32_t&,int&,bool&,size_t&,int,int,int,bool&>(VoxOutData &,_Ty &&,uint32_t &,int &,bool &,size_t &,int &&,int &&,int &&,bool &) noexcept' being compiled
          with
          [
              _Ty=glm::ivec3
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(472,14):
          see reference to function template instantiation '_Ty *std::construct_at<_Ty,glm::vec<3,int,glm::packed_highp>,uint32_t&,int&,bool&,size_t&,int,int,int,bool&>(_Ty *const ,glm::vec<3,int,glm::packed_highp> &&,uint32_t &,int &,bool &,size_t &,int &&,int &&,int &&,bool &) noexcept' being compiled
          with
          [
              _Ty=VoxOutData
          ]
  
  pugixml.cpp
  File.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.cpp(152,31): warning C4244: 'argument': conversion from 'saba::File::Offset' to 'irr::u32', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.cpp(173,31): warning C4244: 'argument': conversion from 'saba::File::Offset' to 'irr::u32', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.cpp(193,31): warning C4244: 'argument': conversion from 'saba::File::Offset' to 'irr::u32', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.cpp(218,13): warning C4244: 'argument': conversion from 'saba::File::Offset' to 'long', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.h(72,37): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file 'saba/src/Saba/Base/File.cpp')
      D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.h(72,37):
      the template instantiation context (the oldest one first) is
          D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.cpp(162,9):
          see reference to function template instantiation 'bool saba::File::Read<_Ty>(T *,size_t)' being compiled
          with
          [
              _Ty=char,
              T=char
          ]
  
  Log.cpp
  Path.cpp
  Singleton.cpp
  Time.cpp
  UnicodeUtil.cpp
  RenderPrimitive.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.h(41,45): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
D:\AProj\CommonStaticLib\MidiMan.h(91,31): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.h(400,63): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
  MMDCamera.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDCamera.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDCamera.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDCamera.cpp')
  
  MMDIkSolver.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDIkSolver.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDIkSolver.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDIkSolver.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'saba/src/Saba/Model/MMD/MMDIkSolver.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(16,34): warning C4305: 'initializing': truncation from 'double' to 'const float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(16,41): warning C4305: 'initializing': truncation from 'double' to 'const float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305:         ]
  MMDMaterial.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDMaterial.cpp')
  
  MMDModel.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDModel.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'saba/src/Saba/Model/MMD/MMDModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDModel.cpp(382,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  MMDMorph.cpp
  MMDNode.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDNode.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDNode.cpp')
  
  MMDPhysics.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysics.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysics.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysics.cpp')
  
  MMDPhysicsPhysX.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(205,48): warning C4311: 'type cast': pointer truncation from 'void *' to 'physx::PxU32'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(205,48): warning C4302: 'type cast': truncation from 'void *' to 'physx::PxU32'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(420,36): warning C4244: '=': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(422,37): warning C4244: '=': conversion from 'physx::PxI32' to 'PhyReal', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(426,36): warning C4244: '=': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(468,14): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305: '=': truncation from 'double' to 'T'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305: '=': truncation from 'double' to 'T'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305: '=': truncation from 'double' to 'T'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(100,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(111,35): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(118,35): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(125,51): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(300,34): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(308,41): warning C4834: discarding return value of function with [[nodiscard]] attribute
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(309,9): warning C4834: discarding return value of function with [[nodiscard]] attribute
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(366,29): warning C4305: 'argument': truncation from 'double' to 'physx::PxReal'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(907,67): warning C4311: '<function-style-cast>': pointer truncation from 'void *' to 'physx::PxU32'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(907,67): warning C4302: '<function-style-cast>': truncation from 'void *' to 'physx::PxU32'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(1034,19): warning C4267: 'argument': conversion from 'size_t' to 'const uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(1982,29): warning C4267: 'argument': conversion from 'size_t' to 'const uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(1988,110): warning C4267: 'argument': conversion from 'size_t' to 'physx::PxU32', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(2143,22): warning C4018: '<': signed/unsigned mismatch
  Compiling...
  MMDPhysicsPhysX_vehicle.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX_vehicle.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX_vehicle.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDPhysicsPhysX_vehicle.cpp')
  
  PhysXVehicle.cpp
  PMXFile.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/PMXFile.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(796,47): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(799,22): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(823,31): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(824,39): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(832,52): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(835,36): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(839,29): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(862,141): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1026,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1028,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1030,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1032,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1034,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1036,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.h(72,37): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/PMXFile.cpp')
      D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.h(72,37):
      the template instantiation context (the oldest one first) is
          D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(64,16):
          see reference to function template instantiation 'bool saba::File::Read<char16_t>(T *,size_t)' being compiled
          with
          [
              T=char16_t
          ]
  
  PMXFile_Generator.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/PMXFile_Generator.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(492,87): warning C4267: 'argument': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(649,31): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(649,20): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(674,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(674,32): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(675,39): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(675,20): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(817,35): warning C4267: 'initializing': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(818,35): warning C4267: 'initializing': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(819,122): warning C4267: 'argument': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(819,44): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(842,34): warning C4267: 'initializing': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(852,30): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(853,39): warning C4267: 'initializing': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(854,39): warning C4267: 'initializing': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(894,51): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(912,53): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(916,45): warning C4267: 'initializing': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(999,26): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  PMXModel.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/PMXModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/PMXModel.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/PMXModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(971,21): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1188,66): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1245,48): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1250,48): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1270,79): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244: '+=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305: '*=': truncation from 'double' to 'T'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1410,40): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1442,37): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1548,58): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1555,34): warning C4312: 'type cast': conversion from 'int' to 'void *' of greater size
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1572,16): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1624,5): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1635,5): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1655,5): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1669,5): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2170,14): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2346,52): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2348,80): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2368,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2378,28): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2379,25): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2569,31): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2598,30): warning C4018: '>=': signed/unsigned mismatch
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(3333,11): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(4046,10): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(4096,39): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(4114,10): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  PMXFile_P2.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/PMXFile_P2.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(174,27): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(194,34): warning C4018: '<': signed/unsigned mismatch
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267: '=': conversion from 'size_t' to '_Ty', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267:             _Ty=int32_t
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(228,21): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(349,62): warning C4018: '<': signed/unsigned mismatch
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(356,24): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(369,24): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(384,24): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(414,25): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(429,26): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(512,26): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(546,31): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(547,31): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(604,61): warning C4018: '<': signed/unsigned mismatch
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(613,24): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
  PMXWriter.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/PMXWriter.cpp')
  
  PMXGenerator_Test.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/pmx/PMXGenerator_Test.cpp')
  
  SjisToUnicode.cpp
  VMDAnimation.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/VMDAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/VMDAnimation.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/VMDAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'saba/src/Saba/Model/MMD/VMDAnimation.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'saba/src/Saba/Model/MMD/VMDAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(532,43): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(553,21): warning C4085: expected pragma parameter to be 'on' or 'off'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(553,25): warning C4081: expected 'newline'; found ')'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(575,21): warning C4085: expected pragma parameter to be 'on' or 'off'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(575,24): warning C4081: expected 'newline'; found ')'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(667,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(668,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  VMDCameraAnimation.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/VMDCameraAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/VMDCameraAnimation.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/VMDCameraAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'saba/src/Saba/Model/MMD/VMDCameraAnimation.cpp')
  
  VMDFile.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.h(72,37): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/VMDFile.cpp')
      D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.h(72,37):
      the template instantiation context (the oldest one first) is
          D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDFile.cpp(252,14):
          see reference to function template instantiation 'bool saba::File::Read<char>(T *,size_t)' being compiled
          with
          [
              T=char
          ]
  
  VPDFile.cpp
  Splines.cpp
  stlUtils.cpp
D:\AProj\CommonStaticLib\stlUtils.cpp(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\AProj\CommonStaticLib\stlUtils.cpp(358,15): warning C4309: '=': truncation of constant value
D:\AProj\CommonStaticLib\stlUtils.cpp(359,15): warning C4309: '=': truncation of constant value
D:\AProj\CommonStaticLib\stlUtils.cpp(360,15): warning C4309: '=': truncation of constant value
  SvgRenderer.cpp
D:\AProj\CommonStaticLib\nanosvg\src\nanosvg.h(1425,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'SvgRenderer.cpp')
  
D:\AProj\CommonStaticLib\nanosvg\src\nanosvg.h(1433,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'SvgRenderer.cpp')
  
D:\AProj\CommonStaticLib\nanosvg\src\nanosvg.h(1466,26): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'SvgRenderer.cpp')
  
D:\AProj\CommonStaticLib\nanosvg\src\nanosvg.h(2509,29): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'SvgRenderer.cpp')
  
D:\AProj\CommonStaticLib\nanosvg\src\nanosvg.h(2513,29): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'SvgRenderer.cpp')
  
D:\AProj\CommonStaticLib\nanosvg\src\nanosvg.h(2517,30): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'SvgRenderer.cpp')
  
D:\AProj\CommonStaticLib\nanosvg\src\nanosvg.h(2521,31): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'SvgRenderer.cpp')
  
  png.c
  pngerror.c
  pngget.c
  pngmem.c
  pngpread.c
  pngread.c
  pngrio.c
  pngrtran.c
  pngrutil.c
  pngset.c
  pngtrans.c
  pngwio.c
  pngwrite.c
  pngwtran.c
  pngwutil.c
  adler32.c
  compress.c
  crc32.c
  deflate1.c
  gzclose.c
  Compiling...
  infback.c
  inffast.c
  inflate.c
  inftrees.c
  trees.c
  uncompr.c
  zutil.c
  blocksplitter.c
  gzip_container.c
  katajainen.c
D:\AProj\CommonStaticLib\apng\zopfli\katajainen.c(169,35): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  tree.c
D:\AProj\CommonStaticLib\apng\zopfli\tree.c(54,38): warning C4267: '=': conversion from 'size_t' to 'unsigned int', possible loss of data
D:\AProj\CommonStaticLib\apng\zopfli\tree.c(62,29): warning C4267: '=': conversion from 'size_t' to 'unsigned int', possible loss of data
D:\AProj\CommonStaticLib\apng\zopfli\tree.c(77,17): warning C4267: '+=': conversion from 'size_t' to 'unsigned int', possible loss of data
D:\AProj\CommonStaticLib\apng\zopfli\tree.c(79,29): warning C4244: 'function': conversion from 'size_t' to 'double', possible loss of data
D:\AProj\CommonStaticLib\apng\zopfli\tree.c(85,45): warning C4244: 'function': conversion from 'const size_t' to 'double', possible loss of data
D:\AProj\CommonStaticLib\apng\zopfli\tree.c(98,53): warning C4267: 'function': conversion from 'size_t' to 'int', possible loss of data
  util.c
  zlib_container.c
  zopfli_lib.c
  alpha_dec.c
  buffer_dec.c
  frame_dec.c
  idec_dec.c
  io_dec.c
  quant_dec.c
  Compiling...
  tree_dec.c
  vp8l_dec.c
  vp8_dec.c
  webp_dec.c
  alpha_processing.c
  alpha_processing_sse2.c
  alpha_processing_sse41.c
  cost.c
  cost_sse2.c
  cpu.c
  dec.c
  dec_clip_tables.c
  dec_sse2.c
  dec_sse41.c
  enc.c
  enc_sse2.c
  enc_sse41.c
  filters.c
  filters_sse2.c
  lossless.c
  Compiling...
  lossless_enc.c
  lossless_enc_sse2.c
  lossless_enc_sse41.c
  lossless_sse2.c
  rescaler.c
  rescaler_sse2.c
  ssim.c
  ssim_sse2.c
  upsampling.c
  upsampling_sse2.c
  upsampling_sse41.c
  yuv.c
  yuv_sse2.c
  yuv_sse41.c
  alpha_enc.c
  analysis_enc.c
  backward_references_cost_enc.c
  backward_references_enc.c
  config_enc.c
  cost_enc.c
  Compiling...
  filter_enc.c
  frame_enc.c
  histogram_enc.c
  iterator_enc.c
  near_lossless_enc.c
  picture_csp_enc.c
  picture_enc.c
  picture_psnr_enc.c
  picture_rescale_enc.c
  picture_tools_enc.c
  predictor_enc.c
  quant_enc.c
  syntax_enc.c
  token_enc.c
  tree_enc.c
  vp8l_enc.c
  webp_enc.c
  anim_encode.c
  muxedit.c
  muxinternal.c
  Compiling...
  muxread.c
  bit_reader_utils.c
  bit_writer_utils.c
  color_cache_utils.c
  filters_utils.c
  huffman_encode_utils.c
  huffman_utils.c
  quant_levels_dec_utils.c
  quant_levels_utils.c
  random_utils.c
  rescaler_utils.c
  thread_utils.c
  utils.c
  pmutil.c
  portmidi.c
  pmwin.c
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwin.c(78,41): warning C4133: 'function': incompatible types - from 'char [9]' to 'LPCWSTR'
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwin.c(82,28): warning C4133: 'function': incompatible types - from 'char [9]' to 'LPCWSTR'
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwin.c(86,28): warning C4133: 'function': incompatible types - from 'char [6]' to 'LPCWSTR'
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwin.c(90,28): warning C4133: 'function': incompatible types - from 'char [11]' to 'LPCWSTR'
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwin.c(94,31): warning C4133: 'function': incompatible types - from 'char *' to 'LPCWSTR'
  pmwinmm.c
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(183,27): warning C4312: 'type cast': conversion from 'UINT' to 'void *' of greater size
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(201,23): warning C4312: 'type cast': conversion from 'UINT' to 'void *' of greater size
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(223,27): warning C4312: 'type cast': conversion from 'UINT' to 'void *' of greater size
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(239,23): warning C4312: 'type cast': conversion from 'UINT' to 'void *' of greater size
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(265,12): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(550,16): warning C4311: 'type cast': pointer truncation from 'void *' to 'DWORD'
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(699,27): warning C4244: '=': conversion from 'DWORD_PTR' to 'long', possible loss of data
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(711,31): warning C4244: '=': conversion from 'DWORD_PTR' to 'PmTimestamp', possible loss of data
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(712,29): warning C4244: '=': conversion from 'DWORD_PTR' to 'PmMessage', possible loss of data
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(733,57): warning C4244: 'function': conversion from 'DWORD_PTR' to 'PmTimestamp', possible loss of data
D:\AProj\CommonStaticLib\portmidi\pm_win\pmwinmm.c(830,16): warning C4311: 'type cast': pointer truncation from 'void *' to 'DWORD'
  ptwinmm.c
  gzlib.c
  gzread.c
  gzwrite.c
  cache.c
D:\AProj\CommonStaticLib\apng\zopfli\cache.c(36,9): warning C4477: 'fprintf' : format string '%lu' requires an argument of type 'unsigned long', but variadic argument 1 has type 'size_t'
      D:\AProj\CommonStaticLib\apng\zopfli\cache.c(36,9):
      consider using '%zu' in the format string
  
  deflate.c
D:\AProj\CommonStaticLib\apng\zopfli\deflate.c(538,14): warning C4244: '=': conversion from 'size_t' to 'double', possible loss of data
D:\AProj\CommonStaticLib\apng\zopfli\deflate.c(539,14): warning C4244: '=': conversion from 'size_t' to 'double', possible loss of data
D:\AProj\CommonStaticLib\apng\zopfli\deflate.c(550,15): warning C4244: '=': conversion from 'size_t' to 'double', possible loss of data
D:\AProj\CommonStaticLib\apng\zopfli\deflate.c(551,15): warning C4244: '=': conversion from 'size_t' to 'double', possible loss of data
D:\AProj\CommonStaticLib\apng\zopfli\deflate.c(598,27): warning C4244: 'return': conversion from 'size_t' to 'double', possible loss of data
  hash.c
  lz77.c
  squeeze.c
  CommonStaticLib.vcxproj -> D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib
