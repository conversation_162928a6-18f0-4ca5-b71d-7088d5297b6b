#pragma once
#include "UPCOMMON.h"
#include <cmath>
#include <vector>
#include <unordered_map>
#include <string>
#include <glm/glm.hpp>
#include <glm/gtc/constants.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtx/fast_square_root.hpp>
#include <ctime>
#include <glm/gtx/matrix_interpolation.hpp>
#include <glm/gtx/quaternion.hpp>
#include <glm/gtx/euler_angles.hpp>
const double piDouble = glm::pi<double>();
const float piFloat = glm::pi<float>();
const float halfPi = glm::pi<float>() * 0.5f;

namespace glh {

	// Custom constexpr definition for Pi
	constexpr float pi() {
		return 3.14159265358979323846f;
	}

	glm::vec3 rotateTowards(glm::vec3 A, glm::vec3 B, float limitAngleDeg);

	glm::vec3 rotateTowardsXZ(glm::vec3 A, glm::vec3 B, float limitAngleDeg);


	//inline void vecLimitLengthMax(glm::vec3& vec, float len) {
	//    if (glm::length(vec) > len) vec = glm::normalize(vec) * len;
	//}
	//inline void vecLimitLengthMin(glm::vec3& vec, float len) {
	//    if (glm::length(vec) < len) vec = glm::normalize(vec) * len;
	//}
	inline glm::vec3 vecLimitFast(const glm::vec3& vec, float minv, float maxv) { return glm::fastNormalize(vec) * glm::clamp(glm::fastLength(vec), minv, maxv); }
	inline glm::vec3 vecLimit(const glm::vec3& vec, float minv, float maxv) { return glm::normalize(vec) * glm::clamp(glm::length(vec), minv, maxv); }

	inline void vecLimitLengthMinMax(glm::vec3& vec, float lenMin, float lenMax) {
		float vlen = glm::length(vec);
		if (vlen > lenMax) vec = glm::normalize(vec) * lenMax;
		else if (vlen < lenMin) vec = glm::normalize(vec) * lenMin;
	}
	float angleRadBetweenVec(glm::vec3 A, glm::vec3 B);
	float angleDegBetweenVec(glm::vec3 A, glm::vec3 B);

    float posInCameraViewAngleDeg(glm::mat4 camMat, glm::vec3 pos);

	inline glm::vec3 matTransformVec(glm::mat4 m, glm::vec3 v){	return glm::vec3(m * glm::vec4(v, 1));	}
	inline glm::vec3 matRotateVec(glm::mat4 m, glm::vec3 v)	{	return   glm::mat3(m) * v;	}
    inline glm::vec3 invMatTransformVec(glm::mat4 m, glm::vec3 v) { return glm::vec3(glm::inverse(m) * glm::vec4(v, 1)); }
    inline glm::vec3 invMatRotateVec(glm::mat4 m, glm::vec3 v) { return   glm::mat3(glm::inverse(m)) * v; }  //relative in local transform
    inline glm::vec3 relativePosInMat( glm::mat4 m,glm::vec3 pos, glm::vec3 posBase ) { return   glm::mat3(glm::inverse(m)) * (pos-posBase); }
    inline glm::vec3 posToMat(glm::mat4 m, glm::vec3 v) { return glm::vec3(glm::inverse(m) * glm::vec4(v, 1)); }
    inline float angleDegToAxisY(const glm::mat4 &m)
    {
        const glm::quat parentRot(m);
        const glm::vec3 downDir = parentRot * glm::vec3(0, -1, 0);
        return glm::degrees(glm::acos(glm::dot(glm::vec3(0, -1, 0), downDir)));
    }
    glm::vec3 mirrorPoint(const glm::vec3& LinePt0, const glm::vec3& linePt1, const glm::vec3& point);

	glm::vec3 mirrorPointLimitDis(const glm::vec3& LinePt0, const glm::vec3& linePt1, const glm::vec3& point, float minDis,float maxDis);
    inline glm::mat4 mirrorMatOnX(glm::mat4 m) { static glm::mat4 m1={-1,0,0,0, 0,1,0,0, 0,0,1,0, 0,0,0,1}; return m1 * m; }
    
    inline glm::mat4 rttMat(glm::vec3 rtt)  {      return glm::mat4(glm::quat(rtt));    }

	glm::mat4 buildLookAtMatrixLH(
		const glm::vec3& position,
		const glm::vec3& target,
		const glm::vec3& upVector);


	// Function to create view matrix from position, target, and right vectors
	glm::mat4 createViewMatrixPTR(const glm::vec3& position, const glm::vec3& target, const glm::vec3& right);



    float getYaw(const glm::quat& q);
    glm::quat keepRotationY(glm::quat q);
    glm::quat keepRotationYaddPI(glm::quat q);

    inline glm::mat4 rttRadZYX(float x, float y, float z) { return glm::mat4(glm::quat(glm::vec3(x, y, z))); }
    inline glm::mat4 rttDegZYX(float x, float y, float z) { return glm::mat4(glm::quat(glm::radians(glm::vec3(x, y, z)))); }

	//inline glm::quat eulerToQuatXYZ(const glm::vec3& euler) { // Same to glm::quat(euler)
    //    glm::quat qz = glm::angleAxis(euler.z, glm::vec3(0.0f, 0.0f, 1.0f));
    //    glm::quat qx = glm::angleAxis(euler.x, glm::vec3(1.0f, 0.0f, 0.0f));
    //    glm::quat qy = glm::angleAxis(euler.y, glm::vec3(0.0f, 1.0f, 0.0f));
    //    return qz * qy * qx;
    //}
    inline glm::vec3 quatToEulerXYZ(const glm::quat& q) {
		return glm::eulerAngles(q);
    }

    inline glm::quat eulerToQuatZXY(const glm::vec3& euler) // MMD RIGID BODY order
    {
        glm::quat qz = glm::angleAxis(euler.z, glm::vec3(0.0f, 0.0f, 1.0f));
        glm::quat qx = glm::angleAxis(euler.x, glm::vec3(1.0f, 0.0f, 0.0f));
        glm::quat qy = glm::angleAxis(euler.y, glm::vec3(0.0f, 1.0f, 0.0f));
        return qy * qx * qz;
    }
 //   inline glm::vec3 quatToEulerZXY(const glm::quat& q) {
 //       glm::vec3 v;
 //       glm::extractEulerAngleZXY(glm::mat4(q), v.z, v.x, v.y);
 //       return v;
	//}
    inline glm::vec3 quatToEulerYXZ(const glm::quat& q) // make MMD RB order
    {
        glm::vec3 v;
		glm::extractEulerAngleYXZ(glm::mat4(q), v.y, v.x, v.z);        
        return v;
    }
    inline glm::quat eulerToQuatZYX(const glm::vec3& euler) {
        glm::quat qz = glm::angleAxis(euler.z, glm::vec3(0.0f, 0.0f, 1.0f));
        glm::quat qx = glm::angleAxis(euler.x, glm::vec3(1.0f, 0.0f, 0.0f));
        glm::quat qy = glm::angleAxis(euler.y, glm::vec3(0.0f, 1.0f, 0.0f));
        return qx * qy * qz;
    }
    inline glm::vec3 quatToEulerZYX(const glm::quat& q) {
        glm::vec3 v;
        glm::extractEulerAngleZYX(glm::mat4(q), v.z, v.y, v.x);
        return v;
	}

    inline glm::mat4 rttRadZXY(float x, float y, float z) { return glm::mat4((eulerToQuatZXY(glm::vec3(x, y, z)))); }
    inline glm::mat4 rttDegZXY(float x, float y, float z) { return glm::mat4(eulerToQuatZXY(glm::radians(glm::vec3(x, y, z)))); }
    
    glm::quat fromEulerWithOrder(
        const glm::vec3& eulerAngles,
        const std::string& rotationOrder = "XYZ"
    );
    glm::vec3 toEulerWithOrder(
        const glm::quat& q,
        const std::string& rotationOrder = "XYZ"
    );
    
    
    inline glm::mat4 rotationFromTo(glm::vec3 from, glm::vec3 to)  { return glm::mat4(glm::rotation(glm::normalize(from), glm::normalize(to))); }

    // Function to interpolate between two transformation matrices
    glm::mat4 interpolateTransform(const glm::mat4& M1, const glm::mat4& M2, float ratio);
    void quaternionToAxisAngle(const glm::quat& q, glm::vec3& outAxis, float& outAngle);
    void getTransformDeltaVel(const glm::mat4& M1, const glm::mat4& M2, float deltaTime, glm::vec3& outVel, glm::vec3& outAngVel);

    glm::vec3 calculateInitialPosition(const glm::vec3& direction, float initialSpeedLength, glm::vec3& initialVelocity, const glm::vec3& targetPosition,
        const glm::vec3& gravity, float time, float linearDamping,
        int stepsPerSecond);

    struct calcSpeedDirP2PinTimeGuess_Param {
        glm::vec3 startPosition;
        glm::vec3 targetPosition;
        float velLen; 
        glm::vec3 initVel; // for calc time if time==0
        float ignoreVelLenLimit = 10.f;
        glm::vec3 tgtMovDir; // if cannot close, move target along tgtMovDir
        
        float tgtMoveLimit = 100.f;
        glm::vec3 gravity;
        float time;
        float linearDamping;
        int stepsPerSecond = 60 * 3;// * SABA_PHYSICS_FRAMESTEP
        float maxErrorMag = 1000.f;
        int maxGuess;
    };
    glm::vec3 calcSpeedDirP2PinTimeGuess(const calcSpeedDirP2PinTimeGuess_Param& params);
    glm::vec3 calcSpeedDirP2PinTimeGuess(const glm::vec3& startPosition, const glm::vec3& targetPosition, 
        float initVelLen, glm::vec3 tgtMovDir, const glm::vec3& gravity, float time, float linearDamping, int stepsPerSecond, int maxGuess);
    glm::vec3 calcVelocityP2PinTimeGuess(const glm::vec3& startPosition, const glm::vec3& targetPosition, 
        const glm::vec3& gravity, float time, float linearDamping, int stepsPerSecond, int maxGuess=10);
    glm::vec3 calcP2PAdjustVelocityGuess(const glm::vec3& startPosition, const glm::vec3& targetPosition, glm::vec3 initVel, const glm::vec3& gravity, float linearDamping, float maxSimulateSeconds, int stepsPerSecond, int maxGuess, float adjustSpeed);
    glm::vec3 calcVelocityP2PinT(const glm::vec3& startPos, const glm::vec3& targetPos, float durationTime, const glm::vec3& gravity );

    glm::quat calculateJointRotation(const glm::vec3& currentPos, const glm::vec3& posTarget, const glm::vec3& jointPos);

    
    glm::quat directionToRotation(const glm::vec3& direction, const glm::vec3& upNormalized = glm::vec3(0, 1, 0)); //left hand system
    glm::quat directionToRotationRHS(const glm::vec3& direction, const glm::vec3& upNormalized = glm::vec3(0, 1, 0)); //right hand system

    inline glm::vec3 posAtoBmul(const glm::vec3& A, const glm::vec3& B, float t) {
        auto dir=B-A; float len = glm::length(dir); return A + dir * t;
    }
    inline glm::vec3 BaddAtoB(const glm::vec3& A, const glm::vec3& B, float len) {
		auto dir=B-A;  return B + glm::normalize(dir) * len ;
    }
    inline glm::vec3 AaddAtoB(const glm::vec3& A, const glm::vec3& B, float len) {
        auto dir = B - A;  return A + glm::normalize(dir) * len;
    }

 

    bool isStickInTriangle(glm::vec3 A, glm::vec3 B, glm::vec3 C, glm::vec3 E, glm::vec3 F, float lenMul, glm::vec3* intersectPt);

    bool isStickInCornerABC(const glm::vec3& A, const glm::vec3& B, const glm::vec3& C, glm::vec3 E, glm::vec3 F, float stickMul=1.f, glm::vec3* intersectPt=nullptr );

    inline glm::mat3 InvZ(const glm::mat3& m)
    {
        const glm::mat3 invZ = glm::scale(glm::mat4(1.0f), glm::vec3(1, 1, -1));
        return invZ * m * invZ;
    }
    inline glm::mat4 InvZ(const glm::mat4& m)
    {
        const glm::mat4 invZ = glm::scale(glm::mat4(1.0f), glm::vec3(1, 1, -1));
        return invZ * m * invZ;
    }
    inline void calculateInversePose( //need test!!
        const glm::vec3& relPos,  // Position from A to B
        const glm::quat& relRtt,  // Rotation from A to B
        glm::vec3& invPos,        // Output: Position from B to A
        glm::quat& invRtt         // Output: Rotation from B to A
    ) {
        // Inverse rotation is the conjugate of the quaternion
        invRtt = glm::conjugate(relRtt);
        // Inverse position = -(R^-1 * t)
        // Where R^-1 is the inverse rotation and t is the original translation
        invPos = -(invRtt * relPos);
    }







    // K-d Tree Node
    struct KDNode {
        glm::vec3 point;
        int index;
        KDNode* left;
        KDNode* right;

        KDNode(const glm::vec3& pt, int idx) : point(pt), index(idx), left(nullptr), right(nullptr) {}
    };

    // Comparator for constructing k-d tree
    struct KDComparator {
        int axis;
        KDComparator(int axis) : axis(axis) {}

        bool operator()(const std::pair<glm::vec3, int>& a, const std::pair<glm::vec3, int>& b) {
            return a.first[axis] < b.first[axis];
        }
    };

    class KDTree {
    public:
        KDTree() : root(nullptr) {}
        ~KDTree(){reset();} 
        void addPoint(const glm::vec3& point, int index) {
            root = insert(root, point, index, 0);
        }

        int nearestNeighbor(const glm::vec3& point) const {
            float minDist = std::numeric_limits<float>::max();
            int closestIndex = -1;
            nearestNeighborSearch(root, point, 0, minDist, closestIndex);
            return closestIndex;
        }

        void reset() {
            deleteTree(root);
            root = nullptr;
        }
         

    private:
        KDNode* root;

        KDNode* insert(KDNode* node, const glm::vec3& point, int index, int depth) {
            if (!node) {
                return new KDNode(point, index);
            }
            int axis = depth % 3;
            if (point[axis] < node->point[axis]) {
                node->left = insert(node->left, point, index, depth + 1);
            }
            else {
                node->right = insert(node->right, point, index, depth + 1);
            }
            return node;
        }

        void nearestNeighborSearch(KDNode* node, const glm::vec3& target, int depth, float& minDist, int& closestIndex) const {
            if (!node) {
                return;
            }
            auto d = target - node->point;
            float dist = glm::dot(d,d);
            if (dist < minDist) {
                minDist = dist;
                closestIndex = node->index;
            }
            int axis = depth % 3;
            KDNode* nextBranch = nullptr;
            KDNode* oppositeBranch = nullptr;
            if (target[axis] < node->point[axis]) {
                nextBranch = node->left;
                oppositeBranch = node->right;
            }
            else {
                nextBranch = node->right;
                oppositeBranch = node->left;
            }
            nearestNeighborSearch(nextBranch, target, depth + 1, minDist, closestIndex);

            if (fabs(target[axis] - node->point[axis]) < minDist) {
                nearestNeighborSearch(oppositeBranch, target, depth + 1, minDist, closestIndex);
            }
        }

        void deleteTree(KDNode* node) {
            if (!node) {
                return;
            }
            deleteTree(node->left);
            deleteTree(node->right);
            delete node;
        }
    };





}//glh

const glm::mat4 MatRttX180 = glm::mat4(
	1.0f, 0.0f, 0.0f, 0.0f,
	0.0f, -1.0f, 0.0f, 0.0f,
	0.0f, 0.0f, -1.0f, 0.0f,
	0.0f, 0.0f, 0.0f, 1.0f
);
const glm::mat4 MatRttY180 = glm::mat4(
	-1.0f, 0.0f, 0.0f, 0.0f,
	0.0f, 1.0f, 0.0f, 0.0f,
	0.0f, 0.0f, -1.0f, 0.0f,
	0.0f, 0.0f, 0.0f, 1.0f
);//glm::rotate(glm::mat4(1.f), pi_float, glm::vec3(0.f, 1.f, 0.f));
const glm::mat4 MatRttZ180 = glm::mat4(
	-1.0f, 0.0f, 0.0f, 0.0f,
	0.0f, -1.0f, 0.0f, 0.0f,
	0.0f, 0.0f, 1.0f, 0.0f,
	0.0f, 0.0f, 0.0f, 1.0f
);
