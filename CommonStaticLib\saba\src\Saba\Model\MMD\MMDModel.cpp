﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//
#include "mmdPCH.h"
#include "MMDModel.h"
#include "PMXModel.h"
#include "MMDPhysics.h"
#include "VPDFile.h"
#include "VMDAnimation.h"

#include <glm/gtc/matrix_transform.hpp>

#include <Saba/Base/Log.h>
#include <chrono>
#include <stlUtils.h>
#include <random>
extern float gGameTime , gSceneTime , gEditorTime, gTimeMul, gPhyTime , gFrameTime ;
namespace saba
{
 
	MMDPhysics* g_mmdPhysics{};
 
	MMDPhysicsManager::MMDPhysicsManager()
	{
	}

	MMDPhysicsManager::~MMDPhysicsManager()
	{
		for (auto& joint : m_joints)
		{
			g_mmdPhysics->RemoveJoint(joint.get());
		}
		m_joints.clear();

		for (auto& rb : m_rigidBodys)
		{
			DP(("remove RB %p",rb.get()));
			g_mmdPhysics->RemoveRigidBody(rb.get());
		}
		m_rigidBodys.clear();
		 
	}

	MMDPhysics* createPhysicsInstance(EPhysicsEngine phtype) {
		g_mmdPhysics = newPhysics(phtype);
		g_mmdPhysics->Create();
		return g_mmdPhysics;
	}

	bool MMDPhysicsManager::Create(EPhysicsEngine phtype)
	{		
		mPhyType = phtype;
		if (!g_mmdPhysics 
#if USE_JOLT && USE_PHYSX
			|| m_mmdPhysics->getType()!=mPhyType
#endif
			) {
			 
			return createPhysicsInstance(mPhyType)!=nullptr;
		}
		else return true;
	}

	MMDPhysics* MMDPhysicsManager::GetMMDPhysics()
	{
		return g_mmdPhysics;
	}

	MMDRigidBody* MMDPhysicsManager::newRigidBody(bool alsoAddSorted)
	{
		SABA_ASSERT(g_mmdPhysics != nullptr);
		std::unique_ptr<MMDRigidBody> rigidBody(GetMMDPhysics()->newRigidBody());
		auto ret = rigidBody.get();
		m_rigidBodys.emplace_back(std::move(rigidBody));

		if (alsoAddSorted) {
			sortedRBs.emplace_back(ret);
		}
		return ret;
	}

	void MMDPhysicsManager::RemoveRigidBody(MMDRigidBody* rb) {
		for (auto it = m_rigidBodys.begin(); it != m_rigidBodys.end();)
		{
			if (it->get() == rb)
				it = m_rigidBodys.erase(it);
			else it++;
		}
	}

	MMDJoint* MMDPhysicsManager::AddJoint()
	{
		SABA_ASSERT(g_mmdPhysics != nullptr);
		std::unique_ptr<MMDJoint> joint(GetMMDPhysics()->newJoint());
		auto ret = joint.get();
		m_joints.emplace_back(std::move(joint));

		return ret;
	}

	void MMDPhysicsManager::RemoveJoint(MMDJoint *jt)
	{
		for (auto it=m_joints.begin();it!=m_joints.end();)
		{
			if (it->get() == jt)
				it = m_joints.erase(it);
			else it++;
		}
	}

	void MMDPhysicsManager::sortRbByNode()
	{
		std::sort(m_rigidBodys.begin(), m_rigidBodys.end(), [&](RigidBodyPtr& a, RigidBodyPtr& b) {
			//auto na = a.get()->mmdNode, nb = b.get()->mmdNode;
		 
			return a.get()->node->sortedIdx < b.get()->node->sortedIdx;
			});
	}

	bool MMDPhysicsManager::_getDynRbActive()
	{
		return m_rigidBodys[0]->GetActivation();
	}

	void MMDPhysicsManager::_setDynRbActive(bool active)
	{
		for (auto& rb : m_rigidBodys)
		{
			if (rb->dynRbType == 1 ) {
				rb->SetActivation(active);
				//if (active)				rb->mmdNode->EnableIK(false);
			}	
		}
	}

 
	void MMDPhysicsManager::syncDynRb()
	{
		for (auto& rb : m_rigidBodys)
		{
			if (rb->dynRbType == 1) 
				rb->setGlmMat(rb->node->GetGlobalTransform());
			
		}
	}

	void MMDPhysicsManager::updateAllRbMass()
	{
		for (auto& rb : m_rigidBodys)
		{
			rb->updateMassOnDensity();
		}
	}

	void MMDPhysicsManager::addAllBodies() {
		for (auto& rb : m_rigidBodys)
		{
			g_mmdPhysics->AddRigidBody(rb.get());

		}
	}
	void MMDModel::saveGlobalTransform()
	{
		auto nodeMan = GetNodeManager();
		for (size_t i = 0; i < nodeMan->GetNodeCount(); i++)
		{
			auto node = nodeMan->GetMMDNode(i);
			node->savedGlobalTransform=	node->GetGlobalTransform();
		}
	}
	void MMDModel::loadGlobalTransform()
	{
		auto nodeMan = GetNodeManager();
		for (size_t i = 0; i < nodeMan->GetNodeCount(); i++)
		{
			auto node = nodeMan->GetMMDNode(i);
			node->SetGlobalTransform(node->savedGlobalTransform); 
		}
	}
	void MMDModel::SaveBaseAnimation()
	{
		auto nodeMan = GetNodeManager();
		for (size_t i = 0; i < nodeMan->GetNodeCount(); i++)
		{
			auto node = nodeMan->GetMMDNode(i);
			node->SaveBaseAnimation();
		}

		auto morphMan = GetMorphManager();
		for (size_t i = 0; i < morphMan->GetMorphCount(); i++)
		{
			auto morph = morphMan->GetMorph(i);
			morph->SaveBaseAnimation();
		}

		auto ikMan = GetIKManager();
		for (size_t i = 0; i < ikMan->GetIKSolverCount(); i++)
		{
			auto ikSolver = ikMan->GetMMDIKSolver(i);
			ikSolver->SaveBaseAnimation();
		}
	}

	void MMDModel::LoadBaseAnimation()
	{
		auto nodeMan = GetNodeManager();
		for (size_t i = 0; i < nodeMan->GetNodeCount(); i++)
		{
			auto node = nodeMan->GetMMDNode(i);
			node->LoadBaseAnimation();
		}

		auto morphMan = GetMorphManager();
		for (size_t i = 0; i < morphMan->GetMorphCount(); i++)
		{
			auto morph = morphMan->GetMorph(i);
			morph->LoadBaseAnimation();
		}

		auto ikMan = GetIKManager();
		for (size_t i = 0; i < ikMan->GetIKSolverCount(); i++)
		{
			auto ikSolver = ikMan->GetMMDIKSolver(i);
			ikSolver->LoadBaseAnimation();
		}
	}
	void MMDModel::saveNodesTransform()
	{
		auto nodeMan = GetNodeManager();
		for (size_t i = 0; i < nodeMan->GetNodeCount(); i++)
		{
			auto node = nodeMan->GetMMDNode(i);
			node->SaveAnimation();
		}
	}
	void MMDModel::loadNodesTransform()
	{
		auto nodeMan = GetNodeManager();
		for (size_t i = 0; i < nodeMan->GetNodeCount(); i++)
		{
			auto node = nodeMan->GetMMDNode(i);
			node->LoadAnimation();
		}
	}

	void MMDModel::ClearBaseAnimation()
	{
		auto nodeMan = GetNodeManager();
		for (size_t i = 0; i < nodeMan->GetNodeCount(); i++)
		{
			auto node = nodeMan->GetMMDNode(i);
			node->ClearBaseAnimation();
		}

		auto morphMan = GetMorphManager();
		for (size_t i = 0; i < morphMan->GetMorphCount(); i++)
		{
			auto morph = morphMan->GetMorph(i);
			morph->ClearBaseAnimation();
		}

		auto ikMan = GetIKManager();
		for (size_t i = 0; i < ikMan->GetIKSolverCount(); i++)
		{
			auto ikSolver = ikMan->GetMMDIKSolver(i);
			ikSolver->ClearBaseAnimation();
		}
	}

	namespace
	{
#ifdef SABA_INVZ
		glm::mat3 InvZ(const glm::mat3& m)
		{
			const glm::mat3 invZ = glm::scale(glm::mat4(1.0f), glm::vec3(1, 1, -1));
			return invZ * m * invZ;
		}
		glm::quat InvZ(const glm::quat& q)
		{
			auto rot0 = glm::mat3_cast(q);
			auto rot1 = InvZ(rot0);
			return glm::quat_cast(rot1);
		}
#else
#define InvZ(_x_)   ( _x_ )
		//const glm::mat3& const InvZ(const glm::mat3& m)
		//{
		//	return m;
		//}
		//const glm::quat& const InvZ(const glm::quat& q)
		//{
		//	return q;
		//}
#endif
	}

	void MMDModel::syncPhyToFrame(float frame, int count, int mode )
	{
		ResetPhysics();
		syncingPhysics = mode; sypId = 0; sypCount = count;
		SaveBaseAnimation();
	}

	void MMDModel::UpdateAllAnimation(VMDAnimation * vmdAnim, float vmdFrame, float physicsElapsed)
	{

		animeTime = vmdFrame / 30.f;
		if (vmdFrame < 0.f) vmdFrame = 0.f;

		if ( vmdAction &&  addAnimOn &&  vmdAddAnims.size() > 0) //ADDON
		{
			if (gSceneTime - vas[0].start >= 0) {
				bool lastPlaying = gSceneTime - vas[1].start < vmdAddAnims[vmdAddAnimId[1]].maxFrame / 30.f;
				if (lastPlaying)
				{
					DP(("mod"));

					auto& va0 = vmdAddAnims[vmdAddAnimId[0]];
					va0.vmd->EvaluateSave((gSceneTime - vas[0].start + vas[0].offset) * 30.f, 0);
					auto& va1 = vmdAddAnims[vmdAddAnimId[1]];
					va1.vmd->EvaluateSave((gSceneTime - vas[1].start + vas[1].offset) * 30.f, 1);
					va0.weight = std::min(1.f, (gSceneTime - vas[0].start) / std::max(va0.vi.actB, 0.2f * gTimeMul));
					va0.vmd->EvaluateMontage(va0.weight);
				}
				else
				{
					auto& va0 = vmdAddAnims[vmdAddAnimId[0]];
					animeTime = gSceneTime - vas[0].start;
					vmdFrame = animeTime * 30.f;
					vmdAnim = va0.vmd.get();

					vmdAnim->Evaluate(vmdFrame, 1.f, 0);
				}
			}
			else if (gSceneTime - vas[1].start>= 0)
			{
				auto& va1 = vmdAddAnims[vmdAddAnimId[1]];
				animeTime = gSceneTime - vas[1].start;
				vmdFrame = animeTime * 30.f;
				vmdAnim = va1.vmd.get();

				vmdAnim->Evaluate(vmdFrame, 1.f, 0);
			}
			else {
				auto& va0 = vmdAddAnims[vmdAddAnimId[0]];
				vmdAnim = va0.vmd.get();
				vmdAnim->Evaluate(0, 1.f, 0);
			}
		}		
		else if (vmdAnim != nullptr)  // NORMAL ANIM
		{
#if !SABA_PHYSICS_ASYNC
			if (syncingPhysics==1 &&sypId < sypCount)
			{
				vmdAnim->Evaluate(vmdFrame,float(sypId)/sypCount);
				sypId++;
			}
			else 
				syncingPhysics = 0;

			if (!syncingPhysics)
#endif
			{
				vmdAnim->Evaluate(vmdFrame, 1.f, vmdFrame > vmdAnim->GetMaxMotionTimeFrame() + 1);// NORMAL ANIMATION
				if (vmdBase[1].vmd) {					 
					vmdBase[1].vmd->Evaluate((gSceneTime - vmdBase[1].startTime)*30.f, 1.f, vmdFrame > vmdAnim->GetMaxMotionTimeFrame() + 1);
				}
			}
		}
		else if (syncingPhysics == 2) 
		{
			if (sypId <= sypCount)
			{
				std::vector<Pose>* posePtr;
				if (curPoseSlot >= 0) {
					posePtr = &poseSlot[curPoseSlot];
				}
				else
					posePtr = &curPose;
				// evaluate
				float w = (sypId == sypCount) ? 1.f : (1 - cos(3.1415927 * sypId / sypCount)) / 2;// sin(3.1415927/2 * sypId / sypCount);
				for (auto& pose : *posePtr)
				{
					auto t = glm::mix(pose.m_beginTranslate, pose.m_endTranslate, w);
					auto q = glm::slerp(pose.m_beginRotate, pose.m_endRotate, w);
					pose.m_node->SetAnimationTranslate(t);
					pose.m_node->SetAnimationRotate(q);
				}
				sypId++;
			}
			else 
				syncingPhysics = 0;
		}
		if (vmdAddAnimCount > 1 && addAnimOn &&  vmdAddAnims.size() > 0)
		{

			auto& va0 = vmdAddAnims[vmdAddAnimId[0]];
			va0.vmd->EvaluateSave(va0.frame, 0);
			auto& va1 = vmdAddAnims[vmdAddAnimId[1]];
			va1.vmd->EvaluateSave(va1.frame, 1);
			va0.vmd->EvaluateMontage(va0.weight);
			
		}
		if (isCharacter) {
			DoCallback(NodeCbStage::NS9);
			//CPU_COUNT_B(m2);

			UpdateMorphAnimation();
			//CPU_COUNT_E(m2);
		}
		UpdateNodeAnimation(false); //d:2ms,j r:0.1ms
#if !SABA_USE_PHYSX
		if (allPhyAct && ALL_NODE_PHYSICS) {
			//CPU_COUNT_B(SABAPHY);
			if (physicsElapsed > 0.0001f) UpdatePhysicsAnimation2Pass(physicsElapsed, 0);
			//d:11ms , r:1ms  
			//d:DISABLE_DEACTIVATION D2.7ms  
			//	0.01,0.1:2.2ms
			//	0.11,1.1:2.2ms
			//CPU_COUNT_E(SABAPHY);
			UpdateNodeAnimation(true);
			if (physicsElapsed > 0.0001f) UpdatePhysicsAnimation2Pass(physicsElapsed, 1);
		}
		else
#endif
		{
			//CPU_COUNT_B(PHY);
			 UpdatePhysicsAnimation(physicsElapsed);
			//CPU_COUNT_E(PHY);
			int cc = UpdateNodeAnimation(true);


			if (phy2ndPass)
				UpdatePhysicsAnimation(0);
		}
 
	}
	bool MMDModel::setPoseId(int slot)
	{
		if (slot < 0) {
			curPoseSlot = -1; 
			return false;
		}
		if (poseSlot[slot].size() == 0) return false;
		curPoseSlot = slot;
		return true;
	}
	void MMDModel::LoadPose(const VPDFile & vpd, int frameCount, bool immediate, int slot)
	{
		std::vector<Pose>* posePtr;
		curPoseSlot = slot;
		if (slot >= 0) {
			posePtr = &poseSlot[slot];
		}
		else 
			posePtr=&curPose;
		posePtr->clear();
		
		for (const auto& bone : vpd.m_bones)
		{
			auto nodeIdx = GetNodeManager()->FindNodeIndex(bone.m_boneName);
			if (MMDNodeManager::NPos != nodeIdx)
			{
				Pose pose;
				pose.m_node = GetNodeManager()->GetMMDNode(bone.m_boneName);
				pose.m_beginTranslate = pose.m_node->GetAnimationTranslate();
#ifdef SABA_INVZ
				pose.m_endTranslate = bone.m_translate MUL_VEC3INVZ;
#else
				pose.m_endTranslate = bone.m_translate;
#endif
				pose.m_beginRotate = pose.m_node->GetAnimationRotate();
				pose.m_endRotate = InvZ(bone.m_quaternion);
				posePtr->emplace_back(std::move(pose));
			}
		}

		struct Morph
		{
			MMDMorph*	m_morph;
			float		m_beginWeight;
			float		m_endWeight;
		};
		std::vector<Morph> morphs;
		for (const auto& vpdMorph : vpd.m_morphs)
		{
			auto morphIdx = GetMorphManager()->FindMorphIndex(vpdMorph.m_morphName);
			if (MMDMorphManager::NPos != morphIdx)
			{
				Morph morph;
				morph.m_morph = GetMorphManager()->GetMorph(vpdMorph.m_morphName);
				morph.m_beginWeight = morph.m_morph->GetWeight();
				morph.m_endWeight = vpdMorph.m_weight;
				morphs.emplace_back(std::move(morph));
			}
		}
		if (immediate) {
			// Physicsを反映する
			for (int i = 0; i < frameCount; i++)
			{
				BeginAnimation();

				// evaluate
				float w = float(1 + i) / float(frameCount);
				for (auto& pose : *posePtr)
				{
					auto t = glm::mix(pose.m_beginTranslate, pose.m_endTranslate, w);
					auto q = glm::slerp(pose.m_beginRotate, pose.m_endRotate, w);
					pose.m_node->SetAnimationTranslate(t);
					pose.m_node->SetAnimationRotate(q);
					//if (i == frameCount - 1)
					//{
					//	DPWCS((L"lp%9s %f  %f", pose.m_node->GetNameU().c_str(), w, t.y));
					//}
				}

				for (auto& morph : morphs)
				{
					auto weight = glm::mix(morph.m_beginWeight, morph.m_endWeight, w);
					morph.m_morph->SetWeight(weight);
				}

				UpdateMorphAnimation();
				UpdateNodeAnimation(false);				
				UpdatePhysicsAnimation(1.0f / 30.0f);

				UpdateNodeAnimation(true);

				EndAnimation();
			}
		}
		//SaveBaseAnimation();
	}



	void MMDModel::UpdateAnimation()
	{
		UpdateMorphAnimation();
		UpdateNodeAnimation(false);
	}

	void MMDModel::UpdatePhysics(float elapsed)
	{
		UpdatePhysicsAnimation(elapsed);
	}

	bool MMDModel::vaPastT(float ofs)
	{
		return gSceneTime - vas[0].start+ ofs + vas[0].offset> vmdAddAnims[0].vi.actT;
	}
	bool MMDModel::vaPastE(float ofs)
	{
		return gSceneTime - vas[0].start + ofs + vas[0].offset > vmdAddAnims[0].vi.actE;
	}

	int MMDModel::vaGetLessTime(float t)
	{
		if (t > 1.0f)
		{
			int maxI = 0;
			for (int i = 0; i < vmdAddAnims.size(); i++)
			{
				if (vmdAddAnims[i].vi.actT  > vmdAddAnims[maxI].vi.actT)
					maxI = i;
			}
			return maxI;
		}
		//else return rand() % vmdAddAnims.size();
		t += 0.2f;
		std::vector<int> arr;
		for (int i = 0; i < vmdAddAnims.size(); i++)
		{
			if (vmdAddAnims[i].vi.actT < t)
				arr.push_back(i);
		}
		if (arr.size() > 0)
		{
			std::random_device rd;
			std::mt19937 gen(rd()); 
			std::shuffle(arr.begin(), arr.end(), std::default_random_engine(gen));
			return arr[0];
		}
		return rand() % vmdAddAnims.size();
		
		int minI = 0;
		for (int i = 0; i < vmdAddAnims.size(); i++)
		{
			if (vmdAddAnims[i].vi.actT < vmdAddAnims[minI].vi.actT)
				minI = i;
		}
		return minI;
	}

	int MMDModel::vaGetLessTimeRand(float t)
	{
		 
		std::vector<int> arr;
		for (int i = 0; i < vmdAddAnims.size(); i++)
		{
			if (vmdAddAnims[i].vi.actT < t)
				arr.push_back(i);
		}
		if (arr.size() > 0)
		{
			std::random_device rd;
			std::mt19937 gen(rd());
			std::shuffle(arr.begin(), arr.end(), std::default_random_engine(gen));
			return arr[0];
		}
		return rand() % vmdAddAnims.size();

		int minI = 0;
		for (int i = 0; i < vmdAddAnims.size(); i++)
		{
			if (vmdAddAnims[i].vi.actT < vmdAddAnims[minI].vi.actT)
				minI = i;
		}
		return minI;
	}

#if 1 

	// can not read by mmd?
	std::string MMDModel::getPoseVPD()
	{
		auto mgr = GetNodeManager();
		char szj[256] = { 0 };
		std::string bones;
		int cc = 0;
		for (int i = 0; i < mgr->GetNodeCount(); i++) {
			saba::MMDNode* nd = mgr->GetMMDNode(i);
			if ((nd->flMove || nd->flRotate)) 
			{
				auto trs = nd->GetAnimationTranslate();
				auto rtt = nd->GetAnimationRotate();
				
				int len=ualib::utf8ToMbcs(szj, 256, nd->GetName().c_str(), 0);
				if (trs != glm::vec3(0, 0, 0) || rtt != glm::quat(1, 0, 0, 0))
				{
					char sz[512];
					snprintf(sz, 512, "Bone%d{%s\r\n%.6f,%.6f,%.6f;\r\n%.6f,%.6f,%.6f,%.6f;\r\n}\r\n\r\n",
						cc++, szj,  trs.x, trs.y, trs.z, rtt.x, rtt.y, rtt.z, rtt.w);
					bones += sz;
				}
			}
		}


		std::string morphs;
		int ccm = 0;
		auto mm = GetMorphManager();
		for (int i = 0; i < mm->GetMorphCount(); i++) {
			saba::MMDMorph* mp = mm->GetMorph(i);
 
			auto w = mp->GetWeight();
				if (w!=0.f)
				{
					int len = ualib::utf8ToMbcs(szj, 256, mp->GetName().c_str(), 0);
					char sz[512];
					snprintf(sz, 512, "Morph%d{%s\r\n%.6f;\r\n}\r\n\r\n",
						ccm++, szj, w);
					morphs += sz;
				}
			
		}

		char sz[512];
		snprintf(sz, 512, "Vocaloid Pose Data file\r\n\r\nfilename.pmx;\r\n%d;\r\n\r\n", cc);
		std::string vpd = sz;
		vpd += bones;
		vpd += morphs;
		return vpd;
	}
#endif

 

}


#if 0

#include <iostream>
#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtx/vector_angle.hpp>

int maixnxx() {
	glm::vec3 initialParentPosition(0.0f, 0.0f, 0.0f);
	glm::vec3 initialChildPosition(1.0f, 0.0f, 0.0f);
	glm::vec3 initialGrandchildPosition(2.0f, 0.0f, 0.0f);
	glm::vec3 newParentPosition(0.0f, 0.0f, 0.0f);
	glm::vec3 newChildPosition(0.0f, 1.0f, 0.0f);
	glm::vec3 newGrandchildPosition(0.0f, 2.0f, 0.0f);

	// Calculate the initial and new vectors
	glm::vec3 initialVector = initialChildPosition - initialParentPosition;
	glm::vec3 newVector = newChildPosition - newParentPosition;

	// Normalize the vectors
	initialVector = glm::normalize(initialVector);
	newVector = glm::normalize(newVector);

	// Calculate the rotation quaternion between the initial and new parent-child vectors
	glm::quat parentChildRotation = glm::rotation(initialVector, newVector);

	// Apply the parent-child rotation to the initial grandchild position
	glm::vec3 rotatedInitialGrandchildPosition = glm::rotate(parentChildRotation, initialGrandchildPosition - initialChildPosition);

	// Calculate the new grandchild position by adding the rotated position to the new child position
	glm::vec3 newGrandchildPosition = newChildPosition + rotatedInitialGrandchildPosition;

	// Calculate the new grandchild vector
	glm::vec3 newGrandchildVector = newGrandchildPosition - newChildPosition;

	// Normalize the new grandchild vector
	newGrandchildVector = glm::normalize(newGrandchildVector);

	// Calculate the rotation quaternion between the initial and new child-grandchild vectors
	glm::quat childGrandchildRotation = glm::rotation(initialVector, newGrandchildVector);

	// Print the rotation quaternions
	std::cout << "Parent-Child Rotation Quaternion: " << parentChildRotation.w << " + "
		<< parentChildRotation.x << "i + " << parentChildRotation.y << "j + "
		<< parentChildRotation.z << "k" << std::endl;

	std::cout << "Child-Grandchild Rotation Quaternion: " << childGrandchildRotation.w << " + "
		<< childGrandchildRotation.x << "i + " << childGrandchildRotation.y << "j + "
		<< childGrandchildRotation.z << "k" << std::endl;

	return 0;
}


//-----------------------------------------------
#include <iostream>
#include <vector>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>

struct Bone
{
	glm::vec3 oldPosition;
	glm::vec3 newPosition;
	glm::mat4 localRotation;
};

void calculateLocalRotations(std::vector<Bone>& boneLink)
{
	for (size_t i = 0; i < boneLink.size(); ++i)
	{
		Bone& bone = boneLink[i];

		glm::vec3 direction = bone.newPosition - bone.oldPosition;
		float length = glm::length(direction);

		if (length > 0.0f)
		{
			direction /= length;
			glm::vec3 defaultDirection(0.0f, 0.0f, 1.0f); // Assuming default direction of the bone

			glm::vec3 rotationAxis = glm::cross(defaultDirection, direction);
			float rotationAngle = glm::acos(glm::dot(defaultDirection, direction));

			bone.localRotation = glm::rotate(glm::mat4(1.0f), rotationAngle, rotationAxis);
		}
		else
		{
			bone.localRotation = glm::mat4(1.0f); // Identity matrix
		}
	}
}

int main()
{
	// Example bone link with 4 nodes
	std::vector<Bone> boneLink(4);

	boneLink[0].oldPosition = glm::vec3(0.0f, 0.0f, 0.0f);
	boneLink[0].newPosition = glm::vec3(1.0f, 0.0f, 0.0f);

	boneLink[1].oldPosition = glm::vec3(1.0f, 0.0f, 0.0f);
	boneLink[1].newPosition = glm::vec3(1.0f, 1.0f, 0.0f);

	boneLink[2].oldPosition = glm::vec3(1.0f, 1.0f, 0.0f);
	boneLink[2].newPosition = glm::vec3(2.0f, 1.0f, 0.0f);

	boneLink[3].oldPosition = glm::vec3(2.0f, 1.0f, 0.0f);
	boneLink[3].newPosition = glm::vec3(2.0f, 2.0f, 0.0f);

	// Calculate local rotations
	calculateLocalRotations(boneLink);

	// Print results
	for (size_t i = 0; i < boneLink.size(); ++i)
	{
		std::cout << "Bone " << i + 1 << " Local Rotation:" << std::endl;
		const float* rotationPtr = glm::value_ptr(boneLink[i].localRotation);
		for (int j = 0; j < 16; ++j)
		{
			std::cout << rotationPtr[j] << " ";
			if ((j + 1) % 4 == 0)
				std::cout << std::endl;
		}
		std::cout << std::endl;
	}

	return 0;
}


#endif