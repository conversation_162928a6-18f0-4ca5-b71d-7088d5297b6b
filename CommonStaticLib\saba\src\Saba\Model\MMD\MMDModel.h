﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//

#ifndef SABA_MODEL_MMD_MMDMODEL_H_
#define SABA_MODEL_MMD_MMDMODEL_H_
#define SIMPLE_MMD_MAX_GPU_NODE_COUNT 768 
#include "MMDNode.h"
#include "MMDIkSolver.h"
#include "MMDMorph.h"
#include "MMDPhysics.h"
#include <vector>
#include <map>
#include <string>
#include <algorithm>
#include <cstdint>
#include <memory>
#include <glm/vec2.hpp>
#include <glm/vec3.hpp>
#include <functional>
#include "pmxfile.h"
namespace irr::scene {
	class IrrSaba;
}
namespace saba
{
	
	struct MMDMaterial;
	class MMDPhysics;
	class MMDRigidBody;
	class MMDJoint;
	struct VPDFile;

	extern MMDPhysics* g_mmdPhysics;

	class MMDNodeManager
	{
	public:
		virtual ~MMDNodeManager() {};
		static const size_t NPos = -1;

		virtual size_t GetNodeCount() = 0;
		virtual size_t FindNodeIndex(const std::string& name) = 0;
		virtual size_t FindNodeIndex(const std::wstring& name) = 0;
		virtual MMDNode* FindNode(const std::wstring& name) = 0;
		virtual MMDNode* FindNode(const std::string& name) = 0;
		virtual MMDNode* GetMMDNode(size_t idx) = 0;

		MMDNode* GetMMDNode(const std::string& nodeName)
		{
			auto findIdx = FindNodeIndex(nodeName);
			if (findIdx == NPos)
			{
				return nullptr;
			}
			return GetMMDNode(findIdx);
		}

		MMDNode* getRootNode() {
			size_t i = 0;
			while (i < GetNodeCount())
			{
				if (GetMMDNode(i)->isRoot())
					return GetMMDNode(i);
				i++;
			}
			return nullptr;
		}
	};

	class MMDIKManager
	{
	public:
		static const size_t NPos = -1;

		virtual size_t GetIKSolverCount() = 0;
		virtual size_t FindIKSolverIndex(const std::string& name) = 0;
		virtual MMDIkSolver* GetMMDIKSolver(size_t idx) = 0;

		MMDIkSolver* GetMMDIKSolver(const std::string& ikName)
		{
			auto findIdx = FindIKSolverIndex(ikName);
			if (findIdx == NPos)
			{
				return nullptr;
			}
			return GetMMDIKSolver(findIdx);
		}
	};

	class MMDMorphManager
	{
	public:
		static const size_t NPos = -1;

		virtual size_t GetMorphCount() = 0;
		virtual size_t FindMorphIndex(const std::string& name) = 0;
		virtual MMDMorph* GetMorph(size_t idx) = 0;

		size_t previewIdx=NPos;
		size_t addWeightIdx = NPos;
		float addWeight = 0.f;
		MMDMorph* GetMorph(const std::string& name)
		{
			auto findIdx = FindMorphIndex(name);
			if (findIdx == NPos)
			{
				return nullptr;
			}
			return GetMorph(findIdx);
		}

		virtual void resetAll() {
			for (size_t i = 0; i < GetMorphCount(); i++) {
				auto mp = GetMorph(i);
				mp->SetWeight(0);
			}
		}
	};

	class MMDPhysicsManager
	{
	public:
		using RigidBodyPtr = std::unique_ptr<MMDRigidBody>;
		using JointPtr = std::unique_ptr<MMDJoint>;

		MMDPhysicsManager();
		~MMDPhysicsManager();

		bool Create(EPhysicsEngine phtype);

		MMDPhysics* GetMMDPhysics();

		MMDRigidBody* newRigidBody(bool alsoAddSorted=false);
		std::vector<MMDRigidBody*>* GetRigidBodys() { return &sortedRBs; }
		void RemoveRigidBody(MMDRigidBody* rb);
		MMDJoint* AddJoint();
		void RemoveJoint(MMDJoint* jt);
		std::vector<JointPtr>* GetJoints() { return &m_joints; }

		//ckadd
		//dyn rigidbody type
		void sortRbByNode();
		bool _getDynRbActive();
		void _setDynRbActive(bool active);
		 
		void syncDynRb();
		void updateAllRbMass();
		EPhysicsEngine getPhysicsType() { return mPhyType; }
		std::vector<RigidBodyPtr>	m_rigidBodys;
		std::vector<MMDRigidBody*>	sortedRBs;

		void addAllBodies();
	private:

		EPhysicsEngine mPhyType;
		//std::vector<RigidBodyPtr>	m_rigidBodys;
		std::vector<JointPtr>		m_joints;

	};

	struct MMDSubMesh
	{
		int	m_beginIndex;
		int	m_vertexCount; //draw face's vertex count = index count
		int	m_materialID;
	};
	struct VaInfo {
		std::wstring name, nodeName;
		float actB, actT, actE; //begin , time, end		
		int group;
	};
	class VMDAnimation;
	enum class NodeCbStage { NS1, NS9, NS10, NS18,NSSaveVmd, NS200,NS_NodeFw };

	class MMDModel
	{
	public:
		virtual ~MMDModel() {};
		virtual MMDNodeManager* GetNodeManager() = 0;
		virtual MMDIKManager* GetIKManager() = 0;
		virtual MMDMorphManager* GetMorphManager() = 0;
		virtual MMDPhysicsManager* GetPhysicsManager() = 0;

		virtual size_t GetVertexCount() const = 0;
		virtual const glm::vec3* GetPositions() const = 0;
		virtual const glm::vec3* GetNormals() const = 0;
		virtual const glm::vec2* GetUVs() const = 0;
		virtual const glm::vec3* GetUpdatePositions() const = 0;
		virtual const glm::vec3* GetUpdateNormals() const = 0;
		virtual const glm::vec2* GetUpdateUVs() const = 0;

		virtual size_t GetIndexElementSize() const = 0;
		virtual size_t GetIndexCount() const = 0;
		virtual const void* GetIndices() const = 0;

		virtual size_t GetMaterialCount() const = 0;
		virtual const MMDMaterial* GetMaterials() const = 0;

		virtual size_t GetSubMeshCount() const = 0;
		virtual const MMDSubMesh* GetSubMeshes() const = 0;

		virtual MMDPhysics* GetMMDPhysics() = 0;

		// ノードを初期化する
		virtual void InitializeAnimation() = 0;

		// ベースアニメーション(アニメーション読み込み時、Physics反映用)
		void SaveBaseAnimation();
		void LoadBaseAnimation();
		void ClearBaseAnimation();

		void saveNodesTransform(); //for temply calc node transform
		void loadNodesTransform();

		// アニメーションの前後で呼ぶ (VMDアニメーションの前後)
		virtual void BeginAnimation() = 0;
		virtual void EndAnimation() = 0;
		// Morph
		virtual void UpdateMorphAnimation() = 0;
		// ノードを更新する
		[[deprecated("Please use UpdateAllAnimation() function")]]
		void UpdateAnimation();
		virtual int UpdateNodeAnimation(bool afterPhysicsAnim, bool realMove = true) = 0;
		// Physicsを更新する
		virtual void ResetPhysics(bool dynActive= false) = 0;
		[[deprecated("Please use UpdateAllAnimation() function")]]
		void UpdatePhysics(float elapsed);
		virtual void UpdatePhysicsAnimation(float elapsed) = 0;
		virtual void UpdatePhysicsAnimation2Pass(float elapsed, uint32_t flag) = 0;
		// 頂点を更新する
		virtual void Update(bool onlyTrans) = 0;
		virtual void SetParallelUpdateHint(uint32_t parallelCount) = 0;

		void UpdateAllAnimation(VMDAnimation* vmdAnim, float vmdFrame, float physicsElapsed);
		void LoadPose(const VPDFile& vpd, int frameCount = 30, bool immediate=true, int slot=-1);
		bool setPoseId(int slot);		
		struct Pose
		{
			MMDNode* m_node;
			glm::vec3	m_beginTranslate;
			glm::vec3	m_endTranslate;
			glm::quat	m_beginRotate;
			glm::quat	m_endRotate;
		};
		std::vector<Pose> curPose, poseSlot[12];
		int curPoseSlot=-1;

		//ckadd
		bool getDynRbActive() {
			return GetPhysicsManager()->_getDynRbActive();
		}
		void setDynRbActive(bool active) {
			allPhyAct = active;
			GetPhysicsManager()->_setDynRbActive(active);
		}
		std::string modelName;
		typedef std::function<void(NodeCbStage reason, MMDNode &node)> NodeCallback;
		NodeCallback cbNode;
		typedef std::function<void(int reason, MMDMorph& morph)> MorphCallback;
		MorphCallback cbMorph;
		std::function<void(NodeCbStage reason)> cbStage;
		virtual void DoCallback(NodeCbStage reason) = 0;

		bool isCharacter = false, hasInflate = false;
		float animSpdMul = 1.f;

		void* gpuVtxPtr{};
		bool vtxPosUpdated = false;
		size_t gpuVtxStride=0,gpuNormalOfs=0, gpuUVOfs=0;
		bool saveVMD=false;
		bool needMorph = true;
		bool needPhysics = true;
		bool enableCCD = false;
		int syncingPhysics = 0; // SABA_PHYSICS_ASYNC NOT NEED
		int sypId=0,sypCount = 60;
		void syncPhyToFrame(float frame,int count=60, int mode=1);

		float controlPanelAccum[16];

		struct VaStruct {
			std::shared_ptr<VMDAnimation> vmd;
			bool enable = true;
			int maxFrame = 0;
			float frame=0,weight=0,startTime;
			bool faceCam = false; glm::quat faceCamRtTgt,faceCamRtSrc;
			MMDNode* ndAct{};
			VaInfo vi;
			//state
			bool afterT = true;
			bool afterE = true;
		};

		VaStruct vmdBase[2];

		bool addAnimOn = false;
		std::vector<uint32_t> vaIds[8];
		std::vector<VaStruct> vmdAddAnims;
		int vmdAddAnimId[2] = { -1,-1 };
		struct VAState {
			float start = -999;
			float offset = 0;
		}vas[2];
		
		bool vaPastT(float ofs=0);
		bool vaPastE(float ofs=0);
		int vmdAddAnimCount = 0;
		int vmdAction = 0;
		VaStruct *curVA = nullptr;
		bool phyActAnimating = 0;
		int vaGetLessTime(float t);
		int vaGetLessTimeRand(float t);

		std::map<std::wstring, std::vector<PMXDispalyFrame::Target>> mapDIsp;
		float camDirRate = 0.f;
		virtual irr::io::IFileArchive* getFileArchive() { return nullptr; }
		std::string getPoseVPD();
		bool scaled = false;
		glm::mat4 mmdRootMat = glm::mat4(1), mmdRootMatInv = glm::mat4(1);
		glm::mat4 rootM0 = glm::mat4(1), rootM0i = glm::mat4(1), rootM1 = glm::mat4(1);
		//glm::vec3 nodeRootOfs{0, 0, 0};
		glm::vec3 rootTr{0, 0, 0}, rootSc{ 1,1,1 };
		glm::quat rootRt=glm::quat( 1, 0, 0, 0 );
		glm::vec3 rt1Tr{0, 0, 0}, rt1Rt{ 0,0,0 }, rt1Sc{ 1,1,1 };
		bool allPhyAct = false;
		float iksMulArm = 1.f;
		bool rootHasParent = false;
		irr::scene::IrrSaba* saba{};
		float animeTime;
		bool phy2ndPass = false;
		void saveGlobalTransform();
		void loadGlobalTransform();

		float runTimeMassMulOnScale = 1.f;
		//tmp
		bool afterPhysics = false;

	protected:

		template <typename NodeType>
		class MMDNodeManagerT : public MMDNodeManager
		{
		public:
			virtual ~MMDNodeManagerT() {};
			using NodePtr = std::unique_ptr<NodeType>;

			size_t GetNodeCount() override { return m_nodes.size(); }

			size_t FindNodeIndex(const std::string& name) override
			{
				auto findIt = std::find_if(
					m_nodes.begin(),
					m_nodes.end(),
					[&name](const NodePtr& node) { return node->GetName() == name; }
				);
				if (findIt == m_nodes.end())
				{
					return NPos;
				}
				else
				{
					return findIt - m_nodes.begin();
				}
			}
			size_t FindNodeIndex(const std::wstring& name) override
			{
				auto findIt = std::find_if(
					m_nodes.begin(),
					m_nodes.end(),
					[&name](const NodePtr& node) { return node->GetNameU() == name; }
				);
				if (findIt == m_nodes.end())
				{
					return NPos;
				}
				else
				{
					return findIt - m_nodes.begin();
				}
			}
			MMDNode* FindNode(const std::string& name) override
			{
				auto findIt = std::find_if(
					m_nodes.begin(),
					m_nodes.end(),
					[&name](const NodePtr& node) { return node->GetName() == name; }
				);
				if (findIt == m_nodes.end())
				{
					return nullptr;
				}
				else
				{
					return m_nodes[findIt - m_nodes.begin()].get();
				}
			}
			MMDNode* FindNode(const std::wstring& name) override
			{
				auto findIt = std::find_if(
					m_nodes.begin(),
					m_nodes.end(),
					[&name](const NodePtr& node) { return node->GetNameU() == name; }
				);
				if (findIt == m_nodes.end())
				{
					return nullptr;
				}
				else
				{
					return m_nodes[findIt - m_nodes.begin()].get();
				}
			}

			MMDNode* GetMMDNode(size_t idx) override
			{
				//if (idx >= m_nodes.size()) return nullptr;
				assert(idx < m_nodes.size());
				return m_nodes[idx].get();
			}

			NodeType* AddNode()
			{
				auto node = std::make_unique<NodeType>();
				node->SetIndex((uint32_t)m_nodes.size());
				m_nodes.emplace_back(std::move(node));
				return m_nodes[m_nodes.size() - 1].get();
			}

			NodeType* GetNode(size_t i)
			{
				return m_nodes[i].get();
			}

			std::vector<NodePtr>* GetNodes()
			{
				return &m_nodes;
			}

		private:
			std::vector<NodePtr>	m_nodes;
		};

		template <typename IKSolverType>
		class MMDIKManagerT : public MMDIKManager
		{
		public:
			using IKSolverPtr = std::unique_ptr<IKSolverType>;

			size_t GetIKSolverCount() override { return m_ikSolvers.size(); }

			size_t FindIKSolverIndex(const std::string& name) override
			{
				auto findIt = std::find_if(
					m_ikSolvers.begin(),
					m_ikSolvers.end(),
					[&name](const IKSolverPtr& ikSolver) { return ikSolver->GetName() == name; }
				);
				if (findIt == m_ikSolvers.end())
				{
					return NPos;
				}
				else
				{
					return findIt - m_ikSolvers.begin();
				}
			}

			MMDIkSolver* GetMMDIKSolver(size_t idx) override
			{
				return m_ikSolvers[idx].get();
			}

			IKSolverType* AddIKSolver()
			{
				m_ikSolvers.emplace_back(std::make_unique<IKSolverType>());
				return m_ikSolvers[m_ikSolvers.size() - 1].get();
			}



			std::vector<IKSolverPtr>* GetIKSolvers()
			{
				return &m_ikSolvers;
			}

			MMDIkSolver* solverTmp{};
		private:
			std::vector<IKSolverPtr>	m_ikSolvers;
		};

		template <typename MorphType>
		class MMDMorphManagerT : public MMDMorphManager
		{
		public:
			using MorphPtr = std::unique_ptr<MorphType>;

			size_t GetMorphCount() override { return m_morphs.size(); }

			size_t FindMorphIndex(const std::string& name) override
			{
				auto findIt = std::find_if(
					m_morphs.begin(),
					m_morphs.end(),
					[&name](const MorphPtr& morph) { return morph->GetName() == name; }
				);
				if (findIt == m_morphs.end())
				{
					return NPos;
				}
				else
				{
					return findIt - m_morphs.begin();
				}
			}

			MMDMorph* GetMorph(size_t idx) override
			{
				return m_morphs[idx].get();
			}

			MorphType* AddMorph()
			{
				m_morphs.emplace_back(std::make_unique<MorphType>());
				return m_morphs[m_morphs.size() - 1].get();
			}

			std::vector<MorphPtr>* GetMorphs()
			{
				return &m_morphs;
			}
 
		private:
			std::vector<MorphPtr>	m_morphs;
		};
	};
	MMDPhysics* createPhysicsInstance(EPhysicsEngine phtype);
}

#endif // !SABA_MODEL_MMD_MMDMODEL_H_
