﻿#include "AppGlobal.h"
#include "ShaderToy.h"
#include <cppIncDefine.h>
#include <VulkanRenderer/VkDriver.h>
#include <VulkanRenderer/VkMr2D.h>
#include "stlUtils.h"
#include "irrfw/eqv/EQV.h"

using namespace irr;
using namespace irr::scene;
using namespace ualib;
using namespace irr::video;


ualib::ShaderToyMan::ShaderToyMan(ShaderToyManParam pm)
	:Pm(pm)
{
	Ctx = Pm.ctx;
	Eqv = Pm.eqv;
	Driver = Pm.ctx->getDriver();
	SceneManager = Pm.ctx->getSceneManager();
	stc.fv1.z = 1.f;
	stc.fv1.y = 1.f;

}

ualib::ShaderToyMan::~ShaderToyMan()
{
	Driver->removeTexture(texBand);
}

void ualib::ShaderToyMan::drawWithBufA( irr::video::ITexture* tex0, bool drawAll)
{

	if ( tex0) {
		if (!texBufA) {
			texBufA = Driver->addRenderTargetTexture(Driver->getScreenSize(), "bufa", ECF_A32B32G32R32F);
		}
		auto ort = Driver->getRenderTarget();
		if (drawAll) Driver->setRenderTarget(texBufA, true, true, 0);
		drawTex(Fx2DIdEnum::Fx2D_ShaderToyBufA, tex0);

		if (drawAll) Driver->setRenderTarget(ort, false, false);
		//Driver->saveTexture(texBufA, "r:/bufA.png");
	}

	if (drawAll) drawTex(Fx2DIdEnum::Fx2D_ShaderToy, texBufA);
	//ualib::SleepMs(22);
}

void ualib::ShaderToyMan::drawImage(irr::video::ITexture* tex0)
{
	drawTex(Fx2DIdEnum::Fx2D_ShaderToy,  tex0);
}

void ualib::ShaderToyMan::updateBandDat()
{
	if (!texBand)	texBand = Driver->addTexture(core::dimension2du(32, 1), "banddat", ECF_A32B32G32R32F);
	void *pb=texBand->lock(ETLM_WRITE_ONLY);
	memcpy(pb, fTexDat, sizeof(fTexDat));
	texBand->unlock();
}

void ualib::ShaderToyMan::drawTex(irr::video::Fx2DIdEnum fx, irr::video::ITexture* tex0)
{
	if (stc.iv.x == 0) stc.iv.x = 2;
#if DRAW_SHADER_TOY
#define USE_MMD_POS 0 //HAS_MMD

	auto Driver = Ctx->getDriver();

	video::SMaterial mr;
	mr.MaterialType = DRV_GET_MT(Driver, fx);
 
	float camSc = 1.0f/250;
	auto cam = SceneManager->getActiveCamera();
	auto camPos = cam->getAbsolutePosition()* camSc;
	auto tgtPos = cam->getTarget() * camSc;
	//auto camUp= cam->getUpVectorByNode();
	{
		mr.setTexture(0, tex0);
		static int cc = 0;
		video::VkMr2D* mr2d = (video::VkMr2D*)Driver->getMaterialRenderer(mr.MaterialType);
		auto ss = Driver->getScreenSize();
		auto& scb = mr2d->cbDraw.shadertoy;
		scb = stc;
		scb.iResolution = float3(ss.Width, ss.Height, 1);
		scb.iTime = Ctx->gd.time;
		scb.iTimeDelta = Ctx->gd.deltaTime;
#if USE_MMD_POS
		auto cam = SceneManager->getActiveCamera(); cam->updateMatrices();
		auto mv = cam->getViewMatrix(), mp = cam->getProjectionMatrix();
		auto mvp = mp * mv;
		core::vector3df scrCtr, scrPosCtr = mmd->sabas[0]->centerPos;
		mvp.transformVect(scrCtr);
		mvp.transformVect(scrPosCtr);
		scrPosCtr /= scrCtr.Z;
		DP(("CTR %f,%f  disZ=%f  pt %d,%d ", scrPosCtr.X, scrPosCtr.Y, scrCtr.Z, Ctx->gd.pointerPos.X, Ctx->gd.pointerPos.Y));
#endif

		scb.iMouse = float4(
#if  USE_MMD_POS
			ss.Width * (0.5f + 0.5f * scrPosCtr.X), ss.Height * (0.5f - 0.5f * scrPosCtr.Y),
#else
			Ctx->gd.pointerPos.X, Ctx->gd.pointerPos.Y,//
#endif
													   //core::clamp( scrPosCtr.X, -(float)ss.Width,(float)ss.Width),
													   //Ctx->gd.pointerPos.Y, 
			Ctx->gd.pointerClick.X, Ctx->gd.pointerClick.Y);
		scb.iFrame = cc;
		scb.iDate = {};
		scb.fv.x = Eqv->CurWavePeak; //AvrWavePeak;
									 //scb.fv1.z = USE_MMD_POS?SceneManager->getActiveCamera()->getAbsolutePosition().getDistanceFrom(SceneManager->getActiveCamera()->getTarget()) / Driver->dsd.stdDistance: 0; //AvrWavePeak;
		scb.fv1.w = core::clamp(camPos.getDistanceFrom(tgtPos) / Driver->dsd.stdDistance, 0.1f, 8.f);
		scb.camPos = camPos*float3(-1,1,1); 
		scb.fovDZ = 0.5/tan(cam->getFOV()/2); //    // ray direction -0.5<y<0.5		vec3 rd = ca * normalize(vec3(uv.xy, fov));
		scb.tgtPos = tgtPos * float3(-1, 1, 1); scb.tgtDis = (camPos - tgtPos).getLength();
		//scb.camUp = camUp * float3(-1, 1, 1); 
		scb.camRoll = cam->getRotation().Z*core::DEGTORAD;
		scb.is_debugdraw = scb.is_pause = false;
		scb.main_image_srgb = 1;
		if (texBand) mr.setTexture(1, texBand);
		Driver->draw2DImageMr(mr, tex0->getRectI(), tex0->getRectI());

	}

#endif
}
