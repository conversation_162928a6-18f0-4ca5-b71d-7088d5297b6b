﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="CeFw">
      <UniqueIdentifier>{f0a3deb1-d055-44bf-8b9f-c4529c9412b5}</UniqueIdentifier>
    </Filter>
    <Filter Include="CeFw\dsp">
      <UniqueIdentifier>{b9c9a245-869b-4914-a9ad-5b294a8f5b7a}</UniqueIdentifier>
    </Filter>
    <Filter Include="App">
      <UniqueIdentifier>{743e9c2f-60dd-4cfe-b27c-29b66912bdfc}</UniqueIdentifier>
    </Filter>
    <Filter Include="CeFw\CS">
      <UniqueIdentifier>{05e04bba-ceeb-4505-8da5-6cc1624cf1e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="App\misc">
      <UniqueIdentifier>{fa912792-f797-43ee-b3b1-345ecedd1944}</UniqueIdentifier>
    </Filter>
    <Filter Include="App\FF">
      <UniqueIdentifier>{08c085b2-f0de-41c2-b43e-a1d3c787c104}</UniqueIdentifier>
    </Filter>
    <Filter Include="App\header">
      <UniqueIdentifier>{41eb5f71-2121-4d37-9393-a3f8c1e92a14}</UniqueIdentifier>
    </Filter>
    <Filter Include="App\mmd">
      <UniqueIdentifier>{efaddaef-22cb-49c3-b8c2-a7c3419a10f8}</UniqueIdentifier>
    </Filter>
    <Filter Include="CeFw\fw">
      <UniqueIdentifier>{13b49860-5b45-4a21-982a-850b0d1754c4}</UniqueIdentifier>
    </Filter>
    <Filter Include="App\EQV">
      <UniqueIdentifier>{03b5a1b2-dfa0-498e-8a39-0854c1778aa5}</UniqueIdentifier>
    </Filter>
    <Filter Include="App\EQV\wave">
      <UniqueIdentifier>{76ac210f-e149-43c6-b300-de3ae38c12ac}</UniqueIdentifier>
    </Filter>
    <Filter Include="App\EQV\OldNode">
      <UniqueIdentifier>{43d975ee-2d24-4d96-80af-c6806b9ba19a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{49c250a5-5ae0-42da-b093-d643405c934b}</UniqueIdentifier>
    </Filter>
    <Filter Include="App\mmd\h">
      <UniqueIdentifier>{b6b808a6-8255-4966-8fa7-c119ee49ff05}</UniqueIdentifier>
    </Filter>
    <Filter Include="App\mmd\sv">
      <UniqueIdentifier>{1cbced84-7b7d-405e-8cf6-df8fcacf0fa8}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\targetver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UaJsonSetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UaLibMain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UaUtils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UaLibStage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UaCommon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UaLib.h" />
    <ClInclude Include="src\dsp\EQMan.h">
      <Filter>CeFw\dsp</Filter>
    </ClInclude>
    <ClInclude Include="src\dsp\FFT.hpp">
      <Filter>CeFw\dsp</Filter>
    </ClInclude>
    <ClInclude Include="src\AppGlobal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\dsp\EqTypes.h">
      <Filter>CeFw\dsp</Filter>
    </ClInclude>
    <ClInclude Include="src\UaLibContext.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvLoader.h">
      <Filter>App\EQV</Filter>
    </ClInclude>
    <ClInclude Include="src\UaLibEvtRcv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ScopeGuard.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvTouchActionManager.h">
      <Filter>App\EQV</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvItemWaveStrip.h">
      <Filter>App\EQV\wave</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvItemWave.h">
      <Filter>App\EQV\wave</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvItemWaveFw.h">
      <Filter>App\EQV\wave</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvItemWaveLine.h">
      <Filter>App\EQV\wave</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvItemWaveMesh.h">
      <Filter>App\EQV\wave</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvNodeWave.h">
      <Filter>App\EQV\wave</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\SnCsParticle.h">
      <Filter>CeFw\CS</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\MrCsParticle.h">
      <Filter>CeFw\CS</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvFwNode.h">
      <Filter>App\EQV</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvFwNodeHelper.h">
      <Filter>App\EQV</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvHelpers.h">
      <Filter>App\EQV</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvItemBar2DSn.h">
      <Filter>App\EQV\OldNode</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvItemBar3D.h">
      <Filter>App\EQV\OldNode</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvItemBar3DObj.h">
      <Filter>App\EQV\OldNode</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvItemSn.h">
      <Filter>App\EQV\OldNode</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvNodeBand.h">
      <Filter>App\EQV\OldNode</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EqvNode.h">
      <Filter>App\EQV\OldNode</Filter>
    </ClInclude>
    <ClInclude Include="src\ulMedia\yuv2rgb.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\ulMedia\rgb2yuv.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\ulMedia\MediaProcessorAndroid.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\FFHelper\UaFfmpegFile.h">
      <Filter>App\FF</Filter>
    </ClInclude>
    <ClInclude Include="src\FFHelper\UaFfmpeg.h">
      <Filter>App\FF</Filter>
    </ClInclude>
    <ClInclude Include="src\FFHelper\AssHelper.h">
      <Filter>App\FF</Filter>
    </ClInclude>
    <ClInclude Include="src\cppIncDefine.h" />
    <ClInclude Include="app\MusicFirework\AppBase.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="app\MusicFirework\AppMain.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="app\MusicFirework\AppMainTextFw.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\DataRecorderBase.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="app\MusicFirework\DbgHelpers.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\MatrixRecorder.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="app\MusicFirework\UndoList.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\VideoFrameProcessor.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\FwClock.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\FT\FMAndroid.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\FT\UaFontMetric.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\ThreadPool\ThreadPool.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\FT\FT2Man.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NetMan.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\UaIrrlicht\source\Irrlicht\VulkanRenderer\shader\GsParticleShared.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\PhysicsMan.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\eqv\EQV.h">
      <Filter>App\EQV</Filter>
    </ClInclude>
    <ClInclude Include="src\ShaderToy.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\AppTypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="app\ArMmPLayer\SnArItem.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="app\ArMmPLayer\SnArRoot.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\FwCommon.h">
      <Filter>CeFw\fw</Filter>
    </ClInclude>
    <ClInclude Include="src\FwManager.h">
      <Filter>CeFw\fw</Filter>
    </ClInclude>
    <ClInclude Include="src\FwShaderEmu.h">
      <Filter>CeFw\fw</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\SnGuQin.h">
      <Filter>CeFw\fw</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\SnLevelWheel.h">
      <Filter>CeFw\fw</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\SnPiano.h">
      <Filter>CeFw\fw</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\SnWater.h">
      <Filter>CeFw\fw</Filter>
    </ClInclude>
    <ClInclude Include="src\IrrFw\SvgMan.h">
      <Filter>CeFw\fw</Filter>
    </ClInclude>
    <ClInclude Include="app\ArMmPLayer\AppMainAMP.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\IAppBase.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\ffi_shared_header.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="app\ArMmPLayer\FL_ArEditor.h">
      <Filter>App</Filter>
    </ClInclude>
    <ClInclude Include="src\FlutterDartFFI.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\VideoHelpers.h">
      <Filter>App</Filter>
    </ClInclude>
    <ClInclude Include="src\ImgVideoEncoder.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\CLabelSceneNode.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\IrrMMD.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\irrSaba.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\MmdNodeHandler.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\CLineGridSceneNode.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\PythonMan.h">
      <Filter>App</Filter>
    </ClInclude>
    <ClInclude Include="src\LeapMan.h">
      <Filter>App</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\MmdMidiPlayer.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\SnPhyCloth.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\SnPhyInflatable.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\SnPhyMesh.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\PhyObjMan.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\CharacterAttacker.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\SnTestAI.h">
      <Filter>App\mmd</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\CCubeGridSceneNode.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\sabaCloth.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\SbFwLauncher.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\CInstancedMeshSceneNode.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\VmdEventExt.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\PhysicsHelper.h">
      <Filter>App\mmd</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\CMidiPlateSceneNode.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\CharacterCatcher.h">
      <Filter>App\header</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\MmdPhyAnimator.h">
      <Filter>App\mmd</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\MmdNodePhyAnimator.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\ImGuiMmdHelper.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\MmdPhysicsHelper.h">
      <Filter>App\mmd</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\CVoxelMeshSceneNode.h">
      <Filter>App\mmd\h</Filter>
    </ClInclude>
    <ClInclude Include="..\CommonStaticLib\winUtils.h">
      <Filter>App\misc</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\sv\KawaiiLyricGenerator.h">
      <Filter>App\mmd\sv</Filter>
    </ClInclude>
    <ClInclude Include="src\irrmmd\SnPhyFluid.h">
      <Filter>App\mmd</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\dsp\EQMan.cpp">
      <Filter>CeFw\dsp</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvFw.cpp">
      <Filter>App\EQV</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvLoader.cpp">
      <Filter>App\EQV</Filter>
    </ClCompile>
    <ClCompile Include="app\MusicFirework\AppBase.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvTouchActionManager.cpp">
      <Filter>App\EQV</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemWave.cpp">
      <Filter>App\EQV\wave</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemWaveFw.cpp">
      <Filter>App\EQV\wave</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemWaveLine.cpp">
      <Filter>App\EQV\wave</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemWaveMesh.cpp">
      <Filter>App\EQV\wave</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemWaveStrip.cpp">
      <Filter>App\EQV\wave</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvNodeWave.cpp">
      <Filter>App\EQV\wave</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\SnCsParticle.cpp">
      <Filter>CeFw\CS</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\MrCsParticle.cpp">
      <Filter>CeFw\CS</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvFwNode.cpp">
      <Filter>App\EQV</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvHelpers.cpp">
      <Filter>App\EQV</Filter>
    </ClCompile>
    <ClCompile Include="app\MusicFirework\AppMainTextFw.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemBar.cpp">
      <Filter>App\EQV\OldNode</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemBar2DSn.cpp">
      <Filter>App\EQV\OldNode</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemBar3D.cpp">
      <Filter>App\EQV\OldNode</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemBar3DObj.cpp">
      <Filter>App\EQV\OldNode</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvItemSn.cpp">
      <Filter>App\EQV\OldNode</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvNodeBand.cpp">
      <Filter>App\EQV\OldNode</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EqvNode.cpp">
      <Filter>App\EQV\OldNode</Filter>
    </ClCompile>
    <ClCompile Include="src\ulMedia\rgb2yuv.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="src\ulMedia\yuv2rgb.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="src\ulMedia\MediaProcessorAndroid.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="src\FFHelper\UaFfmpeg.cpp">
      <Filter>App\FF</Filter>
    </ClCompile>
    <ClCompile Include="src\FFHelper\UaFfmpegFile.cpp">
      <Filter>App\FF</Filter>
    </ClCompile>
    <ClCompile Include="src\FFHelper\AssHelper.cpp">
      <Filter>App\FF</Filter>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass_strtod.c">
      <Filter>App\FF</Filter>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass_utils.c">
      <Filter>App\FF</Filter>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass.c">
      <Filter>App\FF</Filter>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass_library.c">
      <Filter>App\FF</Filter>
    </ClCompile>
    <ClCompile Include="src\FFHelper\libass\ass_parse.c">
      <Filter>App\FF</Filter>
    </ClCompile>
    <ClCompile Include="src\FwClock.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="src\FT\FMAndroid.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="app\ArMmPLayer\AppMainAMP.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\IrrMMD.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\irrSaba.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\MmdNodeHandler.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="app\ArMmPLayer\AppMainAMP_P2.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="src\FwCommon.cpp">
      <Filter>CeFw\fw</Filter>
    </ClCompile>
    <ClCompile Include="src\FwManager.cpp">
      <Filter>CeFw\fw</Filter>
    </ClCompile>
    <ClCompile Include="src\FwShaderEmu.cpp">
      <Filter>CeFw\fw</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\SnGuQin.cpp">
      <Filter>CeFw\fw</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\SnLevelWheel.cpp">
      <Filter>CeFw\fw</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\SnPiano.cpp">
      <Filter>CeFw\fw</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\SnWater.cpp">
      <Filter>CeFw\fw</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\SvgMan.cpp">
      <Filter>CeFw\fw</Filter>
    </ClCompile>
    <ClCompile Include="src\IrrFw\eqv\EQV.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="src\VideoFrameProcessor.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="app\MusicFirework\AppMainTextFwP2.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="src\ShaderToy.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="app\ArMmPLayer\SnArItem.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="app\ArMmPLayer\SnArRoot.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="src\MatrixRecorder.cpp">
      <Filter>App\EQV</Filter>
    </ClCompile>
    <ClCompile Include="src\FlutterDartFFI.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="src\FT\FT2Man.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AppGlobal.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetMan.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UaJsonSetting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UaLibContext.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UaLibEvtRcv.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UaLibMain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UaLibStage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UaUtils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\VideoHelpers.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="src\ImgVideoEncoder.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\CLabelSceneNode.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\CLineGridSceneNode.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\PythonMan.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="src\LeapMan.cpp">
      <Filter>App</Filter>
    </ClCompile>
    <ClCompile Include="..\..\SDK\LeapSDK\samples\ExampleConnection.c">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\MmdMidiPlayer.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\SnPhyCloth.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\SnPhyInflatable.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\sabaCloth.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\SnPhyMesh.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\PhyObjMan.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\CharacterAttacker.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\SbFwLauncher.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\irrSabaPhysics.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\SnTestAI.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\CCubeGridSceneNode.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\CInstancedMeshSceneNode.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\VmdEventExt.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\PhysicsHelper.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\irrSabaAnimation.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\CMidiPlateSceneNode.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\CharacterCatcher.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\MmdPhyAnimator.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\MmdNodePhyAnimator.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\ImGuiMmdHelper.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\MmdPhyAnimator_part2.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\MmdPhysicsHelper.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\CVoxelMeshSceneNode.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
    <ClCompile Include="..\CommonStaticLib\winUtils.cpp">
      <Filter>App\misc</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\sv\KawaiiLyricGenerator.cpp">
      <Filter>App\mmd\sv</Filter>
    </ClCompile>
    <ClCompile Include="src\irrmmd\irrSabaWalk.cpp" />
    <ClCompile Include="src\irrmmd\SnPhyFluid.cpp">
      <Filter>App\mmd</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="cpp.hint">
      <Filter>Header Files</Filter>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="$(MSBuildThisFileDirectory)..\..\natvis\wil.natvis" />
  </ItemGroup>
</Project>