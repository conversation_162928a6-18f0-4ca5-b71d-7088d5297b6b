﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//
#include "mmdPCH.h"
#include "MMDPhysicsPhysX.h"
#if USE_PHYSX
#include "PxPhysicsAPI.h"
#include "MMDNode.h"
#include "MMDModel.h"
#include "PMXModel.h"
#include "../snippets/snippetutils/SnippetUtils.h"
#include "foundation/PxSimpleTypes.h"
#include "jsoncpp/json5cpp.h"
#if PHY_GPU
#endif
//#include "extensions/PxRemeshingExt.h"
//#include "cooking/PxCooking.h"
//#include "omnipvd/PxOmniPvd.h"
//#include "../pvdruntime/include/OmniPvdWriter.h"
//#include "../pvdruntime/include/OmniPvdFileWriteStream.h"

#define PHY_ST_TGS		0
#define USE_CCD			0
#define SABA_USE_DENSITY		0
#define WALL_RADIUS 1111125
#define WALL_RADIUSZ 1111125
#define MAX_SPEED	1000.f		//10 * pxScale.length
#define FORCE_FRICTION	0
#define FRICTION_MUL			1	//def:1
#define USE_DOFSPRING_2  1
#define HAS_GROUND					1
#define DBG_VISUAL_ONLY_SET_ONES	0
#define DBG_VISUAL_ONLY_DYNRB1		0
#define PICK_DAMPING		0
#define SCALE_MASS_WHEN_SCALING		1
#define PX_USE_ASYNC_SUB_STEP	SABA_PHYSICS_ASYNC

#define PHYSX_SIM_STEP  SABA_PHYSICS_FRAMESTEP


#define PHYSX_USE_FILTERW3			0
using namespace saba;
using namespace physx;
bool gPhyRttSetAV = 0;
bool gPhyUseAddForce = 1;
float gPhyRttMul = 1.f;
PhysXMan* gPxMan = nullptr;
#if SABA_USE_PHYSX

#if PHYSX_SABA_INFLATE
#include "extensions/PxParticleExt.h"
#include "cudamanager/PxCudaContext.h"
#include "cudamanager/PxCudaContextManager.h"
#include "extensions/PxRemeshingExt.h"
#include "extensions/PxParticleExt.h"
#include "extensions/PxParticleClothCooker.h"

#endif

#if USE_DIRECT_GPU
#define SET_LIN_VEL(x) \
    if (m_directGPUEnabled && m_directGPUInitialized) { \
        PxVec3 vl(x); \
        PxU32 gpuIndex = mRb->getGPUIndex(); \
        if (gpuIndex != PxU32(-1)) { \
            gPxScene->getDirectGPUAPI().setRigidDynamicData( \
                &vl, &gpuIndex, PxRigidDynamicGPUAPIWriteType::eLINEAR_VELOCITY, 1 ); \
        } \
    } else mRb->setLinearVelocity(x);

#define SET_ANG_VEL(x) \
    if (m_directGPUEnabled && m_directGPUInitialized) { \
        PxVec3 vl(x); \
        PxU32 gpuIndex = mRb->getGPUIndex(); \
        if (gpuIndex != PxU32(-1)) { \
            gPxScene->getDirectGPUAPI().setRigidDynamicData( \
                &vl, &gpuIndex, PxRigidDynamicGPUAPIWriteType::eANGULAR_VELOCITY, 1 ); \
        } \
    } else mRb->setAngularVelocity(x);
#else
#define SET_LIN_VEL(x) mRb->setLinearVelocity(x)
#define SET_ANG_VEL(x) mRb->setAngularVelocity(x)
#endif


#include "physxinc.inl"
bool m_directGPUEnabled = USE_DIRECT_GPU;
bool m_directGPUInitialized{ false };
void newGeomVol(const saba::PMXRigidbody& pm, const glm::vec3 mul, physx::PxGeometry*& gm, float& volume)
{
	auto ssize = pm.m_shapeSize * mul;
	if (ssize.x <= 0.01) ssize.x = 0.01;
	if (ssize.y <= 0.01) ssize.y = 0.01;
	if (ssize.z <= 0.01) ssize.z = 0.01;
	switch (pm.m_shape)
	{
	case PMXRigidbody::Shape::Sphere:
		gm = new PxSphereGeometry(ssize.x);
		volume = (4.0f / 3.0f) * PxPi * pow(ssize.x, 3);
		break;
	case PMXRigidbody::Shape::Box:
		gm = new PxBoxGeometry(ssize.x, ssize.y, ssize.z);
		volume = ssize.x * ssize.y * ssize.z * 8;
		break;
	case PMXRigidbody::Shape::Capsule:
		gm = new  PxCapsuleGeometry(
			ssize.x,
			ssize.y / 2
		);
		volume = PxPi * pow(ssize.x, 2) * ((ssize.y) + (4.0f / 3.0f) * ssize.x);
		break;
	case PMXRigidbody::Shape::Cylinder:
	{// Create a custom cylinder geometry using PxCustomGeometryExt
		PxCustomGeometryExt::CylinderCallbacks* cylinder = new PxCustomGeometryExt::CylinderCallbacks(ssize.y, ssize.x, 0, 0.01f);
		gm = new PxCustomGeometry(*cylinder);
		// Volume of a cylinder: π * r² * h
		volume = PxPi * pow(ssize.x, 2) * ssize.y;
	}	break;
	case PMXRigidbody::Shape::Cone:
	{// Create a custom cone geometry using PxCustomGeometryExt
		PxCustomGeometryExt::ConeCallbacks* cone = new PxCustomGeometryExt::ConeCallbacks(ssize.y, ssize.x, 0, 0.01f);
		gm = new PxCustomGeometry(*cone);
		// Volume of a cone: (1/3) * π * r² * h
		volume = (1.0f / 3.0f) * PxPi * pow(ssize.x, 2) * ssize.y;
	}	break;
	default:
		break;
	}
}
#else
#include "btInc.inl"
#endif

namespace saba
{
	namespace
	{
#ifdef SABA_INVZ
		glm::mat4 InvZ(const glm::mat4& m)
		{
			const glm::mat4 invZ = glm::scale(glm::mat4(1), glm::vec3(1, 1, -1));
			return invZ * m * invZ;
		}
#else
#define InvZ(_x_)   ( _x_ )

#endif

	}


	PhysXRigidBody::PhysXRigidBody(MMDPhysics* phy)		:MMDRigidBody(phy)
	{
	}
	PhysXRigidBody::~PhysXRigidBody()
	{
		Destroy();
		//if (pxRbMaterial) pxRbMaterial->release();

		if (aggregate) {
			gPxScene->removeAggregate(*aggregate);
			aggregate->release();
		}
	}

	bool PhysXRigidBody::CreatePMX(const PMXRigidbody& pm, MMDModel* model, MMDNode* _node)
	{
		Pm = pm;
		phyObjOwner = pm.pOwner;
		auto Pmx = (PMXModel*)model;
		
		if (_node) {


			if (!isSubRb) {
				assert(_node->rb0 == this);//_node->rb0 = this;
				for (int i = 0; i < MAX_PHO_CONN; i++) _node->lastConnRb[i] = this;
			}

			_node->rbs.emplace_back(this);
		}

		Destroy();
		m_rigidBodyType = (RigidBodyType)pm.m_op;
		m_group = pm.m_group;		m_groupFlag = 1 << pm.m_group;
		//if (pm.pmd) {
		//	m_groupMask = -1; m_groupFlag = CLDMASK_PMD; assert(m_groupFlag == 1 << 20);
		//	phyObjOwner = 0;
		//}
		//else
		{
			if (pm.m_collideMask32 & 0xFFFF)
				m_groupMask = pm.m_collideMask32;  // (uint32_t(pm.m_collideMask32) | (collideOtherChar ? 0x80000000 : 0));
			if (pm.m_group < 16 && !pm.doNotIgnoreSameGroupCollision)			m_groupMask = m_groupMask & (~m_groupFlag);
			if (dynRbType && pm.m_collideMask32!=0)
				m_groupMask |= CLDMASK_PMD;
			else
				m_groupMask &= ~CLDMASK_PMD;
		}
		//else
		//	m_groupMask = m_groupMask | 0xFFFF0000;

		float volume = 1.f;
		PxGeometry* gm{};
		isSDF = pm.pjv && pm.pjv->isMember("SDF");
		if (pm.pmd && pm.pmd->idxs.size()) {
			createGeom(pm, { 1,1,1 }, gm, volume, isSDF);

		}
		else {
			newGeomVol(pm, { 1,1,1 }, gm, volume);

		}

		if (!isnan(pm.m_rotate.x)) {
			auto rx = glm::rotate(glm::mat4(1), pm.m_rotate.x, glm::vec3(1, 0, 0));
			auto ry = glm::rotate(glm::mat4(1), pm.m_rotate.y, glm::vec3(0, 1, 0));
#if SABA_RB_LOCAL_TRANSFORM
			auto rz = glm::rotate(glm::mat4(1), pm.m_rotate.z, glm::vec3(0, 0, 1));//+ (!pm.m_shape == PMXRigidbody::Shape::Capsule ? PxPi / 2 : 0)
#else
			auto rz = glm::rotate(glm::mat4(1), pm.m_rotate.z + (pm.m_shape == PMXRigidbody::Shape::Capsule ? PxPi / 2 : 0), glm::vec3(0, 0, 1));
#endif
			rttMat = ry * rx * rz; // use my quatToEulerYXZ or glm::extractEulerAngleYXZ to get back from rttMat
		}
		glm::quat rtt = rttMat;
		glm::mat4 translateMat = glm::translate(glm::mat4(1),  //(pm.pmd)?glm::vec3(0,0,0):
			pm.m_translate);
#if SABA_RB_LOCAL_TRANSFORM
		glm::mat4 rbMat = InvZ(translateMat);
#else
		glm::mat4 rbMat = InvZ(translateMat * rttMat);
#endif
		initTransform = rbMat;
		initPos = rbMat[3];
		PxTransform t = GlmMatToPx(rbMat);

		if (pm.failIfOverlap)
		{
			PxOverlapBuffer buffer;
			auto ovl = gPxScene->overlap(*gm, t, buffer, PxQueryFilterData(PxQueryFlag::eANY_HIT | PxQueryFlag::eDYNAMIC));
			if (buffer.hasAnyHits()) {
				delete gm;
				return false;
			}
		}

		static physx::PxMaterial* pxRbMaterialS{}, *pxDefMaterial[32]{};
		physx::PxMaterial* pxRbMaterial{};
		if (pm.materialId || pm.modelMtrId) {
			physx::PxMaterial** ppMtr;

			if (pm.modelMtrId) {
				ppMtr = &Pmx->Sd->pxMtrPtr[pm.modelMtrId];
			}
			else ppMtr  = &pxDefMaterial[pm.materialId];

			if (!*ppMtr) {
				*ppMtr = gPxPhysics->createMaterial(pm.m_friction * FRICTION_MUL, pm.m_friction * FRICTION_MUL, pm.m_repulsion);
			}
			pxRbMaterial = *ppMtr;
			//DP(("mtrcc %d",gPxPhysics->getNbMaterials()));

		}
		else if (pm.pmd) pxRbMaterial = gPxPhysics->createMaterial(Pm.m_friction , Pm.m_friction  , pm.m_repulsion);
		else pxRbMaterial = gPxPhysics->createMaterial(Pm.m_friction * FRICTION_MUL , Pm.m_friction * FRICTION_MUL, Pm.m_repulsion);
		//pxRbMaterial->setFrictionCombineMode(PxCombineMode::eMIN);
		//pxRbMaterial->setRestitutionCombineMode(physx::PxCombineMode::eMAX);
		//t.p += {-10, 0, 0};
		if (_node != nullptr)
		{
			m_offsetMatOrig = m_offsetMat = glm::inverse(_node->mGlobalInit) * rbMat;
		}
		else
		{
			m_offsetMatOrig = m_offsetMat =  rbMat;
		}
		m_invOfsMat = glm::inverse(m_offsetMat);

		float mass = pm.m_mass;

		bool isStatic = pm.m_op == PMXRigidbody::Operation::Static || mass == 0.f;
		//assert(!isStatic);//to do 脚跟

		//PxCollection* collection = PxCreateCollection();PX_ASSERT(collection);
		//collection->add(*material);

		if (pm.pjv) {
			auto& v = *pm.pjv;
			if (v.isMember("mass")) {
				mass = v["mass"].asFloat();
				isSubRb = false;
			}

		}
		m_mass = mass;
#if !SABA_USE_DENSITY
		float density =// isRootRb ? 10 : 1;//
			mass / volume / GRAVITY_MUL;
		density1 = (isSubRb ? 0.1 : 1) *
			density;//
#else
		float density1 = (  isRootRb ? 2 : isSubRb||!dynRbType ? 0.1:1) * pm.density;//
#endif
		density1 *= pm.massMul;
		density0 = density1;
		collideOtherChar || isWeightRb ? 1 : 0.001;
		collideOtherChar || isWeightRb ? std::clamp(mass / 1000, 0.1f, 100.0f) :
			std::clamp(mass / 1000, 0.00001f, 0.1f);//std::min(100.f,std::max(10.f, mass));
		//collection->release();


		{
			PxRigidDynamic* rb{};

			if (pm.pmd) {
				if (1)
					mRb = rb = gPxPhysics->createRigidDynamic(t);
				else
				{
					mRb = rb = PxCreateKinematic(*gPxPhysics, t, *gm, *pxRbMaterial, density1);

				}
			}
			else
			{
				if (1) {

					if (isStatic) {
						density1 = 1000000.f;
						//PxCreateStatic(*gPxPhysics, t, *gm, *pxRbMaterial);
						mRb = rb = PxCreateKinematic(*gPxPhysics, t, *gm, *pxRbMaterial, density1);
					}
					else {
						mRb = rb = PxCreateDynamic(*gPxPhysics, t, *gm, *pxRbMaterial, density1);
						if (USE_CCD) mRb->setRigidBodyFlag(PxRigidBodyFlag::eENABLE_CCD, true);
					}
				}

				if (collideOtherChar || isWeightRb) {
					//jrb->setMass(mass); jrb->setMassSpaceInertiaTensor(PxVec3(1, 1, 1));
				}
				physx::PxRigidBodyExt::updateMassAndInertia(*rb, density1);
			}

			//DP(("CR Mass %f    %f   dm:%f", mass, mass, pm.m_translateDimmer));
			if (DBG_VISUAL_ONLY_SET_ONES) rb->setActorFlag(PxActorFlag::eVISUALIZATION, pm.dbgVisual);
			else if (DBG_VISUAL_ONLY_DYNRB1) rb->setActorFlag(PxActorFlag::eVISUALIZATION, dynRbType==1 || isStatic);

			//assert(isStatic || pm.m_translateDimmer==0);
			rb->setLinearDamping(pm.m_translateDimmer);
			rb->setAngularDamping(pm.m_rotateDimmer);
			//rb->setLinearDamping(1);			rb->setAngularDamping(1);
			if (pm.modContactThreshold) rb->setContactReportThreshold(pm.modContactThresholdValue);

			auto articuDefault = MAX_SPEED;
			canUpdateMass =   !isStatic;
			if (pm.pmd) {
				//rb->setLinearDamping(0.2f);rb->setAngularDamping(0.1f);

				if (isStatic) rb->setRigidBodyFlag(PxRigidBodyFlag::eKINEMATIC, true);
				else rb->setWakeCounter(100000000.f);


				PxShape* shape = PxRigidActorExt::createExclusiveShape(*rb, *gm, *pxRbMaterial);
				shape->setContactOffset(0.02);
				shape->setRestOffset(0.0);




				if (canUpdateMass) {
					rb->setRigidBodyFlag(PxRigidBodyFlag::eENABLE_GYROSCOPIC_FORCES, true);
					rb->setRigidBodyFlag(PxRigidBodyFlag::eENABLE_SPECULATIVE_CCD, true);
					PxRigidBodyExt::updateMassAndInertia(*rb, density1);
					rb->setSolverIterationCounts(50, 1);
					rb->setMaxDepenetrationVelocity(50.f);

					rb->setMaxLinearVelocity(articuDefault * 5);
					rb->setMaxAngularVelocity(10);
				}
			}
			else {

				rb->setMaxLinearVelocity(std::min(pm.maxLinVel ,isRootRb || isWeightRb ? 10000.f : 10000.f) * (node ? node->absScale.x : 1.f));
				rb->setMaxAngularVelocity(std::min(pm.maxAngVel, isRootRb || isWeightRb ? 1000.f :300.f));
			}
			if (pm.materialId) {
				rb->setSolverIterationCounts(2, 1);
				rb->setWakeCounter(0);
			}

			//if (dynRbType)	rb->setRigidBodyFlag(PxRigidBodyFlag::eUSE_KINEMATIC_TARGET_FOR_SCENE_QUERIES, true);
			//DP(("PxTolerancesScale::speed %f", gPxPhysics->getTolerancesScale().speed));
			//rb->setSleepThreshold(0.1f); //rb->putToSleep();

			int shapeNb = rb->getNbShapes();
			//cout << "shapeNb:" << shapeNb << endl;
			PxShape* shape = nullptr;

			rb->getShapes(&shape, 1);
			assert(shape);
			if (shape) {

				if (SABA_RB_LOCAL_TRANSFORM) {
					if (pm.m_shape == PMXRigidbody::Shape::Capsule || pm.m_shape == PMXRigidbody::Shape::Cylinder || pm.m_shape == PMXRigidbody::Shape::Cone) {
						PxQuat rotation = PxQuat(PxHalfPi, PxVec3(0, 0, 1));
						PxTransform localTransform(PxVec3(0.0f), rotation);
						shape->setLocalPose(GlmMatToPx(rttMat) * localTransform );
					}
					else shape->setLocalPose(GlmMatToPx(rttMat));
					rttLocal = glm::quat_cast(rttMat);
					if (canUpdateMass) PxRigidBodyExt::updateMassAndInertia(*rb, density1);
				}
				if (pm.isTrigger) {
					m_groupMask = 1;
					shape->setFlag(PxShapeFlag::eSIMULATION_SHAPE, false);
					shape->setFlag(PxShapeFlag::eTRIGGER_SHAPE, true);
					cbTrigger.onEnter = [this](MMDRigidBody* other) {
						if (other->cbOnTriggerEnter) other->cbOnTriggerEnter(this);
						if (other->canInTrigger) {
							other->inTriggerRbs.insert(this);
							rbsInThisTrigger.insert(other);
						}
						};
					cbTrigger.onExit = [this](MMDRigidBody* other) {
						if (other->cbOnTriggerExit) other->cbOnTriggerExit(this);
						if (other->canInTrigger) {
							other->inTriggerRbs.erase(this);
							rbsInThisTrigger.erase(other);
						}
						};
				}
				//cout << "shape is not null" << endl;
				// 设置shape的FilterData，用在filtershape做匹配
				curGourpMask = m_groupMask;
				setFilterInternal(shape);
				//DP(("SPN=%s",shape->getConcreteTypeName()));
				//if (!pm.pmd && dynRbType == 1) { shape->setContactOffset(0.12); shape->setRestOffset(0.1); }
				if (pm.flag & PHRBF_ContactOffset)
				{
					shape->setContactOffset(0.02f);  //DEFAULT  0.02f * PxTolerancesScale::length = 0.2
					shape->setRestOffset(0.f);
				}
			}
			if (pm.noGravity)
				rb->setActorFlag(PxActorFlag::eDISABLE_GRAVITY, true);
			if (pm.massCtr!=glm::vec3(0))
				rb->setCMassLocalPose(PxTransform(Vec3Cvt(pm.massCtr)));

			canInTrigger = pm.canInTrigger;
			usrDat.owner = this;
			rb->userData = &usrDat;

			/*	PxBoxGeometry newBoxGeometry(2.0f, 2.0f, 2.0f);
			shape->setGeometry(newBoxGeometry);*/
#if USE_DIRECT_GPU
			if (m_directGPUEnabled) {
				gPxMan->addRigidBodyToGPU(rb, *gm);
			}
#endif
		}
		if (pm.materialId==0 && pm.modelMtrId==0)
			pxRbMaterial->release();
		delete gm;




		node = (PMXNode*)_node;
		if (node)
		{
			m_mmdModel = _node->model;
			_node->IsPhysicsActive = pm.m_op != PMXRigidbody::Operation::Static;
			if (_node->IsPhysicsActive) _node->exd.rttRecursionAdd.x = 1;
			//if (node->GetNameU() == L"pinHandR")
			{
				DP((L"node %9s rb=%p act=%d  m %f,%f", node->GetNameU().c_str(), node->rb0, node->IsPhysicsActive,m_mass,getMass()));
			}
		}
		m_name = pm.m_name;
#ifdef _DEBUG
		nameU = ualib::Utf8toWcs(m_name);
#endif
		/*#ifdef _DEBUG
		static int ccc = 0;
		char sz[32];
		_itoa(m_groupMask, sz, 2);
		DP(("%d %s", ccc++, sz));
		#endif*/



		return true;
	}

	void PhysXRigidBody::Destroy()
	{
	}

	const glm::mat4 PhysXRigidBody::GetTransform()
	{
#if 0
		alignas(16) glm::mat4 mat;
		mat = PxMatToGlm(mRb->getGlobalPose());
		return mat;
#else
		return PxMatToGlm(mRb->getGlobalPose());
#endif
	}

	const glm::vec3 PhysXRigidBody::getPosition()
	{

		const auto& pos = mRb->getGlobalPose().p;
		return glm::vec3(pos.x, pos.y, -pos.z);

	}

	void PhysXRigidBody::ResetMovement(MMDPhysics* physics)
	{
		//Set the rigid body to rest and clear all the accumulated forces and impulses.

		assert(gPxPhysics);
		if (!(mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) {
			SET_LIN_VEL(PxVec3(0, 0, 0));
			SET_ANG_VEL(PxVec3(0, 0, 0));
			//Support case where actor is not in a scene and constraints get solved via immediate mode
#if 1
			mRb->clearForce();
			mRb->clearTorque();

#else

			//if (mRb->getScene())
			{
				mRb->clearForce(PxForceMode::eFORCE);
				mRb->clearForce(PxForceMode::eIMPULSE);
				mRb->clearForce(PxForceMode::eACCELERATION);
				mRb->clearForce(PxForceMode::eVELOCITY_CHANGE);
				mRb->clearTorque(PxForceMode::eFORCE);
				mRb->clearTorque(PxForceMode::eIMPULSE);
				mRb->clearTorque(PxForceMode::eACCELERATION);
				mRb->clearTorque(PxForceMode::eVELOCITY_CHANGE);
			}
#endif
			//mRb->setLinearDamping(100);
			//mRb->setAngularDamping(100);
		}

	}

	void PhysXRigidBody::SetActivation(bool activation, bool force)
	{
		auto flags = mRb->getRigidBodyFlags();
		if (Pm.pmd && !isSDF) return;
		if (dynRbType || force)
		{
			//auto model = node->model;
			if (activation)
			{				
				mRb->setRigidBodyFlag(PxRigidBodyFlag::eKINEMATIC, false);
				mRb->wakeUp();
				ResetMovement(0);
				if (USE_CCD)
					mRb->setRigidBodyFlag(PxRigidBodyFlag::eENABLE_CCD, true);
			}
			else
			{
				ResetMovement(0);
				if (USE_CCD) mRb->setRigidBodyFlag(PxRigidBodyFlag::eENABLE_CCD, false);
				mRb->setRigidBodyFlag(PxRigidBodyFlag::eKINEMATIC, true);
				updateMotionState();
			}
			mActivation = activation;
		}
		else
		{
			mActivation = activation && Pm.m_op != PMXRigidbody::Operation::Static;

			//jrb->setRigidBodyFlag(PxRigidBodyFlag::eKINEMATIC, 1);
		}

	}

	void PhysXRigidBody::setScale(glm::vec3 scv)
	{
		if (!mRb) return;
		scaleMul = scv;
		float volume = 1.f;
		PxGeometry* gm{};
		newGeomVol(Pm, scv, gm, volume);
		PxShape* shape{};
		mRb->getShapes(&shape, 1);
		shape->setGeometry(*gm);
		delete gm;
		if (!SCALE_MASS_WHEN_SCALING)
		density1 = density0 / scv.x / scv.y / scv.z;
		updateMassOnDensity();
#if 0
		//auto msc = glm::scale(glm::mat4(1), scv);
		m_offsetMat[3].x = m_offsetMatOrig[3].x * scv.x;
		m_offsetMat[3].y = m_offsetMatOrig[3].y * scv.y;
		m_offsetMat[3].z = m_offsetMatOrig[3].z * scv.z;
		m_invOfsMat = glm::inverse(m_offsetMat);
#endif
		for (auto& j : jtsToParent) {
			j->setScale();
		}
	}

	void PhysXRigidBody::updateMassOnDensity()
	{
		if (canUpdateMass)
		physx::PxRigidBodyExt::updateMassAndInertia(*mRb,// scv.x<1.f?density1/pow(scv.x,3):
			density1 * (m_mmdModel ? m_mmdModel->runTimeMassMulOnScale : 1.f));
	}

	void PhysXRigidBody::addToWorld()
	{

		// Add it to the world
		auto jrb = GetRigidBody();
		assert(jrb);
		if (rbInWhat == 0) gPxScene->addActor(*jrb); rbInWhat = 1;

	}



	void PhysXRigidBody::removeFromWorld()
	{
		if (mRb) {
			if (rbInWhat == 1)
				gPxScene->removeActor(*mRb);
			mRb->release();
		}
		rbInWhat = 0;

	}

	void PhysXRigidBody::setGravityMul(float gm)
	{

		assert(0);

	}

	void PhysXRigidBody::setDamping(float d, int what)
	{
		if (what & 1) mRb->setLinearDamping(d);
		if (what & 2) mRb->setAngularDamping(d);
	}

	glm::vec3 PhysXRigidBody::getLinearVel()
	{

		const auto& v = mRb->getLinearVelocity();
		return { v.x,v.y,-v.z };

	}

	glm::vec3 PhysXRigidBody::getAngularVel()
	{

		const auto& v = mRb->getAngularVelocity();
		return { v.x,-v.y,-v.z };

	}

	void PhysXRigidBody::setLinearVel(const glm::vec3& spd)
	{
		if ((mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) return;
		SET_LIN_VEL(PxVec3(spd.x, spd.y, -spd.z));
#ifdef _DEBUG
		assert(dbgBreak_addVel == 0); dbgBreak_addVel = 0;
#endif
	}

	glm::vec3 PhysXRigidBody::addLinearVel(const glm::vec3& vel)
	{

		if ((mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) return glm::vec3(0);
		////if (oldVelMul != 1.f) SET_LIN_VEL(mRb->getLinearVelocity() * oldVelMul);
		if (gPhyUseAddForce) mRb->addForce(PxVec3(vel.x, vel.y, -vel.z) * (MMDPhysics::stepTimeMul), PxForceMode::eVELOCITY_CHANGE);
		else SET_LIN_VEL(mRb->getLinearVelocity() + PxVec3(vel.x, vel.y, -vel.z) * (MMDPhysics::stepTimeMul));
#ifdef _DEBUG
		assert(dbgBreak_addVel == 0); dbgBreak_addVel = 0;
		if (Pm.dbgVisual)
			irr::scene::sbFw2LineD("pt", getPosition(), getPosition() + vel, 0xFF00FF00, 60);
#endif
		return vel;
	}
	void PhysXRigidBody::addLinearVelThenLimit(const glm::vec3& vel,float maxSpeed)
	{

		if ((mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) return;
		//if (oldVelMul != 1.f) SET_LIN_VEL(mRb->getLinearVelocity() * oldVelMul);
		auto pxvel = mRb->getLinearVelocity() + PxVec3(vel.x, vel.y, -vel.z) * (MMDPhysics::stepTimeMul);
		if (pxvel.magnitude()>maxSpeed)  pxvel = pxvel / pxvel.magnitude() * maxSpeed;
		SET_LIN_VEL(pxvel);
#ifdef _DEBUG
		assert(dbgBreak_addVel == 0); dbgBreak_addVel = 0;
#endif

	}


	void PhysXRigidBody::addForce(const glm::vec3& f, bool isVelChange)
	{

		if ((mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) return;
		auto v = f; //if (onMass) v *= GetRigidBody()->getMass();

		//mRb->setForceAndTorque(PxVec3(f.x, f.y, -f.z)*1000.f,PxVec3(0,0,0));//
		mRb->addForce(PxVec3(v.x, v.y, -v.z) * (MMDPhysics::stepTimeMul), isVelChange ?PxForceMode::eFORCE:PxForceMode::eFORCE);

	}

	void PhysXRigidBody::addTorque(const glm::vec3& torque, bool isVelChange)
	{
		if ((mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) return;
		mRb->addTorque(PxVec3(torque.x, torque.y, torque.z) * MMDPhysics::stepTimeMul, isVelChange ? PxForceMode::eVELOCITY_CHANGE : PxForceMode::eFORCE);
	}

	void PhysXRigidBody::addTorqueOnMatRtt(const glm::mat4&m, const glm::vec3& torque, bool isVelChange)
	{
		if ((mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) return;
		auto t = PxVec3(torque.x, torque.y, torque.z);
		PxQuat rotation = GlmMatToPx(m).q;

		// Transform the torque from local space to world space
		PxVec3 worldTorque = rotation.rotate(t);

		mRb->addTorque(worldTorque *  MMDPhysics::stepTimeMul, isVelChange ? PxForceMode::eVELOCITY_CHANGE : PxForceMode::eFORCE);
	}

	void PhysXRigidBody::setAngularVel(const glm::vec3& vel)
	{
		SET_ANG_VEL(PxVec3(vel.x, -vel.y, -vel.z));
	}



	void PhysXRigidBody::SetCoMTranslate(const glm::vec3& v, float dummy)
	{

		auto t = mRb->getGlobalPose();
		t.p = Vec3Cvt(v);
		if (mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC) {
			mRb->setKinematicTarget(t);
		}
		else mRb->setGlobalPose(t);
		pos = v;
	}

	void PhysXRigidBody::SetCoMTransform(const glm::mat4& m, float mul)
	{

		auto pose = GlmMatToPx(m).getNormalized();

		mRb->setGlobalPose(pose); pos = Vec3Cvt(pose.p);
		if (!(mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) if (mul == 0) {
			SET_LIN_VEL(PxVec3(0, 0, 0));
			SET_ANG_VEL(PxVec3(0, 0, 0));
			mRb->clearForce();
			mRb->clearTorque();
		}
		else if (mul != 1.f) {
			SET_LIN_VEL(mRb->getLinearVelocity() * mul);
			SET_ANG_VEL(mRb->getAngularVelocity() * mul);
#ifdef _DEBUG
			assert(dbgBreak_addVel == 0); dbgBreak_addVel = 0;
#endif
		}
	}

	void PhysXRigidBody::scaleVel(PhyReal s, int what)
	{
		if ((mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) return;
		assert(s >= 0.f); if (s < 0.f) s = 0.f;
		s = pow(s, MMDPhysics::stepTimeMul);

		if (what & 1)
			SET_LIN_VEL(mRb->getLinearVelocity() * s);
		if (what & 2) SET_ANG_VEL(mRb->getAngularVelocity() * s);
#ifdef _DEBUG
		assert(dbgBreak_addVel == 0); dbgBreak_addVel = 0;
#endif
	}

	void PhysXRigidBody::scaleVel3(glm::vec3 s, int what)
	{
		if ((mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) return;
		assert(s.x >= 0.f); if (s.x < 0.f) s = glm::vec3(0.f);
		if (what & 1) SET_LIN_VEL(PxVec3(mRb->getLinearVelocity().x * s.x, mRb->getLinearVelocity().y * s.y, mRb->getLinearVelocity().z * s.z));
		if (what & 2) SET_ANG_VEL(PxVec3(mRb->getAngularVelocity().x * s.x, mRb->getAngularVelocity().y * s.y, mRb->getAngularVelocity().z * s.z));
	}

	float PhysXRigidBody::getMass()
	{
		return mRb->getMass();
	}

	void PhysXRigidBody::setMassMul(float massMul) { PxRigidBodyExt::updateMassAndInertia(*mRb, density1 * massMul); }

	void PhysXRigidBody::setGlmMat(glm::mat4 m)
	{

		if (mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC) {
			//mRb->setGlobalPose(GlmMatToPx(m));
			//PxRigidDynamic* rb = static_cast<PxRigidDynamic*>(jrb);
			PxTransform pm;
			if (m_mmdModel && m_mmdModel->rootHasParent)
				pm = GlmMatToPx(*mtRootParent * m);
			else
				pm = GlmMatToPx(m);
			pm = pm.getNormalized();
			//if (pm.isSane())
			mRb->setKinematicTarget(pm);
			//mRb->setGlobalPose(pm);
		}
		else {

			PxTransform pm = GlmMatToPx(m);
			pm = pm.getNormalized();
			mRb->setGlobalPose(pm);
		}
	}

	void PhysXRigidBody::setContactTest(bool con)
	{

	}

	int PhysXRigidBody::connectJointCount()
	{
		// use mRB to get joints connecting to it
		if (!mRb) return 0;
		assert(__super::connectJointCount() == (int)mRb->getNbConstraints());
		// Get the number of constraints connected to this rigid body
		return (int)mRb->getNbConstraints();
	}

	int PhysXRigidBody::getConnectedConstraints(physx::PxConstraint** userBuffer, int bufferSize, int startIndex)
	{
		if (!mRb) return 0;

		// Get the constraints connected to this rigid body
		return (int)mRb->getConstraints(userBuffer, bufferSize, startIndex);
	}

	void PhysXRigidBody::onAllRbCreated(MMDModel* model)
	{
		//if (Pm.pmd)
			return;


		PxU32 nbActors=0;     // Max number of actors expected in the aggregate
		PxU32 nbShapes=10;
		bool selfCollisions = true;
		auto& rbs = model->GetPhysicsManager()->m_rigidBodys;
		for (auto &mmdrb:rbs) {
			nbActors++;
			PhysXRigidBody* rb = static_cast<PhysXRigidBody*>(mmdrb.get());
			nbShapes += rb->mRb->getNbShapes();
		}

		PxAggregateFilterHint hint = PxGetAggregateFilterHint(PxAggregateType::eGENERIC, selfCollisions);
		aggregate = gPxPhysics->createAggregate(nbActors, nbShapes, hint);

		for (auto& mmdrb : rbs) {
			PhysXRigidBody* rb = static_cast<PhysXRigidBody*>(mmdrb.get());
			PxActor* rd = rb->mRb;
			if (rb->rbInWhat==1) rb->removeFromWorld();
			aggregate->addActor(*rd); rb->rbInWhat = 2;

		}
		gPxScene->addAggregate(*aggregate);
	}

	void PhysXRigidBody::setCollideFilterMask(int setMode, uint32_t ft, int flag )
	{
		if (setMode == 0) ft = m_groupMask;
		if (curGourpMask == ft)
			return;
		PxShape* shape = nullptr;
		mRb->getShapes(&shape, 1);
		assert(shape);
		if (shape) {
			//cout << "shape is not null" << endl;
			// 设置shape的FilterData，用在filtershape做匹配
			curGourpMask =  ft;
			setFilterInternal(shape);
			if ((flag&1)==0)gPxScene->resetFiltering(*mRb);

		}
		//if (!(mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC))		mRb->wakeUp();

	}

	void PhysXRigidBody::setFilterInternal(physx::PxShape* shape)
	{
		if (!shape) return;

		PxU32 flags = ((dynRbType ? PXFILTER0_DYNRB : 0)) | (Pm.modContact ? PXFILTER0_MODCONTACT : 0) |
			(Pm.forceNotify ? PXFILTER0_FORCE_NOTIFY : 0) | (0xFFFF & PxU32(phyObjOwner));

		shape->setSimulationFilterData(
			PxFilterData(flags, m_groupFlag, curGourpMask, Pm.filterW3));

	}

	glm::vec3 PhysXRigidBody::predictPosition(float afterTimeS)
	{
		auto ret=predictRigidBodyPosition(mRb, afterTimeS, PHYSX_SIM_STEP*60);
		//auto ret = predictRigidBodyPosition0(mRb, afterTimeS);
		glm::vec3 pos = { ret.finalPosition.x, ret.finalPosition.y, -ret.finalPosition.z };
		return pos;
	}

	void PhysXRigidBody::setGravityOn(bool on)
	{

		mRb->setActorFlag(PxActorFlag::eDISABLE_GRAVITY, !on);
		if (mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC) return;
		if (on) mRb->wakeUp();
	}


	void PhysXRigidBody::addRotationToMatOnNode(glm::mat3 rttMat, float angleMul, float chgVel )
	{
		if (mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC) return;

		//DPWCS((L"addRotationToMatOnNode %10s	 %d ", node->GetNameU().c_str(), rbTreeLevel));

		// Assuming these are given
		PxQuat currentOrientation = mRb->getGlobalPose().q;; // Current orientation
		PxQuat targetOrientation= QuatCvt(glm::quat(SABA_RB_LOCAL_TRANSFORM? rttMat:rttMat*glm::mat3(getOfsMat()))); // Desired orientation
 		float dampingCoefficient=0; // Damping coefficient
		if (currentOrientation.dot(targetOrientation) < 0.0f) {
			targetOrientation = -targetOrientation;
		}
		// Calculate the quaternion representing the rotation from the current orientation to the target
		PxQuat rotationDifference = targetOrientation * currentOrientation.getConjugate();

		// Convert to axis-angle to find how much we need to rotate
		PxVec3 rotationAxis;
		PxReal rotationAngle;
		rotationDifference.toRadiansAndUnitAxis(rotationAngle, rotationAxis);
		if (rotationAngle < 0.01f) return;
		angleMul *= gPhyRttMul;
		if (!gPhyRttSetAV && !chgVel) {

			// Normalize the angle to the range [-PI, PI]
			//while (rotationAngle > PxPi)			rotationAngle -= PxTwoPi;
			//while (rotationAngle < -PxPi)			rotationAngle += PxTwoPi;
			//DP(("ang %8.2f  axis %.2f,%.2f,%.2f", rotationAngle, rotationAxis.x, rotationAxis.y, rotationAxis.z));
			// Assuming a small enough rotation angle, approximate angular velocity needed
			// This is a simplification. In a full implementation, you would probably normalize the rotation duration.
			PxVec3 angularVelocity = rotationAxis * rotationAngle;// std::min(3.0f, rotationAngle * rotationAngle * 30.f); // This is a crude approximation

			// Apply moment of inertia (I). This step actually requires you to know the angular acceleration rather than velocity,
			// but for simplicity, we're skipping directly to torque via angular velocity.
			PxVec3 inertia = mRb->getMassSpaceInertiaTensor(); // Get the body's moment of inertia
			// We assume a unit angular acceleration for simplicity; in a real case, you'd calculate this based on time to target orientation.
			PxVec3 torque = inertia.multiply(angularVelocity);

			// Add damping (simplified model)
			PxVec3 dampingTorque = angularVelocity * -dampingCoefficient;
#define TORQUE_ACC 0
			// Final torque is the sum of inertia and damping torques
			PxVec3 finalTorque = torque
#if !TORQUE_ACC
				*10.f
#endif
				 //* density1
				//.multiply(angularVelocity)// +dampingTorque;
				;
			// Apply the torque
			mRb->addTorque(finalTorque * angleMul * MMDPhysics::stepTimeMul  /// mRb->getMass()
#if TORQUE_ACC
				, PxForceMode::eACCELERATION, true
#endif
			);

			//if (jtsToParent[0]) jtsToParent[0]->setDriveVel(glm::vec3(0),Vec3Cvt(finalTorque) * angleMul * MMDPhysics::stepTimeMul*10.f, 2);
		}
		else
		{
			PxVec3 angularVelocity = rotationAxis * rotationAngle * angleMul/20.f  * MMDPhysics::stepTimeMul;
#if 1
			mRb->addTorque(angularVelocity, PxForceMode::eVELOCITY_CHANGE);
#else
			auto angVel = mRb->getAngularVelocity();
			SET_ANG_VEL(angVel+angularVelocity);
#endif
		}
	}
	void PhysXRigidBody::setAngVelToRotateOnNode(glm::mat4 rttMat, float velMul)
	{
#if 1
		addRotationToMatOnNode(rttMat, velMul*10.f);
#else
		if ((mRb->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)) return;
		glm::vec3 dirRb, dirMt;
		dirRb = glm::normalize(glm::mat3(InvZ2(GetTransform())) * glm::vec3(0, 0, -1));
		dirMt = glm::normalize((glm::mat3(InvZ2(rttMat)) * glm::mat3(m_offsetMat)) * glm::vec3(0, 0, -1));
		auto axis = glm::normalize(glm::cross(dirRb, dirMt));
		float len = glm::length(dirRb - dirMt);
		float angle = acos(glm::dot(dirRb, dirMt) / (glm::length(dirRb) * glm::length(dirMt)));
		//DPWCS((L"R2I %10s	a=%8.3f  m=%8.3f ",m_node->GetNameU().c_str(), angle*irr::core::RADTODEG, GetRigidBody()->getMass()));
		if (angle > 0.002f)if (!isnan(axis.x)) {


			SET_ANG_VEL(PxVec3(axis.x, axis.y, axis.z) * (1 + pow(angle, 1) * velMul));
		}
#endif
	}


	void PhysXRigidBody::pdbResetPos()
	{
		PxPBDParticleSystem* particleSystem = (PxPBDParticleSystem*)pdbPmd->ptcSystem;

		PxCudaContextManager* cudaContextManager = gPxScene->getCudaContextManager();
		PxCudaContext* cudaContext = cudaContextManager->getCudaContext();
		PxParticleClothBuffer* clothBuf = (PxParticleClothBuffer * )pdbPmd->ptcBuf;
		PxU32 numParticles = clothBuf->getNbActiveParticles();
		PxVec4* positions = clothBuf->getPositionInvMasses();
		PxArray<PxVec4> vertices;
		auto count = pdbPmd->v4s.size();

		vertices.resize(count);
		auto mat=GetTransform();
		auto rbPos = mRb->getGlobalPose().p;
		PxTransform pxRbPos;
		if (mRb->getKinematicTarget(pxRbPos)) mat = PxMatToGlm(pxRbPos);
		auto invm = glm::inverse(initTransform);
		pos = invm * glm::vec4(pos, 1);
		mat = mat * invm;
		for (PxU32 i = 0; i < count; ++i) {
			glm::vec4 pos = pdbPmd->v4s[i]; pos.z = -pos.z;
			pos.w = 1;
			pos = mat * pos;
			vertices[i] = PxVec4( Vec3Cvt(pos),pdbPmd->v4s[i].w);
		}

		numParticles = vertices.size();
		cudaContext->memcpyHtoD(CUdeviceptr(positions), &vertices[0], sizeof(PxVec4) * numParticles);

		PxVec4* velocities = clothBuf->getVelocities();
		for (PxU32 i = 0; i < count; ++i)
			vertices[i] = PxVec4(0,0,0, 0);
		cudaContext->memcpyHtoD(CUdeviceptr(velocities), &vertices[0], sizeof(PxVec4) * numParticles);

	}



	PhysXJoint::PhysXJoint():MMDJoint()
	{
	}

	PhysXJoint::~PhysXJoint()
	{
		destroy();
	}

	bool PhysXJoint::CreateJointPmx(PMXJoint pmxJoint, MMDRigidBody* rigidBodyA, MMDRigidBody* rigidBodyB, float scale)
	{
		mScale = scale;
		Pm = pmxJoint;
		glm::quat q = glm::quat(pmxJoint.rotate);
#if GP_FAST_CVT
		PxQuat qt = QuatCvt(q);
#else
		q = InvZ2Q(q); xx
		PxQuat qt = *(PxQuat*)&q;
#endif
		PxTransform transform(PxVec3(
			Pm.translate.x * scale,
			Pm.translate.y * scale,
			-Pm.translate.z * scale
		), qt);
		rb1=rbA = static_cast<PhysXRigidBody*>(rigidBodyA);
		rb2=rbB = static_cast<PhysXRigidBody*>(rigidBodyB);

		PxTransform tA = Pm.useInitRbPos ? GlmMatToPx(rbA->initTransform) : rbA->GetRigidBody()->getGlobalPose();
		PxTransform tB = Pm.useInitRbPos ? GlmMatToPx(rbB->initTransform) : rbB->GetRigidBody()->getGlobalPose();
		PxTransform invA = tA.getInverse();
		PxTransform invB = tB.getInverse();
		invA = invA * transform;
		invB = invB * transform;
		localPoseA = invA;
		localPoseB = invB;
		createJointInternal();

		//assert(rigidBodyB->jtToParent == nullptr);
		MMDJoint::onCreatedJoint(Pm, rigidBodyA, rigidBodyB);
		return true;
	}


	void PhysXJoint::createJointInternal()
	{
		auto lba = localPoseA;		lba.p *= rbA->scaleMul.x;
		auto lbb = localPoseB;		lbb.p *= rbB->scaleMul.x;

		auto prev = rbA->GetRigidBody();
		auto current = rbB->GetRigidBody();

		PxJoint* jt;

		if (!USE_DIRECT_GPU && Pm.m_type == PMXJoint::JointType::Fixed)
		{
			auto j = PxFixedJointCreate(*gPxPhysics, prev, lba, current, lbb);
			jt = j;
		}
		//else if  (Pm.lockPos) {
		//	PxSphericalJoint*  j = PxSphericalJointCreate(*gPxPhysics, prev, lba, current, lbb);
		//	jt = j;
		//	if (1) {
		//		j->setSphericalJointFlag(PxSphericalJointFlag::eLIMIT_ENABLED, true);
		//		PxJointLimitCone limit(0, 0); // Default values
		//		if (Pm.limitMaxR.y < 6.28f && Pm.limitMaxR.z < 6.28f) {
		//			limit = PxJointLimitCone(
		//				Pm.limitMaxR.y - Pm.limitMinR.y + 0.00001f,
		//				Pm.limitMaxR.z - Pm.limitMinR.z + 0.00001f
		//			);
		//			limit.yAngle = PxClamp(limit.yAngle, 0.000001f, 0.999999f * PxPi);
		//			limit.zAngle = PxClamp(limit.zAngle, 0.000001f, 0.999999f * PxPi);
		//		}
		//		j->setLimitCone(limit);
		//
		//	}
		//}
		else
		{
			auto j = PxD6JointCreate(*gPxPhysics, prev, lba, current, lbb);

			bool forceLock = rbB->dynRbType != 0;
			bool lx = forceLock || Pm.limitMinT.x >= Pm.limitMaxT.x, ly = forceLock || Pm.limitMinT.y >= Pm.limitMaxT.y, lz = forceLock || Pm.limitMinT.z >= Pm.limitMaxT.z;

			j->setMotion(PxD6Axis::eX, lx ? PxD6Motion::eLOCKED : Pm.limitMaxT.x - Pm.limitMinT.x > 199 ? PxD6Motion::eFREE : PxD6Motion::eLIMITED);
			j->setMotion(PxD6Axis::eY, ly ? PxD6Motion::eLOCKED : Pm.limitMaxT.y - Pm.limitMinT.y > 199 ? PxD6Motion::eFREE : PxD6Motion::eLIMITED);
			j->setMotion(PxD6Axis::eZ, lx ? PxD6Motion::eLOCKED : Pm.limitMaxT.z - Pm.limitMinT.z > 199 ? PxD6Motion::eFREE : PxD6Motion::eLIMITED);
			if (!lx)
				j->setLinearLimit(PxD6Axis::eX, PxJointLinearLimitPair(Pm.limitMinT.x, Pm.limitMaxT.x, PxSpring(0, 0)));
			if (!ly)
				j->setLinearLimit(PxD6Axis::eY, PxJointLinearLimitPair(Pm.limitMinT.y, Pm.limitMaxT.y, PxSpring(0, 0)));
			if (!lz)
				j->setLinearLimit(PxD6Axis::eZ, PxJointLinearLimitPair(Pm.limitMinT.z, Pm.limitMaxT.z, PxSpring(0, 0)));

			float  dF = FLT_MAX; bool acc = true;
			j->setDrive(PxD6Drive::eX, PxD6JointDrive(Pm.springT.x * springMul, Pm.dampingT.x * springMul, dF, acc));
			j->setDrive(PxD6Drive::eY, PxD6JointDrive(Pm.springT.y * springMul, Pm.dampingT.y * springMul, dF, acc));
			j->setDrive(PxD6Drive::eZ, PxD6JointDrive(Pm.springT.z * springMul, Pm.dampingT.z * springMul, dF, acc));
			//j->setBreakForce(1, 1);
			auto d6m = Pm.m_type == PMXJoint::JointType::Fixed ? PxD6Motion::eLOCKED : PxD6Motion::eLIMITED;

			j->setMotion(PxD6Axis::eSWING1,// Pm.limitMinR.z>= Pm.limitMaxR.z? PxD6Motion::eLOCKED : //err:joint shaking
				Pm.limitMaxR.z > 6.28f ? PxD6Motion::eFREE : d6m);
			j->setMotion(PxD6Axis::eSWING2,// Pm.limitMinR.y >= Pm.limitMaxR.y ? PxD6Motion::eLOCKED : 
				Pm.limitMaxR.y > 6.28f ? PxD6Motion::eFREE : d6m);
			j->setMotion(PxD6Axis::eTWIST,// Pm.limitMinR.x >= Pm.limitMaxR.x ? PxD6Motion::eLOCKED : 
				Pm.limitMaxR.x > 6.28f ? PxD6Motion::eFREE : d6m);


			j->setDrive(PxD6Drive::eSWING, PxD6JointDrive((Pm.springR.y), Pm.dampingR.y, FLT_MAX, true));
			j->setDrive(PxD6Drive::eTWIST, PxD6JointDrive((Pm.springR.x), Pm.dampingR.x, FLT_MAX, true));
			//j->setDrive(PxD6Drive::eSLERP, PxD6JointDrive((Pm.m_springR.x)*1, Pm.m_dampingR.x , FLT_MAX, true));
			//PxJointLimitCone c(Pm.limitMaxR.y - Pm.limitMinR.y + 0.00001f,
			//	Pm.limitMaxR.z - Pm.limitMinR.z + 0.00001f
			//);
			//c.yAngle = PxClamp(c.yAngle, 0.000001f, 0.999999f);
			//c.zAngle = PxClamp(c.zAngle, 0.000001f, 0.999999f);
			//j->setSwingLimit(c);
			if (Pm.limitMinR.x <= Pm.limitMaxR.x && !(Pm.limitMaxR.x > 6.28f))
				j->setTwistLimit(PxJointAngularLimitPair(Pm.limitMinR.x, Pm.limitMaxR.x + 0.00001f));

			float yL = Pm.limitMinR.y, yU = Pm.limitMaxR.y;
			//float zL = Pm.m_rotateLowerLimit.z, zU = Pm.m_rotateUpperLimit.z;
			float zL = -Pm.limitMaxR.z, zU = -Pm.limitMinR.z;

			if (!(Pm.limitMaxR.y > 6.28f || Pm.limitMaxR.z > 6.28f))
			{
				PxJointLimitPyramid pr(
					std::max(-PxPi + 0.00001f, yL),
					std::min(PxPi - 0.00001f, yU > yL ? yU : yL + (yU <= yL ? 0.00001f : 0)),
					std::max(-PxPi + 0.00001f, zL),
					std::min(PxPi - 0.00001f, zU > zL ? zU : zL + (zU <= zL ? 0.00001f : 0))
				);
				if (pr.yAngleMin > pr.yAngleMax) pr.yAngleMin = pr.yAngleMax;
				if (pr.zAngleMin > pr.zAngleMax) pr.zAngleMin = pr.zAngleMax;
				j->setPyramidSwingLimit(pr);
			}
			updateDrive(j);
			jt = j;
		}
		float mr = rbA->getMass() / rbB->getMass();
		if (mr > 9.9f || mr < 1.f / 9.9f) {
			((PxRigidDynamic*)rbA->GetRigidBody())->setSolverIterationCounts(8, 2);
			((PxRigidDynamic*)rbB->GetRigidBody())->setSolverIterationCounts(8, 2);
		}
		//PxRigidActor* actor0, * actor1;
		//m_constraint->getActors(actor0, actor1);
		if (Pm.visualize)
			jt->setConstraintFlag(PxConstraintFlag::eVISUALIZATION, true);

		auto fl = jt->getConstraintFlags();
		if (fl & PxConstraintFlag::eCOLLISION_ENABLED)
			jt->setConstraintFlag(PxConstraintFlag::eCOLLISION_ENABLED, false);
		if (pJoint) pJoint->release();
 
		pJoint = jt;


		if (Pm.breakMassMul>0.00001f) //gpu simulation error
		{
			setBreakThreshold(std::min(rbB->getMass(),rbA->getMass())*Pm.breakMassMul);

		}
	}


	bool PhysXJoint::CreateJoint2pt(const PMXJoint& pmxJoint, MMDRigidBody* rigidBodyA, MMDRigidBody* rigidBodyB, int lockRtt, int lockPos)
	{
		Pm = pmxJoint;
		glm::quat q = glm::quat(Pm.rotate);
#if GP_FAST_CVT
		PxQuat qt = QuatCvt(q);
#else
		q = InvZ2Q(q);
		PxQuat qt = *(PxQuat*)&q;
#endif
		PxTransform transform(PxVec3(
			Pm.translate.x,
			Pm.translate.y,
			-Pm.translate.z
		), qt);
		rb1 = rbA = static_cast<PhysXRigidBody*>(rigidBodyA);
		rb2 = rbB = static_cast<PhysXRigidBody*>(rigidBodyB);
		PxTransform tA = Pm.useInitRbPos ? GlmMatToPx(rbA->initTransform) : rbA->GetRigidBody()->getGlobalPose();
		PxTransform tB = Pm.useInitRbPos ? GlmMatToPx(rbB->initTransform) : rbB->GetRigidBody()->getGlobalPose();

		PxTransform invA = tA.getInverse();
		PxTransform invB = tB.getInverse();
		PxTransform rtoA = invA * transform;
		PxTransform rtoB = invB * transform;
		localPoseA = rtoA;
		localPoseB = rtoB;
		//JointCreateFunction createJoint = createBreakableFixed;
		// Create constraint
		auto prev = rbA->GetRigidBody();
		auto current = rbB->GetRigidBody();
		//m_constraint = (*createJoint)(prev, invA, current, invB);

		if (Pm.setLocalPos) {
			rtoB = PxTransform(PxVec3(
				Pm.t2B.x,
				Pm.t2B.y,
				-Pm.t2B.z
			), QuatCvt(glm::quat(Pm.r2B)));//  *GlmMatToPx(rbA->getOfsMat());
			//rtoB.p.y = -1;
			rtoA = transform;
			//rtoA.p = { 0,10,0 };
#if !SABA_RB_LOCAL_TRANSFORM
			if (rbA->Pm.m_shape == PMXRigidbody::Shape::Capsule) {
				rtoA.p = PxQuat(-PxPi / 2, PxVec3(0, 0, 1)).rotate(rtoA.p);
				rtoA.q = PxQuat(-PxPi / 2, PxVec3(0, 0, 1)) * rtoA.q;
			}
#endif
		}
#if 0
		auto j = PxFixedJointCreate(*gPhysics, prev, invA, current, invB);
		auto fl = j->getConstraintFlags();

		//	j->setBreakForce(1000, 100000);
		j->setConstraintFlag(PxConstraintFlag::eDRIVE_LIMITS_ARE_FORCES, true);
		j->setConstraintFlag(PxConstraintFlag::eDISABLE_PREPROCESSING, true);
#else
		//rtoB.p = {0,0,0};

		PxJoint* j = nullptr;

		if (0&&lockPos) {
			// Create a spherical joint (ball joint) when position should be locked
			j = PxSphericalJointCreate(*gPxPhysics, prev, rtoA, current, rtoB);

			// Configure the spherical joint if needed
			PxSphericalJoint* sphericalJoint = static_cast<PxSphericalJoint*>(j);

			// Set limits if rotation should be constrained
			if (lockRtt) {
				sphericalJoint->setSphericalJointFlag(PxSphericalJointFlag::eLIMIT_ENABLED, true);

				// Create a cone limit based on joint parameters
				PxJointLimitCone limit(PxPi/4, PxPi/4); // Default values

				// Use actual limits if available
				if (Pm.limitMaxR.y < 6.28f && Pm.limitMaxR.z < 6.28f) {
					limit = PxJointLimitCone(
						Pm.limitMaxR.y - Pm.limitMinR.y + 0.00001f,
						Pm.limitMaxR.z - Pm.limitMinR.z + 0.00001f
					);
					limit.yAngle = PxClamp(limit.yAngle, 0.000001f, 0.999999f * PxPi);
					limit.zAngle = PxClamp(limit.zAngle, 0.000001f, 0.999999f * PxPi);
				}

				sphericalJoint->setLimitCone(limit);
			}
		} else {

			PxD6Joint* j6 = PxD6JointCreate(*gPxPhysics, prev, rtoA, current, rtoB);
#if 1
		bool forceLock = lockPos == 2;
		bool lx = forceLock || Pm.limitMinT.x >= Pm.limitMaxT.x, ly = forceLock || Pm.limitMinT.y >= Pm.limitMaxT.y, lz = forceLock || Pm.limitMinT.z >= Pm.limitMaxT.z;

		j6->setMotion(PxD6Axis::eX, lx ? PxD6Motion::eLOCKED : Pm.limitMaxT.x - Pm.limitMinT.x > 199 ? PxD6Motion::eFREE : PxD6Motion::eLIMITED);
		j6->setMotion(PxD6Axis::eY, ly ? PxD6Motion::eLOCKED : Pm.limitMaxT.y - Pm.limitMinT.y > 199 ? PxD6Motion::eFREE : PxD6Motion::eLIMITED);
		j6->setMotion(PxD6Axis::eZ, lx ? PxD6Motion::eLOCKED : Pm.limitMaxT.z - Pm.limitMinT.z > 199 ? PxD6Motion::eFREE : PxD6Motion::eLIMITED);
		if (!lx)
			j6->setLinearLimit(PxD6Axis::eX, PxJointLinearLimitPair(Pm.limitMinT.x, Pm.limitMaxT.x, PxSpring(0, 0)));
		if (!ly)
			j6->setLinearLimit(PxD6Axis::eY, PxJointLinearLimitPair(Pm.limitMinT.y, Pm.limitMaxT.y, PxSpring(0, 0)));
		if (!lz)
			j6->setLinearLimit(PxD6Axis::eZ, PxJointLinearLimitPair(Pm.limitMinT.z, Pm.limitMaxT.z, PxSpring(0, 0)));


		float  dF = FLT_MAX; bool acc = true;
		j6->setDrive(PxD6Drive::eX, PxD6JointDrive(Pm.springT.x * springMul, Pm.dampingT.x * springMul, dF, acc));
		j6->setDrive(PxD6Drive::eY, PxD6JointDrive(Pm.springT.y * springMul, Pm.dampingT.y * springMul, dF, acc));
		j6->setDrive(PxD6Drive::eZ, PxD6JointDrive(Pm.springT.z * springMul, Pm.dampingT.z * springMul, dF, acc));
		//j->setBreakForce(1, 1);
		auto d6m = Pm.m_type == PMXJoint::JointType::Fixed ? PxD6Motion::eLOCKED : PxD6Motion::eLIMITED;

		j6->setMotion(PxD6Axis::eSWING1, Pm.limitMaxR.z > 6.28f ? PxD6Motion::eFREE : d6m);
		j6->setMotion(PxD6Axis::eSWING2, Pm.limitMaxR.y > 6.28f ? PxD6Motion::eFREE : d6m);
		j6->setMotion(PxD6Axis::eTWIST, Pm.limitMaxR.x > 6.28f ? PxD6Motion::eFREE : d6m);


		j6->setDrive(PxD6Drive::eSWING, PxD6JointDrive((Pm.springR.y), Pm.dampingR.y, FLT_MAX, true));
		j6->setDrive(PxD6Drive::eTWIST, PxD6JointDrive((Pm.springR.x), Pm.dampingR.x, FLT_MAX, true));
#else
		j->setMotion(PxD6Axis::eX, lockPos == 2 ? PxD6Motion::eLOCKED : lockPos == 0 ? PxD6Motion::eFREE : PxD6Motion::eLIMITED);
		j->setMotion(PxD6Axis::eY, lockPos == 2 ? PxD6Motion::eLOCKED : lockPos == 0 ? PxD6Motion::eFREE : PxD6Motion::eLIMITED);
		j->setMotion(PxD6Axis::eZ, lockPos == 2 ? PxD6Motion::eLOCKED : lockPos == 0 ? PxD6Motion::eFREE : PxD6Motion::eLIMITED);
		bool hasLimit = 0;// (Pm.limitMinR != Pm.limitMaxR);  // LIMIT = max hard spring
		j->setMotion(PxD6Axis::eSWING1, lockRtt ? PxD6Motion::eLOCKED : hasLimit? PxD6Motion::eLIMITED : PxD6Motion::eFREE);
		j->setMotion(PxD6Axis::eSWING2, lockRtt ? PxD6Motion::eLOCKED : hasLimit? PxD6Motion::eLIMITED : PxD6Motion::eFREE);
		j->setMotion(PxD6Axis::eTWIST,  lockRtt ? PxD6Motion::eLOCKED : hasLimit? PxD6Motion::eLIMITED : PxD6Motion::eFREE);
		//j->setDistanceLimit( PxJointLinearLimit(0, PxSpring(0, 0)));
		j->setLinearLimit(PxD6Axis::eX, PxJointLinearLimitPair(Pm.limitMinT.x, Pm.limitMaxT.x, PxSpring(0, 0)));
		j->setLinearLimit(PxD6Axis::eY, PxJointLinearLimitPair(Pm.limitMinT.y, Pm.limitMaxT.y, PxSpring(0, 0)));
		j->setLinearLimit(PxD6Axis::eZ, PxJointLinearLimitPair(Pm.limitMinT.z, Pm.limitMaxT.z, PxSpring(0, 0)));
#endif
		//j->setConstraintFlag(PxConstraintFlag::eDRIVE_LIMITS_ARE_FORCES, true);
		j = j6;
		updateDrive(j6);

		}

		j->setConstraintFlag(PxConstraintFlag::eVISUALIZATION, true);
#endif
		pJoint = j;
		jointType = '2p';
		MMDJoint::onCreatedJoint(Pm, rigidBodyA, rigidBodyB);
		return true;
	}



	void PhysXJoint::setDrive(float spring, float damping)
	{

		PxD6Joint* j = static_cast<PxD6Joint*>(pJoint);
		float dS = spring, dD = damping, dF = FLT_MAX; bool acc = 1;
		j->setDrive(PxD6Drive::eX, PxD6JointDrive(dS, dD, dF, acc));
		j->setDrive(PxD6Drive::eY, PxD6JointDrive(dS, dD, dF, acc));
		j->setDrive(PxD6Drive::eZ, PxD6JointDrive(dS, dD, dF, acc));

	}

	void PhysXJoint::setDriveMul(float springMultiplier, float  dampingMultiplier)
	{
		if (oldSpringMul == springMultiplier && springMultiplier!=1.f)
			return;
		oldSpringMul = springMul;
		assert(springMultiplier < 1000000.f);
		springMul = springMultiplier;
		assert(springMul >=0.f);
		if (Pm.springR == glm::vec3(0)) {
			Pm.springR = glm::vec3(10000);
			Pm.dampingR = Pm.springR/100.f;
			PxD6Joint* j = (PxD6Joint*)pJoint;
			j->setMotion(PxD6Axis::eSWING1,PxD6Motion::eFREE  );
			j->setMotion(PxD6Axis::eSWING2,PxD6Motion::eFREE  );
			j->setMotion(PxD6Axis::eTWIST, PxD6Motion::eFREE );
		}
		updateDrive(static_cast<PxD6Joint*>(pJoint));

		//rb1->addForce({ 0,0,0 }, true);
		rb2->addForce({ 0,0,0 }, true); //activate

	}

	void PhysXJoint::updateDrive(physx::PxD6Joint* j)
	{
		if (rbB->node && rbB->node->GetNameU()==L"tieHead")
		{
			DP(("c"));
		}
		if (Pm.limitMaxT.x < 0 || Pm.limitMaxT.y < 0 || Pm.limitMaxT.z < 0 || Pm.limitMinT.x>0 || Pm.limitMinT.y>0 || Pm.limitMinT.z>0)
		{
			auto tr = j->getDrivePosition();
			tr.p = PxVec3((Pm.limitMinT.x + Pm.limitMaxT.x)*rbA->scaleMul.x, (Pm.limitMinT.y + Pm.limitMaxT.y)*rbA->scaleMul.y, (Pm.limitMinT.z + Pm.limitMaxT.z)*rbA->scaleMul.z) * 0.5f;

			j->setDrivePosition(tr);
		}
		float dampingMul = std::min(10000.f, springMul);

		//Pm.dampingT = glm::clamp( Pm.dampingT, Pm.springT / 100.f, glm::vec3(600.f));
		float  dF = FLT_MAX; bool acc = 1;
		j->setDrive(PxD6Drive::eY, PxD6JointDrive(Pm.springT.y * springMul*rbA->scaleMul.y, Pm.dampingT.y * dampingMul *rbA->scaleMul.y, dF, acc));
		j->setDrive(PxD6Drive::eZ, PxD6JointDrive(Pm.springT.z * springMul*rbA->scaleMul.z, Pm.dampingT.z * dampingMul *rbA->scaleMul.z, dF, acc));
		j->setDrive(PxD6Drive::eX, PxD6JointDrive(Pm.springT.x * springMul*rbA->scaleMul.x, Pm.dampingT.x * dampingMul *rbA->scaleMul.x, dF, acc));
		//j->setDrive(PxD6Drive::eSLERP, PxD6JointDrive(springMul, 1000* springMul, FLT_MAX, true));

		j->setDrive(PxD6Drive::eSWING, PxD6JointDrive((Pm.springR.y* springMul), Pm.dampingR.y* dampingMul, FLT_MAX, true));
		j->setDrive(PxD6Drive::eTWIST, PxD6JointDrive((Pm.springR.x* springMul), Pm.dampingR.x* dampingMul, FLT_MAX, true));
	}

	void PhysXJoint::destroy()
	{

		if (pJoint) {
			pJoint->release();
			pJoint = nullptr;
		}

	}
	void PhysXJoint::setBreakThreshold(float s)
	{

		PxReal force, torque;
		pJoint->getBreakForce(force, torque);
		pJoint->setBreakForce(s, torque);


	}
	void PhysXJoint::scaleBreakThreshold(float s)
	{

		PxReal force, torque;
		pJoint->getBreakForce(force, torque);
		pJoint->setBreakForce(force*s, torque*s);

	}

	bool PhysXJoint::isConnected()
	{
		//PxRigidActor* actor0, * actor1;
		//m_constraint->getActors(actor0, actor1);
		bool broken = (pJoint->getConstraintFlags() & PxConstraintFlag::eBROKEN);
		return !broken;// (actor0 && actor1);


	}

	void PhysXJoint::setScale()
	{
#if 0
		auto lb = pd->localPoseA;
		lb.p *= rbA->mmdNode->absScale.x;	m_constraint->setLocalPose(PxJointActorIndex::eACTOR0, lb);
		lb = pd->localPoseB;
		lb.p *= rbB->mmdNode->absScale.x;	m_constraint->setLocalPose(PxJointActorIndex::eACTOR1, lb);
		DPWCS((L"SC RB %.2f  %s  -  %.2f  %s", rbA->mmdNode->absScale.x, rbA->mmdNode->GetNameU().c_str(), rbB->mmdNode->absScale.x, rbB->mmdNode->GetNameU().c_str()));
#else
		if ( pJoint && mScale != rbB->node->absScale.x) {
			if (jointType==0) createJointInternal();
			else if (jointType == '2p') {
				auto j = static_cast<PxD6Joint*>(pJoint);
				auto lba = localPoseA;		lba.p *= rbA->scaleMul.x;
				auto lbb = localPoseB;		lbb.p *= rbB->scaleMul.x;
				j->setLocalPose(PxJointActorIndex::eACTOR0, lba);
				j->setLocalPose(PxJointActorIndex::eACTOR1, lbb);
			}

		}



#endif
	}

	void PhysXJoint::setDrivePose(glm::mat4 m, uint32_t flag, float driveMul)
	{
		auto jt = (PxD6Joint*)pJoint; if (!jt) return;
		PxTransform tr,t = GlmMatToPx(m);
		//jt->setDrive(PxD6Drive::eX, PxD6JointDrive(10000000, 100000, FLT_MAX, true));
		//jt->setDrive(PxD6Drive::eY, PxD6JointDrive(10000000, 100000, FLT_MAX, true));
		//jt->setDrive(PxD6Drive::eZ, PxD6JointDrive(10000000, 100000, FLT_MAX, true));

		//jt->setDrive(PxD6Drive::eSWING, PxD6JointDrive(10000000, 100000, FLT_MAX, true));
		//jt->setDrive(PxD6Drive::eTWIST, PxD6JointDrive(10000000, 100000, FLT_MAX, true));
		setDriveMul(driveMul);


		if (flag == 3) {
			PxD6JointDrive x=jt->getDrive(PxD6Drive::eX);
			jt->setDrivePosition(t);
		}
		else
		{
			tr=jt->getDrivePosition();
			if (flag & 1)	tr.p = t.p;
			if (flag & 2) {
				tr.q = t.q;
			}
			jt->setDrivePosition(tr);
		}
	}

	void PhysXJoint::setDriveVel(glm::vec3 lv, glm::vec3 av, uint32_t flag)
	{
		auto jt=(PxD6Joint*)pJoint;
		if (flag==3)
			jt->setDriveVelocity(PxVec3(lv.x, lv.y, -lv.z), PxVec3(av.x, -av.y, -av.z));
		else {
			PxVec3 olv, oav;
			jt->getDriveVelocity(olv, oav);
			if (flag & 1)	jt->setDriveVelocity(PxVec3(lv.x, lv.y, -lv.z), oav);
			if (flag & 2)	jt->setDriveVelocity(olv, PxVec3(av.x, -av.y, -av.z));
		}
	}

	glm::vec3 PhysXJoint::getPosition()
	{
		auto jt = (PxD6Joint*)pJoint;
		PxTransform tr = jt->getDrivePosition();
		return Vec3Cvt(tr.p);
	}


	void PhysXJoint::getLocalFrame(int id, glm::vec3& t, glm::quat& r)
	{
		auto lp = // pJoint->getLocalPose(id == 0 ? PxJointActorIndex::eACTOR0: PxJointActorIndex::eACTOR1);
			id == 0 ? localPoseA : localPoseB;
		r = QuatCvt(lp.q);
		PxVec3& pt = lp.p;
		t = glm::vec3(pt.x, pt.y, -pt.z);
		//
	}

	void PhysXJoint::setLocalFrame(int id, const glm::vec3& t, const glm::quat& r)
	{
		physx::PxTransform lp;
		lp.p = *(PxVec3*)&t; lp.p.z = -lp.p.z;
		lp.q = QuatCvt(r);
		pJoint->setLocalPose(id == 0 ? PxJointActorIndex::eACTOR0 : PxJointActorIndex::eACTOR1, lp);
	}

	void PhysXJoint::lockRtt(bool lk, glm::vec3 angLimit)
	{

		if (lk && !rttlocked) {
			PxD6Joint* j = (PxD6Joint*)pJoint;
			glm::bvec3 lk{ angLimit.x == 0, angLimit.y == 0 , angLimit.z == 0 };
			j->setMotion(PxD6Axis::eSWING1, lk.x? PxD6Motion::eLOCKED: PxD6Motion::eLIMITED);
			j->setMotion(PxD6Axis::eSWING2, lk.y ? PxD6Motion::eLOCKED : PxD6Motion::eLIMITED);
			j->setMotion(PxD6Axis::eTWIST, lk.z ? PxD6Motion::eLOCKED : PxD6Motion::eLIMITED);

			if (!lk.x) j->setTwistLimit(PxJointAngularLimitPair(-angLimit.x, angLimit.x));
			if (!lk.y && !lk.z) j->setPyramidSwingLimit(PxJointLimitPyramid(-angLimit.y,angLimit.y,-angLimit.z,angLimit.z));
			j->setDrive(PxD6Drive::eSWING, PxD6JointDrive(10000, 100, FLT_MAX, true));
			j->setDrive(PxD6Drive::eTWIST, PxD6JointDrive(10000, 100, FLT_MAX, true));
			j->setDrive(PxD6Drive::eSLERP, PxD6JointDrive(10000, 100, FLT_MAX, true));
		}
		else if (!lk && rttlocked)
			createJointInternal();
		rttlocked = lk;
	}

	PhysXMan::PhysXMan() :MMDPhysics()
	{
		gPxMan = this;
	}

	PhysXMan::~PhysXMan()
	{
#if USE_DIRECT_GPU
		cleanupDirectGPU();
#endif
		Destroy();
	}

	bool PhysXMan::Create()
	{
		if (!gFoundation) {
			ownWorld = true;
			gFoundation = PxCreateFoundation(PX_PHYSICS_VERSION, gAllocator, gErrorCallback);
			gPvd = PxCreatePvd(*gFoundation);
			PxPvdTransport* transport = PxDefaultPvdSocketTransportCreate("127.0.0.1", 5725, 10);
			pxScale.length = PHY_GRAVITY_MUL;			pxScale.speed = 9.81f * PHY_GRAVITY_MUL;
#if 0
			gOmniPvd = PxCreateOmniPvd(*gFoundation);
			if (!gOmniPvd)
			{
				printf("Error : could not create PxOmniPvd!");
				return  false ;
			}
			OmniPvdWriter* omniWriter = gOmniPvd->getWriter();
			if (!omniWriter)
			{
				printf("Error : could not get an instance of PxOmniPvdWriter!");
				return  false ;
			}
			OmniPvdFileWriteStream* fStream = gOmniPvd->getFileWriteStream();
			if (!fStream)
			{
				printf("Error : could not get an instance of PxOmniPvdFileWriteStream!");
				return  false ;
			}
			fStream->setFileName(gOmniPvdPath);
			omniWriter->setWriteStream(static_cast<OmniPvdWriteStream&>(*fStream));
#endif



#if PHY_ST_TGS
			gPvd->connect(*transport, PxPvdInstrumentationFlag::eALL);
			gPxPhysics = PxCreatePhysics(PX_PHYSICS_VERSION, *gFoundation, pxScale, true, gPvd);
			PxInitExtensions(*gPxPhysics, gPvd);

			PxCudaContextManager* cudaContextManager = NULL;
			if (PxGetSuggestedCudaDeviceOrdinal(gFoundation->getErrorCallback()) >= 0)
			{
				// initialize CUDA
				PxCudaContextManagerDesc cudaContextManagerDesc;
				cudaContextManager = PxCreateCudaContextManager(*gFoundation, cudaContextManagerDesc, PxGetProfilerCallback());
				if (cudaContextManager && !cudaContextManager->contextIsValid())
				{
					cudaContextManager->release();
					cudaContextManager = NULL;
				}
			}
			if (cudaContextManager == NULL)
			{
				PxGetFoundation().error(PxErrorCode::eINVALID_OPERATION, PX_FL, "Failed to initialize CUDA!\n");
			}
			PxSceneDesc sceneDesc(gPxPhysics->getTolerancesScale());
			sceneDesc.gravity = PxVec3(0.0f, -9.81f * PHY_GRAVITY_MUL, 0.0f);
			gDispatcher = PxDefaultCpuDispatcherCreate(8);
			sceneDesc.cpuDispatcher = gDispatcher;
			sceneDesc.filterShader = filterShader;// PxDefaultSimulationFilterShader;
			sceneDesc.cudaContextManager = cudaContextManager;
			sceneDesc.staticStructure = PxPruningStructureType::eDYNAMIC_AABB_TREE;
			sceneDesc.flags |= PxSceneFlag::eENABLE_PCM;
			sceneDesc.flags |= PxSceneFlag::eENABLE_GPU_DYNAMICS;
			sceneDesc.broadPhaseType = PxBroadPhaseType::eGPU;
			sceneDesc.solverType = PxSolverType::eTGS;
#elif PHY_GPU	//WHY GPU SLOW?
			gPvd->connect(*transport, PxPvdInstrumentationFlag::ePROFILE);
			gPxPhysics = PxCreatePhysics(PX_PHYSICS_VERSION, *gFoundation, pxScale, true, gPvd); //false, nullptr);//,// 0, gOmniPvd);
			PxCudaContextManagerDesc cudaDesc;
			gCudaContextManager = PxCreateCudaContextManager(*gFoundation, cudaDesc,
				//&gCustomProfilerCallback,1
				PxGetProfilerCallback(), 0
			);	//Create the CUDA context manager, required for GRB to dispatch CUDA kernels.
			if (gCudaContextManager)
			{
				if (!gCudaContextManager->contextIsValid())
					PX_RELEASE(gCudaContextManager);
			}
			PxSceneDesc sceneDesc(gPxPhysics->getTolerancesScale());

			sceneDesc.gravity = PxVec3(0.0f, -9.81f * PHY_GRAVITY_MUL, 0.0f);
			gDispatcher = PxDefaultCpuDispatcherCreate(8);			//Create a CPU dispatcher using 4 worther threads
			sceneDesc.cpuDispatcher = gDispatcher;
			sceneDesc.filterShader = filterShader; //PxDefaultSimulationFilterShader;
			sceneDesc.cudaContextManager = gCudaContextManager;		//Set the CUDA context manager, used by GRB.
			sceneDesc.flags |= PxSceneFlag::eENABLE_GPU_DYNAMICS;	//Enable GPU dynamics - without this enabled, simulation (contact gen and solver) will run on the CPU.
			sceneDesc.flags |= PxSceneFlag::eENABLE_PCM;			//Enable PCM. PCM NP is supported on GPU. Legacy contact gen will fall back to CPU
			//sceneDesc.flags |= PxSceneFlag::eREQUIRE_RW_LOCK;
			sceneDesc.flags |= PxSceneFlag::eENABLE_STABILIZATION;//May cuda ERROR when sleep	//Improve solver stability by enabling post-stabilization.
			sceneDesc.broadPhaseType = PxBroadPhaseType::eGPU;		//Enable GPU broad phase. Without this set, broad phase will run on the CPU.
			sceneDesc.gpuMaxNumPartitions = 8;						//Defines the maximum number of partitions used by the solver. Only power-of-2 values are valid.
			if (USE_DIRECT_GPU)
				sceneDesc.flags |= PxSceneFlag::eENABLE_DIRECT_GPU_API;
			//sceneDesc.staticStructure = PxPruningStructureType::eDYNAMIC_AABB_TREE;

			sceneDesc.gpuDynamicsConfig.tempBufferCapacity *= PHY_RBS_MUL;
			sceneDesc.gpuDynamicsConfig.maxRigidContactCount *= PHY_RBS_MUL;
			sceneDesc.gpuDynamicsConfig.maxRigidPatchCount *= PHY_RBS_MUL;
			sceneDesc.gpuDynamicsConfig.heapCapacity *= PHY_RBS_MUL;  //or internal error 700
			sceneDesc.gpuDynamicsConfig.foundLostPairsCapacity *= PHY_RBS_MUL;
			sceneDesc.gpuDynamicsConfig.foundLostAggregatePairsCapacity *= 2;
			//sceneDesc.gpuDynamicsConfig.maxParticleContacts *= 8;
			sceneDesc.gpuDynamicsConfig.collisionStackSize *= 2;
#else

			gPvd->connect(*transport, PxPvdInstrumentationFlag::eALL);

			gPxPhysics = PxCreatePhysics(PX_PHYSICS_VERSION, *gFoundation, pxScale, true, gPvd);
			PxInitExtensions(*gPxPhysics, gPvd);
			PxSceneDesc sceneDesc(gPxPhysics->getTolerancesScale());
			sceneDesc.gravity = PxVec3(0.0f, -9.81f * PHY_GRAVITY_MUL, 0.0f);
			//sceneDesc.bounceThresholdVelocity = 1.2f * pxScale.speed;
			gDispatcher = PxDefaultCpuDispatcherCreate(20);
			if (USE_CCD) sceneDesc.flags |= PxSceneFlag::eENABLE_CCD;
			sceneDesc.cpuDispatcher = gDispatcher;
			sceneDesc.filterShader = filterShader; //PxDefaultSimulationFilterShader;//
			//sceneDesc.filterCallback = &gContactFilterCallback;
			//sceneDesc.broadPhaseType = PxBroadPhaseType::ePABP;

#endif
			//sceneDesc.contactModifyCallback = &gContactModifyCallback;
			gPxScene = gPxPhysics->createScene(sceneDesc);


			//    m_scene->setVisualizationParameter(px::PxVisualizationParameter::eCULL_BOX, 1.0f);
			PxPvdSceneClient* pvdClient = gPxScene->getScenePvdClient();
			if (pvdClient)
			{
				pvdClient->setScenePvdFlag(PxPvdSceneFlag::eTRANSMIT_CONSTRAINTS, true);
				pvdClient->setScenePvdFlag(PxPvdSceneFlag::eTRANSMIT_CONTACTS, true);
				pvdClient->setScenePvdFlag(PxPvdSceneFlag::eTRANSMIT_SCENEQUERIES, true);
			}

			//SUBSTEP
			// Storage and synchronization for substepping.
			gStepContext.taskPool = reinterpret_cast<SubstepCompletionTask*>(malloc(NUM_SUBSTEPS * sizeof(SubstepCompletionTask)));
			gStepContext.completionSync = syncCreate();
#if HAS_GROUND
			gMaterial = gPxPhysics->createMaterial(
				//100000, 1000000,0.f
				1.f, 1.f, 0.1f
				//1000.f, 1000.f, 0.f
			);

			PxRigidStatic* groundPlane = PxCreatePlane(*gPxPhysics, PxPlane(0, 1, 0, 0), *gMaterial);
			setShapeColFilter(groundPlane, groundPlane, 0x10000, -1, 0);	gPxScene->addActor(*groundPlane);
 			groundPlane->userData = &groundUserData;
			groundPlane->setActorFlag(PxActorFlag::eVISUALIZATION, false);
			//groundPlane = PxCreatePlane(*gPhysics, PxPlane(0, -1, 0, WALL_RADIUS*2), *gMaterial);
			//setShapeColFilter(groundPlane, groundPlane, 0, -1, 0);		gPxScene->addActor(*groundPlane);

			//glm::vec3 Wnorm[] = { {0, 0, -1},{0, 0, 1}, { -1,0, 0},{1, 0, 0}, };
			//for (int i = 0; i < 4; i++)
			//{
			//	auto norm = Wnorm[i];
			//	PxRigidStatic* groundPlane = PxCreatePlane(*gPxPhysics, PxPlane(norm.x, norm.y, -norm.z, WALL_RADIUS), *gMaterial);
			//	setShapeColFilter(groundPlane, groundPlane, 0xF000, -1, 0);
			//	gPxScene->addActor(*groundPlane);
			//	//static_cast<MMDFilterCallback*>(m_filterCB.get())->m_nonFilterProxy.push_back(m_groundRBW[i]->getBroadphaseProxy());
			//}
#endif

			gPxScene->setSimulationEventCallback(&collisionCallback);
#if USE_DIRECT_GPU
			initDirectGPU();
#endif
		}



		return true;
	}

	MMDRigidBody*  PhysXMan::newRigidBody()
	{
		return new PhysXRigidBody(this);
	}

	MMDJoint* PhysXMan::newJoint()
	{
		return new PhysXJoint();
	}

	void PhysXMan::senPhyDbgVisual(bool show)
	{
		phyDbgView = show;
#if SABA_USE_PHYSX
		float val = show ? 1.0f : 0.0f;
		gPxScene->setVisualizationParameter(PxVisualizationParameter::eSCALE, show ? 1.0f : 0.0f);
		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eACTOR_AXES, val);
		gPxScene->setVisualizationParameter(PxVisualizationParameter::eWORLD_AXES, val);
		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eBODY_AXES, val);
		gPxScene->setVisualizationParameter(PxVisualizationParameter::eBODY_MASS_AXES, val);
#if DBG_VISUAL_ONLY_SET_ONES   //|| DBG_VISUAL_ONLY_DYNRB1
		gPxScene->setVisualizationParameter(PxVisualizationParameter::eBODY_LIN_VELOCITY, val);
		 gPxScene->setVisualizationParameter(PxVisualizationParameter::eBODY_ANG_VELOCITY, val);
#endif

		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eCOLLISION_STATIC, val);
		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eCOLLISION_DYNAMIC, val);
		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eCOLLISION_COMPOUNDS, val);
		 gPxScene->setVisualizationParameter(PxVisualizationParameter::eCOLLISION_SHAPES, val);
		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eCOLLISION_FNORMALS, val);

		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eCOLLISION_AXES, val);
		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eCOLLISION_EDGES, val);
		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eCOLLISION_AABBS, val);
		gPxScene->setVisualizationParameter(PxVisualizationParameter::eJOINT_LIMITS, val*2);
		gPxScene->setVisualizationParameter(PxVisualizationParameter::eJOINT_LOCAL_FRAMES, val);
		//gPxScene->setVisualizationParameter(PxVisualizationParameter::eSDF, val);
#endif



		if (show) {
			//gOmniPvd->startSampling();
		}
		else
		{
			//gPxPhysics->release();
			//gOmniPvd->release();
		}
	}

	void PhysXMan::Destroy()
	{

		if (ownWorld && 0) {
			//SUBSTEP

			syncRelease(gStepContext.completionSync);
			free(gStepContext.taskPool);
			gMaterial->release();
			PX_RELEASE(gPxScene);
			PX_RELEASE(gDispatcher);
			PX_RELEASE(gPxPhysics);

			if (gPvd)
			{
				PxPvdTransport* transport = gPvd->getTransport();
				gPvd->release();	gPvd = NULL;
				PX_RELEASE(transport);
			}

			PX_RELEASE(gCudaContextManager);
			PX_RELEASE(gFoundation);
		}
		//gOmniPvd->release();
	}

	void PhysXMan::updateStart(float time)
	{

		assert(subStepStarted == false);
		if (ownWorld) {



#if PX_USE_ASYNC_SUB_STEP
			using namespace physx::SnippetUtils;
			// Initialize the substepping context.
			syncReset(gStepContext.completionSync);
			gStepContext.nbSubstepsFinished = 0;
			gStepContext.nbTasksDestroyed = 0;

			// Start the first substep, then wait for the last one to finish.
			subStepStarted = true;
			startNextSubstep(this, time / PHYSX_SIM_STEP);

#else
			CPU_COUNT_B(phy);
			for (int i = 0; i < PHYSX_SIM_STEP; i++) {
				MMDPhysics::stepTimeMul = MMDPhysics::phyTimeMul / PHYSX_SIM_STEP; MMDPhysics::stepTimeMulInv = 1.0 / MMDPhysics::stepTimeMul;
				MMDPhysics::curStep = i; MMDPhysics::stepCount = PHYSX_SIM_STEP;
				if (cbOnPhysicsStep)
					cbOnPhysicsStep(i, PHYSX_SIM_STEP);
				MMDPhysics::stepTimeMul = MMDPhysics::phyTimeMul; MMDPhysics::stepTimeMulInv = 1.0 / MMDPhysics::stepTimeMul;

				gPxScene->simulate(time / PHYSX_SIM_STEP);

				gPhyTime += time / PHYSX_SIM_STEP;
				gPxScene->fetchResults(i < PHYSX_SIM_STEP - 1 ? 1 : true);

			}
			CPU_COUNT_E(phy);
#endif

			//PxScene* scene;
			//PxGetPhysics().getScenes(&scene, 1);
			//PxU32 nbActors = scene->getNbActors(PxActorTypeFlag::eRIGID_DYNAMIC | PxActorTypeFlag::eRIGID_STATIC);
			//if (nbActors)
			//{
			//	const PxVec3 dynColor(1.0f, 0.5f, 0.25f);

			//	std::vector<PxRigidActor*> actors(nbActors);
			//	scene->getActors(PxActorTypeFlag::eRIGID_DYNAMIC | PxActorTypeFlag::eRIGID_STATIC, reinterpret_cast<PxActor**>(&actors[0]), nbActors);
			//	//Snippets::renderActors(&actors[0], static_cast<PxU32>(actors.size()), true, dynColor);
			//}
		}
#if USE_DIRECT_GPU
			if (m_directGPUEnabled && gCudaContextManager) {
				// Read current poses for rendering
				gPxScene->getDirectGPUAPI().getRigidDynamicData(
					reinterpret_cast<void*>(m_rbPosesD),
					reinterpret_cast<const PxRigidDynamicGPUIndex*>(m_rbIndicesD),
					PxRigidDynamicGPUAPIReadType::eGLOBAL_POSE,
					m_rbIndices.size()
				);

				PxCudaContext* cudaContext = gCudaContextManager->getCudaContext();
				cudaContext->memcpyDtoH(&m_rbPoses[0], m_rbPosesD,
					m_rbIndices.size() * sizeof(PxTransform));
			}
#endif
	}

	void PhysXMan::updateWait()
	{
#if PX_USE_ASYNC_SUB_STEP
		if (subStepStarted) {
			subStepStarted = false;
			syncWait(gStepContext.completionSync);
		}
#else

#endif
	}



	void PhysXMan::clearForces()
	{
		return;
		PxU32 numActors = gPxScene->getNbActors(PxActorTypeFlag::eRIGID_DYNAMIC);
		std::vector<PxRigidDynamic*> actors(numActors);
		gPxScene->getActors(PxActorTypeFlag::eRIGID_DYNAMIC, reinterpret_cast<PxActor**>(&actors[0]), numActors);

		for (PxRigidDynamic* actor : actors) if(!(actor->getRigidBodyFlags() & PxRigidBodyFlag::eKINEMATIC)){
			// Set linear and angular velocity to zero
			actor->setLinearVelocity(PxVec3(0.0f, 0.0f, 0.0f));
			actor->setAngularVelocity(PxVec3(0.0f, 0.0f, 0.0f));

			// If you also want to clear forces, you can use:
			actor->clearForce(PxForceMode::eACCELERATION);
			actor->clearForce(PxForceMode::eVELOCITY_CHANGE);
			actor->clearTorque(PxForceMode::eACCELERATION);
			actor->clearTorque(PxForceMode::eVELOCITY_CHANGE);
		}

	}

	void PhysXMan::getLines(DebugLine*& ptr, int& num)
	{

		num = gPxScene->getRenderBuffer().getNbLines();
		ptr = (DebugLine*)gPxScene->getRenderBuffer().getLines();

		auto tn = gPxScene->getRenderBuffer().getNbTriangles();
	}
	void PhysXMan::setGravity(glm::vec3 g)
	{
		gPxScene->setGravity(PxVec3(g.x ,g.y,-g.z));
	}


#if PHYSX_SABA_INFLATE
	using namespace physx::ExtGpu;

	namespace {
		PxReal pressure = 60.0f; //Pressure is used to compute the target volume of the inflatable by scaling its rest volume

		PxReal stretchStiffness = 100000.f;
		PxReal shearStiffness = stretchStiffness*.5f;
		PxReal bendStiffness = stretchStiffness*.5f;
	}

	PxParticleClothBuffer* initInflatable(const std::vector<glm::vec4>& verts, const std::vector<uint32_t>& indices, const PhyMeshData &pmd, PxPBDParticleSystem*&psystem)
	{

		PxCudaContextManager* cudaContextManager = gPxScene->getCudaContextManager();
		if (cudaContextManager == NULL)
			return 0;

		PxArray<PxVec4> vertices;
		vertices.resize(verts.size());
		for (PxU32 i = 0; i < verts.size(); ++i)
			vertices[i] =  PxVec4(verts[i].x, verts[i].y, verts[i].z, verts[i].w);

		const PxU32 numParticles = vertices.size();
		// Cook cloth
		PxParticleClothCooker* cooker = PxCreateParticleClothCooker(vertices.size(), vertices.begin(), indices.size(), (PxU32*)&indices[0],
			PxParticleClothConstraint::eTYPE_ALL,
			//PxParticleClothConstraint::eTYPE_HORIZONTAL_CONSTRAINT | PxParticleClothConstraint::eTYPE_VERTICAL_CONSTRAINT | PxParticleClothConstraint::eTYPE_DIAGONAL_CONSTRAINT,
			PxVec3(0.0f, 1.0f, 0.0f), 90.0f * PxTwoPi / 360.0f
		);
		cooker->cookConstraints();
		cooker->calculateMeshVolume();
		float volume = cooker->getMeshVolume();
		// Apply cooked constraints to particle springs
		PxU32 constraintCount = cooker->getConstraintCount();
		PxParticleClothConstraint* constraintBuffer = cooker->getConstraints();
		PxArray<PxParticleSpring> springs;
		springs.reserve(constraintCount);
		for (PxU32 i = 0; i < constraintCount; i++)
		{
			const PxParticleClothConstraint& c = constraintBuffer[i];
			PxReal stiffness = 0.0f;
			switch (c.constraintType)
			{
			case PxParticleClothConstraint::eTYPE_INVALID_CONSTRAINT:
				continue;
			case PxParticleClothConstraint::eTYPE_HORIZONTAL_CONSTRAINT:
			case PxParticleClothConstraint::eTYPE_VERTICAL_CONSTRAINT:
				stiffness = stretchStiffness;
				break;
			case PxParticleClothConstraint::eTYPE_DIAGONAL_CONSTRAINT:
				stiffness = shearStiffness;
				break;
			case PxParticleClothConstraint::eTYPE_BENDING_CONSTRAINT:
				stiffness = bendStiffness;
				break;
			default:
				PX_ASSERT("Invalid cloth constraint generated by PxParticleClothCooker");
			}

			PxParticleSpring spring;
			spring.ind0 = c.particleIndexA;
			spring.ind1 = c.particleIndexB;
			spring.stiffness = stiffness;
			spring.damping = stretchStiffness/100;
			spring.length = c.length;
			springs.pushBack(spring);
		}
		const PxU32 numSprings = springs.size();

		// Read triangles from cooker
		const PxU32 numTriangles = cooker->getTriangleIndicesCount() / 3;
		const PxU32* triangles = cooker->getTriangleIndices();

		// Material setup
		PxPBDMaterial* defaultMat = gPxPhysics->createPBDMaterial(pmd.friction, 0.5f, 1e+6f, 0.001f, 0.5f, 0.005f, 0.05f, 0.f, 0.f);

		PxPBDParticleSystem* particleSystem = gPxPhysics->createPBDParticleSystem(*cudaContextManager);

		// General particle system setting
		float restOffset = pmd.restOffset;
		particleSystem->setRestOffset(restOffset * 2);
		particleSystem->setContactOffset(restOffset * 2 + restOffset);
		particleSystem->setParticleContactOffset(restOffset + restOffset);
		particleSystem->setSolidRestOffset(restOffset);
		particleSystem->setFluidRestOffset(0.0f);

		gPxScene->addActor(*particleSystem);

		// Create particles and add them to the particle system
		const PxU32 particlePhase = particleSystem->createPhase(defaultMat,
			PxParticlePhaseFlags(0)
			//PxParticlePhaseFlags(PxParticlePhaseFlag::eParticlePhaseSelfCollideFilter | PxParticlePhaseFlag::eParticlePhaseSelfCollide)
		);
		PxU32* phases = cudaContextManager->allocPinnedHostBuffer<PxU32>(numParticles);
		PxVec4* positionInvMass = cudaContextManager->allocPinnedHostBuffer<PxVec4>(numParticles);
		PxVec4* velocity = cudaContextManager->allocPinnedHostBuffer<PxVec4>(numParticles);


		for (PxU32 v = 0; v < numParticles; v++)
		{
			positionInvMass[v] = vertices[v];
			velocity[v] = PxVec4(0.0f, 0.0f, 0.0f, 0.0f);
			phases[v] = particlePhase;
		}

		PxParticleVolumeBufferHelper* volumeBuffers = PxCreateParticleVolumeBufferHelper(1, numTriangles, cudaContextManager); //Volumes are optional. They are used to accelerate scene queries, e. g. to support picking.
		PxParticleClothBufferHelper* clothBuffers = PxCreateParticleClothBufferHelper(1, numTriangles, numSprings, numParticles, cudaContextManager);

		clothBuffers->addCloth(0.0f, cooker->getMeshVolume(), pressure, triangles, numTriangles, springs.begin(), numSprings, positionInvMass, numParticles);
		volumeBuffers->addVolume(0, numParticles, triangles, numTriangles);
		cooker->release();

		ExtGpu::PxParticleBufferDesc bufferDesc;
		bufferDesc.maxParticles = numParticles;
		bufferDesc.numActiveParticles = numParticles;
		bufferDesc.positions = positionInvMass;
		bufferDesc.velocities = velocity;
		bufferDesc.phases = phases;
		bufferDesc.maxVolumes = volumeBuffers->getMaxVolumes();
		bufferDesc.numVolumes = volumeBuffers->getNumVolumes();
		bufferDesc.volumes = volumeBuffers->getParticleVolumes();

		PxParticleClothPreProcessor* clothPreProcessor = PxCreateParticleClothPreProcessor(cudaContextManager);

		PxPartitionedParticleCloth output;
		const PxParticleClothDesc& clothDesc = clothBuffers->getParticleClothDesc();
		clothPreProcessor->partitionSprings(clothDesc, output);
		clothPreProcessor->release();

		auto userClothBuffer = physx::ExtGpu::PxCreateAndPopulateParticleClothBuffer(bufferDesc, clothDesc, output, cudaContextManager);
		particleSystem->addParticleBuffer(userClothBuffer);

		clothBuffers->release();
		volumeBuffers->release();

		cudaContextManager->freePinnedHostBuffer(positionInvMass);
		cudaContextManager->freePinnedHostBuffer(velocity);
		cudaContextManager->freePinnedHostBuffer(phases);

		//ckadd
		particleSystem->setSimulationFilterData(PxFilterData((PxU32)0x1000 + 0, 0x00000001, ~0x00000001, 0));
		//gParticleSystem->setParticleFlag(PxParticleFlag::eFULL_DIFFUSE_ADVECTION, true);
		//gParticleSystem->setMaxDepenetrationVelocity(100000.f);
		psystem = particleSystem;

		return userClothBuffer;
	}

	void* PhysXMan::createInflate( PhyMeshData& pd)
	{
		PxPBDParticleSystem* sys{};
		PxParticleClothBuffer* userClothBuffer =
			initInflatable(pd.v4s, pd.idxs, pd ,sys);
		pd.ptcSystem=(void*)sys;

		return userClothBuffer;
	}
	void PhysXMan::updateInflate(const PhyMeshData& pd, std::vector< InflateBufBind> *bindArray)
	{

		PxParticleClothBuffer* userBuffer = (PxParticleClothBuffer * )pd.ptcBuf;
		PxVec4* positions = userBuffer->getPositionInvMasses();

		const PxU32 numParticles = userBuffer->getNbActiveParticles();

		PxScene* scene;
		PxGetPhysics().getScenes(&scene, 1);
		PxCudaContextManager* cudaContextManager = scene->getCudaContextManager();

		cudaContextManager->acquireContext();

		PxCudaContext* cudaContext = cudaContextManager->getCudaContext();



			auto pb = new glm::vec4[numParticles];
			cudaContext->memcpyDtoH(pb, CUdeviceptr(positions), sizeof(PxVec4) * numParticles);


			for (int i = 0; i < numParticles; i++) {
				float* p = (float*)((char*)pd.posBuf + pd.v4ids[i]*pd.stride) ;
				glm::vec3 pp = glm::vec3(pb[i].x, pb[i].y, -pb[i].z);
				//glm::mat4 m = ((PhysXRigidBody*)pd.v4rbs[i])->GetTransform();
				//glm::mat4 invm = glm::inverse(m);
				//pp = invm * glm::vec4(pp, 1.f);
				*(glm::vec3*)p = pp;
				//p[0] = pb[i].x;
				//p[1] = pb[i].y;
				//p[2] = -pb[i].z;
				//		buffer->Vertices[i].Pos.set(ptcBuf[i].x, ptcBuf[i].y, -ptcBuf[i].z);
			}

			delete[]pb;
#if 1

		if (bindArray)
		{
			auto& ba = *bindArray;
			//S3DVertex* vb = (S3DVertex*)Mesh->getMeshBuffer(0)->getVertices();
			PxParticleAttachmentBuffer* pab = PxCreateParticleAttachmentBuffer(*(PxParticleClothBuffer*)pd.ptcBuf, *(PxPBDParticleSystem*)pd.ptcSystem);
			for (int i = 0; i < ba.size(); i++)	pab->addRigidAttachment( ((PhysXRigidBody*)ba[i].rb)->GetRigidBody(), ba[i].id, *(PxVec3*)&(ba[i].pos ));
			pab->copyToDevice();
			PX_DELETE(pab);
		}
#endif
	}
#else
void* PhysXMan::createInflate( PhyMeshData& pd)
{
	return nullptr;
}
void PhysXMan::updateInflate(const PhyMeshData& pd, std::vector< InflateBufBind>* bindArray)
{
}
#endif
}

#if USE_DIRECT_GPU
void PhysXMan::initDirectGPU() {

	if (!gCudaContextManager || !m_directGPUEnabled) {
		return;
	}

	// Enable direct GPU access in scene
	if (!m_directGPUInitialized) {
		//error gPxScene->getSceneDesc().flags |= PxSceneFlag::eENABLE_DIRECT_GPU_API;
		m_directGPUInitialized = true;
	}

	// Initialize buffers only if we have rigid bodies
	if (m_rbIndices.empty()) {
		return;
	}

	PxCudaContext* cudaContext = gCudaContextManager->getCudaContext();

	// Allocate initial buffer size
	size_t initialSize = std::max(size_t(64), m_rbIndices.size());
	reallocateGPUBuffers(initialSize);

	// Copy initial data
	cudaContext->memcpyHtoD(m_rbIndicesD, m_rbIndices.data(),
		m_rbIndices.size() * sizeof(PxU32));
}

void PhysXMan::cleanupDirectGPU() {
	if (!gCudaContextManager || !m_directGPUEnabled) {
		return;
	}

	PxCudaContext* cudaContext = gCudaContextManager->getCudaContext();

	if (m_rbInitAngVelsD) {
		cudaContext->memFree(m_rbInitAngVelsD);
		m_rbInitAngVelsD = 0;
	}
	if (m_rbInitLinVelsD) {
		cudaContext->memFree(m_rbInitLinVelsD);
		m_rbInitLinVelsD = 0;
	}
	if (m_rbInitPosesD) {
		cudaContext->memFree(m_rbInitPosesD);
		m_rbInitPosesD = 0;
	}
	if (m_rbPosesD) {
		cudaContext->memFree(m_rbPosesD);
		m_rbPosesD = 0;
	}
	if (m_rbIndicesD) {
		cudaContext->memFree(m_rbIndicesD);
		m_rbIndicesD = 0;
	}

	m_rbIndices.clear();
	m_rbGeometries.clear();
	m_rbPoses.clear();
}



void PhysXMan::reallocateGPUBuffers(size_t newSize) {
	if (!m_directGPUEnabled || !gCudaContextManager) {
		return;
	}

	PxCudaContext* cudaContext = gCudaContextManager->getCudaContext();

	// Allocate new buffers
	uint64_t newIndicesD;
	uint64_t newPosesD;
	cudaContext->memAlloc(&newIndicesD, newSize * sizeof(PxU32));
	cudaContext->memAlloc(&newPosesD, newSize * sizeof(PxTransform));

	// Copy existing data if any
	if (m_currentGPUBufferSize > 0) {
		cudaContext->memcpyDtoD(newIndicesD, m_rbIndicesD, m_currentGPUBufferSize * sizeof(PxU32));
		cudaContext->memcpyDtoD(newPosesD, m_rbPosesD, m_currentGPUBufferSize * sizeof(PxTransform));

		// Free old buffers
		cudaContext->memFree(m_rbIndicesD);
		cudaContext->memFree(m_rbPosesD);
	}

	m_rbIndicesD = newIndicesD;
	m_rbPosesD = newPosesD;
	m_currentGPUBufferSize = newSize;
}

void PhysXMan::addRigidBodyToGPU(PxRigidDynamic* rb, const PxGeometry& geometry)
{
	if (!m_directGPUEnabled || !m_directGPUInitialized) {
		return;
	}

	// Ensure GPU buffers are allocated
	if (m_rbIndices.size() >= m_currentGPUBufferSize) {
		size_t newSize = std::max(size_t(64), m_currentGPUBufferSize * 2);
		reallocateGPUBuffers(newSize);
	}

	// Add to host buffers
	PxU32 gpuIndex = rb->getGPUIndex();
	if (gpuIndex != PxU32(-1)) {
		m_rbIndices.push_back(gpuIndex);
		m_rbGeometries.push_back(geometry.getType());
		m_rbPoses.push_back(rb->getGlobalPose());

		// Update GPU buffers
		size_t idx = m_rbIndices.size() - 1;
		gCudaContextManager->getCudaContext()->memcpyHtoD(
			(uint64_t)((char*)m_rbIndicesD + idx * sizeof(PxU32)),
			&gpuIndex,
			sizeof(PxU32)
		);
	}
}
#endif


#if 0// PhysX 对系统内各个刚体施加力，但保持总体重心位置不变 from GPT4o , to check
PxVec3 calculateCenterOfMass(const std::vector<PxRigidBody*>& rigidBodies) {
	float totalMass = 0.0f;
	PxVec3 weightedPositionSum(0.0f, 0.0f, 0.0f);

	for (auto body : rigidBodies) {
		float mass = body->getMass();
		PxVec3 position = body->getGlobalPose().p;
		totalMass += mass;
		weightedPositionSum += position * mass;
	}

	return weightedPositionSum / totalMass;
}
void applyForces(const std::vector<PxRigidBody*>& rigidBodies, const std::vector<PxVec3>& forces) {
	for (size_t i = 0; i < rigidBodies.size(); ++i) {
		rigidBodies[i]->addForce(forces[i], PxForceMode::eFORCE);
	}
}
PxVec3 calculateCompensationForce(const std::vector<PxRigidBody*>& rigidBodies, const std::vector<PxVec3>& forces) {
	PxVec3 totalForce(0.0f, 0.0f, 0.0f);
	PxVec3 totalTorque(0.0f, 0.0f, 0.0f);
	PxVec3 centerOfMass = calculateCenterOfMass(rigidBodies);

	for (size_t i = 0; i < rigidBodies.size(); ++i) {
		PxVec3 r = rigidBodies[i]->getGlobalPose().p - centerOfMass;
		totalForce += forces[i];
		totalTorque += r.cross(forces[i]);
	}

	// Assuming you want to apply this to a single body, find a suitable compensation force
	// Here you can use an iterative solver or a heuristic to find an acceptable solution
	// Simplification:
	return -totalForce; // The simplest form is to equalize the forces (ignoring torques for now)
}
void applyCompensationForce(PxRigidBody* compensationBody, const PxVec3& compensationForce) {
	compensationBody->addForce(compensationForce, PxForceMode::eFORCE);
}

main{
	std::vector<PxRigidBody*> rigidBodies = {/* ... */ };
	std::vector<PxVec3> forces = {/* ... */ };

	// 步骤1：计算初始重心
	PxVec3 initialCenterOfMass = calculateCenterOfMass(rigidBodies);

	// 步骤2：施加力
	applyForces(rigidBodies, forces);

	// 步骤3：计算补偿力
	PxVec3 compensationForce = calculateCompensationForce(rigidBodies, forces);

	// 步骤4：施加补偿力
	PxRigidBody* compensationBody = /* ... */; // 选择一个作为补偿力的刚体
	applyCompensationForce(compensationBody, compensationForce);

	// 可选：实时调整重心位置
	PxVec3 currentCenterOfMass = calculateCenterOfMass(rigidBodies);
	PxVec3 correctionForce = (initialCenterOfMass - currentCenterOfMass) * correctionFactor;
	applyCompensationForce(compensationBody, correctionForce);
}
#endif

#if 0 //TO CHECK
PxVec3 calculateRequiredTorque(PxRigidDynamic* body, const PxVec3& omegaA, const PxVec3& omegaB, float deltaTime) {
	// Calculate angular acceleration
	PxVec3 angularAcceleration = (omegaB - omegaA) / deltaTime;

	// Get the moment of inertia tensor
	PxVec3 inertiaTensor = body->getMassSpaceInertiaTensor();
	PxMat33 worldInertiaTensor = PxMat33(body->getGlobalPose().q) * PxMat33::createDiagonal(inertiaTensor);

	// Calculate required torque
	PxVec3 requiredTorque = worldInertiaTensor.transform(angularAcceleration);

	return requiredTorque;
}
#endif


#endif