﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//

#ifndef SABA_MODEL_MMD_MMDNODE_H_
#define SABA_MODEL_MMD_MMDNODE_H_

#define USE_CPU_ACC 1

#define SABA_NO_PMD 1
#pragma warning(disable : 4265) 

#define SABA_HAS_BASE_ROTATION 0
#ifdef SABA_INVZ
#define MUL_VEC3INVZ			*glm::vec3(1, 1, -1)
#define INV_MARK -
#else
#define MUL_VEC3INVZ			
#define INV_MARK
#endif

#ifdef _WIN32
#define SABA_HAS_SCALE 1
#else
#define SABA_HAS_SCALE 0
#endif
#define SABA_PHYSICS_ASYNC					1		//if not use old bullet engine, now always 1
#define SABA_PHYSICS_FRAMESTEP				3

#define SABA_ONE_WORLD 1
#define PHY_GRAVITY_MUL GRAVITY_MUL  //10.f
#define ALL_NODE_PHYSICS	1
#define MAX_PHO_CONN 16
#define DEBUG_UpdateGlobalTransform	0

#define MMD_NID_MAX 10

#define GlmTr(x,y,z) glm::translate(glm::mat4(1), glm::vec3(x, y, z))
#define GlmRt(x,y,z,deg) glm::rotate(glm::mat4(1),glm::radians(float(deg)), glm::vec3(x, y, z))



#include <helpers/DebugLogLib.h>
#include <helpers/uputils.h>
#include "../../base/unicodeutil.h"
#include <string>

#include <glm/vec3.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/mat4x4.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtx/norm.hpp> 
#include <helpers/cpu_acc_matrix.h>
#include <functional>
#include "MMDPhysics.h"
extern float  gSceneTime, gFrameTime; //set by gamelib
extern float gPhyTime;	//set by MMDPhysics


namespace ualib {
	std::wstring Utf8toWcs(std::string _s);
	std::string	WcstoUtf8(std::wstring _s);

	inline glm::vec3 projectOnTo(glm::vec3 a, glm::vec3 b) {
		return (glm::dot(a, b) / glm::dot(b, b)) * b;
	}
}
#include "helpers/utils/exponential_moving_average.h"

namespace saba
{

	void printMatTRS(glm::mat4 m,const std::wstring s=L"");
	glm::mat4 glmMatSwapYZ(const glm::mat4& m);
	glm::quat getDirRtt(glm::vec3& toDir, glm::vec3& fromDir);

}

namespace saba
{
	struct PMXBone;
	class VMDNodeController;
	class MMDRigidBody;
	struct PhonemeItem {
		float b, e;
		char ph,p1;
		float volAdd;
	};
	struct GD{
		float frameTime = 1 / 60.f;
		int globalTransformUpdateCount = 0;
	};
	extern GD gd;
	//ckadd
	enum MNFlag {
		mnfNone = 0,
		mnfCb = 1,
		mnfFw = 2,
		mnfRight =		8,
		mnfMorphCb =	0x00000010,
		mnfCenter =		0x00000100,
		headIK =		0x00001000,
		handIK =		0x00002000,
		footIK =		0x00004000,
		mnfEyesIK =		0x00008000,
		mnfLegD =		0x00040000,
		mnfSaveVMD =	0x00080000,
		mnfIKCache =	0x00100000,
		mnfLv1 =		0x01000000,
		mnfLv2 =		0x02000000,
	};
	enum MPFlag {
		mpfNone = 0,
		mpfCb = 1,
	};
	enum class EId {
		none = 0,
		root,
		center,
		group,
		yao,
		shdL, shdR,
		armL, armR,
		handL, handR,
		handLf2h, handRf2h,
		//handLf23,	handRf23,
		legL, legR,
		footL, footR,
		catEarL, catEarR,
		ahoge, ahogeT,
		kneeD, armD,
		head,
		eyes,eye,
		upper,
		upper2,
		upper3,
		lower,
		mouth,
		pussy,tail,
		handPts,

		ikHead, headTgt,
		ikArmL, ikArmR,
		ikRingerL, ikFingerR,
		ikFootL, ikFootR,
		ikFootTopL, ikFootTopR,
		ikEyes, eyeTgt,
		opaiAdj,oppai, chikubi,
		camPos,
		music0, music1,
		LWing, RWing,

		ikTemp,
		weighter,//add bone
		end
	};

	struct AtkObjData {
		int nid;
		int objId = 0, objIdLast = 0;
		float lastLockTime = 0;
		int kicked = 999999, leaveCount = 0, stage=0;
		float lockTime = 0.f, firstTTime=0.f,Tdur=0.f;		float lastDis = 99999.f,lastTDis=99999.f, vsc=1.f ,firstTDis= 99999.f;
		float forceMul = 1.0f;
		bool tposLocked = false; glm::vec3 tposNd;
		int grabCount = 0; float grabTime = 0.f;

		MMDJoint* jtGrab{};
		void addKick(int a, int from) {
			kicked += a;
				DP(("addkick %d = %d line %d  o %d", a, kicked, from, objId));
		}
		void resetStatus(bool setLast = false) {
			if (setLast) objIdLast = objId;
			objId = 0; kicked = 0; leaveCount = 0; stage = 0;
			lastDis = 99999.f; firstTDis = 99999.f; lastTDis = 99999.f; vsc = 1.f;
			lockTime = 0.f; firstTTime = 0.f;
			tposLocked = false; 
			
		}
	};
	struct NodeExtData {
		uint32_t flag = 0;
		EId eId = EId::none;
		int ctrlLabel = -1;
		int mid = 0; //fw matrix id
		int fwt1Idx = 0;
		int fwType = 0;
		int fwCount = 0;
		float ndNextFwTime = 0.f;
		float fwIntvl = 0.01f, fwLastFwS = -999.f, disAcc = 0.f;
		float motionRecRttDis = 0.001f;
		glm::vec3 irrSpeed{}, lastPos{}, lastMmdPos{}, realLastPos{}, direction{}, lastFwVel{};
		glm::vec3 lsPos{}, armDtr{}; glm::quat lsRtt{ 0,0,0,1 };	 int lsFrame = -1;
		float lastTime = 0.f;
		MovingSimulator ms;
		//anim when enable 
		bool enableAnim = false;
		float enableTime = -1.f;
		glm::vec3 rttRecursionAdd = { 0,0,0 };
		glm::vec2 pickStartPos = { 0,0 };
		glm::mat4 pickStartTransform;
		glm::quat pickStartAnimRotate;
		ExponentialMovingAverageXYZ* realSpdVec{};
		bool footFw = false;
		uint32_t color = -1;

		~NodeExtData() {
			if (realSpdVec) delete realSpdVec;
		}

	};
	class MMDModel; class PMXModel;
	class MMDNode
	{
	public:
		MMDNode();
		virtual ~MMDNode() {};
		void AddChild(MMDNode* child);
		// アニメーションの前後て呼ぶ
		void BeginUpdateTransform();
		void EndUpdateTransform();

		void UpdateLocalTransform();
		void UpdateGlobalTransform(bool notDynamic = true );
		void UpdateGlobalTransformSelf();
		void UpdateChildTransform(bool notDynamic = true);
		void UpdateChildAbsScale();

		void SetIndex(uint32_t idx) { 			m_index = idx; 		}
		uint32_t GetIndex() const { return m_index; }

		void SetName(const std::string& name, const std::wstring& nameU);
		const std::string& GetName() const { return m_name; }
		const std::wstring& GetNameU() const { return m_nameU; }
		void EnableIK(bool enable) {
			//if (m_enableIK != enable) {
			//	exd.enableTime = exd.lastTime;
			//}
			m_enableIK = enable; 
		}
		void EnableIK_withAnime(bool enable, bool anim = false, float time = 0.f) {

			if (time - exd.enableTime < 0.33f)
			{
				if (time < 0.001f)
					exd.enableTime = -1.f;
				return;
			}
			if (anim && m_enableIK != enable) {
				
				exd.enableTime = time;
			}
			exd.enableAnim = anim;
			m_enableIK = enable;
		}
		virtual bool IsIK() const { return false; }
		bool isRoot() {			return mIsRoot;		}
		void setRoot(bool rt) { mIsRoot = rt; }
		void EnableDeformAfterPhysics(bool enable);

		bool IsDeformAfterPhysics() { return m_isDeformAfterPhysics; }
		
		void SetTranslate(const glm::vec3& t) { m_translate = t; }
		const glm::vec3& GetTranslate() const { return m_translate; }
#if SABA_HAS_BASE_ROTATION
		void SetRotate(const glm::quat& r) { m_rotate = r; }
		const glm::quat& GetRotate() const { return m_rotate; }
#endif
#if SABA_HAS_SCALE
		void SetScale(const glm::vec3& s );
		const glm::vec3& GetScale() const { return m_scale; }
#endif
		//Set relative position 
		void SetAnimationTranslate(const glm::vec3& t, int addSlot = -1) 
		{ 
			if (addSlot < 0) 
				m_animTranslate = t; 
			else m_animTranslateAdd[addSlot] = t;		
		}
		void setAnimToGlobalPos(glm::vec3 pos) {
			glm::mat4 mp = GetParent() ? GetParent()->GetGlobalTransform() : glm::mat4(1);
			glm::mat4 mi = glm::inverse(mp);
			pos = glm::vec3(mi * glm::vec4(pos, 1));
			SetAnimationTranslate(pos - GetTranslate());
		}
		const glm::vec3& GetAnimationTranslate(int addSlot = -1) const //not final translate, just addon to base translate
		{ 
			return (addSlot < 0) ? m_animTranslate : m_animTranslateAdd[addSlot];
		};

		void SetAnimationRotate(const glm::quat& q, int addSlot = -1) {
			//if compile related NAN , check nan here
			if (addSlot < 0) m_animRotate = q; else m_animRotateAdd[addSlot] = q;
			if (debug) __debugbreak(); 
		}
		void SetRbRotate(const glm::quat& q);
		const glm::quat& GetAnimationRotate(int addSlot=-1) const { return (addSlot < 0)?m_animRotate: m_animRotateAdd[addSlot]; }
		
		//Set relative TR by matrix
		void setAnimationMatrix(glm::mat4 m, bool rtt=true) 
		{
			auto r = glm::quat(m);
			glm::vec3 t = m[3];
			if (rtt) SetAnimationRotate(r);
			SetAnimationTranslate(t - GetTranslate());
		}
		glm::vec3 AnimateTranslate() const { return m_animTranslate + m_translate; }  //true translate to parent
		const glm::quat& AnimateRotate() const { 
#if SABA_HAS_BASE_ROTATION
			return m_animRotate * m_rotate; 
#else
			return m_animRotate;
#endif
		}
		glm::vec3 getGlobalPos() const { return glm::vec3(GetGlobalTransform()[3]); }
		glm::vec3 getIfRbPos() const {	return rb0 ? rb0->getPosition() : getGlobalPos();	}
		glm::mat4 getIfRbMat() const { return rb0 ? rb0->GetTransform() : GetGlobalTransform(); }
		void SetIKRotate(const glm::quat& ikr) { m_ikRotate = ikr; }
		const glm::quat& GetIKRotate() const { return m_ikRotate; }

		MMDNode* GetParent() const { return m_parent; }
		MMDNode* GetChild() const { return m_child; }
		MMDNode* GetNext() const { return m_next; }
		MMDNode* GetPrev() const { return m_prev; }

		void SetLocalTransform(const glm::mat4& m) { m_local = m; }
		const glm::mat4& GetLocalTransform() const { return m_local; }

		void SetGlobalTransform(const glm::mat4& m) { m_global = m; }
		virtual const glm::mat4& GetGlobalTransform() const { return m_global; }  //include scale, but rb transform not include
		glm::mat4* GlobalTransformPtr() { return &m_global; }
		void CalculateInverseInitTransform();
		const glm::mat4& GetInverseInitTransform() const { return m_inverseInit; }

		// ノードの初期化時に呼び出す
		void SaveInitialTRS()
		{
			m_initTranslate = m_translate;
#if SABA_HAS_BASE_ROTATION
			m_initRotate = m_rotate;
#endif
#if SABA_HAS_SCALE
			m_initScale = m_scale;
#endif
			assert(m_initRotate.x == 0.f);
			assert(m_initRotate.y == 0.f);
			assert(m_initRotate.z == 0.f);
		}
		void LoadInitialTRS()
		{
			m_translate = m_initTranslate;
#if SABA_HAS_BASE_ROTATION
			m_rotate = m_initRotate;
#endif
#if SABA_HAS_SCALE
			m_scale = m_initScale;
#endif
			//m_animTranslate = glm::vec3(0);
			//m_animRotate = glm::quat(1, 0, 0, 0);
		}
		const glm::vec3& GetInitialTranslate() const { return m_initTranslate; }  //relative to parent
		const glm::quat& GetInitialRotate() const { return m_initRotate; }
		const glm::vec3& GetInitialScale() const { return m_initScale; }

		void SetInitialScale(const glm::vec3& s);//ckadd

		void SaveBaseAnimation()
		{
			m_baseAnimTranslate = m_animTranslate;
			m_baseAnimRotate = m_animRotate;
		}

		void LoadBaseAnimation()
		{
			m_animTranslate = m_baseAnimTranslate;
			m_animRotate = m_baseAnimRotate;
		}
		void SaveAnimation()
		{
			savedTranslate = m_animTranslate;
			savedRotate = m_animRotate;
			savedLocal = m_local;
			savedGlobal = m_global;
			savedGlobalAnim = mGlobalAnim;
			savedLocalAnim = mLocalAnim;
		}

		void LoadAnimation()
		{
			m_animTranslate = savedTranslate;
			m_animRotate = savedRotate;
			m_local = savedLocal;
			m_global = savedGlobal;
			mGlobalAnim = savedGlobalAnim;
			mLocalAnim = savedLocalAnim;
		}
		void ClearBaseAnimation()
		{
			m_baseAnimTranslate = glm::vec3(0);
			m_baseAnimRotate = glm::quat(1, 0, 0, 0);
		}

		const glm::vec3& GetBaseAnimationTranslate() const { return m_baseAnimTranslate; };
		const glm::quat& GetBaseAnimationRotate() const { return m_baseAnimRotate; }


		//ckadd
		PMXModel* model{};
		uint32_t sortedIdx = -1;
		NodeExtData exd;
		AtkObjData* atk{}; bool atkLocking = false;
		bool ikIgnoreAnim = false;
		glm::quat rttM=glm::quat(1, 0, 0, 0);
		glm::vec3 crossM{},posOffset;
		float angleM{};
		VMDNodeController* nodeCtrl{};
		MMDRigidBody* rb0{}, * rbAtk{}, * lastConnRb[MAX_PHO_CONN]{};

		MMDJoint* joint2p() { return rb0->jtsToParent[0]; }
		glm::vec3 rbPos() { return rb0->getPosition(); }
		std::vector<MMDRigidBody*> rbs;
		int phyAnim = 0;
		float phyAnimRatT = 1.f, phyAnimRatR = 1.f, phyAnimCD=0, phyAnimTime=1;
		void setPhyAnim(float time) { phyAnimCD = phyAnimTime = time; }
		void phyAnimReset() {
			phyAnim = 0; 
			if (rb0) rb0->jtsToParent[0]->setDriveMul(0);
		}
		float phyAnimRatio() { return  (1.f - phyAnimCD) / phyAnimTime; }
		int rbcc = 0, debug = 0;
		bool lowerNd = false;
		glm::quat baseRt,baseRtI;
#if SABA_HAS_SCALE
		glm::vec3	absScale{ 1,1,1 };
#endif
		glm::mat4  savedGlobalTransform;

		saba::PMXBone* pmxBone{};
		;
		bool IsPhysicsActive = false;
		bool flMove = false; //flag can move
		bool flRotate = false;
		bool flVisible = false;
		bool flFixedAxis = false;
		bool isFoot = false, isHand = false, isHead = false;
		float jointAddT=0.f,jointAddR=0.f;

		glm::quat localRtt;

		glm::vec3 cmPos{0,0,0},fixedAxis{1,0,0};//current morph
		bool cmStopped=false;
		bool afterPhyUpdatedAnim = false; glm::mat4 apuaMat0, apuaMat1;

		//misc
		bool distListenerOn = true;
		glm::vec3 baseLastRtt = glm::vec3(0);

		void setCmPos(glm::vec3 pos, float rat) { cmPos = glm::mix(cmPos, pos, rat); };
		void setCmPosGlobal(glm::vec3 pos, float rat,float minDis=0.f) { 

			if (minDis>0.0f && glm::distance(pos, cmPos) < minDis) {
				cmPos = pos; cmStopped = true;
			}
			else {
				setCmPos(pos, rat); cmStopped = false;
			}
			*((glm::vec3*)&m_global[3]) = cmPos; 
		};
		void setTranslationGlobal(glm::vec3 pos) {
			cmPos = pos;
			*((glm::vec3*)&m_global[3]) = pos;
		};
		void setAddRotation(const glm::quat* addRtt) { addRotation = addRtt; }
		glm::quat bkAddRtt = glm::quat(1, 0, 0, 0);
		bool canControl() {	return (flMove || flRotate) && !IsPhysicsActive;}

		void forEachSubNodes(std::function<void(MMDNode* node)> cb, bool includeSelf = false)
		{
			if (includeSelf) cb(this);
			auto node = GetChild();
			while (node) {			
				cb(node);
				node->forEachSubNodes(cb);
				node = node->GetNext();
			}
		}
		void setTreePhyAnimRat(float rat) { forEachSubNodes([=](saba::MMDNode* nd) { nd->phyAnimRatT = nd->phyAnimRatR = rat; },true); }
		float disTo(const saba::MMDNode* node) { return glm::length(getGlobalPos() - node->getGlobalPos()); }
		float disTo(glm::vec3 pos) { return glm::length(getGlobalPos() - pos); }
		glm::vec3 vecTo(const saba::MMDNode* node) { return node->getGlobalPos() - getGlobalPos(); }
		glm::vec3 vecTo(const glm::vec3& pos) { return pos - getGlobalPos(); }
		glm::vec3 vecToNorm(const glm::vec3& pos) { return glm::normalize(pos - getGlobalPos()); }
		glm::vec3 localOfs(const saba::MMDNode* node) {
			return glm::inverse(GetGlobalTransform())*glm::vec4(node->getGlobalPos(),1);
		}
		//get pos/vel in node matrix
		glm::vec3 localOfs(glm::vec3 v) { return glm::vec3(glm::inverse(GetGlobalTransform()) * glm::vec4(v, 1)); }

		void scaleVel(float scale, int what=3, int level=1,float levelMul=1.f);
		alignas(16) glm::mat4 mGlobalInit{ 1 }, mLocalInit{ 1 };
		alignas(16) glm::mat4 mGlobalAnim = glm::mat4(1), mLocalAnim{ 1 }, savedGlobalAnim{ 1 }, savedLocalAnim{ 1 }; // m_global will change after physics update
		virtual const glm::mat4 getMatT(int type) const {
			switch (type)
			{
			case 1:return m_global;
			case 2:return rb0?rb0->getNodeTransform(): mGlobalAnim;
			case 3:return rb0 ? glm::translate(glm::mat4(1),glm::vec3(rb0->getNodeTransform()[3]))
				*glm::mat4(glm::mat3(mGlobalAnim)) 
				: mGlobalAnim;
			}
			return mGlobalAnim;
		}
		const glm::mat4&	getRbAnimGlobal();
		const glm::vec3&	getRbAnimGlobalPos();
		glm::vec3 transformVec(glm::vec3 pos) { return glm::vec3(m_global * glm::vec4(pos, 1)); }
		glm::vec3 rotateVec(glm::vec3 vec) { return glm::mat3(m_global) * vec; }
		void addVelToLocalPos(MMDNode* ndBase,glm::vec3 pos, float mul) {
			rb0->addLinearVelToPos(ndBase->transformVec(pos), mul);
		}


		void setAnimGlobalPos(glm::vec3 pos) { SetAnimationTranslate(pos - (GetParent()?GetParent()->getGlobalPos():glm::vec3(0,0,0)) - GetTranslate()); }
		void setNodePhyAnimRat(float v, bool setSubs, bool isMul);
 
		glm::quat getLocalRttToPos(const glm::vec3& pos, const glm::vec3& refInitPos);

		glm::quat getRttToPos(const glm::vec3& pos, const glm::vec3& refInitPos);
		void lockJointToPos(const glm::vec3& pos, const glm::vec3& refInitPos);

		const int& getVmdId() const { return vmdId; 	}
		void setVmdId(int v, bool setChildren = true) // -1: disable anim
		{  
			forEachSubNodes([=](saba::MMDNode* node) {node->vmdId = v; }, setChildren);
		}
		MMDNode* mirrorNode{};
		MMDNode* getMirrorNode();



		//tmp
		float lastAngleY = 0;
	protected:
		virtual void OnBeginUpdateTransform();
		virtual void OnEndUpdateTransfrom();
		virtual void OnUpdateLocalTransform();

	protected:
		uint32_t		m_index;
		std::string		m_name;
		std::wstring    m_nameU;
		bool			m_enableIK;
		int             vmdId = 0;	

		MMDNode*		m_parent;
		MMDNode*		m_child;
		MMDNode*		m_next;
		MMDNode*		m_prev;

		glm::vec3	m_translate;
#if SABA_HAS_BASE_ROTATION
		glm::quat	m_rotate;
#endif
#if SABA_HAS_SCALE
		glm::vec3	m_scale;
#endif
		glm::vec3	m_animTranslate, m_animTranslateAdd[2],savedTranslate;
		glm::quat	m_animRotate, m_animRotateAdd[2],savedRotate;

		glm::vec3	m_baseAnimTranslate;
		glm::quat	m_baseAnimRotate;

		glm::quat	m_ikRotate;

		__m128  pad;

		alignas(16) glm::mat4	m_local,m_global, savedLocal,savedGlobal;
		glm::mat4	m_inverseInit;
		glm::mat4   mScale;

		glm::vec3	m_initTranslate; //relative to parent
		glm::quat	m_initRotate; 
		glm::vec3	m_initScale;
		bool hasScale = false;


		bool		m_isDeformAfterPhysics=false;
		//ckadd
		const glm::quat* addRotation{};
		bool		mIsRoot=false, did_getMirrorNode=false;

		void setSubNodeDeformAfterPhysics(saba::MMDNode* node, bool active);
	};



}

#endif // !SABA_MODEL_MMD_MMDNODE_H_

