﻿#include "AppGlobal.h"
#include "MmdMidiPlayer.h"
#include "UaLibContext.h"
#include "IrrFw/eqv/EQV.h"
#include <UaUtils.h>

//#include <Eigen/Dense>
//#include <unsupported/Eigen/Splines>

using namespace irr;
using namespace irr::scene;
using namespace irr::core;
using namespace ualib;
//using namespace Eigen;
#define AOU 1
#define AOU_AUTO		1
#define VOICEFX_ON_DT_VEL		0
#define TEXT_FW		0
#define TEXT_FW_1CH_TO_ALL 0
#define FX_TEXT_FW	0
#define AOU_RANDOM_LYRIC	0
#define LRC_EVERY_NOTE 0//no -
#define MULTI_CH 0 //tocheck
#define MIDI_INPUT	0
#define MAX_OUT_TRACK 11
#define ID_ADD_KEY 0//-2+2  //
#define LYRIC_ADD_SPACE     0 //bad vocie
#define MDPLAY_START_OFS (0.3)
#define OUT_TIME_ADD	(MMD_JOYSTICK_GAMECAST?0.1f:0.0f)
#define LAB_OFS (-0.12)
#define MIDI_FILE_OFS -MDPLAY_START_OFS //-1.0  // SV_Export= -1  
#define MIDI_OP_PLAY(...)  do{}while(0) //midi.keyOp(__VA_ARGS__) // // or nothing
//#define MIDI_FILE "data/yoasobi" //seetinh"//sddlm"//nierXG"//"D:/Tmp/!sv/seetinh"//1" // qbyT1.mid");
#if			1
#define SVNAME "v2_kawaii5"//"v2_yoasobi"//
#elif		0
#define SVNAME "v2_gg"//"loli"//zdj"//yoasobi"//jpinsults"//awawa"//mys"//"trust1"//gok"//VisionV22"//kaze111"//qby"//moon"//clorinde"//abab2"//ababa"//shiningstar"//loveJF"//ron1"//sac"//9tcat"//cn1"//jgirl"//heroR"//thm2"//ganyu"//wwwwwww"//heorje"//abaen2"//idolCut"//dango"//hwy_seino"//benihana"//3"//bliss"//hananinate"//XYH"//dlYuuSha"//hikariCN"//senpai"//seetinh"//
#else
#define SVNAME "roam2"//ganyu"//air"// open"//"  "jp1"//
#endif
#define SVP_FILE "D:/Tmp/!sv/" SVNAME //Yuusha"//hwy_seino"//record"//yoasobi"// "data/base"
#define USE_SV_TIME  1
#define WORD_TIME_MUL 1
#define VOICEFX_TIMEMUL 1.0f
#define MAX_SHORT_NOTE_TIME 0.5f
#define MAX_LONG_NOTE_TIME 1.5f
#define PT_SIN_T	10
#define PT_SIN_MUL	0
#define AOU_VEL_DT  6.f 

namespace {
	int JmpLrcKey = 60;
	static const float PitchMulY = 0.6f, PitchMulV = 0.025f;// 
	static const float PitchMulFxY = 0.1f, PitchMulFxV = 0.009f;

	static int			SvTrackMap[] = { 0,1,2,3,4,5,6,7,0,1,2,3,4,5,6,7 }; // mmd to sv track map
	static const float			FontSize[] = { 1.5f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f,1.0f }; //*100
	static const std::string	FontName[] = { "x","x","x","x","x","x","x","x","x","x","x","x","x","x","x","x","x","x","x","x","x" };//"lrcGF","lrcGF","lrcGF","lrcGF","lrcGF","lrcGF","lrcGF","lrcGF","lrcGF", "numFw1","numFw0", "numFw0", "numFw0", "numFw0", "numFw0", "numFw0", "numFw0", "numFw0", "numFw0", "numFw0", "numFw0", "numFw0", "numFw0", "numFw0" };
	static std::wstring LyricText = //L"我们的孤独就像天空中漂浮的城市，仿佛是一个秘密，却无从述说。——《天空之城》";
		//L"原石をください--原石をください--原石をください--原石をください--原石をください--原石をください--原石をください--";
	//L"a暮色苍茫江上望 孤舟一叶向晚风 夕阳西下残霞红 天涯离人何处同 一声长笛吹断肠 万里征途路漫漫 相思泪水洒江心 只愿相逢不相忘 yoyoyoyoyoyoyoioioioioioioioio piapiapapapapapa oooooooooooo nnnnnnnnnn gggggggggg aaaaaaaaaaa";
		L"rarararararararararararararararararararararararararararararararararaxaxaxaxaxavavavavavauauauauaaquququququqaaaaaaaa";
	//L"我对一般人没有兴趣 如果你们之中有外星人 未来人 超能力者 就来找我吧 以上！";// L"ただの人間には興味ありません この中に宇宙人 未来人 異世界人 超能力者がいたらあたしのところに来なさい 以上 "; //L"逃げちゃだめだ 逃げちゃだめだ 逃げちゃだめだ";// L"美しく最後を飾りつける暇があるなら最後まで美しく生きようじゃねーか"; //

	static std::vector<std::wstring> JmpLrc[] = {
		//{ L"en",L"n",L"n",L"n",L"na"},	{ L"en",L"n",L"n",L"n",L"na"},	{ L"en",L"n",L"n",L"n",L"na"},
		//{ L"gong",L"xi",L"fa",L"cai",L"-",L"-",L"-",L"-",L"-",L"-",},{ L"gong",L"xi",L"fa",L"cai",L"-",L"-",L"-",L"-",L"-",L"-",},{ L"gong",L"xi",L"fa",L"cai",L"-",L"-",L"-",L"-",L"-",L"-",},{ L"gong",L"xi",L"fa",L"cai",L"-",L"-",L"-",L"-",L"-",L"-",},{ L"gong",L"xi",L"fa",L"cai",L"-",L"-",L"-",L"-",L"-",L"-",},{ L"gong",L"xi",L"fa",L"cai",L"-",L"-",L"-",L"-",L"-",L"-",},{ L"gong",L"xi",L"fa",L"cai",L"-",L"-",L"-",L"-",L"-",L"-",},{ L"gong",L"xi",L"fa",L"cai",L"-",L"-",L"-",L"-",L"-",L"-",},
		//{ L"a" ,L"-",L"-",L"-",L"-",L"-"},{ L"ba" ,L"-",L"-",L"-",L"-",L"-"},{ L"wa" ,L"-",L"-",L"-",L"-",L"-"},{ L"fa" ,L"-",L"-",L"-",L"-",L"-"},{ L"cai" ,L"-",L"-",L"-",L"-",L"-"},{ L"la" ,L"-",L"-",L"-",L"-",L"-"},		{ L"ha" ,L"ha",L"-",L"-",L"-",L"-",L"-"},{ L"a" ,L"ba",L"-",L"-",L"-",L"-",L"-"},
		//{ L"ha" ,L"ha",L"ha",L"-",L"-",L"-",},{ L"ha" ,L"ha",L"ha",L"-",L"-",L"-",},{ L"ha" ,L"ha",L"ha",L"-",L"-",L"-",},{ L"ha" ,L"ha",L"ha",L"-",L"-",L"-",},{ L"ha" ,L"ha",L"ha",L"-",L"-",L"-",},{ L"ha" ,L"ha",L"ha",L"-",L"-",L"-",},{ L"ha" ,L"ha",L"ha",L"-",L"-",L"-",},{ L"ha" ,L"ha",L"ha",L"-",L"-",L"-",},
		//{L"bo",L"ko",L"nya" },
		// { L"nya",L"nya",L"nya",L"-",L"u", },
		//{L"@",L"@",L"@" },{L"@",L"@",L"@" },
		//{L"@" ,L"@" },{L"@" ,L"@"},
		//{L"@" ,L"br"}, 
		 //L"br",L"n"},
		//{L"n" ,L"br"},{L"n" ,L"n" ,L"br"},
		//{L"m" ,L"br"}, {L"j" ,L"r",L"br"},
		 { L"en",L"e",L"h" },{ L"en",L"e",L"h" },
		{ L"aba",L"br" },{L"br" ,L"m"},{L"u" ,L"m"},{L"br" ,L"n"},
		{ L"a" ,L"-" ,L"re",L"-",},
		//{ L"ma" ,L"ra",L"ni",},{ L"nia" ,L"u",L"-",},
		//{ L"pian",L"yi",L"de",L"san",L"jie",L"gun",},
		//{ L"ma",L"ka",L"ba",L"ka",L" a "},
		//{ L"Hur" ,L"ry",L"Hur",L"ry",L" wa "},
		//{ L"hana" ,L"bi",L"hana" ,L"bu",L" - ",},

		//{ L"br" ,L"nia" ,L"u",L"a" ,L"u",L" - ",},

		//{ L"ha" ,L"lo",L"br" },{ L"ba" ,L"tto"},
		
		{ L"bro" ,L"br"},{ L"ha" ,L"ha",L"br"},{ L"br" ,L"byu",L"-"},

		//{ L"a-" , L"-'patsu",L"br" },
		{ L"nia" ,L"mu",L"nia" ,L"mu",L"-",},
		{ L"nia" , L"u",L"br" },
		//{ L"aha" ,L"ha"},
		//{ L"ya" ,L"ba",},
		//{ L"ya" ,L"hu",L"u",},
		{ L"mi" ,L"pa",L"a",},
		{ L"mi" ,L"-" ,L"pa",L"-",},

		//
		{ L"wa" ,L"ku" ,L"wa" ,L"ku" ,L"-",},
		//{ L"bi" ,L"li" ,L"ba",L"la",L"bi",L"li",L"ba",L"la",L"la",L"la",L"-",L"-" },
		{ L"aha" ,L"aha" ,L"aha" ,L"ha" ,L"ha" ,L"ha",L"-"},
		{ L"rua" ,L"-" ,L"rua" ,L"rua" ,L"-",L"-",},
		{ L"nia" ,L"a" ,L"a",L"a",L"a",L"a",L"a",L"o",L"-",L"u",L"-",L"-" },
		{ L"nia" ,L"a" ,L"i",L"a",L"i",L"a",L"i",L"a",L"i",L"u",L"-",L"-" },
		{ L"ba" ,L"la" ,L"ba",L"la",L"ba",L"la",L"la",L"la",L"la",L"la",L"-",L"-" },
		{ L"bi" ,L"li" ,L"bi",L"li",L"bi",L"li",L"bi",L"li",L"li",L"li",L"-",L"-" },

		{ L"a" ,L"ba" ,L"a",L"ba",L"a",L"ba",L"ba",L"ba",L"ba",L"ba",L"-",L"-" },

		{ L"hei" ,L"i" },
		{ L"ha" ,L"-",L"-",L"-" },
		{ L"ni" ,L"a",L"a",L"u"},
		{ L"r" ,L"a",L"r",L"a",L"r",L"a"},
	};
	static const int MAX_AOU = 5;
	//static float JmpKeyAdd[16][16] = {
	//	{ 2,0,	6,6,6,6,6,6,6 },{ 2,0,	6,6,6,6,6,6,6 },{ 2,0,	6,6,6,6,6,6,6 },{ 2,0,	6,6,6,6,6,6,6 },{ 2,0,	6,6,6,6,6,6,6 },
	//	{ 2,0,	6,6,6,6,6,6,6 },
	//	{ 0,0,0,0,0,0,0,00,0, },
	//	{ 0,0,0,0,0,0,0,00,0, },
	//	{ 0,0,0,0,0,0,0,00,0, },
	//	{ 0,0,0,0,0,0,0,00,0, },
	//};

	float calculateVolume(float distance) {
		float maxDistance = 300.0f, minDistance = 30.0f;
		// 确保距离在有效范围内
		distance = std::max(minDistance, std::min(maxDistance, distance));

		// 使用对数映射将10-100的距离映射到-3到3的范围
		// 使用log10进行计算
		float normalized = (log10f(distance) - log10f(minDistance)) / (log10f(maxDistance) - log10f(minDistance));

		// 将0-1的归一化值映射到-3到3的范围
		float volume = 2.0f * (1.0f - 2.0f * normalized) - 2.f;

		
		return std::max(-3.0f, std::min(0.0f, volume));
	}

	float calculateDopplerEffect(MmdMidiPlayer::ChDat& cd)
	{
		const glm::vec3& audPos = cd.cPos, audVel = cd.cVel-cd.camVel, listenPos = cd.camPos;
		// 计算声源到接收者的方向向量
		glm::vec3 direction = glm::normalize(listenPos - audPos);

		// 计算声源在接收者方向上的速度分量
		float velocityComponent = glm::dot(audVel, direction);

		// 声音在空气中的传播速度 (约343m/s)
		const float SOUND_SPEED = 343.0f  
			* 0.06f; //非真实，调节输出范围

		// 计算多普勒效应的实际频率比例
		// 完整的多普勒公式：f' = f * (c/(c-v))
		float frequencyRatio;
		if (std::abs(velocityComponent) >= SOUND_SPEED) {
			// 防止除以零或负数
			frequencyRatio = velocityComponent > 0 ? 2.0f : 0.5f; // 限制最大变化为一个八度
		}
		else {
			frequencyRatio = SOUND_SPEED / (SOUND_SPEED - velocityComponent);
		}

		// 将频率比转换为音阶变化（半音数）
		// 在平均律中，n个半音的频率比为 2^(n/12)
		// 因此，给定频率比r，半音数n = 12 * log2(r)
		float keyDelta = 12.0f * std::log2(frequencyRatio);

		// 限制音高变化在一个八度范围内
		
		auto pdt = glm::clamp(keyDelta, -12.0f, 12.0f) / 12.f;
		//DP(("doppler %f", pdt));
		// 限制在[-1, 1]范围内，确保不会产生极端值
		return pdt;
	}


}

#include "sv/KawaiiLyricGenerator.h"

#include <memory>

// Create a global instance of the KawaiiLyricGenerator
std::unique_ptr<KawaiiLyricGenerator> g_lyricGenerator = nullptr;
 


irr::scene::MmdMidiPlayer::MmdMidiPlayer()
{

}


irr::scene::MmdMidiPlayer::~MmdMidiPlayer()
{

}

void irr::scene::MmdMidiPlayer::recordStart(int joyAct)
{
	UP_LOCK_GUARD(mlock);
	joyActMode = joyAct;
	//midi.stopPlay();
	for (int i = 0; i < MAX_KEY; i++)
	{
		auto& cd = chd[i];
		int c = MULTI_CH ? i : 0;
		if (chd[i].keyDown)
			MIDI_OP_PLAY(c, chd[i].ldkey, 0, 1);
		chd[i] = ChDat();

		cd.loudPt.pts.clear(); cd.loudPt.mul = 9.f;
		cd.pitchPt.pts.clear(); cd.pitchPt.mul = 300.f;
		cd.openPt.pts.clear(); cd.openPt.mul = 1.f;
	}


	//ualib::split(LyricText, L' ', words);

	startTime = 0;// Ctx->gd.time;

#if MIDI_INPUT
	//midi.startGettingEvt(MIDI_FILE ".mid",1.0); 
#endif

	midi.recordBegin();
	recording = true;
	playInTime = playOutTime = -fixedDeltaTime + 0.001f;

	updateFrame(0);
	//SVP
	jsv.SetFile(SVP_FILE ".svp", true);

	//jsRoot = jsv.copyRootValue();
	auto& root = jsv.refRootValue();

	// Detect version
	int version = root["version"].asInt();
	isV2Format = (version >= 180); // V2 format  

	bpm = root["time"]["tempo"][0]["bpm"].asDouble();
	JBSEC = 705600000.0 * 2 * bpm / 120;
	auto& tracks = root["tracks"];
	svTrackNum = tracks.size();
	if (svTrackNum < 1)
		throw "no track in svp !";

	// For v2 format, we need to handle library content carefully
	if (isV2Format) {
		// Make sure we have library items to work with
		if (root["library"].empty()) {
			DP(("Warning: V2 format detected but library is empty"));
		}
	}

 

	outTrackNum = std::min(16ull, _countof(SvTrackMap));
	Json::Value  notes0[16];

	Json::Value t0;
	for (int i = 0; i < outTrackNum; i++)
	{
		if (SvTrackMap[i] >= svTrackNum) SvTrackMap[i] = 0;

		for (int j = 0; j < tracks.size(); j++)
			if (SvTrackMap[i] == tracks[j]["dispOrder"].asInt())
			{

				if (tracks[j]["mainRef"].isMember("audio") && !t0.isNull())
					jbt[i] = t0;
				else
					jbt[i] = tracks[j];
				if (!isV2Format) jbt[i]["mainRef"]["systemPitchDelta"].clear();

				if (isV2Format)
				{
					//find notes in library with uuid 
					auto& lib = root["library"];
					for (int k = 0; k < lib.size(); k++)
					{
						if (lib[k]["uuid"].asString() == jbt[i]["groups"][0]["groupID"].asString())
						{
							notes0[i] = lib[k]["notes"];
							DP(("track %d , notes count %d",j,notes0[i].size()));
							break;
						}
					}
				}
				else notes0[i] = jbt[i]["mainGroup"]["notes"];

				if (i == 0)
					jbn = notes0[i][0];
				if (SvTrackMap[i] == 0)
					t0 = tracks[j];
				break;
			}

		svOutNotes[i].clear();
		auto& cd = chd[i];
		cd.pitchPt.pts.clear();
		cd.loudPt.pts.clear();
		cd.openPt.pts.clear();
		cd.loudPt.lastTime = 0;
		cd.openPt.lastTime = 0;
		addLoudPoint(cd, cd.loudPt, -1, 0);
		addOpenPoint(cd, cd.openPt, -1, 0);
	}

	for (int j = 0; j < outTrackNum; j++) {
		pns[j].ntpid = -1;
		pns[j].notes.clear();
		pns[j].notes.reserve(notes0[j].size());
		assert(!(j == 0 && notes0[j].size() < 1));// 解散音符组

		UaJson::forEach(notes0[j], [=](const Json::Value& nv)
			{
				SvNoteData snd;
				snd.time = nv["onset"].asInt64() / double(JBSEC);
				snd.dur = nv["duration"].asInt64() / double(JBSEC);
				snd.lyric = ualib::Utf8toWcs(nv["lyrics"].asString());
				snd.key = nv["pitch"].asInt();
				snd.rap = nv["musicalType"].asString() == "rap";
				pns[j].notes.push_back(snd);
			});
	}


	root["library"].clear();

	//if (joyActMode>0) jas.clear();
#if MMD_JOYSTICK_GAMECAST
	if (joyActMode == 2)
	{
		if (joySb) joySb->hdlJgc->setPlayback(true);

		ualib::UaJsonSetting js;
		if (!js.SetFile("data/outActions.json", true))
			joyActMode = 0;
		else {
			//auto& root = js.refRootValue();
			//for (auto& v : root["actions"]) {
			//	JoyAction ja;				
			//	ja.ts=v["ts"].asFloat();
			//	ja.dur = v["dur"].asFloat();
			//	ja.act = v["act"].asInt();
			//	jas.emplace_back(ja);
			//} 
			//joyActId = -1;
		}
	}
	else {
		if (joySb) {
			if (joyActMode == 1) joySb->hdlJgc->setRecording(true);
			joySb->hdlJgc->setPlayback(false);
		}
	}
#endif
}




void irr::scene::MmdMidiPlayer::recordEnd()
{
	if (!recording) return;
	UP_LOCK_GUARD(mlock);
	recording = false;
	midi.recordEnd("r:/mmd.mid");
	auto& root = jsv.refRootValue();
	root["tracks"].clear();
	Json::Value libItem, newLibs,oldLIbs= root["library"];
	std::string uuid,uuidBase="b179e6cb-8911-485c-85ee-4badd08da1";

	for (int i = 0; i < outTrackNum; i++)
		
		if (i < 16 && svOutNotes[i].size() > 0) {
			if (isV2Format) {
				libItem = oldLIbs[i< oldLIbs.size()?i:0];

			}
			uuid = uuidBase + ualib::strFmt("%02d", i);
			auto& track = jbt[i];
			track["dispOrder"] = i;
			track["name"] = "tn" + std::to_string(i);
			DP(("TRACK %d notes %d", i, svOutNotes[i].size()));
			bool isFx = i >= mmdCount;
			//if (isFx|| realTime>playTime+0.1f) 
			{
				if (isV2Format) { 
					libItem["notes"] = svOutNotes[i];
					libItem["uuid"] = uuid;
				}				 
				else track["mainGroup"]["notes"] = svOutNotes[i];
				//if (isFx) track["mixer"]["gainDecibel"] = -10.0f;
			}
			auto& cd = chd[i];
			if (cd.loudPt.pts.size() > 1)
			{
				track["mixer"]["mute"] = false;

				if (isV2Format) {
					libItem["parameters"]["loudness"]["points"] = cd.loudPt.pts;
				}
				else track["mainGroup"]["parameters"]["loudness"]["points"] = cd.loudPt.pts;
			}
			if (isV2Format && cd.openPt.pts.size() > 1)
			{	 
				//libItem["parameters"]["mouthOpening"]["points"] = cd.openPt.pts;				 
			}

			if (cd.pitchPt.pts.size() > 1)
			{
				if (isV2Format) {
					//if (root["library"][0]["parameters"]["pitchDelta"]["points"].size() > 1) { //TOCHECK
					//	auto jspts = (isFx ? cd.pitchPt.wordsPts : root["library"][0]["parameters"]["pitchDelta"]["points"]);
					//	combinePts(cd.pitchPt, cd, jspts, isFx ? 0 : 0xFFFFFFFF * 0);
					//}
					libItem["parameters"]["pitchDelta"]["points"] = cd.pitchPt.pts;
				}
				else {
					if (jbt[i]["mainGroup"]["parameters"]["pitchDelta"]["points"].size() > 1) {
						auto jspts = (isFx ? cd.pitchPt.wordsPts : jbt[i]["mainGroup"]["parameters"]["pitchDelta"]["points"]);
						combinePts(cd.pitchPt, cd, jspts, isFx ? 0 : 0xFFFFFFFF * 0);
					}
					track["mainGroup"]["parameters"]["pitchDelta"]["points"] = //isFx?cd.pitchPt.wordsPts:
						cd.pitchPt.pts;
				}
			}
			
			if (isV2Format)
			{
				 
				newLibs.append(libItem);

				jbt[i]["groups"][0]["groupID"] = uuid;
				jbt[i]["groups"][0]["blickAbsoluteEnd"] = int64_t(cd.lastSvTime+ JBSEC);
				root["tracks"].append(jbt[i]);// assert(i + 1 == tracks.size());
			}
			else 
				root["tracks"].append(jbt[i]);// assert(i + 1 == tracks.size());
		}
	if (isV2Format)
		root["library"] = newLibs;
	jsv.SetFile(joyActMode == 1 ? "r:/outMMMMMMMMooo.svp" : "r:/outPlayback.svp", false);
	//jsv.setRootValue(std::move(jsRoot));
	jsv.SaveFile();

#if MMD_JOYSTICK_GAMECAST
	if (joyActMode == 1)
	{
		joySb->hdlJgc->setRecording(false);
		//ualib::UaJsonSetting js;
		//js.SetFile("r:/outActions.json", false);
		//auto& root = js.refRootValue();
		//Json::Value jaArr;
		//for (auto& ja : jas) {
		//	Json::Value v;
		//	v["ts"] = ja.ts;
		//	v["dur"] = ja.dur;
		//	v["act"] = ja.act;
		//	jaArr.append(v);
		//}
		//root["actions"] = jaArr;
		//js.SaveFile();

	}
#endif
}

std::string irr::scene::MmdMidiPlayer::getLabFile(int idx, double& ofs)
{
	ofs = MDPLAY_START_OFS + MIDI_FILE_OFS + LAB_OFS;
	if (idx >= 0 && idx < _countof(SvTrackMap))
		return ualib::strFmt("%s-t%d.lab", SVP_FILE, SvTrackMap[idx] + 1);
	else return "";
}


void irr::scene::MmdMidiPlayer::updateFrame(float stepTime)
{
	playInStepTime = stepTime;
	if (stepTime > 0.000001f)		curFrameTimeMul = std::max(0.1f, fixedDeltaTime / stepTime * WORD_TIME_MUL);
	playInTime += stepTime;
	playOutTime += fixedDeltaTime;
	//DP(("In %3.6f,%3.6f  Out %3.6f,%3.6f", playInTime,stepTime, playOutTime, fixedDeltaTime));
	midi.setRecTime(playOutTime);
	realTime = playOutTime + OUT_TIME_ADD;

	//if (joyActMode == 2 && joySb)
	//{
	//	int nid = joyActId + 1;
	//	if (nid < jas.size()) {
	//		auto& ja = jas[nid];
	//		if (realTime > ja.ts+0.3f) {
	//			joyActId = nid;
	//			joySb->hdlJgc->onJoyAct(ja.act, ja.dur);
	//		}
	//	}
	//}
}

void irr::scene::MmdMidiPlayer::updateMdNode(int id, irr::core::vector3df pos, irr::core::vector3df vel, float phyTimeMul)
{
	if (!recording) return;
	UP_LOCK_GUARD(mlock);
	//if (!USE_SV_TIME && midi.recording() && midi.getRecTime() < 1) return;

	float time = playOutTime;
	auto& cd = chd[id];
	cd.ch = id;
	cd.cPos = pos;
	cd.dtPos = cd.cPos - cd.lPos;
	cd.lVel = cd.cVel;
	cd.cVel = vel;
	cd.velLen = glm::length(cd.cVel);
	cd.ldtVel = cd.dtVel;
	cd.dtVel = (cd.cVel - cd.lVel) / std::min(2.f, phyTimeMul);
	cd.dtVelLenLL = cd.dtVelLenL;
	cd.dtVelLenL = cd.dtVelLen;
	cd.dtVelLen = glm::length(cd.dtVel);
	if (cd.dtVelLen > cd.dtVelCache)
		cd.dtVelCache = glm::mix(cd.dtVelCache, std::min(cd.dtVelLen, 20.f), 0.9f);
	else
		cd.dtVelCache = glm::mix(cd.dtVelCache, std::min(cd.dtVelLen, 20.f), 0.3f);
	//cd.toCamDir = sb->mmdCamPos - pos;
	//cd.camDis = cd.toCamDir.getLength();
#define USE_ACC_VEL 1
#if USE_ACC_VEL
	if (cd.dtVelLen > 0.98f) {
		cd.dtVel = glm::normalize(cd.dtVel);
		//if (cd.lVel.getLengthSQ()<100 && !cd.keySound && cd.aFxCD < 1) aouStart(id, 8, irrpos);
	}
	else if (cd.dtVelLen < 0.02f) { cd.dtVel = float3(0, 0, 0); cd.dtVelLen = 0.f; }
#else
	if (cd.cVel.getLength() >= 1.f) { cd.cVel = cd.cVel.normalize(); cd.dtVel = cd.cVel - cd.lVel; cd.dtVelLen = cd.dtVel.getLength(); }
	else if (cd.cVel.getLength() < 0.1f) { cd.cVel.set(0, 0, 0);  cd.dtVel = cd.cVel - cd.lVel; cd.dtVelLen = cd.dtVel.getLength(); }
#endif
	//pos.Y /= 2;
	float dy = pos.Y - cd.lPos.y;//	DP(("dy %f",dy));
	float dkdy = pos.Y - cd.ldpos.y;

	if (id >= mmdCount) {
		updateFxTrack(id);
		cd.dur -= playInStepTime;
		cd.lPos = pos;
		return;
	}
	float afrat = cd.aFxMax<=0? 0: cd.aFxCD / cd.aFxMax;
	float dtmt = AOU_VEL_DT * saba::MMDPhysics::phyTimeMul;
	if (		
		cd.dtVelLen / cd.dtVelLenL > 1.1f && (
			afrat < 0.9f && cd.dtVelLen > dtmt
			|| afrat < 0.75f &&  cd.dtVelLen > dtmt *0.75f
			|| cd.aFxCD < 1 && cd.dtVelLen > dtmt * .5f
			))
	{
#if VOICEFX_ON_DT_VEL

		Sbs[cd.ch]->actVoiceFx(cd.ch, 10, 66);
#else
		int fxid = (aouId + id) % MAX_AOU;
		if (AOU_AUTO) aouStart(id, JmpLrc[fxid].size(), fxid, true, AOU_RANDOM_LYRIC);
		DP(("DTVEL %f, r%f",cd.dtVelLen, afrat));
#endif
	}

	if (cd.aFxCD < 1)
#if USE_ACC_VEL
		if (glm::length(cd.dtVel - cd.ldtVel) > 0.01f) {
			float v = cd.dtVelLen;
#else
		if (cd.dtVelLen > 0.1f) {
			float v = cd.cVel.getLength();
#endif	
			cd.lastLoudVal = std::max(cd.lastLoudVal * 0.397f, v / 10.f);
			cd.lastLoudVal = std::min(cd.lastLoudVal, 1.f);
			addLoudPoint(cd, cd.loudPt, 0, cd.lastLoudVal);			
		}

	if (cd.dtVelLenL > 1.f)
	{
		double pastTime = realTime - cd.openPt.lastTime / JBSEC;
		if (cd.lastOpenVal>0.1f && pastTime>1.f) addOpenPoint(cd, cd.openPt,  - pastTime*0.1f, 0);
		addOpenPoint(cd, cd.openPt, 0, std::clamp(cd.dtVel.y,0.f,1.f));
	}
	//else 		if (cd.dtVelLenL > 0.2f)
	//{
	//	float v = 0.1f + 0.65f * sin(gPhyTime * piFloat * 2 * 3);
	//	addOpenPoint(cd, cd.openPt, 0, v);
	//	Sbs[cd.ch]->Rb0()->addLinearVel(glm::vec3(0, v, 0) * 10.f);
	//	Sbs[cd.ch]->mpE->addWeight = (std::clamp(v, 0.f, 1.95f));
	//}
	else {
		Sbs[cd.ch]->mpE->addWeight *= 0.99f;
		if (abs(cd.lastOpenVal) > 0.1f) {

			//cd.lastOpenVal = std::min(cd.lastOpenVal * 0.99f,1.f);	addOpenPoint(cd, cd.openPt, 0, cd.lastOpenVal);
		}
	}

	cd.lkey = cd.ckey;
	//pos.set( 0,10,0 );
	float x = pos.X / 2 + 60 + cd.lfm.x;

	float fmx = x - int(x);
	float addy = (pos.Y - 12) / 1 + cd.lfm.y;
	float fmy = addy + 100 - int(addy + 100);
	//cd.ckey = x + addy;
	//if (pos.Z < -10 || pos.Y >10) cd.ckey = -1;

	float vol = 0.5f + (cd.aFxCD > 0 ? 0.5 : cd.dtVelLen);// clamp((pos.Z + 3) / 3.f, 0.f, 1.f);

#if 0
	if (dy < -0.6 && !cd.keyDown) {
		cd.keyDown = true;
		int ch = core::clamp(pos.Z / 6 + 1, 0.f, 2.1f); DP(("CH %d", ch));
		MIDI_OP_PLAY(ch, cd.ckey, cd.ckey >= 0, p1);
		cd.keyDownCh = ch;
	}
	else if (dy > 0.3 && cd.keyDown) {
		//MIDI_OP_PLAY(cd.keyDownCh, cd.lkey, 0, 0);
		cd.keyDown = false;
	}
#else
	int c = id;// MULTI_CH ? id : 0;

	//if (id == 0)
	playAtTime(id);
#if MIDI_INPUT
	//else midi.geinfo[id] = midi.geinfo[0];
	midilib::MidiMan::GetEvtInfo& gi = midi.geinfo[id];
	cd.ckey = gi.keyDn + ID_ADD_KEY * id;// +(cd.act == 1 ? 0.f : pos.Y - cd.ldpos.Y + 0.5);//12+ addy;// -12 * id + addy;// +x - 60 - 24;

#else
	midilib::MidiMan::GetEvtInfo gi{};

#endif
	cd.act = gi.act;
	if (cd.act == 1) {
		cd.aFxCD = 0;
		cd.fxAdKey = 0;
	}


	if (cd.act != 1 && (cd.isAou || cd.aFxMax == cd.aFxCD))
	{
		auto fxid = cd.aFxMax - cd.aFxCD;
		cd.fxAdKey = fxid < cd.aouKeyAdd.size() ? cd.aouKeyAdd[fxid] : 0;
	}


	vol = 1;// p1;
	if (cd.ckey < 6) cd.ckey = 66;


	bool isFx = false;
	if (cd.aFxCD >= 1 && (playOutTime - cd.lkTime > 0.1f) && !cd.keyDown) {
		cd.act = 1; isFx = true; //cd.ckey = 66 + cd.ch*2;
		gi.evDur = 1.f;
	}

	// -------------------------------------------------------------------------------------------------
	if (
 
		cd.act == 1
 		
		)  // KEY DOWN !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
	{
#if !MULTI_CH
		//for (int i = 0; i < MAX_KEY; i++)
		{
			if (cd.keySound) {
				noteEnd(c, cd);
				cd.keySound = false;
			}
			//cd.keyDown = false;
		}
#endif

		//MIDI_OP_PLAY(id, cd.ckey, 1, vol);

		int lid = cd.lrcIdx;

		std::wstring s; s += lid < LyricText.size() ? LyricText[lid] : L' ';
 

		//if (c == 0)	s = L"a";
		//if (c == 1)	s = L"a";
		//if (c == 2) 
		{
			cd.lrcIdx++;
			if (s == L" ") { s = L"br"; }
		}
		if (!MMD_JOYSTICK_GAMECAST && pns[id].ntpid >= 0 && pns[id].ntpid + 1 < pns[id].notes.size()) {
			auto& note = pns[id].notes[pns[id].ntpid];
			s = note.lyric;// ualib::Utf8toWcs(gi.lyric);
			if (onNoteCb[c]) onNoteCb[c](c, note);

		}
 

		//s = L"nia";
		if (!midi.recording() || s != L" ")
		{
			if (cd.aFxCD < cd.aFxMax / 2)
				cd.aFxCD = 0;

			midi.tickOfs[c] = c;
			MIDI_OP_PLAY(c, cd.ckey + cd.fxAdKey, 1, vol); cd.keySound = true;
			cd.dur = gi.evDur;
			cd.endedDur = 0.f;
			s = addLyric(c, s);
			if (!isFx && (!TEXT_FW_1CH_TO_ALL || c == 0)) textFw(c, s);
			cd.lfm.x = 0.5f - fmx;
			cd.lfm.y = 0.5f - fmy;
			cd.keyDown = true;
			cd.ldpos = pos;
			cd.ldkey = cd.ckey;
			cd.lkTime = cd.kdTime = playOutTime;
			cd.lkRealTime = realTime;
			
			cd.isAou = cd.aFxCD > 0;

			DPWCS((L"KD %d,%d  y=%f  %s ", c, cd.ckey, pos.Y, s.c_str()));

		}
		else { MIDI_OP_PLAY(c, cd.ldkey, 0, vol); cd.keySound = false; }
		cd.pitchPt.intervalS = 0.001f;
	}
	else if (  //---------------------------------------------------------------------------------
 
		cd.dur <= 0.f && cd.keySound
 
		)  // KEY UP !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
	{
 
		if (cd.ldkey == cd.ckey && (cd.lrcIdx >= LyricText.size() || LyricText[cd.lrcIdx] == L' '
			|| playOutTime - cd.lkTime > 0.001
			))
 
 
		{
			noteEnd(c, cd); cd.keySound = false;
			DPWCS((L"UP %d", cd.ldkey));
		}
		cd.lfm = glm::vec3(0, 0, 0);


		cd.keyDown = false;

	}
	else if (  cd.keySound &&
		((!cd.isAou && cd.ldkey != cd.ckey) || cd.isAou && (playOutTime - cd.lkTime > getWordTime(cd)))
		)
	{
		noteEnd(c, cd);
		//MIDI_OP_PLAY(id, cd.ckey, 1, p1);
		//midi.lyricOp(id, L"-");
#if LRC_EVERY_NOTE
		int lid = lrcIdx++;
		std::wstring s; s += lid < LyricText.size() ? LyricText[lid] : L' ';
		midi.lyricOp(ch, s);
		textFw(s);
		if (!midi.recording() || s != L" ")	MIDI_OP_PLAY(ch, cd.ckey, 1, vol);
#else		
		MIDI_OP_PLAY(c, cd.ckey, 1, vol);
		addLyric(c, L"-");
		DPWCS((L"K- %d,%d  y=%f  ", c, cd.ckey, pos.Y));
#endif
		cd.lkTime = playOutTime;
		cd.lkRealTime = realTime;
		cd.ldkey = cd.ckey;
	}


#endif


	if (cd.pitchPt.intervalS >= 0.f) {
		cd.pitchPt.intervalS -= fixedDeltaTime;
		if (cd.pitchPt.intervalS <= 0.f) {

			float sint = (sin((playOutTime - cd.lkTime) * 12 * core::PI) > 0 ? 1 : -1) * PT_SIN_MUL;
			float v = 0;
			if (cd.keySound)
			{
				v = std::min(6.f, std::clamp((cd.dtPos.y) * PitchMulY,-1.f,1.f) + (cd.hitVal * 1.f - 0.f) + (cd.dtVelCache * cd.dtVelCache) * PitchMulV);
				//DP(("pitchPt   %d dtVel %f", cd.ch,cd.dtVelCache));
			}
			v += calculateDopplerEffect(cd);
			addPoint(cd, cd.pitchPt, 0, v/*+ sint*/);
			//addPoint(cd, cd.loudPt, 0, v * 0.25f);

			cd.pitchPt.intervalS = 0.05f;
			cd.hitVal = std::max(0.f, cd.hitVal * 0.66f);
		}
	}
	cd.dur -= playInStepTime;
	cd.lPos = pos;

}

void irr::scene::MmdMidiPlayer::updateFxTrack(int c)
{
	if (!fxs.size()) loadWords();
	auto& cd = chd[c];
#define AOUKEY 30 

	if (cd.act == 1)  // KEY DOWN !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
	{
		cd.act = 0;
		if (playOutTime - cd.kdTime < 0.1f) return;
		if (cd.objKey < AOUKEY)
			cd.objKey = AOUKEY;
		cd.aFxMax = chd[c].aFxCD = 5;


		if (cd.keySound) {
			MIDI_OP_PLAY(c, cd.ldkey, 0, 0);//noteEnd(c, cd);
			cd.keySound = false;
		}

		int lid = cd.lrcIdx;

		std::wstring s; s += L' ';

		midi.tickOfs[c] = c;
		cd.ckey = key2 ? key2 : cd.objKey;// +JmpKeyAdd[cd.aFxId][cd.aFxMax - cd.aFxCD + 1];
		MIDI_OP_PLAY(c, cd.ckey, 1, 1.f); cd.keySound = true;


		cd.keyDown = true;
		cd.ldpos = cd.cPos;
		cd.ldkey = cd.ckey;
		cd.lkTime = cd.kdTime = playOutTime;
		cd.lkRealTime = realTime;
		cd.dur = 1;
		cd.curWordMul *= glm::clamp(VOICEFX_TIMEMUL / pow(glm::length(cd.cVel), 0.5f), 0.666f, 2.0f);
		{
			cd.wordFxId = cd.aFxId;
			cd.curWordId = UaRand(fxs[cd.wordFxId].words.size());
			auto& w = fxs[cd.wordFxId].words[cd.curWordId];
			auto notes = w.jNotes;
			int64_t dt = w.startI64;
			int64_t t = int64_t(cd.lkRealTime * JBSEC + 0.5);
			DP(("MmdFx %d", cd.wordFxId));
			int rmcount = 0;
			for (int i = svOutNotes[c].size() - 1; i > 0; i--)
				if (svOutNotes[c][i]["onset"].asInt64() >= t) rmcount += 1;	else break;
			if (rmcount)	svOutNotes[c].resize(svOutNotes[c].size() - rmcount);

			if (svOutNotes[c].size() > 0) //cover last
			{
				auto& last = svOutNotes[c][svOutNotes[c].size() - 1];
				if (last["onset"].asInt64() + last["duration"].asInt64() > t) {
					double oldDur = last["duration"].asInt64();
					last["duration"] = t - last["onset"].asInt64();
					double durScale = last["duration"].asInt64() / oldDur;
					auto scalePtDat = [&](irr::scene::MmdMidiPlayer::ChDat::PointsData& ptdat, double sc) {
						for (int i = ptdat.lastWordIdx; i < ptdat.wordsPts.size(); i += 2)
							ptdat.wordsPts[i] = ptdat.lastWordT + int64_t((ptdat.wordsPts[i].asInt64() - ptdat.lastWordT) * sc + 0.5);
						};
					scalePtDat(cd.pitchPt, durScale);
					scalePtDat(cd.loudPt, durScale);
				}
			}
			int64_t lastT = 0, lastD = 0;
			bool first = true;
			for (auto& note : notes DOT_AS_ARRAY)
			{
				int64_t ofst = (note["onset"].asInt64() - dt);
				auto tt = t + int64_t(ofst * cd.curWordMul * curFrameTimeMul + 0.5);
				if (abs(lastT + lastD - tt) < 10) tt = lastT + lastD;
				note["onset"] = lastT = tt;
				note["pitch"] = cd.ckey;
				if (cd.setLyric.size() > 0)
					note["lyrics"] = first ? ualib::WcstoUtf8(cd.setLyric) : "-";//change lyric of note
				else {
#if LYRIC_ADD_SPACE
					auto t = note["lyrics"].asString();
					t = t.substr(0, t.size() - 1) + " " + t[t.size() - 1];//TODO: add space between chars in word
					note["lyrics"] = t;
#endif
				}
				if (cd.lyricToLip) {
					std::string txt = note["lyrics"].asString();
					cbOnFxText[cd.ch](txt, (tt - t) / double(JBSEC), first);
				}
				lastD = int64_t(note["duration"].asInt64() * cd.curWordMul * curFrameTimeMul + 0.5);
				note["duration"] = lastD;
				svOutNotes[c].append(note);
				/*	JoyAction ja; ja.act = cd.aFxId ; ja.ts = tt / JBSEC; ja.dur = lastD / JBSEC;
					if (joyActMode == 1) {
						if (jas.size() > 0) {
							auto& last = jas.back();
							last.dur = std::max(0.1f, std::min(last.dur, ja.ts - last.ts - 0.1f));
						}
						jas.emplace_back(ja);
					}*/
				first = false;
			}

			auto addWords = [&](irr::scene::MmdMidiPlayer::ChDat::PointsData& ptdat, int64_t t, const Json::Value& pitchPts, irr::scene::SvWordStruct& w)
				{

					int rmcount = 0;
					for (int i = ptdat.wordsPts.size() - 2; i > 0; i -= 2) {
						if (ptdat.wordsPts[i].asInt64() >= t) rmcount += 2;
						else break;
					}
					if (rmcount)
						ptdat.wordsPts.resize(ptdat.wordsPts.size() - rmcount);
					//ptdat.wordsPts.append(t); t = t + 1;					ptdat.wordsPts.append(0);
					int64_t lptt = 0, ladt = 0; float lptv = 0; bool first = true;

					auto appendTV = [&](Json::Value& jv, int64_t t, float v) {
						jv.append(t);
						jv.append(v);
						//DP(("APTV %lld %f",t,v));
						assert(t >= ladt);
						ladt = t;

						};

					for (int i = 0; i < pitchPts.size(); i += 2)
					{
						int64_t ptt = pitchPts[i].asInt64();
						float ptv = pitchPts[i + 1].asFloat();
						int64_t adt = t + int64_t((ptt - w.startI64) * cd.curWordMul * curFrameTimeMul + 0.5);

						if (ptt > w.endI64/*+(705600000/10)*/)
						{
							if (first) {
								first = false; appendTV(ptdat.wordsPts, t, lptv); lptv = ptv;
							}

							appendTV(ptdat.wordsPts, t + (w.endI64 - w.startI64) * cd.curWordMul * curFrameTimeMul, lptv + (ptv - lptv) * (w.endI64 - lptt) / (ptt - lptt));
							break;
						}
						if (ptt >= w.startI64)
						{
							if (first) {
								ptdat.lastWordIdx = ptdat.wordsPts.size();
								ptdat.lastWordT = t;
								first = false;
								if (adt > t) {
									appendTV(ptdat.wordsPts, t, lptv + (ptv - lptv) * (w.startI64 - lptt) / (ptt - lptt));
								}
							}
							appendTV(ptdat.wordsPts, adt, ptv);
							assert(t > 0 && adt > 0);


						}

						lptt = ptt; ladt = adt; lptv = ptv;
					}
				};

			addWords(cd.pitchPt, t, w.jTrack["mainGroup"]["parameters"]["pitchDelta"]["points"], w);
			addWords(cd.loudPt, t, w.jTrack["mainGroup"]["parameters"]["loudness"]["points"], w);
		}
		DPWCS((L"KDF %d,%d  y=%f  %s ", c, cd.ckey, cd.cPos.y, s.c_str()));

		cd.pitchPt.intervalS = 0.001f;
	}
	else if (cd.keySound && cd.wordFxId >= 0 && playOutTime - cd.lkTime > fxs[cd.wordFxId].words[cd.curWordId].duration * cd.curWordMul * curFrameTimeMul)
	{
		cd.keySound = false;
		MIDI_OP_PLAY(c, cd.ldkey, 0, 0);
	}


	if (cd.pitchPt.intervalS >= 0.f) {
		cd.pitchPt.intervalS -= fixedDeltaTime;
		if (cd.pitchPt.intervalS <= 0.f) {
			float sint = sin((playOutTime - cd.lkTime) * PT_SIN_T * core::PI) * PT_SIN_MUL;
			float v = 0;
			//(cd.pitchPt.idx % 2)*2;
			if (cd.keySound)
			{
				v = (cd.cPos.y - cd.ldpos.y) * PitchMulFxY + (chd[0].dtVelCache * chd[0].dtVelCache * PitchMulFxV) + sint;
				DP(("pitchPtFx %d dtVel %f", cd.ch, cd.dtVelCache));
				if (v > 2)
				{
					int x = 0;
				}
			}

			addPoint(cd, cd.pitchPt, 0, v);
			addPoint(cd, cd.loudPt, 0, std::min(1.0f, v * 0.1f));
			cd.pitchPt.intervalS = 0.05f;
		}
	}
}



void irr::scene::MmdMidiPlayer::playAtTime(int id)
{
	float t = playInTime - (MDPLAY_START_OFS + MIDI_FILE_OFS) - id * 0;

	if (!USE_SV_TIME) midi.getEvtAtTime(id, t);

	if (!pns[id].notes.size())
		return;
	auto& cd = chd[id];
#if USE_SV_TIME
	auto& gi = midi.geinfo[id];
	if (id < outTrackNum)
	{
		gi.act = 0;
		auto& p = pns[id];
		while (p.ntpid + 1 < p.notes.size() && t >= p.notes[p.ntpid + 1].time)
		{
			auto i = ++p.ntpid;
			gi.act = 1;
			gi.keyDn = p.notes[i].key;
			gi.evDur = p.notes[i].dur;
		}

	}
	else gi = midi.geinfo[0];
#else
	//while ( ntpid+1 < notes.size() && t >= notes[ntpid+1].time)	{		ntpid++;	 	}
#endif

}

void irr::scene::MmdMidiPlayer::aouWords(int c, std::vector<std::wstring> wds)
{
	auto& cd = chd[c];
	if (cd.aFxMax >= 2 && cd.aFxMax - cd.aFxCD < 2) return;
#if AOU 
	DP(("AOU %d", c));
	{
		cd.aFxMax = cd.aFxCD = wds.size();
		cd.aouWords = wds;
		cd.randomLyric = false;
		midi.mof.changeLyric(3, L"R", (playOutTime) * 1000);
		cd.aouKeyAdd = { 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 };
	}
#endif
}

void irr::scene::MmdMidiPlayer::aouStart(int c, int countDown, int fxid, bool textFw, bool random)
{
#if AOU 
	auto& cd = chd[c];
	if (cd.aFxMax >= 2 && cd.aFxMax - cd.aFxCD < 2) return;
	DP(("AOU %d cd=%d", c, countDown));

	{
		cd.aFxMax = cd.aFxCD = countDown;
		if (fxid >= 0)
			cd.aouWords = JmpLrc[fxid];
		cd.randomLyric = random;
		cd.aouKeyAdd = { 0,0,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2 };
		midi.mof.changeLyric(3, L"R", (playOutTime) * 1000);

	}
#endif
}

std::wstring irr::scene::MmdMidiPlayer::addLyric(int c, const std::wstring & s)
{
	auto& cd = chd[c];
	std::wstring txt;
	if (cd.aFxCD > 0) {
		int step = cd.aFxMax - cd.aFxCD;
		cd.aFxCD--;
		if (cd.aFxCD == cd.aFxMax - 1)
		{
			addLoudPoint(cd, cd.loudPt, -0.016, 0);
			addLoudPoint(cd, cd.loudPt, 0., 0.5);
		}
		else if (cd.aFxCD == 0) {
			addLoudPoint(cd, cd.loudPt, 0, 0);
		}
		else addLoudPoint(cd, cd.loudPt, 0, cos(float(step) / cd.aFxMax * core::PI / 2));
		if (step < cd.aFxMax) {
			if (cd.aFxId == 0 && cd.setLyric.size())
				txt = step <= 1 ? cd.setLyric : L"-";
			else if (step >= cd.aouWords.size())
				txt = L"";
			else {
				if (cd.randomLyric)
				{
					//txt = L"niao";// L"nia mu"; //
					txt = L"nmbphy"[ualib::UaRand(6)];// L"ni";
					//txt += L'a' + ualib::UaRand(26);
					txt += L"aeiou"[ualib::UaRand(5)];
					//txt += L"e";  
				}
				else {
					txt = cd.aouWords[step];
					float duration = getWordTime(cd);
					static float lastHitTime = 0;
					if (txt != L"br");
					if ((cd.dtVel.y > 0.6f || (cd.dtVel.y > 0.1f && cd.dtVelLenL>0.1f && cd.dtVelLen/cd.dtVelLenL>10)  ) && cd.lVel.y > -3.f) { //jump start
						static std::vector<std::wstring> ss = { 
							L"n br",L"m br"
							//L"nyau",L"nya" 
						};
						static std::vector<std::wstring> sl = { 
							L"n n br",L"m m br"
							//L"n nyau",L"n nya" 
						};
						txt = duration < 0.16 ? ss[ualib::UaRand(ss.size())] : sl[ualib::UaRand(sl.size())];
					}
					else if (cd.dtVelLen > 10.f && gSceneTime- lastHitTime>0.333f) // impact
					{
						lastHitTime = gSceneTime;
						static std::vector<std::wstring> ss = { L"nya",L"kya",L"gya" };
						txt = ss[ualib::UaRand(ss.size())];
					}
					else 
						if ( txt == L"@") {

						if (!g_lyricGenerator) 
							g_lyricGenerator = std::make_unique<KawaiiLyricGenerator>();				
						
						// Replace '@' with kawaii syllable
						txt = g_lyricGenerator->chooseSyllableForDuration( duration, cd);
						//DPWCS((L"chtxt %.1f, %f, %s", cd.dtVel.y * 100, duration,txt.c_str()));
					}

 				}
				if (cbOnFxText[cd.ch]) cbOnFxText[cd.ch](ualib::WcstoUtf8(txt), 0, 1);

				if (FX_TEXT_FW) textFw(c, txt);
				if (cd.ckey < 10)
					cd.ckey = 66;				//cd.ckey = JmpLrcKey;
			}
		}
		else txt = L"";
	}
	else {
		addLoudPoint(cd, cd.loudPt, 0, 0);
		txt = s;
	}

	midi.lyricOp(c, txt);
	chd[c].kdLyric = txt;
	return txt;
}



void irr::scene::MmdMidiPlayer::addLoudPoint(ChDat & cd, ChDat::PointsData & ptd, float ofsTime, float val)
{
	float addv = //(5.f / std::max(2.f, cd.camDis) - 1.f) * 1.0f;//
		//(20.f / std::clamp(cd.camDis, 3.0f, 20.f) - 1.f) * 0.3f;  // close scene
		calculateVolume(cd.camDis);
	//float addv = std::clamp( - (cd.camDis - 15) / 12 * 1.0f,-1.0f,1.25f);
	if (cd.ch == 0 && val > 0.f) {

		//DP(("C %d DIS %f   v %f", cd.ch, cd.camDis, val + addv)); 
	}
 
	addPoint(cd, ptd, ofsTime, cd.lastLoudVal = val + addv);
}

void irr::scene::MmdMidiPlayer::addOpenPoint(ChDat& cd, ChDat::PointsData& ptd, float ofsTime, float val)
{
	addPoint(cd, ptd, ofsTime, cd.lastOpenVal = val);
}

void irr::scene::MmdMidiPlayer::addPoint(ChDat & cd, ChDat::PointsData & ptd, float ofsTime, float _val, int mulPct)
{
	if (!recording) return;
	auto t = int64_t((realTime + ofsTime) * JBSEC + 0.5);
	float val = _val;
	if (val != 0 && t - ptd.lastTime < int64_t(0.025 * JBSEC + 0.5))
		return;

	if (t <= ptd.lastTime) {
		DP(("Adlt %d  t %f, %f  v=%f", cd.ch, realTime, t / double(JBSEC), val));
		//assert(t >= ptd.lastTime);
		t = ptd.lastTime + 1;
	}
	ptd.lastTime = t;
	//DP(("Adpt %d %f",cd.ch, ptd.lastTime/double(JBSEC)));
	auto size = ptd.pts.size();

	val *= ptd.mul;
	float lastv1 = size > 1 ? ptd.pts[size - 1].asFloat() : 0.f;
	float lastv2 = size > 3 ? ptd.pts[size - 3].asFloat() : 0.f;
	if (size > 3 && abs(val - lastv1) < 0.1f * ptd.mul && abs(val - lastv2) < 0.1f)
	{
		ptd.pts[size - 2] = t;
		ptd.pts[size - 1] = val;
		//DP(("PT = %d, %lld(%.2f) = %f", offsetof(ChDat, loudPt), t, realTime + ofsTime, val));

	}
	else
	{
		ptd.pts.append(t);
		size = ptd.pts.size();
		if (mulPct != 100 && ptd.pts.size() > 2)			val = std::max(val, lastv1 * mulPct / 100.f);

		ptd.pts.append(val);
		//DP(("ch[%2d] PT+  i=%d, ofs%d, %lld(%.2f)  V= %f, o=%f",cd.ch,ptd.idx, (int) & ptd - (int) & cd, t, realTime + ofsTime, val, ptd.pts[size - 1].asFloat()));
		ptd.idx++;
	}

}

void irr::scene::MmdMidiPlayer::noteEnd(int c, ChDat & cd)
{
	if (c == 1)
	{
		DP(("end 1"));
	}
	MIDI_OP_PLAY(c, cd.ldkey, 0, 0);
	if (cd.kdLyric.size() == 0) return;
	float dt = std::min(realTime - cd.lkRealTime, cd.aFxCD>1 && cd.velLen<25.f? MAX_SHORT_NOTE_TIME: MAX_LONG_NOTE_TIME);
	int64_t t = std::max(cd.lastSvTime, int64_t((cd.lkRealTime) * JBSEC + 0.5)),
		dur = int64_t((dt) * JBSEC + 0.5);
	if (t - cd.lastSvTime <= 3LL)
		t = cd.lastSvTime;
	cd.lastSvTime = t + dur;
	jbn["duration"] = dur;
	cd.endedDur = realTime - cd.lkRealTime;
	jbn["onset"] = t;
	jbn["pitch"] = cd.ldkey + cd.fxAdKey;
	jbn["lyrics"] = ualib::WcstoUtf8(cd.kdLyric);// +std::to_string(cd.aFxCD);
	svOutNotes[c].append(jbn);
	DPWCS((L"NOTE %f , %f | %f , %f , %s", cd.lkRealTime, t / JBSEC, realTime, cd.lastSvTime / JBSEC, cd.kdLyric.c_str()));
}

void irr::scene::MmdMidiPlayer::combinePts(ChDat::PointsData & pd, irr::scene::MmdMidiPlayer::ChDat & cd, Json::Value & jspts, uint32_t filter)
{
	if (jspts.size() == 0)
	{
		jspts.append(0); jspts.append(0.f);
		jspts.append(int64_t(JBSEC * 60)); jspts.append(0.f);
	}
	auto jsptt = pd.pts;

	struct PtVal { int64_t t; float v; };
	std::vector<PtVal> ln1, ln2, lnc;
	std::vector<glm::f64vec2> sp1, sp2, spc;
	//Eigen::RowVectorXd t1(jspts.size()/2), v1(jspts.size()/2), t2(jsptt.size()/2), v2(jsptt.size()/2);

	double lt = -1;
	for (int i = 0; i < jspts.size(); i += 2)
	{
		PtVal pv = { jspts[i].asInt64(),jspts[i + 1].asFloat() };
		ln1.emplace_back(pv);
		double t = pv.t / double(JBSEC);
		sp1.emplace_back(t, pv.v);
		assert(t > lt);
		lt = t;
		//t1[i/2] = pv.t / double(JBSEC);
		//v1[i/2] = pv.v;
	}
	auto lne1 = ln1[ln1.size() - 1]; lne1.t += 100;	ln1.emplace_back(lne1); sp1.emplace_back(lne1.t / double(JBSEC), lne1.v);

	for (int i = 0; i < jsptt.size(); i += 2)
	{
		PtVal pv = { jsptt[i].asInt64(),jsptt[i + 1].asFloat() };
		ln2.emplace_back(pv);
		sp2.emplace_back(pv.t / double(JBSEC), pv.v);
		//t2[i/2] = pv.t / double(JBSEC);
		//v2[i/2] = pv.v;
	}
	auto lne2 = ln2[ln2.size() - 1]; lne2.t += 100;	ln2.emplace_back(lne2); sp2.emplace_back(lne2.t / double(JBSEC), lne2.v);
	const int degree = 4;
	//Eigen::Spline<double, 1, degree> spline1(Eigen::SplineFitting<Eigen::Spline<double, 1, degree>>::Interpolate(v1, degree, t1));
	//Eigen::Spline<double, 1, 2> spline2(Eigen::SplineFitting<Eigen::Spline<double, 1, 2>>::Interpolate(v2, 2, t2));
	auto filterSp = [=](std::vector<glm::f64vec2>& sp1, int count)
		{
			for (int i = 0; i < count; i++);
			{
				auto spt = sp1;
				for (int i = 1; i < sp1.size() - 1; i++) {
					spt[i].y = (sp1[i - 1].y + sp1[i].y * 2 + sp1[i + 1].y) / 4;
				}
				sp1 = spt;
			}
		};
	if (filter & 1)
		filterSp(sp1, 1);

	int i1 = 0, i2 = 0, ci = 0;

	while (i1 < ln1.size() - 1 && i2 < ln2.size() - 1)
	{
		float x, y;
		if (abs(sp1[i1].x - sp2[i2].x) < 0.001f) {
			x = (sp1[i1].x + sp2[i2].x) / 2;
			y = (sp1[i1].y + sp2[i2].y) / 2;
			DP(("CMB = %d = %d  , %f  %f ", i1, i2, sp1[i1].x, sp2[i2].x));
			i1++; i2++;

		}
		else if (sp1[i1].x < sp2[i2].x)
		{
			DP(("CMB = %d < %d  , %f  %f ", i1, i2, sp1[i1].x, sp2[i2].x));
			x = sp1[i1].x;	y = sp1[i1].y;
			if (i2 > 0) {
				float r = (sp1[i1].x - sp2[i2 - 1].x) / (sp2[i2].x - sp2[i2 - 1].x);
				assert(r >= -0.001f && r < 1);
				if (r < 0) r = 0;
				y += sp2[i2 - 1].y * r + sp2[i2].y * (1 - r);
			}
			i1++;
		}
		else
		{
			DP(("CMB = %d > %d  , %f  %f ", i1, i2, sp1[i1].x, sp2[i2].x));

			x = sp2[i2].x;	y = sp2[i2].y;
			if (i1 > 0) {
				float r = (sp2[i2].x - sp1[i1 - 1].x) / (sp1[i1].x - sp1[i1 - 1].x);
				assert(r > 0 && r < 1);
				y += sp1[i1 - 1].y * r + sp1[i1].y * (1 - r);
			}
			i2++;
		}
		spc.push_back({ x,y });


	}
	DP(("ln1 %f  ln2 %f", sp1[i1].x, sp2[i2].x));
	while (i1 < ln1.size() - 1) spc.push_back(sp1[i1++]);
	while (i2 < ln2.size() - 1) spc.push_back(sp2[i2++]);

	if (filter & 2)
		filterSp(spc, 1);

	pd.pts.clear();
	for (auto& p : spc) {
		pd.pts.append(int64_t(p.x * JBSEC + 0.5));
		pd.pts.append(p.y);
	}
}


void MmdMidiPlayer::textFw(int c, std::wstring txt, bool splitTxt)
{
	if (c >= mmdCount) return;



#if TEXT_FW	
	auto Eqv = Ctx->eqv.eqv;
	ualib::SubtitleData sd;
	// txt = std::to_wstring(hdrBit) + L" bit";
	sd.ws = txt.size() ? ualib::wcsReplaceAll(txt, L"\r\n", L"\n") : L"♪";// L"大威天龍";//L"镜花水月";

	sd.startMs = 0;

	ualib::ass::AcTextRange rg;
	int ofsMs = 0, timeDur = 100;
	if (splitTxt)
		for (int i = 0; i < sd.ws.length(); i++)
		{
			auto ch = sd.ws[i];
			if (ch == L' ' || ch == L'\n') continue;
			rg.txt = ch;
			rg.len = 1;	rg.start = i;
			rg.timeOfs = ofsMs;
			rg.timeDur = timeDur;
			ofsMs += timeDur;

			sd.ex.ranges.push_back(rg);
		}
	else {
		rg.txt = txt;
		rg.len = txt.size();	rg.start = 0;
		rg.timeOfs = 0;
		rg.timeDur = 100;
		sd.ex.ranges.push_back(rg);
	}
	sd.endMs = ofsMs;
	sd.fontId = Eqv->dmFtTxtPm.fontId;
	//Eqv->mTfpm.tgtRtt = float3(pm.rtt.x, pm.rtt.y, pm.rtt.z);
	//Eqv->mTfpm.srcVelType = 1;
	//Eqv->mTfpm.srcVel = V3dToFloat3(ve);
	sd.hasMatrix = 1;
	//sd.absPos = 1;
	if (sd.hasMatrix) {
		Eqv->mTfpmUE.srcPos = Eqv->mTfpmUE.tgtPos = vector3df(0, 0, 0);
	}
	sd.fwIdStr = FontName[c];
	sd.fsize = FontSize[c] * 100;

	if (pns[c].notes.size() && pns[c].ntpid >= 0)
	{
		auto ci = pns[c].ntpid;
		float ct = pns[c].notes[ci].time;

		if (ci == 0 || ct - pns[c].notes[ci - 1].time > 0.1f)
		{
			std::wstring s; s += pns[c].notes[ci].lyric;
			wdcIdx = 0; 		int cc = 1;
			while (++ci < pns[c].notes.size()) {
				if (pns[c].notes[ci].time - ct > 0.1f)	break;
				ct = pns[c].notes[ci].time; cc += sd.ex.ranges.size();
			}
			wdcMax = cc;
			DPWCS((L"STR %s", s.c_str()));

		}
		else wdcIdx++;
		sd.wdcIdx = wdcIdx;
		sd.wdcMax = wdcMax;//		DP(("MAX %d",cc));
		sd.key = chd[c].ckey;
	}
#if TEXT_FW_1CH_TO_ALL
	static int sbcc = 0;
	sd.sbid = sbcc++;
#else
	sd.sbid = c;
#endif
	sd.vfg.hitCvt = 0;
	sd.vfg.hitGroundCvt = true;
	sd.vfg.hitVelChgLimit = 1.f;
	//sd.isSVG = SVG_MMD_RB;
	Eqv->launchTextFw(sd, true, 1);

#endif
}

float irr::scene::MmdMidiPlayer::getWordTime(const ChDat & cd)
{
	if (cd.aFxMax < 1) return 1.f;
	int step = cd.aFxMax - cd.aFxCD;
	float rat = float(step) / cd.aFxMax;
	float t = glm::clamp(0.5f / (cd.dtVelLen+glm::length(cd.cVel)/10.f), 1 / 10.f, 1.f) * curFrameTimeMul;
	//if (cd.aFxCD > 1)	t = std::min( 0.166f+rat, t);
	if (cd.aFxCD > 0) t = std::min(t, 0.25f) * curFrameTimeMul;
	t = std::clamp(t, 0.1f, 0.5f);
	//DP(("WordT %d %f", step, t));
	return t;
}


void irr::scene::MmdMidiPlayer::loadWords()
{
	std::vector<std::string> fxstrs{
		//"punch","kicku","atama","words","words"
	//"cnJmp","cnA","cnDash","cnF","cnE","cnQ","","","","",
	//"Niao","Niao","Niao","Niao","Niao2","Niao2","","","","",
	//"ha","hei","niao","niao","niao","niao","","","","",
	
	"are","ofu","niao","waku","mipa",
	"ha","hei","are","aha","ehe",
	"faetong",
	"jpGirlMa","jptest","a3","ha2","cn2","cn3","cn4","cn5"
	};

	for (auto& fxstr : fxstrs)
	{
		SvFxStruct fx;
		if (fxstr.size()) {
			jssWords.SetFile(std::string("data/svp/") + fxstr + ".svp", true);
			auto& root = jssWords.refRootValue();

			double jbsec = 705600000.0 * 2;//120BPM
			for (auto& t : root["tracks"] DOT_AS_ARRAY)
			{

				SvWordStruct sw;
				sw.id = t["dispOrder"].asInt();
				sw.name = t["name"].asString();
				sw.jNotes = t["mainGroup"]["notes"];
				sw.jTrack = t;
				sw.startI64 = sw.jNotes[0]["onset"].asInt64();
				auto& lastNote = sw.jNotes[sw.jNotes.size() - 1];
				sw.endI64 = lastNote["onset"].asInt64() + lastNote["duration"].asInt64();
				sw.durationI64 = sw.endI64 - sw.startI64;
				sw.duration = sw.durationI64 / jbsec;
				fx.words.emplace_back(sw);
			}

			std::sort(fx.words.begin(), fx.words.end(), [](auto& a, auto& b) {return a.id < b.id; });
		}
		fxs.emplace_back(fx);
	}

}
