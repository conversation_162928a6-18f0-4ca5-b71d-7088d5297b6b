﻿#include "AppGlobal.h"
#include "CharacterAttacker.h"
//AMP  single thread render  31fps 2ms 10 xl.zip  paused=770fps 
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtx/euler_angles.hpp>
#include <stlUtils.h>
#include <Saba/Model/MMD/MMDNode.h>
#include <Saba/Model/MMD/MMDModel.h>
#include <Saba/Model/MMD/MMDPhysics.h>
#include <Saba/Base/File.h>
#include <UaUtils.h>
#include <helpers/UpUtils.h>
#include <irrfw/eqv/EQV.h>
#include "IrrMMD.h"
#include "irrSaba.h"
#include "vulkanrenderer/VkMr2D.h"
#include "IrrFw/SvgMan.h"
#include "../../AppMainLib/app/ArMmPLayer/SnArItem.h"
#include "LeapMan.h"
#include "PhyObjMan.h"
#define DBG_ATK					  IS_WIN_DBG
#define DBG_ATKBALL1			0
#define BASE_FORCE_MUL			3700.f //3700.0f
#define ATK_FORCE_MUL			1.7f //def=2.f
#define NO_PHY_ANIM_WHEN_ATTACK			1


#define FORCE_LEG_ATK			0
#define ANG_ADD					0
#define YAO_ANTI_FORCE			1

#define ATK_VOICEFX				0
#define LOCK_LOOK				0
#define FORCE_NEAREST_NID		(MMD_GRAB_CONNECT||1)

#define FAR_DETECT				0	
 
#define CENTER_FORCE_MOD		0
#define	KICK_TO_CAM				0
#define SAFE_MMD				0
#define MAX_LOCKER_SB			10
#define DETECT_LEAVE_MAX		0  //0-3
#define ATKDP DP				// DP on off

#if DBG_ATK// IS_WIN_DBG
#define MMDFWD(T,N,P, V, C) 		do{ static int fid = Eqv->getFwIdxByFwIdStr(T,N); Sb->mmdFw(P, fid, V , C);}while(0) //FID will not change at runtime
#else
#define MMDFWD( ) 
#endif
#define MMDFW(T,N,P, V, C) 		do{ static int fid = Eqv->getFwIdxByFwIdStr(T,N); Sb->mmdFw(P, fid, V , C);}while(0) //FID will not change at runtime

#define DPVEC(_A_)   ATKDP(("VEC " #_A_ " = %3.3f  %3.3f  %3.3f",_A_.x,_A_.y,_A_.z))
#define DPVEC2(_A_,_B_)   ATKDP(("VEC " #_A_ " = %3.3f  %3.3f  %3.3f | VEC " #_B_ " = %3.3f  %3.3f  %3.3f",_A_.x,_A_.y,_A_.z,_B_.x,_B_.y,_B_.z))

using namespace glm;
using namespace uu;
using namespace irr::scene;
using namespace irr::core;
using namespace EQVisual;
using namespace saba;
using namespace ualib;
namespace {
#define PI 3.14159265359f
#define BASE_RANGE_SCALE 1.57f// 2.f//
#define ONLY_HEAD_UP		0	//头只顶上方小角度以内的
	int lockMax = MMD_NID_MAX;	const float HeadRttMul = 200.f; float rangeScale = BASE_RANGE_SCALE;
	const float handM = 3, handMF = 0.25f, footMF = 0.39f;

	//enum ENdId {eNdIdNone = -1,			ehL=0,	ehR=1,	efL=2,	efR=3,	elL=4,	elR=5,	eHd=6,	eYa=7,	eoL=8,	eoR=9 };
	//static float scoreAdd[] = {			1.f,	1.f,	0.f,	0.f,	1.f,	1.f,	1.f,	1.0f,	1.0f,	1.0f };
	static float forceMul[MAX_CA_NODES] = { handM,	handM,	2.5,	2.5,	0.9,	0.9,	0.7,	0.5,	0.2,	0.2 };
	static float farAdMul[MAX_CA_NODES] = { handMF,	handMF,	footMF,	footMF,	0.3,	0.3,	0.1,	0.02,	0.5,	0.5 };
	static int ndCtrLevel[MAX_CA_NODES] = { 2,		2,		2,		2,		0,		0,		1,		0,		0,		0 };
	static float actDisRs[MAX_CA_NODES] = { 7.5f,	7.5f,	12.7f,	12.7f,	6.f,	6.f,	6.f,	5.f,	6.f,	6.f };
	static float atkDisRs[MAX_CA_NODES] = { 3.f,	3.f,	3.9f,	3.9f,	3.f,	3.f,	3.1f,	3.0f,	3.5f,	3.5f };
	static const int earlyHit[MAX_CA_NODES] = { 1,	1,		1,		1,		0,		0,		0,		0,		0,		0 };
	static vec4 grabOfs[MAX_CA_NODES] = { vec4(-1, -1,0, 1),vec4(1,-1, 0,1),//kasa
		//vec4(0.2,0.35,.05, 1),vec4(-0.2,0.35,.5, 1),
		{},{},{},	{0,0,1,1},{0,-1,-1.2,1},{0,0,1,1},{0,0,1,1},{0,0,1,1} };
	static vec3 grabRtt[MAX_CA_NODES] = { { -PI*3 / 4, -PI / 2,  0 } ,{ -PI*3 / 4, PI / 2,  0 } };
}

float correctY(vector3df& ndPos, const vector3df& objPos, const vector3df& objVel, const float& r, float& t) {
	vector2df np2d(ndPos.x, ndPos.z), op2d(objPos.x, objPos.z);	t = (np2d.getDistanceFrom(op2d) - r) / vector2df(objVel.x, objVel.z).getLength();	t = t * t * (-9.81 * PHY_GRAVITY_MUL) / 2;	ndPos.y += t;
	return t;
};
void irr::scene::CharacterAttacker::init(const CharacterAttackerParam& pm)
{
	Sb = pm.sb;
	mmd = Sb->mmd;
	rootRb = Sb->Rb0(); nd0 = rootRb->node;
	armGrab = SBATK_CATCHER & 1; legGrab = SBATK_CATCHER&2;

#if MMD_GRAB_CONNECT
	forceMul [eHd] = 6.0f; farAdMul[eHd] = 2.0f; actDisRs[eHd] = 7.f; atkDisRs[eHd] = 5.f;
#endif

	std::copy(std::begin(pm.nds), std::end(pm.nds), std::begin(nds));
	Rcv = Sb->Eqv->Ctx->getEvtRcv();
	Pmx = Sb->Pmx;	Pom = Sb->Pom; Eqv = Sb->Eqv;
	static int cc = 0; cc++;
	for (int i = 0; i < MAX_CA_NODES; i++) {
		atks[i].nid = i;
		if (cc == 1) { actDisRs[i] *= rangeScale;	atkDisRs[i] *= rangeScale; }
		if (nds[i]) nds[i]->atk = &atks[i];
	}

#if DBG_ATKBALL1
	if (!snAtkDbg) {
		snAtkDbg = Sb->getSceneManager()->addSphereSceneNode(1, 32, Sb); snAtkDbg->setMaterialDiffuse(0x80808080);			snAtkDbg->setMaterialType(EMT_POINT_CLOUD);			snAtkDbg->getMaterial(0).Wireframe = true;			snAtkDbg->setOrderForTransparent(1);
		snAtkDbgT = Sb->getSceneManager()->addSphereSceneNode(1, 32, Sb); snAtkDbgT->setMaterialDiffuse(0xFFFF8080);			snAtkDbgT->setMaterialType(EMT_POINT_CLOUD);			snAtkDbgT->getMaterial(0).Wireframe = true;			snAtkDbgT->setOrderForTransparent(1);
	}
#endif
}

void irr::scene::CharacterAttacker::attackObjUpdate(float steptime, int step, int stepCount)
{
	if (!Pom || !Pom->phyObjs.size() || !enable) return;
	timeMul = steptime * 60;
	//if (SAFE_MMD) { forceMul[efL] *= 0.5; forceMul[efR] *= 0.5; forceMul[elL] *= 0.5; forceMul[elR] *= 0.5; }
	bsc = Sb->ndYao->absScale.x; rangeScale *= bsc;
	memset(locked, 0, sizeof(locked));
	memset(acted, 0, sizeof(acted));
	if (!Sb->ndHead->rb0) return;
	headPos = Sb->ndHead->rbPos();
	yaoY = Pmx->yaoPos.y * Sb->ndYao->absScale.y;
	canLockNum = 0;// _countof(locked);
	//if (sb->getItemIdx() == 0) lockMax = 2;
	for (int i = 0; i < lockMax; i++) {
		if (ndctr[i] = nds[i]) canLockNum = i + 1; else break;
		auto p = ndctr[i]->GetParent();
		while (p) {
			if (p->rb0) { ndctr[i] = p; p = p->GetParent(); if (--ndCtrLevel[i] <= 0) break; }
			else p = p->GetParent();
		}
	}
	ndctr[eoL] = ndctr[eoR] = Sb->ndUpper2; ndctr[eHd] = Sb->ndUpper2;

	attackLockNodes(steptime, step, stepCount);

	for (int i = 0; i < canLockNum; i++) {
		auto& atk = atks[i];
		if (atk.objId > 0 && (
			atk.kicked >= MAX_KICK && gPhyTime - atk.lastLockTime > 0.1f
			|| !Pom->objRec[atk.objId].ifObj || !Pom->objRec[atk.objId].ifObj->ageLockable()))
		{
			lastNid = ENdId(i);
			auto obj = Pom->objRec[atk.objId].ifObj;


			if (obj) obj->ndLock_remove(nds[i]);


			if (MMD_GRAB_CONNECT && obj) obj->rb->setCollideFilterMask(0, 0);
			lastKickObjId = atk.objId;
			atk.resetStatus(true);

			auto nd = nds[i];
			if (nd->phyAnim == 2) {
				if (isNid_h(lastNid)) Sb->setPhyAnim(nd->GetParent()->GetParent()->GetParent()->GetParent(), 1, true);
				if (isNid_f(lastNid)) Sb->setPhyAnim(nd->GetParent()->GetParent(), 1, true);
			}
			//sb->ndYao->rbAtk->setLinearVel(sb->ndYao->rbAtk->getLinearVel() * vec3(0, 1, 0));
			ATKDP(("Lock RESET %d  ", i));
		}

#if MMD_GRAB_CONNECT
		if (atk.grabCount == 0 && isNid_h((ENdId)i))
		{
			nds[i]->rbAtk->addLinearVel(vec3(0, 9.81f * PHY_GRAVITY_MUL * 3, 0) * steptime);
		}
#endif
	}

	detectLockNodes(steptime, step, stepCount);

	auto animPhy = [&](MMDNode* n, float mul, int lvl, float rttMul = 0) {
		if (n->phyAnimCD > 0.01f) {
			n->phyAnimCD -= steptime;
			n->rbAtk->addForce((vec3((n->mGlobalAnim * n->rbAtk->getOfsMat())[3]) - n->rbAtk->getPosition()) * n->phyAnimRatio() * mul);
			if (rttMul > 0.f)						n->rbAtk->setAngVelToPos(Sb->mmdLookAt, rttMul);
		}
		};
#if ANIMPHY_AFTER_HIT
	if (!locked[efL]) animPhy(nodes[efL], 1.f, 3);	if (!locked[efR]) animPhy(nodes[efR], 1.f, 3);
	if (!locked[ehL]) animPhy(nodes[ehL], 1, 3);	if (!locked[ehR]) animPhy(nodes[ehR], 1, 3);
	animPhy(ndUpper2, 1, 3, 10.01);
#endif

	if (abs(angAdd.y) > 1.f)
	{
		Sb->ndYao->rbAtk->addTorque(-angAdd * vec3(1000, 1000, 1000));
		angAdd *= (1 - steptime * 8);
		DPVEC(angAdd);
		MMDFWD(2, "hitFw", vec3(0, 30, 0) + angAdd, vec3{ 0.f }, SColorf(1.0, 0, 0.0, 1));
	}

	if (step == 0) {
		for (auto& igo : ignoreObjs)
		{
			igo.second -= steptime * stepCount;

		}
		const auto count = std::erase_if(ignoreObjs, [](const auto& item)
			{
				//auto const& [key, value] = item;
				return (item.second<0);
			});
	}
}

void irr::scene::CharacterAttacker::reset()
{
	for (int i = 0; i < canLockNum; i++) {
		auto& atk = atks[i];
		if (atk.objId > 0)
		{
			auto obj = Pom->objRec[atk.objId].ifObj;
			if (obj) obj->ndLock_remove(nds[i]);
			lastKickObjId = atk.objId;
			atk.resetStatus(true);
			//sb->ndYao->rbAtk->setLinearVel(sb->ndYao->rbAtk->getLinearVel() * vec3(0, 1, 0));
			ATKDP(("Lock!RESET %d   ", i));
		}
	}
}




void irr::scene::CharacterAttacker::attackLockNodes(float steptime, int step, int stepCount)
{
	auto rb0nt = rootRb->getNodeTransform();
#if DBG_ATKBALL1
	snAtkDbg->setVisible(false); snAtkDbgT->setVisible(false);
#endif
	bool firstLock = true;
	auto it = Pom->phyObjs.end() - 1;
	MMDNode* node{};
	PhyObj* obj{};

	for (int i = 0; i < canLockNum; i++) if ((node = nds[i]) && node->rb0 && atks[i].objId > 0) {
		auto& atk = atks[i];
		node->atkLocking = false;
		if (atk.kicked >= MAX_KICK) continue;
		obj = Pom->objRec[atk.objId].ifObj; if (!obj) continue;
		auto& o = *obj;
		auto Os = o.sb;
		ENdId nid = (ENdId)i; if (acted[nid]) continue;
		float objR = (o.rb->Pm.m_shape == PMXRigidbody::Shape::Box ? std::min(o.rb->getSize().x, std::min(o.rb->getSize().y, o.rb->getSize().z)) : o.rb->getSize().x);
		vec3 ndPos = vec4(node->rbAtk->getPosition(), 1.f);
		auto pn = ndctr[nid];
		vec3 pnPos = pn->rbAtk->getPosition();
#if DBG_ATKBALL1
		assert(nid >= 0); snAtkDbg->setVisible(true); snAtkDbgT->setVisible(0);
#endif
		locked[nid] = 1;
		vec3 objv = o.rb->getLinearVel(); if (glm::length2(objv) < 0.01f) continue;
		vec3 opos = o.rb->getPosition();
		if (o.rb->node) 
			opos = o.rb->node->rbPos();		
		vec3 tposNd = opos, tpos = opos, tvel;
		vec3 nvel = node->rbAtk->getLinearVel();
		auto dirp = opos - ndPos; auto lenp = glm::length(dirp);
		float len2pn = pn->disTo(opos);
		bool onActBall = false, inActRange = false;
		node->atkLocking = true;
		line3df line(opos, opos + objv * 1.f);
		vec3 nearPos{};
		auto odir = opos - ndPos;
		auto olen = glm::length(odir);
		float actDis =actDisRs[nid];
		float atkRange = atkDisRs[nid] / BASE_RANGE_SCALE + objR;
 		bool grabNode = (armGrab||legGrab) && nid == eYa;

		if (grabNode) { atkRange = 0.0f;  actDis = Pmx->legLength * bsc* (legGrab?1.f:0.6f) ; }
		bool outAtkRange = olen > atkRange;
		
		//	for (int i = 0; i < 10; i++) 	MMDFWD(2, "sw1", o.rb->predictPosition(0.1*i), vec3{ 0.f }, SColorf(1, 0.0, 0.0, 1));
		
		//CALC PATH
		const int MAX_POS = 120; const float stept = 1 / 60.f; float tm = 0, minT = 100; float linDamp = o.rb->Pm.m_translateDimmer;
		vec3 ps[MAX_POS], vs[MAX_POS]; float tms[MAX_POS]; int pc = 0;
		{
			vec3 v = objv, p = opos, g = vec3(0, -9.81f * PHY_GRAVITY_MUL, 0);
			for (int i = 0; i < MAX_POS; i++) {
				vs[pc] = v; ps[pc] = p; tms[pc] = tm;  pc++;
				if (step == 0) MMDFWD(2, "pt", p, vec3{ 0.f }, SColorf(0, 1.0, 0.0, 1));

				v += stept * (g - linDamp*v); p += stept * v; tm += steptime;
				if (p.y <= o.rb->Pm.m_shapeSize.x) { p.y = o.rb->Pm.m_shapeSize.x; v.y = -v.y;	v *= HIT_GROUND_VMUL; }
			}
		}
		float minDis2 = 9999999.f; int minI = 0 ,firstInRgI = 0;
		float rg2 = pow(actDis / BASE_RANGE_SCALE, 2.f);
		int iOnBall = -1, inBallCC = 0;
		for (int pi = 0; pi < pc; pi++) {
			float dis2 = glm::length2(ps[pi] - ndPos);
			if (dis2 < minDis2) {
				float disp = glm::length(ps[pi] - pnPos);
				float rg = actDis / BASE_RANGE_SCALE;
				if (disp > rg * 1.5f)
					continue;
				minDis2 = dis2;
				minI = pi; minT = tms[minI];
				if (!firstInRgI && dis2 < rg2) {
					firstInRgI = pi;
					break;
				}
				if (earlyHit[nid]) {
					if (disp < rg * 0.7f) {
						if (inBallCC == 0) iOnBall = pi;
						inBallCC++;
						break;
					}
				}
				else if (disp < rg * 0.7f) {
					if (inBallCC == 0) iOnBall = pi;
					inBallCC++;
				}

			}
			  
		}

		//		if (inBallCC > 1)			minI = iOnBall; old early hit 
		if (len2pn > actDis + objR || 1)
		{
			vector3df rbPos = pnPos;
#if 1
			float minDis2 = 9999999.f, lastDis2 = 9999999.f;
			int ib = std::max(0, minI - 1), ie = std::min(MAX_POS - 1, minI + 1), ic = 2 / 60.f / steptime;
			vec3 p = ps[ib], v = vs[ib], minP = p, minV = v; vec3 g = vec3(0, -9.81f * PHY_GRAVITY_MUL, 0);

			if (earlyHit[nid])
			{
				minP = ps[minI]; minV = vs[minI];
			}
			else for (int i = 0; i < ic; i++) {
				v += steptime * g; p += steptime * v;
				float dis2 = glm::length2(p - ndPos);
				if (dis2 < minDis2) {
					minDis2 = dis2;
					minP = p; minV = v;
				}
				if (dis2 > lastDis2)
					break;
				lastDis2 = dis2;
			}
			//MMDFWD(2, "sw1s", minP, vec3(0, -100, 0), SColorf(1, 0, 1, 1));
			if (nid == eYa && legGrab)
			{

			}
			tpos = minP; tvel = minV;// -minV * (lenp / glm::length(objv));// ps[std::max(0, minI - std::min(3, int(lenp / glm::length(objv) + 1)))];
			tposNd = tpos;
#else
			tpos = nearPos = line.getClosestPoint(pnPos);
			if (o.vel.getLengthSQ() >= 0.0001f)	tpos += -glm::normalize(vec3(o.vel)) * std::min(actDis / BASE_RANGE_SCALE / 2, glm::length(tpos - opos) * .33f);
#endif

			if (earlyHit[nid] && outAtkRange)
				if (!atk.tposLocked) {
					atk.tposLocked = true; atk.tposNd = tposNd = tpos + (KICK_TO_CAM ? glm::normalize(tpos - Sb->mmdLookAt) * (objR * 1) : vec3(0));
				}
			//tposNd = atk.tposNd;

#if DBG_ATKBALL1
			snAtkDbg->setPosition(pnPos);
			snAtkDbg->setScale(actDis); snAtkDbg->setMaterialDiffuse(0x80606060);

			snAtkDbgT->setPosition(nds[nid]->getGlobalPos());
			snAtkDbgT->setScale(1); snAtkDbgT->setMaterialDiffuse(0x30FFCCCC);
#endif

			if (Sb->charAtk.adjForceOnTime)
			{
				//ADJUST FORCE ON DIS/TIME
				auto tlen = glm::length(tpos - ndPos);
				if (atk.firstTDis < 1000.f)
				{
					float cr = tlen / atk.firstTDis;
					float dur = atk.Tdur, dt = gPhyTime - atk.firstTTime;
					if (dt < dur) {
						float tr = std::clamp((dur - dt) / dur, 0.01f, 1.f);
						atk.forceMul = tr / cr;
					}
				}
				else //1st add on obj
				{
					atk.firstTTime = gPhyTime; atk.firstTDis = tlen;
					atk.Tdur = minT; atk.forceMul = 1;
				}
			}
			else  atk.forceMul = 1;

		}
		else {
			atk.forceMul = 1;
			actDis = len2pn;
			inActRange = true;
			float hitRat = len2pn / (atkDisRs[nid] * 1.0f);
			//tpos += objv * steptime*1.f;// +vec3(0, (hitRat > 2 ? -1 : hitRat < 1 ? 0 : 1 - 2 * hitRat) * 1, 0);// (lenp + dirp.y / 2) * 0.0035f;
#if DBG_ATKBALL1
			snAtkDbg->setVisible(0);
			snAtkDbgT->setPosition(nds[nid]->getGlobalPos());
			snAtkDbgT->setScale(1); snAtkDbgT->setMaterialDiffuse(0x60FF8080);
#endif
		}
		auto mi = glm::inverse(Sb->ndYao->GetGlobalTransform());
		auto t2c = mi * vec4(tpos, 1);
		//DPVEC2(tpos,t2c);
		bool isL = node == nds[ehL] || node == nds[elL] || node == nds[eoL];
		float mul = forceMul[nid] * o.lockPower * (1 + 1);// std::clamp(pow((1000000.f + dirP.y * 10000), 0.7f) / tlen, 100.f, 1000.f);

		//if (nid == eoL || nid == eoR)	{		MMDFW(2, "sw2", node->getGlobalPos()+vec3(0,0,-1), vec3(0, 0, 0), SColorf(1,0.3,0.3, 1));			}
		bool leaving = atk.lastDis <= olen && minI < 2;
		{
			float mul = 1;
			if (grabNode) if (olen > Pmx->legLength * bsc) mul = 3.0f; else leaving = false;
			if (leaving && (olen > 3 * bsc) 
				//&& !(o.oTgtSb && o.oTgtSb == Sb)
				) 
			{
				atk.addKick(/*nid == ehL || nid == ehR ? MAX_KICK / 3 * timeMul : */MAX_KICK / 6 * timeMul * mul, __LINE__);
				atk.leaveCount++;
				if ((nid == eHd && atk.leaveCount > 1) || atk.leaveCount > int((4 - atk.stage) * .25f / timeMul + 0.5f) && !grabNode) {
					atk.addKick(MAX_KICK, __LINE__); o.rb->usrDat.lastContact = 0;
					o.leaveCount[this]++; if (MMD_GRAB_CONNECT) o.rb->setCollideFilterMask(0, 0);
					//node->rbAtk->setMassMul(1.f);
					ATKDP(("LEAVE _________________________________________ count %d ___________________", atk.leaveCount));
					continue;
				}
			}
			else atk.leaveCount = 0;
		}
		if (step == 0) {
			//	for (int i = 0; i <= 30; i++) { MMDFWD(2, "pt", line.start.getInterpolated(line.end, i / 30.f), vec3{ 0.f }, SColorf(1, 1, 1, 0.3)); }
			MMDFWD(2, "sw2", tpos, vec3{ 0.f }, SColorf(1, .5, 1.0, 1));
			MMDFWD(2, "sw2", tposNd, vec3{ 0.f }, SColorf(1, .5, outAtkRange, 1));
			MMDFWD(2, "sw2", opos, vec3{ 0.f }, SColorf(0, 0, 1, 1));

		}

		auto tdir = tposNd - ndPos;// every step not same
		
		auto tlen = glm::length(tdir);
		if (tlen==0) tdir = vec3(0,0,-1);

		if (step == 0) {
			atk.vsc = atk.lastTDis >= tlen || atk.lastDis > olen ? 1.f : 0.1f;
			//ATKDP(("VSCCCCCCCCCCCCCCCCC %f   l %f  c %f", atk.vsc, atk.lastTDis, tlen));
			atk.lastTDis = tlen;
			MMDFWD(2, "sw2", nds[nid]->getGlobalPos(), vec3{ 0.f }, SColorf(1.1 - atk.vsc, 0, 0, 1 - atk.vsc));
		}
		atk.lastDis = olen;


		//glm::length(tpos-opos) > atkDisRs[nid] ;
		if (isNid_h(nid) && outAtkRange) {
			auto tpos1 = tpos + tvel * (0 / 60.f); ;
			auto tpos2 = tpos + tvel * (2 / 60.f);
			tpos = glm::length(tpos1 - pnPos) > glm::length(tpos2 - pnPos) ? tpos2 : tpos1;
			MMDFWD(2, "sw", tpos, vec3{ 0.f }, SColorf(1, 1, 0.5, 1));
			//tpos += vec3(mat3(sb->ndUpper2->GetGlobalTransform()) * vec3(0, 2 * bsc, 1 * bsc));
			tdir = tpos - ndPos;
		}

		tlen = glm::length(tdir);  
		tdir = glm::fastNormalize(tdir);
		//if (angle < 30.f)	o.ndLock->scaleVel(std::max(0.f, (angle - 15) / 15), 3, 3, 1.f);
#define ADDVF   // addForce// 
#define MUL2   (mul*bsc* BASE_FORCE_MUL *steptime)
			//ndYao->rbAtk->scaleVel(0.0f);

			//olen = tlen;
		glm::vec3 addVel{};
		float addVelMul = 0.f;
		int stageTo = 0;
		auto ndForce = node;


		// ================================= OUT RANGE =================================
		if (outAtkRange && (!Pmx->phyActAnimating 
			|| nds[atk.nid] == Pmx->curVA->ndAct
			)) // CLOSING =================================
		{
			float t0 = gPhyTime - atk.lockTime, t = t0;
			const float maxout = 6.f / 60;

			//pn->GetParent()->scaleVel((0.75f), 3, 2, 1.0f);
			float atkRgRat = (olen - atkRange) / atkRange;
			mul *= atk.forceMul * farAdMul[nid] * (0.3f + 0.7f * std::min(1.f, atkRgRat))*(MMD_GRAB_CONNECT?1:1);  //&& node->GetParent() && node->GetParent()->rb0 
			if (tlen > std::max(10.f, actDis * bsc * 2.75f)) {
				mul *= 0.5f;  t = 0.f;
			}
			else //ACTION START
			{
#if NO_PHY_ANIM_WHEN_ATTACK
				if (node->phyAnim == 1) {
					if (isNid_h(nid)) Sb->setPhyAnim(node->GetParent()->GetParent()->GetParent()->GetParent(),2, true);
					if (isNid_f(nid)) Sb->setPhyAnim(node->GetParent()->GetParent(), 2, true);
				}
#endif
			}
			if (nid == eoL || nid == eoR) ndForce = Sb->ndUpper2;

			//else if (nid == eHd)  node = ndUpper2;
			//else if ((nid==ehL || nid == ehR) ) node = node->GetParent()->GetParent();//mul = std::max(0.f, (angle - 5) / 10);// 

			if (CENTER_FORCE_MOD) { Sb->centerForceMul = 0.1f;   Sb->cfMulTimer = 0.1f; }
			float r = 0;
			//if (olen < actDis + objR) {
			//	r = 1 - (olen - atkDisRs[nid] - objR) / (actDis - atkDisRs[nid]);
			//	//if (nid<=efR) node->rbAtk->ADDVF(tdir * addv);							else 							
			//	addVelMul = MUL2 * (1.0f * r);
			//	//ATKDP(("addv1 %f", addVelMul));
			//	node->rbAtk->addLinearVel(addVel = tdir * addVelMul); 
			//	if (YAO_ANTI_FORCE) { sb->ndYao->rbAtk->setLinearVel(-addVel * vec3(0.1, 0.1, 0.1)); }
			//	kicking = 2;
			//}
			//else 

			//  OUT RANGE ACTION
			{

				//sb->getRb0()->node->scaleVel((0.1f), 3, 2, 1.0f);

				ndForce->scaleVel(((0.37f + glm::clamp(atkRgRat, 0.f, 3.f) * 0.2f) * atk.vsc), 3, 2);

				addVelMul = std::max(0.f, (MUL2 * (.95f)));
				//ATKDP(("addv2 %f",addVelMul));
				//if (nid == efL || nid == efR) addVelMul *= std::clamp(line.getClosestPoint(ndPos).getDistanceFrom(ndPos) / yaoY, 0.1f, 1.f);
				addVelMul *= o.pm.atkForceMul;

				if (t < 0.2f && isNid_f(nid)) {
					float r = t / 0.2, rm11 = (-1 + r * 2);
					ndForce->rbAtk->parentRb->addLinearVel(tdir * vec3(1 - r) * (addVelMul * 1.f));
					ndForce->rbAtk->addLinearVel(addVel = (tdir * vec3(rm11)) * addVelMul);
				}
				else					ndForce->rbAtk->addLinearVel(addVel = tdir * addVelMul );

				//Sb->ndHead->rb0->setAngVelToPos(Sb->mmdLookAt, 1000);
				//ndForce->rbAtk->parentRb->addLinearVel(addVel*0.5f);
				//ndctr[nid]->rbAtk->addLinearVel(addVel );

				stageTo = 1;

				if (DBG_ATK)
				{
					auto p0 = node->rbAtk->getPosition();

					for (int i = 0; i < 32; i++) {
						auto p = p0 + addVel * float(i * 0.1f);
						MMDFWD(2, "pt", p, vec3{ 0.f }, SColorf(0, t < 0.2f?1.f:0.5f, 1.0, 1));
					}
				}

				if (MMD_GRAB_CONNECT  && t >= maxout * 0.5f) {//&& o.sb
					if (olen < actDis + objR)
						o.rb->setCollideFilterMask(1, 0);
					if (o.grabbed == 0 && atk.grabCount > 0) {
						atk.grabCount--;
						assert(atk.grabCount == 0);
						auto antRb = atk.jtGrab->getAnotherRb(node->rb0); 
						antRb->setCollideFilterMask(0, 0);
						if (o.timerBack > 0.f) o.timer = o.timerBack;
						delete atk.jtGrab; atk.jtGrab = nullptr; //assert(o.grabBy == &atk); 
						o.grabBy = nullptr;
						int uid = antRb->phyObj ? antRb->phyObj->uid : 0;
						//ndForce->rbAtk->addLinearVel(vec3(0, 1000, 0));
						if (uid > 0) {
						FRAMEWAITER_CALL_B(0) {
							auto o = Pom->objRec[uid].ifObj; if (!o) return;
							o->rb->addLinearVel(vec3(0, 10, 0));
							if (task.rat01 < 0.5f) ndForce->rb0->parentRb->addLinearVel(vec3(0, 30, 0));
						}, 10);
						}
			
					}
				}
			}
#pragma region outRangeExtActions



			// ==================================== JUMP ====================================
			if (jumpStg == 1)
			{
				vec3 onDir = o.pos - Sb->ndYao->rbPos(); onDir.y = 0;
				float dis = glm::length(onDir);

				if (jumpStartS < 0 && dis < Sb->ndYao->absScale.x * (PHY_CATCH_BALL ? 10.f : 6.f)) {
					jumpStartS = gPhyTime;
				}

				if (gPhyTime - jumpStartS < (PHY_CATCH_BALL ? 0.3f : 0.1f))
				{
					float mul = 0.6;// sin(t / 0.2f * PI);
					auto dir = (tpos - ndForce->rbPos()) * vec3(1, 2, 1);
					//sb->Pmx->scaleBodyVel(0.5f, 3);
					//sb->Pmx->addBodyVel(dir * 10.f*mul);


					Sb->centerForceMul = 0.1f;   Sb->cfMulTimer = 1.0f;
					if (PHY_CATCH_BALL)
					{
						vector3df dirYao = (opos - Sb->ndUpper->rbPos());
						float a = core::clamp(dirYao.getLength() / 2.f - 3, 0.2f, 10.f);
						vec3 hl(-3 * a, 2 * a, 3), hr(3 * a, 2 * a, 3), ll(-3 * a, -3 * a, 3), lr(3 * a, -3 * a, 3);

						vec3 rtt = dirYao.getNormalizeCopy().getHorizontalAngleRad();
						quat qr = rtt;
						mat4 m = glm::translate(mat4(1), opos) * glm::mat4_cast(qr);
						hl = glh::matTransformVec(m, hl);	hr = glh::matTransformVec(m, hr);
						ll = glh::matTransformVec(m, ll);	lr = glh::matTransformVec(m, lr);
						Sb->ndHandL->rbAtk->setLinearVelToPos(hl, 9);
						Sb->ndHandR->rbAtk->setLinearVelToPos(hr, 9);
						Sb->ndLeg1L->rbAtk->setLinearVelToPos(ll, 3);
						Sb->ndLeg1R->rbAtk->setLinearVelToPos(lr, 3);
						Sb->Rb0()->addLinearVel(addVel / 1.f);
						Sb->Rb0()->setAngVelToPos(opos, 30);
					}
					else {
						//Sb->getRb0()->addLinearVel(addVel  );
						//Sb->getRb0()->node->scaleVel((0.8f), 3, 1, 1.0f);
						Sb->Rb0()->setAngVelToPos(opos, 10);


						//Sb->ndUpper2->rbAtk->addLinearVel(dir * 3.5f * mul);
						auto dir1 = (tpos + o.vel * (steptime * stepCount) - ndForce->rbPos());
						//ndForce->rbAtk->addLinearVel(dir1 * 10.f * mul);
					}

					MMDFWD(2, "sw21s", o.pos + vec3(0, 1, 0), vec3{ 0.f }, SColorf(1, 1, 1.0, 1));
				}
				else if (PHY_CATCH_BALL && dis < Sb->ndYao->absScale.x * (PHY_CATCH_BALL ? 30.f : 6.f))
				{
					Sb->Rb0()->setAngVelToPos(opos, 60);
					Sb->ndUpper2->rbAtk->setAngVelToPos(opos, 60);
					Sb->ndUpper2->rbAtk->addLinearVel({ 0, 30, 0 });
				}
			}
 
			//============================ GRAB ==================================================================================================
			//============================ ARM

			if (Os && nid == eYa && armGrab)
			{
				MMDFW(2, "sw", vec3(0, 9, 0), vec3(0.f, 00, 0), SColorf(1, 1, 1, 1));
				vec3 L, R, L2, R2;

				bool isThrow = Rcv->IsKeyDown(KEY_KEY_E);
				bool isReleasing = Rcv->IsKeyDown(KEY_KEY_D);
				bool isDropping = Rcv->IsKeyDown(KEY_KEY_A);
				int turbo = Rcv->IsKeyDown(KEY_KEY_S);
				float disLmt = (Pmx->armLength * 0.8f ) * bsc;
				bool closeYao = olen < disLmt;
				float disRatio = olen / disLmt;  //about 0.5-1
				float ratio = std::clamp((disRatio - 0.5f) / 0.5f, 0.f, 1.f);
				float rat01 = 1 - ratio;
				if (disRatio < 1) armGrabing = true;
				else if (disRatio > 1.35) armGrabing = false;

				bool closeYao1 = !closeYao && olen < (Pmx->armLength ) * bsc;

				if (closeYao || o.throwingBySb && o.vel.y < 0) {
					//limitAtks(*obj, 1);
					if (o.throwingBySb && o.ndLock.size()) o.throwingBySb = o.ndLock[0]->model->saba;
				}


				vec3 armAdd(0); if (t <= 0.f) { armAdd = vec3(0, -10, 0); }
				if (!armGrabing && glm::length(o.vel) > 20) { L = vec3(t0 < 0.1f ? 100 : 28, 0, -0) + armAdd; R = vec3(t0 < 0.1f ? -100 : -28, 0, -0) + armAdd; }
				else { float len = glm::clamp(olen, 3.f, 6.f) * 2; L = vec3(-len, 0, -disRatio * 1 * bsc); R = vec3(len, 0, -disRatio * 1 * bsc); }
				//L=glh::matTransformVec(m, L); R = glh::matTransformVec(m, R);
				MMDFWD(2, "sw", L, vec3{ 0.f }, SColorf(1, 0, 0, 1));
				MMDFWD(2, "sw", R, vec3{ 0.f }, SColorf(0, 1, 0, 1));
				auto opos = o.rb->getPosition();
				auto npos = node->rbAtk->getPosition();
				auto addbase = opos + (opos - npos) * .5f;
				mat4 m = node->rbAtk->getNodeTransform();
				//vector3df dir(npos - opos); matrix4 m1; m1.setRotationDegrees(dir.getHorizontalAngle());m = m1;//MMDFW(2, "sw1s", npos, vec3(dir*30), SColorf(1, 0, 0, 1));
				//MMDFW(2, "sw1s", npos, glh::matTransformVec(m,vec3(0,0,60)), SColorf(1, .5, 0, 1));

				auto tgtL = addbase + glh::matRotateVec(m, L), tgtR = addbase + glh::matRotateVec(m, R);
				MMDFWD(2, "sw2", tgtL, vec3{ 0.f }, SColorf(1, 0, 0, 1));
				MMDFWD(2, "sw2", tgtR, vec3{ 0.f }, SColorf(0, 1, 0, 1));

				auto dirR = (tgtR - o.vel * (1 / 30.f) + vec3(0, 0, 0) - npos);
				auto dirL = (tgtL - o.vel * (1 / 30.f) + vec3(0, 0, 0) - npos);

				float spd = 100.f;


				auto n2nOfsAddVel = [this](MMDNode* ns, MMDNode* nt, float mul, vec3 tnOfs, float scv = 1.f, float maxSpd = 10.f) {
					if (scv != 1.f) ns->rbAtk->scaleVel(scv);
					//auto m2 = nt->rbAtk->getNodeTransform();	auto v3= glm::vec3(m2* glm::vec4(tnOfs, 1));
					auto tpos = glh::matTransformVec(nt->rbAtk->getNodeTransform(), tnOfs);	//		DP(("tpos, %f",tpos.x));					
					ns->rbAtk->addLinearVelToPosLimitDis(tpos, mul, 0, maxSpd);
					//MMDFWD(2, "sw", tpos, vec3{ 0.f }, SColorf(0, 0.5, 1, 1));
					//ns->rbAtk->addForce((tpos - ns->rbAtk->getPosition())*mul);
					};
				auto r2rOfsAddVel = [this](MMDNode* ns, MMDNode* nt, float mul, vec3 tnOfs, float scv = 1.f, float maxSpd = 10.f) {
					if (scv != 1.f) ns->rbAtk->scaleVel(scv);
					auto tpos = glh::matTransformVec(nt->rbAtk->GetTransform(), tnOfs);	//		DP(("tpos, %f",tpos.x));					
					ns->rbAtk->addLinearVelToPosLimitDis(tpos, mul, 0, maxSpd);
					};
				if (!armGrabing) {
					if (Sb == o.ndLock[0]->model->saba || isReleasing) {
						MMDFW(2, "sw", Sb->Rb0()->pos * vec3(1, 0.0, 1), vec3(0.f, 00, 0), SColorf(1, 0, 0, 1));
						//Sb->ndArmL->rbAtk->scaleVel(0.5f);				Sb->ndArmR->rbAtk->scaleVel(0.5f);
						//Sb->ndArmL->rbAtk->addLinearVel(dirL * spd);		Sb->ndArmR->rbAtk->addLinearVel(dirR * spd);

						Sb->ndYao->rbAtk->scaleVel(0.7f);
						Sb->ndYao->rbAtk->addLinearVel(tdir * 10.f);
						if (isReleasing) {
							o.rb->scaleVel(0.5f, 1);//o.sb->Pmx->scaleBodyVel(0.8f, 3);
							n2nOfsAddVel(Os->ndUpper2, Sb->ndUpper2, 3, vec3(0, 0, 0));
						}
						//MMDFW(2, "sw21s", Sb->ndYao->rbAtk->getPo sition(), tdir*10.f, SColorf(0, 1, 0, 1));
						//Sb->ndYao->rbAtk->addLinearVel(-(dirL+dirR)*1.0f);
						//n2nOfsAddVel(Sb->ndYao, Os->ndYao, turbo ? 2 : 1, vec3(0), 1.f, 10.f);

						auto m = glh::buildLookAtMatrixLH(o.rb->pos, Sb->ndYao->rbPos(),
							vec3(0, 1, 0)
							//	glh::matRotateVec(Sb->ndYao->rbAtk->GetTransform(), vec3(0, 0, 1))
						);

						if (o.vel.y < 0 && o.throwingBySb) {
							matrix4 mt; mt.setRotationDegrees(vector3df(Sb->ndYao->rbPos() - o.rb->pos).getHorizontalAngle());
							//glm::mat4 gm = glm::rotate(mat4(1),-pi_float/2.f,vec3(1,0,0))*GlmRttY180(m);
							auto gm = glh::createViewMatrixPTR(o.rb->pos, Sb->ndYao->rbPos(), glh::matRotateVec(Sb->ndCenter->GetGlobalTransform(), vec3(1, 0, 0))) * (MatRttY180 * MatRttZ180);

							o.rb->addRotationToMatOnNode(gm, 30);
						}
						else o.rb->setAngVelToPos(Sb->ndYao->rbPos(), 100);


						//n2nOfsAddVel(Os->ndYao, Sb->ndYao, turbo ? 3 : 0.1, vec3(0, -1, 0),1.f);
						if (o.throwingBySb) {
							if (closeYao1 && o.vel.y < 0)
								o.sb->Pmx->addBodyVel(glm::normalize(node->rbPos() - opos) * float(armGrabRat) * (turbo ? 100.f : 10.f));
						}
						else
						{
							n2nOfsAddVel(Os->ndHandL, Sb->ndHead, turbo ? 1 : 0.1, vec3(-10, 3, 10));
							n2nOfsAddVel(Os->ndHandR, Sb->ndHead, turbo ? 1 : 0.1, vec3(10, 3, 10));
						}
						//n2nOfsAddVel(Os->ndHandL, Sb->ndYao, turbo ? 3 : 1, vec3(-10, -20, 0));
						//n2nOfsAddVel(Os->ndHandR, Sb->ndYao, turbo ? 3 : 1, vec3(10, -20, 0));
						spd = 60.f;
						Sb->ndHandL->rbAtk->scaleVel(0.75f);				Sb->ndHandR->rbAtk->scaleVel(0.75f);
						n2nOfsAddVel(Os->ndUpper2, Sb->ndUpper2, 3, vec3(0, 0, 0));
						Sb->ndHandL->rbAtk->addLinearVel(glh::vecLimitFast(dirL, 1, spd));	Sb->ndHandR->rbAtk->addLinearVel(glh::vecLimitFast(dirR, 1, spd));
						//MMDFW(2, "sw21s", yaotgt, vec3(0), SColorf(1, 1, 0, 1));
					}
				}
				else
				{
					MMDFW(2, "sw", Sb->Rb0()->pos * vec3(1, 0.1, 1), vec3(0.f, 0, 0), SColorf(1, 1, 0, 1));
#if USE_LEAP
					bool armOpen = isDropping || (Sb->leap && Sb->leap->Lhd[0].grab == 0);
#else
					bool armOpen = isDropping;
#endif
					spd = 30 * (armGrabRat);
					if (o.vel.y < 0 && o.throwingBySb)
						o.throwingBySb = nullptr;
					bool throwUp = false;
					if (isThrow)
					{
						Os->Pmx->scaleBodyVel(0.9f, 1);
						Os->Pmx->addBodyVel(vec3(0, 20, 0));

						atk.kicked = 0; o.resetStatus();
						if (Sb->ndYao->rbPos().y > Sb->ndHead->rbPos().y + 2) {
							throwUp = true;
							Sb->ndArm1L->rbAtk->addLinearVel(dirR * 60.f);			Sb->ndHandL->rbAtk->addLinearVel(dirR * 60.f);
							Sb->ndArm1R->rbAtk->addLinearVel(dirL * 60.f);			Sb->ndHandR->rbAtk->addLinearVel(dirL * 60.f);
							o.throwingBySb = Sb;
							//
							Sb->ndYao->rbAtk->addLinearVel(vec3(0, 20, 0));

						}
						else if (Sb->ndYao->rbPos().y < Sb->ndHead->rbPos().y - 1) {
							vec3 ang = glm::eulerAngles(glm::quat(Sb->ndYao->rbAtk->GetTransform()));

							float f = (ang.x > 0 ? 1 : -1);
							Sb->ndYao->rbAtk->addTorqueLocal(vec3(-f * 600, 0, 0));
							Sb->ndUpper2->rbAtk->addTorqueLocal(vec3(-f * 200, 0, 0));
							Sb->ndArmL->rbAtk->addTorqueLocal(vec3(-f * 100, 0, 0));
							Sb->ndArmR->rbAtk->addTorqueLocal(vec3(-f * 100, 0, 0));

							vec3 v = glm::cross(glm::normalize(Sb->ndHead->getGlobalPos() - Sb->ndYao->getGlobalPos()), glh::matRotateVec(Sb->ndYao->GetGlobalTransform(), vec3(f, 0, 0))) * 100.f;
							//MMDFW(2, "sw1s", Sb->ndYao->getGlobalPos(), v, SColorf(1, 1, 0, 1));
							Sb->ndYao->rbAtk->addLinearVel(v);

							Os->ndYao->rbAtk->addLinearVel(v);
						}

					}
					else if (isReleasing && o.lastGrabSb == Sb)
					{
						auto nsb = mmd->nextSaba(Sb->getItemIdx());

						auto dir = nsb->Rb0()->getPosition() + vec3(0, 6 * bsc, 0) - Sb->Rb0()->getPosition();
						dir *= 0.1f;
						rootRb->addLinearVel(dir + vec3(0, disRatio * 3 - 0.6, 0));//rbAtk->addLinearVel(vec3(0, disRatio * 3 - 0.6, 1.33));

						o.sb->Pmx->addBodyVel(dir + vec3(0, disRatio * 3 - 0.6, 0));
						if (disRatio > 1.f) {
							reset(); ignoreObjs[o.uid] = 0.5f;
							o.resetStatus();
							vec3 vad = glh::calcVelocityP2PinT(opos, node->rbPos(), glm::length(nsb->ndYao->rbPos() - opos) / glm::length(o.vel), glm::vec3(0, -9.81 * GRAVITY_MUL, 0));
							o.sb->Pmx->addBodyVel(vad);
						}
					}
					else if (armOpen) {

						Sb->ndArm1L->rbAtk->addLinearVel(dirR * 3.f);			Sb->ndHandL->rbAtk->addLinearVel(dirR * 2.f);
						Sb->ndArm1R->rbAtk->addLinearVel(dirL * 3.f);			Sb->ndHandR->rbAtk->addLinearVel(dirL * 2.f);

					}
					else {
						if (rat01 > 0.67f)
							o.lastGrabSb = Sb;
						else if (rat01 < 0.33) {
							Os->Pmx->scaleBodyVel(0.8f, 1);
							Os->Pmx->addBodyVel(-tdir * 10.f);
						}

						if (turbo) Sb->ndUpper2->rbAtk->scaleVel(0.5f + 0.5f * (1 - armGrabRat));
						//else  Sb->ndYao->rbAtk->scaleVel(0.9f + 0.1f * (1 - armGrabRat));
						//o.rb->scaleVel(0.9f + 0.1f * (1 - armGrabRat));

						float vsc = 0.99f - glm::clamp(ratio, 0.f, 1.f) * 0.39f;
						vsc = 0.99f - ratio * 0.6f;
						//Sb->ndArmL->rbAtk->scaleVel(vsc);			Sb->ndArmR->rbAtk->scaleVel(vsc);Sb->ndArm1L->rbAtk->scaleVel(vsc);		Sb->ndArm1R->rbAtk->scaleVel(vsc);
						auto rbpnt = rb0nt;// Sb->ndArmL->GetParent()->GetGlobalTransform();
						float rt0 = 5 + (rat01 + armGrabRat) * rat01 * 10;
						float rt1 = 5 + (rat01 + armGrabRat) * rat01 * 30;
						//Sb->ndArmL->rbAtk->addRotationToMatOnNode(rbpnt * glh::rttMatYXZDeg({ rt0,-80, 0 }), 10);
						//Sb->ndArmR->rbAtk->addRotationToMatOnNode(rbpnt * glh::rttMatYXZDeg({ rt0, 80, 0 }), 10);
						//Sb->ndArm1L->rbAtk->addRotationToMatOnNode(rbpnt * glh::rttMatYXZDeg({ -rt1,-80, 0 }), 10);
						//Sb->ndArm1R->rbAtk->addRotationToMatOnNode(rbpnt * glh::rttMatYXZDeg({ -rt1, 80, 0 }), 10);

						n2nOfsAddVel(Sb->ndHandL, Sb->ndHandR, turbo ? 29 : 3 + ratio * 6, vec3(0), 1.f, 20.f);
						n2nOfsAddVel(Sb->ndHandR, Sb->ndHandL, turbo ? 29 : 3 + ratio * 6, vec3(0), 1.f, 20.f);
						spd = 30.f * armGrabRat;
						Sb->ndHandL->rbAtk->scaleVel(vsc);				Sb->ndHandR->rbAtk->scaleVel(vsc);

						Sb->ndHandL->rbAtk->addLinearVel(glh::vecLimitFast(dirL, 1, spd));	Sb->ndHandR->rbAtk->addLinearVel(glh::vecLimitFast(dirR, 1, spd));

						//MMDFW(2, "sw", Sb->ndHandL->rbAtk->pos, dirL* spd, SColorf(1, 1, 0, 1));	MMDFW(2, "sw", Sb->ndHandR->rbAtk->pos, dirR* spd, SColorf(0, 1, 1, 1));
 
						//o.timer -= steptime*2;
						n2nOfsAddVel(Os->ndUpper2, Sb->ndUpper2, turbo ? 20 : disRatio * 2, vec3(0, 0, 0));
						//n2nOfsAddVel(Sb->ndYao, Os->ndYao, turbo ? 20 : disRatio*10, vec3(0, 0, 0));
						Sb->ndYao->rbAtk->setAngVelToPos(Os->ndYao->getGlobalPos(), 100);
						if (turbo) {
							n2nOfsAddVel(Os->ndUpper2, Sb->ndUpper2, turbo ? 2 : 1, vec3(0, 1, -2));
							

							auto ndHT = Sb->ndHead;//Sb->ndYao;//
							n2nOfsAddVel(Os->ndHandL->rbAtk->parentRb->node, ndHT, turbo ? 2 : 1, vec3(-3, 0, 1));
							n2nOfsAddVel(Os->ndHandR->rbAtk->parentRb->node, ndHT, turbo ? 2 : 1, vec3(3, 0, 1));
							n2nOfsAddVel(Os->ndHandL, ndHT, turbo ? 3 : 2, vec3(1, 0, 2));
							n2nOfsAddVel(Os->ndHandR, ndHT, turbo ? 3 : 2, vec3(-1, 0, 2));
						}



					}

				}
				if (0.67f < disRatio && disRatio < 2.f) //far arm to MIRROR point
				{
					auto ndf = Sb->ndHandL->rbAtk->dis2To(o.pos) > Sb->ndHandR->rbAtk->dis2To(o.pos) ? Sb->ndHandL : Sb->ndHandR;
					auto ndn = ndf == Sb->ndHandR ? Sb->ndHandL : Sb->ndHandR;
					auto mpos = glh::mirrorPointLimitDis(rootRb->getPosition(), Os->ndUpper->rbPos(), ndn->rbPos(), 6.f * disRatio * bsc, 99999.f);
					MMDFWD(2, "sw", mpos, vec3(0.f, 00, 0), SColorf(1, 1, 1, 1));
					ndf->scaleVel(0.5f, 1); ndf->rbAtk->addLinearVelToPos(mpos, 30);
				}

				if (!isThrow)
					armGrabRat = glm::mix(armGrabRat, 0.5f, 0.1f);
			}

			//============================ LEG =======================================================================
			if (Os && nid == eYa && legGrab)
			{
				MMDFW(2, "sw", vec3(0, 9, 0), vec3(0.f, 00, 0), SColorf(1, 1, 1, 1));
				vec3 L, R, L2, R2;
				
				bool isThrow = Rcv->IsKeyDown(KEY_KEY_E);				
				bool isReleasing =  Rcv->IsKeyDown(KEY_KEY_D); 
				bool isDropping = Rcv->IsKeyDown(KEY_KEY_A);
				int turbo = Rcv->IsKeyDown(KEY_KEY_S);

				float disadd =   (1 - legGrabRat) * 0;
				float disLmt = (Pmx->legLength * 0.8f + disadd) * bsc;
				bool closeYao = olen < disLmt;
				float disRatio = olen / disLmt;  //about 0.5-1
				float ratio = std::clamp((disRatio - 0.5f) / 0.5f, 0.f, 1.f);
				float rat01 = 1 - ratio;
				if (disRatio < 1) legGrabing = true;
				else if (disRatio > 1.35) legGrabing = false;
				 
				bool closeYao1 = !closeYao && olen < (Pmx->legLength + disadd) * bsc;
					
				if (closeYao || o.throwingBySb && o.vel.y < 0) {
					//limitAtks(*obj, 1);
					if (o.throwingBySb && o.ndLock.size()) o.throwingBySb = o.ndLock[0]->model->saba;
				}

				bool changed = closeYaoLast != closeYao;
				if (changed) {
					DP(("changed closeYao %d", closeYao));
				}
				closeYaoLast = closeYao;
				vec3 legAdd(0); if (t <= 0.f) { legAdd = vec3(0, -10, 0); }
				if (!legGrabing && glm::length(o.vel) > 20) { L = vec3(t0 < 0.1f ? 100 : 28, 0, -0) + legAdd; R = vec3(t0 < 0.1f ? -100 : -28, 0, -0) + legAdd; }
				else { float len = glm::clamp(olen, 3.f, 6.f) * 2; L = vec3(-len, 0, -disRatio* 1 *bsc); R = vec3(len, 0, -disRatio * 1 * bsc); }
				//L=glh::matTransformVec(m, L); R = glh::matTransformVec(m, R);
				MMDFWD(2, "sw", L, vec3{ 0.f }, SColorf(1, 0, 0, 1));
				MMDFWD(2, "sw", R, vec3{ 0.f }, SColorf(0, 1, 0, 1));
				auto opos = o.rb->getPosition();
				auto npos = node->rbAtk->getPosition();
				auto addbase = opos + (opos - npos)*.5f;
				mat4 m = node->rbAtk->getNodeTransform();
				//vector3df dir(npos - opos); matrix4 m1; m1.setRotationDegrees(dir.getHorizontalAngle());m = m1;//MMDFW(2, "sw1s", npos, vec3(dir*30), SColorf(1, 0, 0, 1));
				//MMDFW(2, "sw1s", npos, glh::matTransformVec(m,vec3(0,0,60)), SColorf(1, .5, 0, 1));
				
				auto tgtL = addbase+ glh::matRotateVec(m, L), tgtR = addbase + glh::matRotateVec(m, R);
				MMDFWD(2, "sw2", tgtL, vec3{ 0.f }, SColorf(1, 0, 0, 1));
				MMDFWD(2, "sw2", tgtR, vec3{ 0.f }, SColorf(0, 1, 0, 1));
 
				auto dirR = (tgtR - o.vel * (1 / 30.f) + vec3(0, 0, 0) - npos);  
				auto dirL =  (tgtL - o.vel * (1 / 30.f) + vec3(0, 0, 0) - npos);  

				float spd = 100.f;


				auto n2nOfsAddVel = [this](MMDNode* ns, MMDNode* nt, float mul, vec3 tnOfs, float scv = 1.f, float maxSpd = 10.f) {
					if (scv != 1.f) ns->rbAtk->scaleVel(scv);
					//auto m2 = nt->rbAtk->getNodeTransform();	auto v3= glm::vec3(m2* glm::vec4(tnOfs, 1));
					auto tpos = glh::matTransformVec(nt->rbAtk->getNodeTransform(), tnOfs);	//		DP(("tpos, %f",tpos.x));					
					ns->rbAtk->addLinearVelToPosLimitDis(tpos, mul, 0, maxSpd);
					//MMDFWD(2, "sw", tpos, vec3{ 0.f }, SColorf(0, 0.5, 1, 1));
					//ns->rbAtk->addForce((tpos - ns->rbAtk->getPosition())*mul);
					};
				auto r2rOfsAddVel = [this](MMDNode* ns, MMDNode* nt, float mul, vec3 tnOfs, float scv = 1.f, float maxSpd = 10.f) {
					if (scv != 1.f) ns->rbAtk->scaleVel(scv);
					auto tpos = glh::matTransformVec(nt->rbAtk->GetTransform(), tnOfs);	//		DP(("tpos, %f",tpos.x));					
					ns->rbAtk->addLinearVelToPosLimitDis(tpos, mul, 0, maxSpd);
					};
				if (!legGrabing) {
					if (Sb == o.ndLock[0]->model->saba || isReleasing) {
						MMDFW(2, "sw", Sb->Rb0()->pos * vec3(1, 0.0, 1), vec3(0.f, 00, 0), SColorf(1, 0, 0, 1));
						//Sb->ndLegL->rbAtk->scaleVel(0.5f);				Sb->ndLegR->rbAtk->scaleVel(0.5f);
						//Sb->ndLegL->rbAtk->addLinearVel(dirL * spd);		Sb->ndLegR->rbAtk->addLinearVel(dirR * spd);

						Sb->ndYao->rbAtk->scaleVel(0.7f);
						Sb->ndYao->rbAtk->addLinearVel(tdir * 10.f);
						if (isReleasing) {
							o.rb->scaleVel(0.5f, 1);//o.sb->Pmx->scaleBodyVel(0.8f, 3);
							n2nOfsAddVel(Os->ndYao, Sb->ndUpper2, 3, vec3(0, 0, 0));
						}  
						//MMDFW(2, "sw21s", Sb->ndYao->rbAtk->getPo sition(), tdir*10.f, SColorf(0, 1, 0, 1));
						//Sb->ndYao->rbAtk->addLinearVel(-(dirL+dirR)*1.0f);
						//n2nOfsAddVel(Sb->ndYao, Os->ndYao, turbo ? 2 : 1, vec3(0), 1.f, 10.f);

						auto m = glh::buildLookAtMatrixLH(o.rb->pos, Sb->ndYao->rbPos(),
							vec3(0, 1, 0)
							//	glh::matRotateVec(Sb->ndYao->rbAtk->GetTransform(), vec3(0, 0, 1))
						);

						if (o.vel.y < 0 && o.throwingBySb) {
							matrix4 mt; mt.setRotationDegrees(vector3df(Sb->ndYao->rbPos() - o.rb->pos).getHorizontalAngle());
							//glm::mat4 gm = glm::rotate(mat4(1),-pi_float/2.f,vec3(1,0,0))*GlmRttY180(m);
							auto gm = glh::createViewMatrixPTR(o.rb->pos, Sb->ndYao->rbPos(), glh::matRotateVec(Sb->ndCenter->GetGlobalTransform(), vec3(1, 0, 0))) * (MatRttY180 * MatRttZ180);

							o.rb->addRotationToMatOnNode(gm, 30);
						}
						else o.rb->setAngVelToPos(Sb->ndYao->rbPos(), 100);
						Sb->ndYao->rbAtk->setAngVelToPos(Os->ndYao->getGlobalPos(), 30);

						//n2nOfsAddVel(Os->ndYao, Sb->ndYao, turbo ? 3 : 0.1, vec3(0, -1, 0),1.f);
						if (o.throwingBySb) {
							if (closeYao1 && o.vel.y < 0)
								o.sb->Pmx->addBodyVel(glm::normalize(node->rbPos() - opos) * float(legGrabRat) * (turbo ? 100.f : 10.f));
						}
						else
						{
							n2nOfsAddVel(Os->ndHandL, Sb->ndHead, turbo ? 1 : 0.1, vec3(-10, 3, 10));
							n2nOfsAddVel(Os->ndHandR, Sb->ndHead, turbo ? 1 : 0.1, vec3(10, 3, 10));
						}
						//n2nOfsAddVel(Os->ndFootL, Sb->ndYao, turbo ? 3 : 1, vec3(-10, -20, 0));
						//n2nOfsAddVel(Os->ndFootR, Sb->ndYao, turbo ? 3 : 1, vec3(10, -20, 0));
						spd = 60.f;
						Sb->ndFootL->rbAtk->scaleVel(0.75f);				Sb->ndFootR->rbAtk->scaleVel(0.75f);
						n2nOfsAddVel(Os->ndYao, Sb->ndUpper2, 3, vec3(0, 0, 0));						
						Sb->ndFootL->rbAtk->addLinearVel(glh::vecLimitFast(dirL, 1, spd));	Sb->ndFootR->rbAtk->addLinearVel(glh::vecLimitFast(dirR,1, spd));
						//MMDFW(2, "sw21s", yaotgt, vec3(0), SColorf(1, 1, 0, 1));
					}
				}
				else
				{
					MMDFW(2, "sw", Sb->Rb0()->pos * vec3(1, 0.1, 1), vec3(0.f, 0, 0), SColorf(1, 1, 0, 1));
					
#if USE_LEAP
					bool legOpen = isDropping || (Sb->leap && Sb->leap->Lhd[0].grab == 0);
#else
					bool legOpen = isDropping;
#endif
					spd = 30 * (legGrabRat);
					if (o.vel.y < 0 && o.throwingBySb)
						o.throwingBySb = nullptr;
					bool throwUp = false;
					if (isThrow)
					{
						Os->Pmx->scaleBodyVel(0.9f, 1);
						Os->Pmx->addBodyVel(vec3(0, 20, 0));

						atk.kicked = 0; o.resetStatus();
						if (Sb->ndYao->rbPos().y > Sb->ndHead->rbPos().y+2) {
							throwUp = true;
							Sb->ndLeg1L->rbAtk->addLinearVel(dirR * 60.f);			Sb->ndFootL->rbAtk->addLinearVel(dirR * 60.f);
							Sb->ndLeg1R->rbAtk->addLinearVel(dirL * 60.f);			Sb->ndFootR->rbAtk->addLinearVel(dirL * 60.f);
							o.throwingBySb = Sb;
							//
							Sb->ndYao->rbAtk->addLinearVel(vec3(0,20, 0));

						}
						else if (Sb->ndYao->rbPos().y < Sb->ndHead->rbPos().y-1) {
							vec3 ang = glm::eulerAngles(glm::quat(Sb->ndYao->rbAtk->GetTransform()));

							float f = (ang.x > 0 ? 1 : -1);
							Sb->ndYao->rbAtk->addTorqueLocal(vec3(-f * 600, 0, 0));
							Sb->ndUpper2->rbAtk->addTorqueLocal(vec3(-f * 200, 0, 0));
							Sb->ndLegL->rbAtk->addTorqueLocal(vec3(-f * 100, 0, 0));
							Sb->ndLegR->rbAtk->addTorqueLocal(vec3(-f * 100, 0, 0));

							vec3 v = glm::cross(glm::normalize(Sb->ndHead->getGlobalPos() - Sb->ndYao->getGlobalPos()), glh::matRotateVec(Sb->ndYao->GetGlobalTransform(), vec3(f, 0, 0))) * 100.f;
							//MMDFW(2, "sw1s", Sb->ndYao->getGlobalPos(), v, SColorf(1, 1, 0, 1));
							Sb->ndYao->rbAtk->addLinearVel(v);

							Os->ndYao->rbAtk->addLinearVel(v);
						}

					}
					else if (isReleasing && o.lastGrabSb == Sb)
					{
						auto nsb = mmd->nextSaba(Sb->getItemIdx());

						auto dir = nsb->Rb0()->getPosition() + vec3(0, 6 * bsc, 0) - Sb->Rb0()->getPosition();
						dir *= 0.1f;
						rootRb->addLinearVel(dir + vec3(0, disRatio * 3 - 0.6, 0));//rbAtk->addLinearVel(vec3(0, disRatio * 3 - 0.6, 1.33));

						o.sb->Pmx->addBodyVel(dir + vec3(0, disRatio * 3 - 0.6, 0));
						if (disRatio > 1.f) {
							reset(); ignoreObjs[o.uid] = 0.5f;
							o.resetStatus();
							vec3 vad = glh::calcVelocityP2PinT(opos, node->rbPos(), glm::length(nsb->ndYao->rbPos() - opos) / glm::length(o.vel), glm::vec3(0, -9.81 * GRAVITY_MUL, 0));
							o.sb->Pmx->addBodyVel(vad);
						}
					}
					else if (legOpen) {
						
						Sb->ndLeg1L->rbAtk->addLinearVel(dirR * 3.f);			Sb->ndFootL->rbAtk->addLinearVel(dirR * 2.f);
						Sb->ndLeg1R->rbAtk->addLinearVel(dirL * 3.f);			Sb->ndFootR->rbAtk->addLinearVel(dirL * 2.f);
	
					}
					else {
						if (rat01>0.67f) 
							o.lastGrabSb = Sb;
						else if (rat01 < 0.33) {
							Os->Pmx->scaleBodyVel(0.8f, 1);
							Os->Pmx->addBodyVel(-tdir*10.f);
						}

						if (turbo) Sb->ndYao->rbAtk->scaleVel(0.5f + 0.5f * (1 - legGrabRat));
						//else  Sb->ndYao->rbAtk->scaleVel(0.9f + 0.1f * (1 - legGrabRat));
						//o.rb->scaleVel(0.9f + 0.1f * (1 - legGrabRat));
 
						float vsc =   0.99f- glm::clamp(ratio,0.f,1.f)*0.39f;


#if  0
						Sb->ndLegL->rbAtk->scaleVel(vsc);			Sb->ndLegR->rbAtk->scaleVel(vsc);
						Sb->ndLeg1L->rbAtk->scaleVel(vsc);		Sb->ndLeg1R->rbAtk->scaleVel(vsc);
						Sb->ndLegL->rbAtk->scaleVel(vsc);			Sb->ndLegR->rbAtk->scaleVel(vsc);
						Sb->ndLeg1L->rbAtk->scaleVel(vsc);		Sb->ndLeg1R->rbAtk->scaleVel(vsc);
						glm::vec3 vlL = pos1 - Sb->ndLeg1L->rbAtk->getPosition(), vlR = pos1 - Sb->ndLeg1R->rbAtk->getPosition();
						Sb->ndLeg1L->rbAtk->addLinearVel(vlL * spd * 0.2f);				Sb->ndLeg1R->rbAtk->addLinearVel(vlR  * spd * 0.2f);



						vec3 vL(turbo ? 6 :3, -0.0, turbo ? -1 : -0.0), vR(turbo ? -6 : -3, -0.0, turbo ? -1 : -0.0);
						vL = glh::matRotateVec(m, vL * spd*0.3f); vR = glh::matRotateVec(m, vR * spd*0.3f);
						Sb->ndLegL->rbAtk->addLinearVel(vL);		Sb->ndLegR->rbAtk->addLinearVel(vR);
						MMDFWD(2, "sw1s", Sb->ndLegL->rbAtk->pos, vL, SColorf(1, 1, 0, 1));	MMDFWD(2, "sw1s", Sb->ndLegR->rbAtk->pos, vR, SColorf(0, 1, 1, 1));
#else
						vsc = 0.99f - ratio * 0.6f;
						Sb->ndLegL->rbAtk->scaleVel(vsc);			Sb->ndLegR->rbAtk->scaleVel(vsc);
						Sb->ndLeg1L->rbAtk->scaleVel(vsc);		Sb->ndLeg1R->rbAtk->scaleVel(vsc);
						auto rbpnt = rb0nt;// Sb->ndLegL->GetParent()->GetGlobalTransform();
						float rt0 = 5 + (rat01+legGrabRat)*rat01 * 10;
						float rt1 = 5 + (rat01+legGrabRat)*rat01 * 30;
						Sb->ndLegL->rbAtk->addRotationToMatOnNode(rbpnt*glh::rttRadZYX( rt0,-80, 0 ), 10);
						Sb->ndLegR->rbAtk->addRotationToMatOnNode(rbpnt* glh::rttRadZYX( rt0, 80, 0 ), 10);
						Sb->ndLeg1L->rbAtk->addRotationToMatOnNode(rbpnt* glh::rttRadZYX( -rt1,-80, 0 ), 10);
						Sb->ndLeg1R->rbAtk->addRotationToMatOnNode(rbpnt* glh::rttRadZYX( -rt1, 80, 0 ), 10);
#endif

						n2nOfsAddVel(Sb->ndLeg1L, Sb->ndLeg1R, turbo ? 29 : 3+ratio*6, vec3(0), 1.f, 30.f);
						n2nOfsAddVel(Sb->ndLeg1R, Sb->ndLeg1L, turbo ? 29 : 3+ratio*6, vec3(0), 1.f, 30.f);
						spd = 30.f * legGrabRat;
						Sb->ndFootL->rbAtk->scaleVel(vsc);				Sb->ndFootR->rbAtk->scaleVel(vsc);

						Sb->ndFootL->rbAtk->addLinearVel(glh::vecLimitFast(dirL, 1, spd));	Sb->ndFootR->rbAtk->addLinearVel(glh::vecLimitFast(dirR, 1, spd));

						//MMDFW(2, "sw", Sb->ndFootL->rbAtk->pos, dirL* spd, SColorf(1, 1, 0, 1));	MMDFW(2, "sw", Sb->ndFootR->rbAtk->pos, dirR* spd, SColorf(0, 1, 1, 1));
						//o.killY = 3.f;
						//o.timer -= steptime*2;
						n2nOfsAddVel(Os->ndYao, Sb->ndYao, turbo ?20 : disRatio*2, vec3(0, 0, 0));
						//n2nOfsAddVel(Sb->ndYao, Os->ndYao, turbo ? 20 : disRatio*10, vec3(0, 0, 0));
						if (turbo) {
							n2nOfsAddVel(Os->ndUpper2, Sb->ndUpper2, turbo ? 2 : 1, vec3(0, 1, -2));
							 Sb->ndYao->rbAtk->setAngVelToPos(Os->ndYao->getGlobalPos(), 30);

							auto ndHT = Sb->ndHead;//Sb->ndYao;//
							n2nOfsAddVel(Os->ndHandL->rbAtk->parentRb->node, ndHT, turbo ? 2 : 1, vec3(-3, 0, 1));
							n2nOfsAddVel(Os->ndHandR->rbAtk->parentRb->node, ndHT, turbo ? 2 : 1, vec3(3, 0, 1));
							n2nOfsAddVel(Os->ndHandL, ndHT, turbo ? 3 : 2, vec3(1, 0, 2));
							n2nOfsAddVel(Os->ndHandR, ndHT, turbo ? 3 : 2, vec3(-1, 0, 2));
						}

						

					}

				}
				if (0.67f<disRatio && disRatio<2.f) //far leg to MIRROR point
				{
					auto ndf = Sb->ndFootL->rbAtk->dis2To(o.pos) > Sb->ndFootR->rbAtk->dis2To(o.pos) ? Sb->ndFootL : Sb->ndFootR;
					auto ndn = ndf == Sb->ndFootR ? Sb->ndFootL : Sb->ndFootR;
					auto mpos = glh::mirrorPointLimitDis(rootRb->getPosition(), Os->ndUpper->rbPos(), ndn->rbPos(),6.f* disRatio*bsc,99999.f);
					MMDFWD(2, "sw", mpos, vec3(0.f, 00, 0), SColorf(1, 1, 1, 1));
					ndf->scaleVel(0.5f, 1); ndf->rbAtk->addLinearVelToPos(mpos, 30);
				}

				if (!isThrow)
				legGrabRat = glm::mix(legGrabRat, 0.5f, 0.1f);
				if (!isThrow && isThrowLast)
				{
					reset();  o.resetStatus(); Sb->actVoiceFx(Sb->getItemIdx() + Sb->Pm.mmd->mdplr.mmdCount, 5, 60, L"hu");
				}
				else if (isThrow && !isThrowLast) Sb->actVoiceFx(Sb->getItemIdx() + Sb->Pm.mmd->mdplr.mmdCount, 11, 60, L"ya");
				isThrowLast = isThrow;
			}

			
#pragma endregion
			//拿出手来避免阻挡
			if (0 && t < maxout && t>0.f) {
				if (isNid_h(nid))
				{
					auto n2c = mi * vec4(ndPos, 1);
					if (n2c.y < 1) {
						auto dir = mat3(Sb->ndYao->GetGlobalTransform()) * vec3(nid == ehL ? 1 : -1, 0.25f, 0);
						float r = 1 - t / maxout;
						node->rbAtk->addLinearVel(dir * 50.f * r);
						//(node->GetParent()->rb0 ? node->GetParent()->rb0 : node->GetParent()->GetParent()->rb0)->addLinearVel(dir * 10.f * r);

						//MMDFWD(2, "swBall", node->rbAtk->getPosition(), dir * 60.f * r, SColorf(1.0, 1-r,1- r, 1));
						//MMDFW(2, "swBall", node->rbAtk->getPosition(), node->rbAtk->vel * 1.f * r, SColorf(1.0, 1 - r, .5 + 0.5 * (1 - r), 1));

						ATKDP(("ball %f", r));
					}
				}
				else if (isNid_f(nid)) {
					float floatY = bsc * 3.f, py = node->rbPos().y;
					if (py < floatY) {
						float r = 1 - t / maxout;
						node->rbAtk->addLinearVel(vec3(0, floatY - py, 0) * 20.f * r);
						//(node->GetParent()->rb0 ? node->GetParent()->rb0 : node->GetParent()->GetParent()->rb0)->addLinearVel(dir * 10.f * r);

						//MMDFWD(2, "swBall", node->rbAtk->getPosition(), dir * 60.f * r, SColorf(1.0, 1-r,1- r, 1));
						MMDFW(2, "swBall", node->rbAtk->getPosition(), node->rbAtk->vel * 1.f * r, SColorf(1.0, r, .3 + 0.7 * r, 1));

						ATKDP(("ball %f", r));
					}
				}
			}
			//atk.addKick(MAX_KICK / 300 * timeMul, __LINE__);
			//sb->ndYao->rbAtk->setAngVelToPosByAxisY(tpos,10);
			//if (olen > actDis)			sb->ndYao->rbAtk->setAngVelToPos(opos, 100*(olen- actDis )/yaoY);
			//rb0->setAngVelToPos(mmdCamPos, 10.f);
			if (olen < atkRange)
			{
				 o.rb->addLinearVelToPosSc(node->rbAtk->pos, 10, 0.0f);
			}
		}
		
		// ================================= ATK =================================
		if (!outAtkRange) if (!PHY_CATCH_BALL) 
		{

			//sb->centerForceMul = 0.2f; sb->cfMulTimer = 0.5f;
			//node->scaleVel((0.75f), 3, 1, 1.0f);
			if (snAtkDbgT) snAtkDbgT->setMaterialDiffuse(0x80FF0000);
			{
				addVelMul = MUL2 * ATK_FORCE_MUL;

				if (MMD_GRAB_CONNECT && atk.grabCount == 0 && o.grabbed == 0) // && o.sb
				{
					if (olen < atkRange *(o.sb?1.1f:0.78f))
					{
						atk.grabCount++; o.grabbed++; atk.grabTime = gPhyTime;
						glm::vec3 ofs = grabOfs[nid]; if (o.sb) ofs = o.rb->getOfsMatInv() *vec4( ofs,1);

						{
							PMXJoint jt{};
							jt.translate = ofs*(o.sb?0.5f:1.f);
							jt.rotate = grabRtt[nid] ;
							jt.limitMinT = vec3(-100);
							jt.limitMaxT = vec3(100);
							jt.setLocalPos = true;
							jt.springT = vec3(100000000.f);
							jt.springR = vec3(1000000.f);
							jt.dampingT = vec3(10.f);
							jt.dampingR = vec3(10000.f);							 
							atk.jtGrab = Sb->Pmx->connectRb(node->rb0, o.rb,1, 1, jt);
							o.grabStage = 1;
						}
						o.grabBy = &atk;
						o.rb->setCollideFilterMask(1, 0);
						//for (auto& rb : o.sb->Pmx->GetPhysicsManager()->m_rigidBodys) { rb->setCollideFilterMask(1,0); /*o.rb->resetRbCd = 100;*/ }

						atk.addKick(MAX_KICK, __LINE__);
						o.resetStatus(); //o.timerBack = o.timer;  o.timer = 99999;
						o.ndLock.clear();
						o.rb->ResetMovement();
						node->scaleVel(0.5, 3, 3); addVelMul *= 0.5f;
						addVelMul *= std::min(1.f, olen / atkRange * .5f);

						if (o.sb){
							auto sb = o.sb; sb->localPhyAnim = 1;
							if (!sb->isAiCharacter())
							{
								sb->loadAnimation(L"D:/Tmp/Victory/1.vmd"); sb->setPlaying(true);
								sb->setPhyAnim(sb->ndYao, true, true); 
							}
						}
					}
					else addVelMul *= std::min(1.f, olen / atkRange * .25f);

					//o.sb->setParent(sb); o.sb->attachToParentNode(node->GetNameU().c_str());

				}
				addVelMul *= o.pm.atkForceMul2;
				ndForce->rbAtk->addLinearVel(addVel = tdir * addVelMul);
				//node->rbAtk->SetCoMTranslate(tpos);
			}
			atk.addKick(MAX_KICK / (nid == eHd ? 1 : 2) * timeMul, __LINE__);
			if (nid == eHd) {
				Sb->Rb0()->addLinearVel(addVel * vec3(0.7, 1.9, 0.7));
				Sb->ndUpper2->rbAtk->addLinearVel(addVel * vec3(0.7, 2.9, 0.7));
			}
			else if (nid == efR || nid == efL) {
				auto nd = nid == efL ? nds[efR] : nds[efL];
				if (nd->atk->objId <= 0) { 
					nd->rbAtk->setLinearVel(-addVel * (POLE_DANCE?vec3(1.f):vec3(0.3, 0.6, 0.3)));
					if (YAO_ANTI_FORCE) { Sb->ndYao->scaleVel(0.6, 1); Sb->ndYao->rbAtk->setLinearVel(-addVel * vec3(0.2, 0.2, 0.2)); }
				}
			}
			//laucnAllVtxFw();
			stageTo = 3;
		}
		//if (t2c.z > 1)
		if (o.pm.phe == pheOppaiSaver && isNid_o(nid))
		{
			float atkRgRat = Sb->ndHead->rbAtk->disTo(opos) / 10 / bsc;
			bool closeHead = (Sb->ndHead->rbAtk->disTo(opos) < 3.5f * bsc + o.pm.size.x / 2 && Sb->ndHead->rbAtk->pos.y < opos.y);
			if (closeHead) Sb->ndHead->addVelToLocalPos(Sb->ndUpper2, vec3(0, 5, 10) * bsc, 3);
			vec3 uppos = opos + vec3(0, -3, 0); uppos.y = std::min(uppos.y, Sb->ndUpper2->mGlobalInit[3].y);
			ndctr[nid]->rbAtk->addLinearVelToPosLimitDis(uppos, 3 + (closeHead ? 20 : 0), 3 * bsc, 7 * bsc);//addLinearVel(addVel);
			Sb->ndHandL->scaleVel((0.7f), 3, 2, 1.0f);
			Sb->ndHandR->scaleVel((0.7f), 3, 2, 1.0f);
			Sb->ndHandL->addVelToLocalPos(Sb->ndUpper2, { 10,20,1 }, 0.5f * atkRgRat);
			Sb->ndHandR->addVelToLocalPos(Sb->ndUpper2, { -10,20,1 }, 0.5f * atkRgRat);
			Sb->ndUpper2->rbAtk->setAngVelToRotateOnNode(mat3(quat(vec3(half_pi<float>(), 0, 0))), std::max(10.f, 20 - atkRgRat) * 1);
			if (o.sb) o.killY = 3;

		}
		if (mmd->MPA && atk.stage < 1 && stageTo == 1)
		{
			//mmd->MPA->setPPNdB(0, obj->uid);
			//Sb->startSbMPA("data/mpa/slash.json");
		}
#if 0
		if (atk.stage < 1 && kicking == 1 && gGameTime - lastVoice1T>0.5f) {
			//if (VOICEFX) Sb->actVoiceFx(Sb->getItemIdx() + Sb->Pm.mmd->mdplr.mmdCount, 0, 60);// nid == eHd ? 0 : UaRand(10), 60, L"mi pa");
			//node->rbAtk->setMassMul(10.f);
			{
				//Eqv->setCurPtrFwId(Eqv->findPtrFwIdx(1, "soundFw"), 1);
				//sb->lauchAllVtxFw = 1; sb->vtxFrameFw = 0;
				extern int coincount; int charCount;
				auto s = ualib::numberToEnglish(o.id, charCount);
				if (ATK_VOICEFX) Sb->actVoiceFx(8, 5, 60,s,charCount );// , ls[UaRand(ls.size())]);
				DPWCS((L"numstr %10s  [%d]",s.c_str(),charCount));
			}
			lastVoice1T = gGameTime;
			lastKickNid = nid;
		}
#endif
		//if (atk.stage < 2 && kicking >= 2 && gGameTime - lastVoice2T>0.5f) {	//if (VOICEFX) sb->actVoiceFx(sb->getItemIdx() + sb->Pm.mmd->mdplr.mmdCount, 0, 63, L"ni");	lastVoice2T = gGameTime;		}	else 
		if (atk.stage < 3 && stageTo == 3 && o.lastVoiceFxSb!=Sb /*&& nid <= eHd*/ && (gGameTime - lastVoiceTime > 0.32f || gGameTime - lastVoiceTime > 0.05f && lastVoiceOnObjId != nid))
		{
			//node->rbAtk->setMassMul(1.f);
			int fx = (nid == ehL || nid == ehR) ? 0 : (nid == efL || nid == efR) ? 1 : 2;// otherNd == ndHead ? 2 :
			//	o.lockNdId < 2 ? 0 : o.lockNdId < 6 ? 1 : o.lockNdId == eHd ? 2 : (otherNd && otherNd->rbAtk->collideOtherChar ? 3 : 4);
			int objKey = 66;
			if (ATK_VOICEFX) {
				static std::vector<std::wstring> ls = { L"uru sai",L"sai tei",L"fu za ke ru na" ,L"chi ku shou" ,L"a ho" ,L"ba ka" ,L"bo ke" ,L"bu su" ,L"de bu" ,L"da sai" ,L"su ke be" ,L"iya ra shii" ,L"ku so ga ki" ,L"ku so ji jii" ,L"ku so ba baa",L"zu rui",L"ki moi" };
#if 1
				//if (ATK_VOICEFX) Sb->actVoiceFx(Sb->getItemIdx() + Sb->Pm.mmd->mdplr.mmdCount, 10, 66);// , ls[UaRand(ls.size())]);
#else
				static std::vector < std::vector<std::wstring>> ls2;
				if (ls2.size() == 0)	for (const std::wstring& str : ls) {
					std::vector<std::wstring> subVec;
					std::wistringstream iss(str);
					std::wstring word;
					while (std::getline(iss, word, L' ')) {
						subVec.push_back(word);
					}
					ls2.push_back(subVec);
				}
				if (VOICEFX) Sb->Pm.mmd->mdplr.aouWords(Sb->getItemIdx(), ls2[UaRand(ls2.size())]);
#endif
				o.lastVoiceFxSb = this->Sb; o.voiceFxCount++; lastVoiceTime = gGameTime;
			}

			lastVoiceOnObjId = nid; lastKickNid = nid;

		}
		atk.stage = stageTo;
		//MMDFWD(2, "sw2", nds[nid]->getGlobalPos(), vec3{ 0.f }, SColorf(kicking*83/256.f, 1- kicking * 64 / 256.f, 0, 1));
		if (ONLY_HEAD_UP && nid == eHd) {
			nds[eHd]->GetParent()->rbAtk->addLinearVel(addVel * 0.8f);
			nds[eHd]->GetParent()->GetParent()->rbAtk->addLinearVel(addVel * 0.7f);
		}

		acted[nid] = true;
		atk.lastLockTime = gPhyTime;
#if 1
		ATKDP(("ATK s%d o%d n%d [>> %d <<] out%d fm %2.2f dis %2.1f lenp %2.3f vel %3.3f mul %3.3f ob%d AdV %f lev=%d",
			Sb->getItemIdx(),
			o.uid, nid, stageTo, outAtkRange,atk.forceMul, tlen, lenp, glm::length(node->rbAtk->getLinearVel()), mul,
			onActBall, addVelMul, leaving));
#endif
		//if (nid == efL || nid == efR) 	if (node->GetParent()->disTo(opos) < node->disTo(opos)) { node->rbAtk->ResetRigidbody(); node = node->GetParent(); mul *= 3; }
		//if (o.ndLockLast) o.addKick(MAX_KICK /6*timeMul, __LINE__);
		//else o.addKick(MAX_KICK / 60 *timeMul, __LINE__);
		//node->rbAtk->addForce(tdir * mul * 30.f);


		if (firstLock && nds[eHd]->rb0 && nid != eHd) {
			if (LOCK_LOOK) Sb->lastLookObjId = o.uid;

			Sb->phyLookAt(IrrSaba::phyLAParam{
				.pos = o.sb && o.sb->ndHead ? o.sb->ndHead->rbPos() : o.rb->getPosition(),
				.powHead = std::max(3.f, HeadRttMul * bsc / tlen),
				.powEye = 36,
				.powCatEar = 300.f,
				.angleMul = 1.6f,
				});

		}


		//node->rbAtk->addForce(tdir * mul, true);
		//ATKDP(("v%3.3f",glm::length(node->rbAtk->getLinearVel())));
		firstLock = false;
	}


}

void irr::scene::CharacterAttacker::lauchAllVtxFw()
{
	if (gGameTime - lastVoiceTime > 0.1f)
	{
		Eqv->setCurPtrFwId(Eqv->findPtrFwIdx(1, "soundFwo"), 1);
		Sb->lauchAllVtxFw = 1; 
	}
	lastVoiceTime = gGameTime;
}












void irr::scene::CharacterAttacker::detectLockNodes(float steptime, int step, int stepCount)
{
	//{eNdIdNone = -1,		ehL=0,	ehR=1,	efL=2,	efR=3,	elL=4,	elR=5,	eHd=6,	eYa=7,	eoL=8,	eoR=9 };
	float scoreAdd[] = { 1.f,	1.f,	-2.f,	-2.f,	1.f,	1.f,	1.f,	1.0f,	1.0f,	1.0f };
	if (step != 0) return;
 

	detectDis = (FAR_DETECT?300.f: 60.f) * rangeScale;

	int curLockNodes = 0;
	for (int i = 0; i < canLockNum; i++) {
		if (atks[i].objId > 0) {
			curLockNodes++;
			detectDis *= 0.5f;
			switch (i) {
			case elL: case efL: { scoreAdd[elR] = scoreAdd[efR] = detectDis; } break; // one leg/foot
			case elR: case efR: { scoreAdd[elL] = scoreAdd[efL] = detectDis; } break;
			}
		}
	}
	int id = Pom->phyObjs.size();

	while (--id >= 0 ) //o.lockNdId!=ehd
	{
		PhyObj* obj = Pom->phyObjs[id];
		PhyObj& o = *obj;
		if (curLockNodes >= o.pm.maxSbLockNodes) continue;
		if (!(obj && o.ageLockable() &&   o.grabbed == 0 && o.pm.atkFlag && o.pm.atkSbIgnore[Sb->getItemIdx()]==0))
			continue;
		if (o.oTgtSb && o.oTgtSb != Sb) continue;
		if (o.oTgtSb && o.oTgtSb == Sb)
		{
			for (auto n : o.ndLock) if (n->model->saba == Sb) continue;
		}
		auto rb = o.rb; float objR = rb->getSize().x;
		vec3 objv = rb->getLinearVel();
		vec3 opos = rb->getPosition();

		vec3 ypos = Sb->ndYao->rbAtk->getPosition(); float legLen = Pmx->yaoPos.y * bsc;
		float ballToYao = glm::length(opos - ypos);
		line3df line(opos, opos + objv * 1.f);
		f64 insDis;
		ENdId nid = eNdIdNone, nearNid = eNdIdNone; float nearestDis[16] = {}; vector3df closePt[16]; int closePi[16] = {}, closePiHrz[16] = {};; glm::vec3 nearPos{ 0,1000,0 };
		auto setNid = [=, &nid](ENdId id, int line) {
			if (id != eNdIdNone && (id >= canLockNum || !obj->canAtkBy(id) || ((nid == ehL || nid == ehR) && nds[id]->rbAtk->disableAtk))) return;
			nid = id;
			if (isNid_h(nid)) {
				ATKDP(("set hadnd %d		ln %d", nid, line));
			}
			};
		bool atOppai = false;
		bool close = ballToYao < detectDis;
		if (!close) continue;
		if (o.pos.y < 5.f && glm::length2(o.vel) < 1)
			continue;

		if (o.ndLock.size() >= MAX_LOCKER_SB) {
			if (Sb->Rb0()->disTo(o.pos) > o.ndLock.back()->model->saba->Rb0()->disTo(o.pos))
				continue;
		}
 
		if (o.leaveCount[this] > DETECT_LEAVE_MAX /*|| o.mmdHitCount>= lockObjMaxHit*/ || o.mmdHitCount < lockObjMinHit)
		{
			if (o.oTgtSb) {
				o.oTgtSb = nullptr; //Eqv->Ctx->scenePaused = true;
			}
			continue;
		}
		if (ignoreObjs.contains(o.uid))
			continue;

		int myNodeCount = 0;
		for (auto& nd : o.ndLock) if (nd->model == Pmx)	myNodeCount++;
		if (myNodeCount > 0) continue;

 
		vec3 o2C = (Sb->ndYao->rbPos() - opos);		o2C.y = 0;

		o.leaveCount[this] = 0;

		if (ballToYao > yaoY * 2) {
			scoreAdd[elL] = scoreAdd[elR] = scoreAdd[eYa] = yaoY; if (!ONLY_HEAD_UP) scoreAdd[eHd] = yaoY;
		}

		//if (o.ndLock && (gPhyTime-o.lockTime<0.1f || glm::length(opos-o.ndLock->rbAtk->getPosition())< actDisRs[o.lockNdId]) || o.lockNdId==efL || o.lockNdId==efR) continue;
		float nearScore = 99999.f;
		vec3 yaoAgv = Sb->ndYao->rbAtk->getAngularVel();
		auto miYao = glm::inverse(Sb->ndYao->GetGlobalTransform());

		const int MAX_POS = 60; const float stept = 1 / 60.f; float linDamp = o.rb->Pm.m_translateDimmer;
		vec3 ps[MAX_POS];   int pc = 0;
		{
			vec3 v = objv, p = opos, g = vec3(0, -9.81f * PHY_GRAVITY_MUL, 0);
			for (int i = 0; i < MAX_POS; i++) {
				//vs[pc] = v; 
				ps[pc] = p; pc++;
				MMDFWD(2, "pt", p, vec3{ 0.f }, SColorf(1, 1, 0.0 + step * 0.5f, 1));
				v += stept * (g - linDamp * v); p += stept * v;
				if (p.y <= rb->Pm.m_shapeSize.x) { p.y = rb->Pm.m_shapeSize.x; v.y = -v.y;	v *= HIT_GROUND_VMUL; }
			}
		}
		float nearestPosDis = 9999999, minTime=999999.f;
		for (int i = 0; i < canLockNum; i++) if (o.canAtkBy(i)) {
			auto& atk = atks[i];
			vec3 ndPos = ndctr[i]->rbAtk->getPosition();
			float t = 0;

			float minDis2 = 9999999.f, minDisHrz = 9999999.f; 
			int minI = 0, minHrzI, firstInRgI = 0; 
			float rg2 = pow(actDisRs[i]/BASE_RANGE_SCALE,2.f);

			for (int pi = 0; pi < pc; pi++) {
				vec3 dt = ps[pi] - ndPos;
				float dis2 = glm::length2(dt);
				if (dis2 < minDis2) {
					minDis2 = dis2;
					minI = pi;
				}
				dt.y = 0; float dix2Hrz = glm::length2(dt); //xz plane dis
				if (dix2Hrz < minDisHrz) {
					minDisHrz = dix2Hrz;
					minHrzI = pi;
				}
				if (!firstInRgI && dis2 < rg2) firstInRgI = i;
			}
			MMDFWD(2, "sw2", ps[minI], vec3{0.f}, SColorf(1, 1, 0.0 + step * 0.5f, 1));
			closePi[i] = minI;
			closePt[i] = ps[minI];
			closePiHrz[i] = minHrzI;

			float dis = nearestDis[i] = closePt[i].getDistanceFrom(ndPos);
			if (nearestPosDis > nearestDis[i]) {
				nearestPosDis = nearestDis[i];	
				minTime = (firstInRgI? firstInRgI : minI) * stept;
			}
			if (isNid_h(ENdId(i))) {
				if (dis > actDisRs[i] / BASE_RANGE_SCALE)
					dis += 5 * bsc;
			}
			float b2cDis = closePt[i].getDistanceFrom(opos);
			float anmAdd = 0;
			if (Pmx->phyActAnimating && Pmx->curVA)
				nds[atk.nid] == Pmx->curVA->ndAct ? 0.f : 100.f;
			assert(nds[i]->rbAtk);
			//SCORE 越小越好
			float score = dis + b2cDis * 3
				//+ (atk.objId > 0 && atk.objId != o.id  ? 1 : 0) * 6 * bsc
				+ scoreAdd[i] * bsc
				+ 1.f / (gPhyTime - atk.lastLockTime + 0.1f)
				+ (atk.grabCount ? (std::max(0.f, pow(1.f - (gPhyTime - atk.grabTime), 0.333f) * 10.f)) : 0)
				+ (o.uid == lastKickObjId) * 10.f
				+ (nds[i]->rbAtk->disableAtk ? 1000.f : 0.f)
				+ (lastKickNid == i ? 100 : 0)
				+ (anmAdd)
				;
			;
			//ATKDP(("E%d  dis %f,  score %f ", i, dis, score));
			if (score < nearScore && (dis < actDisRs[i] + objR || legGrab && i == eYa) && nds[i] != ndLockLast)
			{
				nearScore = score; nearNid = (ENdId)i; nearPos = closePt[i];

				if (FORCE_NEAREST_NID || legGrab && nearNid == eYa)					setNid(nearNid, __LINE__);
			}
		}
		if (FAR_DETECT)
			nid = nearNid;
		else if (nearestPosDis > 20 * bsc || minTime > 0.975f) {
			//DP(("SKIP dis %8.2f  near %8.2f  time %8.2f", ballToYao, nearestPosDis,minTime));
			continue;
		}

		bool isLastBall = o.uid == lastKickObjId;

		bool upHead = false; if (ONLY_HEAD_UP) {
			vec3 chpt = ps[closePiHrz[eHd]];
			//MMDFWD(2, "swLong", chpt, vec3{ 0.f }, SColorf(1.0, 1, 1.0, 1));
			vector3df hdToObjVector = chpt - headPos;
			float angle = hdToObjVector.getNormalizeCopy().angleBetweenInDegree(vec3(0, 1, 0));
			//ATKDP(("ang %f", angle));
			if (angle < 15.f && hdToObjVector.y>1.f)
				upHead = true;
			if (ONLY_HEAD_UP && !upHead && nearNid == eHd) 	nearNid = eNdIdNone;
		}

		if (o.oTgtSb && o.oTgtSb == Sb)
		{
			MMDFWD(2, "sw1s", Sb->ndHead->getGlobalPos(), vec3(0, 10, 0), SColorf(1, 0.5, 1, 1));
			if (PHY_CATCH_BALL)nid = eYa;
		}
		if (upHead) setNid(eHd, __LINE__);
		else if (nearNid != eNdIdNone && nid < 0) {
			float nearDis = nearestDis[nearNid];
			if (nearDis > atkDisRs[nearNid] && isLastBall && !jump2Obj)
				continue; //碰撞过的球，但不够近，忽略

			if (step == 0) {
				for (int i = 0; i <= 10; i++) { MMDFWD(2, "sw", line.start.getInterpolated(line.end, i / 10.f), vec3{ 0.f }, SColorf(1, 1, 1, 0.7)); }

				MMDFWD(2, "sw2", nearPos, vec3{ 0.f }, SColorf(1.0, 1, 0.0, 1));
				MMDFWD(2, "sw2", nds[nearNid]->getGlobalPos(), vec3{ 0.f }, SColorf(1, 1, 0, 1));
			}

			if (ballToYao > yaoY && !locked[nearNid] && (nearNid == efL || nearNid == efR)) {
				float distofoot = nds[nearNid]->disTo(opos);
				if ((nds[eHd]->disTo(opos) * 2.0f > distofoot && ndctr[nearNid]->disTo(opos) < legLen * 1.5f) || !o.canAtkBy(eHd))
					setNid(nearNid, __LINE__);
			}
			else {
				if (nearNid == elL || nearNid == elR || nearNid == eYa) { //near腰臀，手直线更近就用手
					if (nds[nearNid]->rbAtk->disTo(opos) > nds[ehL]->rbAtk->disTo(opos)) nearNid = ehL;
					else if (nds[nearNid]->rbAtk->disTo(opos) > nds[ehR]->rbAtk->disTo(opos)) nearNid = ehR;
				}
			}
			//if (o.ndLock)
			//	if (nearestDis[o.lockNdId] - nearestDis[nearNid] < 1.f * bsc)
			//		continue;

		}

		auto o2c = miYao * vec4(opos, 1);
		auto n2c = miYao * vec4(nearPos, 1);


		if (ANG_ADD && !isLastBall && (yaoAgv.y > 6 || yaoAgv.y < -6) && o2c.z > 0 && ballToYao > actDisRs[efL])
		{//nid = yaoAgv.y > 10 ? efL : efR;
			angAdd = Sb->ndYao->rbAtk->getAngularVel();
		}


		if ((nid < 0 || MMD_JOYSTICK_GAMECAST) && close) {
			bool isL = ((!locked[ehL] && o2c.x > (locked[ehR] ? -1 : 0)) || o2c.x > 1);// lenL < lenR;//  cd.ckey % 2;//
			float absT2cX = abs(o2c.x);
			vector3df insPt;
			//if (0)
			if ((o.lockNdId == eNdIdNone || o.lockNdId == eHd) && nearestDis[eHd] < 2.7f * bsc && nds[eHd]->disTo(opos) * 1.6f < nds[n2c.x > 0 ? ehL : ehR]->disTo(opos) /* nodes[eHd]->disTo(opos) < actDisRs[eHd] && (ndHead->invTransformVec(closePt[eHd]).y > -1.f)*/) 	//HEAD					 
				setNid(eHd, __LINE__);
			else if (canLockNum > eoL && o.canAtkBy(eoL) && nds[eoL] && o2c.y > 2 * bsc && (nearestDis[eoL] < 3.7f * bsc || nearestDis[eoR] < 3.7f * bsc)) {
				insPt = opos;
				auto oi2c = miYao * vec4(vec3(insPt), 1.f); MMDFWD(2, "sw", insPt, vec3{ 0.f }, SColorf(1, 0.5, 0, 1));
				if (oi2c.z < -1 * bsc && oi2c.x<3 * bsc && oi2c.x>-3 * bsc)
				{
					atOppai = true;
					setNid(nearestDis[eoL] < nearestDis[eoR] ? eoL : eoR, __LINE__);//	if (nodes[nid]->disTo(opos) > oppAtkDis*2) nid = -1;								
					auto p2o = nds[nid]->localOfs(closePt[nid]);
					MMDFWD(2, "sw2", closePt[nid], vec3{ 0.f }, SColorf(1, 0.5, p2o.y < -1 * bsc ? 0 : 0.5, 1));
					//if (p2o.y < -2) 
					//	nid = eYa;
					//else 
					{
						auto ndH = (o2c.x > 0 ? nds[ehL] : nds[ehR]); auto hpos = ndH->GetLocalTransform()[3];
						if (hpos.z < 0 && abs(hpos.x) < 3.f) {
							vec3 vh(o2c.x > 0 ? 1 : -1, 1, 0.5f); vh = Sb->ndUpper2->GetGlobalTransform() * vec4(vh * 1.f, 1);
							ndH->rbAtk->addForce(vh);
						}
					}
				}
				//}
			}
			if (SAFE_MMD && opos.z < 5 || MMD_JOYSTICK_GAMECAST) if (o2c.x > 0) setNid(ehL, __LINE__); else setNid(ehR, __LINE__);
			if (nid < 0 && !atOppai) {
				if (FORCE_LEG_ATK || (o2c.z > legLen && o2c.y < -1 * bsc || o2c.y < (SAFE_MMD ? -legLen / 2 : -legLen / 4) && nearestDis[eYa]>3 * bsc)  /*&& locked[elR]+locked[elL]==0*/) {
					setNid(o2c.x > 0 ? efL : efR, __LINE__);
				}
				else if (n2c.y < -yaoY / 10 && nearestDis[eYa] <= legLen * 0.35f)
					setNid(n2c.x > 0 ? elL : elR, __LINE__);//efL : efR);//
				else if (n2c.y < 0 && nearestDis[eYa] > legLen)
					setNid(n2c.x > 0 ? efL : efR, __LINE__);//efL : efR);//
				else if (n2c.y > -yaoY / 10 && absT2cX > yaoY / 2) {
					if (isNid_h(nearNid)) setNid(nearNid, __LINE__);
					else setNid(isL ? ehL : ehR, __LINE__);
				}
				if (nid < 0 || locked[nid]) {
					if (!locked[ehL] && nearestDis[ehL] < legLen * .3f) setNid(ehL, __LINE__);
					else if (!locked[ehR] && nearestDis[ehR] < legLen * .3f) setNid(ehR, __LINE__);
					//else if (nearestDis[efL] < legLen / 2) nid = efL;				else if (nearestDis[efR] < legLen / 2) nid = efR;
				}
			}
			if (nid >= 0 && nid <= ehR && nds[nid]->disTo(opos) > 10.f * bsc && vector3df(objv).getNormalizeCopy().angleBetweenInDegree(glm::normalize(nds[nid]->rbAtk->getLinearVel())) > 120) setNid(ENdId::eNdIdNone, __LINE__);
			if (nid < 0 &&
				(nearNid >= 0 && nearestDis[nearNid] < atkDisRs[nearNid] + objR //&& nearNid>elR
					)
				) {
				setNid(nearNid, __LINE__);
			}
		}
		if (ndLockLast && nid >= 0) {
			auto dir = core::vector3df(nds[nid]->rbAtk->getPosition()) - o.pos;
			float a = dir.getNormalizeCopy().angleBetweenInDegree(glm::normalize(o.vel));
			ATKDP(("2nd %d  a=%f", nid, a));
			if (nds[nid] == ndLockLast)
			{
				atks[nid].addKick(MAX_KICK, __LINE__); setNid(eNdIdNone, __LINE__);
			}
			else if (a > 90) {
				atks[nid].addKick(MAX_KICK / 4, __LINE__); setNid(eNdIdNone, __LINE__); ATKDP(("2ndAng %d %f", nid, a));
			}
			else if (dir.getLength() > atkDisRs[nid] / BASE_RANGE_SCALE && (nid > efR)) {
				atks[nid].addKick(MAX_KICK, __LINE__); setNid(eNdIdNone, __LINE__);
			}
			if (nid >= 0) ATKDP(("2nd %d  a=%f !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", nid, a));
		}
		//if (nid == efL) nid = ehL;				if (nid == efR) nid = ehR;
		//if (nid == elL) nid = eYa;				if (nid == elR) nid = eYa;
		MMDFWD(2, "sw", opos, vec3{ 0.f }, SColorf(1, 0, 0, 1));
		auto lenT2c = glm::length(o2c);
		//if (SAFE_MMD) {		if (nid == efL) nid = ehL; if (nid == efR) nid = ehR;		}
		//if (nid == ehL || nid == ehR) nid = (ENdId)(int(nid) + int(eoL - ehL));


		if (nid >= 0 && (o.canAtkBy(nid) && lenT2c < actDisRs[nid] * 5 || (jump2Obj || legGrab) && nid == eYa) && locked[nid] == 0)
		{
			o.lockNdId = nid;

			auto& atk = atks[nid];
			if (atk.objId > 0) continue;
			//if (o.ndLock.size()) {for (auto& nd : o.ndLock) if (nd->disTo(o.pos) < nds[nid]->disTo(o.pos))		continue;				else {		nd->atk->addKick(MAX_KICK, __LINE__);		nd->model->scaleBodyVel(0.5f, 3);	}			}
			float timepast = gPhyTime - atk.lastLockTime;
			//	if ((o.id == atk.objIdLast || (lastNid == efL && nid == efR) || (lastNid == efR && nid == efL)) && timepast < 0.3f) continue;

			ATKDP(("LockNid %d    last %d   closeDis=%f  tp=%f", nid, atk.objIdLast, nearestDis[nid], timepast));
			atk.resetStatus();
			atk.objId = o.uid;
			atk.lockTime = gPhyTime;
			atk.tposLocked = false;
			if (jump2Obj) {
				prepareJump();
			}

			o.ndLock.emplace_back(nds[nid]);
			o.isflyingToSb = true; o.flyingToFlag = 0; o.flyingToSbNode = nds[nid];

			limitAtks(o, MAX_LOCKER_SB);

			locked[nid] = 1; curLockNodes++;


			//if (Pmx->curVA && !Pmx->actAnimating)	Sb->setAdAnim(0x10001, 1);

			//o.ndLock->scaleVel(0.0f, 3, 3, 1.f);
		}
		if (SAFE_MMD) break;
	}
}

void irr::scene::CharacterAttacker::limitAtks(irr::scene::PhyObj& o, size_t maxLockSb)
{
#define NDCLOSETO ndHead //ndYao
	if (o.ndLock.size() > maxLockSb) {
		std::sort(o.ndLock.begin(), o.ndLock.end(), [&](MMDNode* a, MMDNode* b) {
			return a->model->saba->NDCLOSETO->disTo(o.pos) < b->model->saba->NDCLOSETO->disTo(o.pos);
			});
		for (int i = 0; i < o.ndLock.size() - maxLockSb; i++)
		{
			auto nd = o.ndLock[o.ndLock.size() - 1 - i];
			nd->atk->addKick(MAX_KICK, __LINE__);
		}
	}
}
