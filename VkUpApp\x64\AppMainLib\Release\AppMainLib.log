﻿  AppMainAMP.cpp
  AppMainAMP_P2.cpp
  AppMainTextFwP2.cpp
  ImgVideoEncoder.cpp
  SnCsParticle.cpp
  EQV.cpp
  ShaderToy.cpp
  VideoHelpers.cpp
  CharacterAttacker.cpp
  CharacterCatcher.cpp
  irrSaba.cpp
  sabaCloth.cpp
D:\AProj\CommonStaticLib\gif-h\gif.h(393,23): warning C4334: '<<': result of 32-bit shift implicitly converted to 64 bits (was 64-bit shift intended?)
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\CommonStaticLib\gif-h\gif.h(394,26): warning C4334: '<<': result of 32-bit shift implicitly converted to 64 bits (was 64-bit shift intended?)
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/VideoHelpers.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/ShaderToy.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/VideoHelpers.cpp')
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/ShaderToy.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(141,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(322,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(353,33): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(387,30): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(680,19): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(710,13): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(860,72): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(864,16): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244: 'initializing': conversion from 'const T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244:             T=irr::u32
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,14): warning C4244:         ]
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244: 'initializing': conversion from 'const T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244:             T=irr::u32
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(994,52): warning C4244:         ]
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244: 'initializing': conversion from 'const _Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244:             _Ty=float
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(999,13): warning C4244:         ]
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1047,38): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(987,7): warning C4101: 'fwtId': unreferenced local variable
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1153,11): warning C4244: 'initializing': conversion from 'float' to 'irr::u8', possible loss of data
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1123,40): warning C4101: 'imgRing': unreferenced local variable
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SnCsParticle.cpp(1123,31): warning C4101: 'imgSQ': unreferenced local variable
  (compiling source file '/src/IrrFw/SnCsParticle.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/VideoHelpers.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/VideoHelpers.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/VideoHelpers.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\VideoHelpers.cpp(379,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/VideoHelpers.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.cpp(384,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/VideoHelpers.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.cpp(814,28): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/VideoHelpers.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(437,23): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(590,41): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(1097,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(1098,18): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\ImgVideoEncoder.cpp(1094,45): warning C4101: 'fl_y': unreferenced local variable
  (compiling source file '/src/ImgVideoEncoder.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\AppTypes.h(9,8): warning C4099: 'ualib::VideoProcessor': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CLineGridSceneNode.h(86,16): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(340,24): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(340,30): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(340,36): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(340,42): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(352,25): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(352,31): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(352,37): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(352,43): warning C4838: conversion from 'int' to 'irr::video::SColor' requires a narrowing conversion
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(173,2): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(186,2): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(198,90): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(480,11): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(481,11): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(482,11): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(483,11): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(206,89): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(207,94): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(570,1): warning C4102: 'testCapScrEnd': unreferenced label
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(231,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(236,88): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(237,95): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(238,104): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(239,104): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(246,63): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(246,99): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(262,94): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(263,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(264,106): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(265,105): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(728,28): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(729,28): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(742,28): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(743,28): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(745,42): warning C4244: '=': conversion from 'float' to 'int64_t', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(795,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(795,54): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(802,9): warning C4244: 'initializing': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(295,2): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(302,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(303,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(308,100): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(309,99): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(311,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(312,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(314,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(315,92): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(333,87): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(335,89): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(371,102): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(372,102): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(1119,25): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\app\MusicFirework\AppMainTextFwP2.cpp(1123,26): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/app/MusicFirework/AppMainTextFwP2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(413,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(414,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(414,63): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(415,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(416,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(427,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(428,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(428,63): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(429,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(430,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(441,6): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(442,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(448,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(462,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(463,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWLineD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(469,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(474,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(475,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(519,19): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(530,2): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(539,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(540,93): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(552,91): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(553,91): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(779,11): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(795,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(809,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(813,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(852,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(861,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(867,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(872,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(877,35): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(914,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(923,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(930,30): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterCatcher.cpp(935,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterCatcher.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\base\VulkanUIOverlay.h(110,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(114,11): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(119,51): warning C4244:         ]
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\sabaCloth.cpp(120,79): warning C4244:         ]
  (compiling source file '/src/irrmmd/sabaCloth.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,66): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,71): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,76): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,86): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(71,91): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(72,74): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(72,79): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(72,84): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(72,89): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(84,182): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(195,3): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(288,20): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(332,77): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(425,96): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(439,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(440,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(441,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(454,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(464,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(537,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(555,7): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(599,16): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(635,6): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(677,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(678,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(687,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(688,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(747,52): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(748,52): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(63,60): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(789,16): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(263,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(263,34): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(264,11): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(283,25): warning C4305:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(862,55): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(866,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(297,118): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(867,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(868,46): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(869,46): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(882,6): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(926,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(927,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(936,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(937,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(607,45): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP_P2.cpp(625,19): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP_P2.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(996,52): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(997,52): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1039,16): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1130,55): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1134,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1135,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1136,46): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1137,46): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1150,6): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1191,7): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1267,48): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1276,48): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1289,58): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(877,16): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1089,41): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1216,34): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1242,38): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(288,76): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(651,20): warning C4101: 'R2': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(651,16): warning C4101: 'L2': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(894,20): warning C4101: 'R2': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(894,16): warning C4101: 'L2': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1231,9): warning C4101: 'isR': unreferenced local variable
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1433,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1509,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1538,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1599,4): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1609,37): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1611,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1612,5): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1559,15): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1651,49): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1657,6): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1716,3): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1661,16): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1712,89): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(1454,7): warning C4101: 'insDis': unreferenced local variable
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1753,8): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1760,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(1975,63): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2023,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2109,23): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2268,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2284,26): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2306,13): warning C4305: '+=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2313,62): warning C4244: 'argument': conversion from 'irr::f32' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2321,36): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2493,17): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305: 'argument': truncation from 'const irr::f64' to 'const T'
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         with
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         [
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:             T=irr::f32
D:\AProj\UaIrrlicht\include\vector3d.h(347,40): warning C4305:         ]
  (compiling source file '/src/irrmmd/CharacterAttacker.cpp')
      D:\AProj\UaIrrlicht\include\vector3d.h(347,40):
      the template instantiation context (the oldest one first) is
          D:\AProj\UaIrrlicht\include\matrix4.h(1190,13):
          see reference to class template instantiation 'irr::core::vector3d<irr::f32>' being compiled
          D:\AProj\UaIrrlicht\include\vector3d.h(345,15):
          while compiling class template member function 'irr::core::vector3d<irr::f32> irr::core::vector3d<irr::f32>::getHorizontalAngleRad(void) const'
              D:\AProj\AppMainLib\src\irrmmd\CharacterAttacker.cpp(612,16):
              see the first reference to 'irr::core::vector3d<irr::f32>::getHorizontalAngleRad' in 'irr::scene::CharacterAttacker::attackLockNodes'
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2647,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2665,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2722,28): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2750,39): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244: 'argument': conversion from 'const _Ty' to 'int', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244:             _Ty=float
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2758,29): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2762,24): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244: '=': conversion from 'const _Ty' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         ]
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         and
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:             T=float
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2811,44): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2877,18): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(2892,59): warning C4244: 'argument': conversion from 'int32_t' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3094,61): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3132,18): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3316,59): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3420,18): warning C4553: '==': result of expression not used; did you intend '='?
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3485,148): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(508,14): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(547,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3646,91): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3862,11): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3864,11): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3890,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3891,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3892,25): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,45): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3893,19): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3910,35): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3916,15): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3917,43): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3948,30): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3949,60): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3977,35): warning C4305: '+=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(3980,54): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244: 'initializing': conversion from 'const _Ty' to 'float', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244:             _Ty=double
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(733,14): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4011,41): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4071,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4147,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1136,49): warning C4244: 'argument': conversion from 'double' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4212,34): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1249,17): warning C4244: '=': conversion from 'float' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1275,25): warning C4244: 'argument': conversion from 'float' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1282,29): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1287,17): warning C4244: 'argument': conversion from 'double' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1319,41): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1493,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1507,39): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1537,16): warning C4244: '+=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1593,44): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1682,69): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1755,12): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1779,68): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1798,55): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244: 'initializing': conversion from 'T' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,12): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244: 'initializing': conversion from 'T' to 'int', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1861,29): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2002,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2073,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2105,8): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2127,34): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2201,66): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4393,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(891,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1081,10): warning C4244: 'initializing': conversion from 'double' to 'glm::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1084,46): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4626,17): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1140,46): warning C4244: 'argument': conversion from 'double' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1141,22): warning C4244: 'argument': conversion from 'double' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4677,17): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(1244,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305: 'argument': truncation from 'double' to 'const T'
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2292,23): warning C4305:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2329,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4876,38): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4899,11): warning C4267: 'initializing': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(4905,35): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(5055,25): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2489,33): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(5688,68): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(5740,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2051,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(2859,24): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2139,13): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2349,25): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2376,8): warning C4244: 'argument': conversion from 'float' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2508,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2509,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2529,67): warning C4244: 'argument': conversion from 'float' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2580,19): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,43): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244:         with
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244:         [
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,73): warning C4244:         ]
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,32): warning C4838: conversion from 'float' to 'LONG' requires a narrowing conversion
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,32): warning C4244: 'initializing': conversion from 'float' to 'LONG', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,62): warning C4838: conversion from 'float' to 'LONG' requires a narrowing conversion
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6266,62): warning C4244: 'initializing': conversion from 'float' to 'LONG', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2686,110):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2691,5):
          while compiling class template member function 'std::function<void (irr::scene::IrrSaba *)>::function(_Fx &&)'
          D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2691,5):
          while processing the default template argument of 'std::function<void (irr::scene::IrrSaba *)>::function(_Fx &&)'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\functional(1102,92):
          see reference to alias template instantiation 'std::_Func_class<_Ret,irr::scene::IrrSaba *>::_Enable_if_callable_t<AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2>,std::function<void (irr::scene::IrrSaba *)>>' being compiled
          with
          [
              _Ret=void
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\functional(940,47):
          see reference to variable template 'const bool conjunction_v<std::negation<std::is_same<`AppNameSpace::AppMainAMP::onKeyUpdate'::`6'::<lambda_2>,std::function<void __cdecl(irr::scene::IrrSaba *)> > >,std::_Is_invocable_r<void,`AppNameSpace::AppMainAMP::onKeyUpdate'::`6'::<lambda_2> &,irr::scene::IrrSaba *> >' being compiled
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\functional(940,47):
          see reference to class template instantiation 'std::_Is_invocable_r<_Ret,AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2> &,irr::scene::IrrSaba *>' being compiled
          with
          [
              _Ret=void
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1827,26):
          see reference to alias template instantiation 'std::_Is_invocable_r_<_Rx,_Callable,irr::scene::IrrSaba*>' being compiled
          with
          [
              _Rx=void,
              _Callable=AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2> &
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1789,38):
          see reference to alias template instantiation 'std::_Decltype_invoke_nonzero<_Callable,irr::scene::IrrSaba*,>' being compiled
          with
          [
              _Callable=AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2> &
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1785,71):
          while compiling class template member function 'unknown-type std::_Invoker_functor::_Call(_Callable &&,_Types ...) noexcept(<expr>)'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits(1576,46):
          see reference to function template instantiation 'auto AppNameSpace::AppMainAMP::onKeyUpdate::<lambda_2>::operator ()<irr::scene::IrrSaba>(_T1 *) const' being compiled
          with
          [
              _T1=irr::scene::IrrSaba
          ]
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,35): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2688,82): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6540,32): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6554,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(3691,12): warning C4244: 'initializing': conversion from 'irr::u32' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6609,76): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2734,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2746,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(3839,19): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(3841,17): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6750,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(3906,19): warning C4305: 'initializing': truncation from 'double' to 'irr::f32'
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(6864,3): warning C4002: too many arguments for function-like macro invocation 'MMDFWD'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2909,53): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2930,13): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2953,68): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2954,59): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2955,54): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2956,56): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:             T=irr::f64
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2958,39): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2974,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2975,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2976,29): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2981,28): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2982,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2983,27): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2984,29): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4133,34): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244: 'argument': conversion from 'float' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,47): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244: 'argument': conversion from 'float' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244:             T=irr::s32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4135,28): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,78): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244: 'argument': conversion from 'double' to 'const T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4167,30): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4221,13): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7027,58): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4241,15): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3196,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7041,55): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4413,49): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4457,10): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4457,27): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4467,68): warning C4244: 'argument': conversion from 'size_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4467,58): warning C4244: 'argument': conversion from 'uint32_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4471,66): warning C4244: 'argument': conversion from 'size_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4471,56): warning C4244: 'argument': conversion from 'uint32_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4472,67): warning C4244: 'argument': conversion from 'size_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4472,57): warning C4244: 'argument': conversion from 'uint32_t' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7190,28): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3413,82): warning C4305:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4595,17): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4596,17): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244: '=': conversion from 'float' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244:             T=int
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4598,21): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244: '=': conversion from 'float' to 'T', possible loss of data
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244:         with
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244:         [
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244:             T=int
D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(4599,21): warning C4244:         ]
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7349,15): warning C4305: '+=': truncation from 'double' to 'float'
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\irrSaba.cpp(7401,45): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/irrSaba.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3670,56): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3699,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3783,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3783,35): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3783,56): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305: 'argument': truncation from 'double' to 'T'
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3801,30): warning C4305:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3803,32): warning C4244: 'argument': conversion from 'int' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(3820,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4586,71): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4594,77): warning C4244: '*=': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4654,38): warning C4244: 'argument': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4660,27): warning C4244: '*=': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4706,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4713,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4780,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4843,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4868,57): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4893,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4959,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(4971,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5145,32): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5233,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5252,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5282,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5291,125): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5307,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5331,125): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5337,123): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5341,70): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5343,17): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244:             T=irr::f32
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5580,60): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244: 'argument': conversion from 'double' to 'T', possible loss of data
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244:         with
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244:         [
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244:             T=float
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5712,48): warning C4244:         ]
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5880,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5881,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5882,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(5883,20): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6251,123): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6251,118): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6348,33): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6358,10): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6741,37): warning C4305: 'argument': truncation from 'double' to 'PhyReal'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6748,42): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6759,23): warning C4244: 'argument': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6760,23): warning C4244: 'argument': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6761,117): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6769,101): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6841,54): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(6959,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7071,21): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7143,41): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7146,28): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7302,50): warning C4244: 'argument': conversion from 'int64_t' to 'irr::u32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7359,21): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7490,20): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7491,23): warning C4244: 'argument': conversion from 'const int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7544,42): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7550,40): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7558,19): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7594,57): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::s32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7614,57): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::s32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7717,64): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7755,60): warning C4244: 'argument': conversion from 'const int64_t' to 'irr::s32', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7906,27): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(7912,27): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8171,25): warning C4101: 'e': unreferenced local variable
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8351,3): warning C4002: too many arguments for function-like macro invocation 'FRAMEWAITER_CALL_B'
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8365,43): warning C4804: '>': unsafe use of type 'bool' in operation
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(8378,89): warning C4244: 'argument': conversion from 'double' to 'EQVisual::DrTimeType', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
      D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\src\MatrixRecorder.h(17,31):
          see reference to class template instantiation 'EQVisual::DataRecorderBase<EQVisual::MatRecData>' being compiled
          D:\AProj\AppMainLib\src\DataRecorderBase.h(226,7):
          while compiling class template member function 'int EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs(EQVisual::DrTimeType,const DT *&,const DT *&,float &,size_t)'
          with
          [
              DT=EQVisual::MatRecData
          ]
              D:\AProj\AppMainLib\src\IrrFw\eqv\EQV.cpp(1286,13):
              see the first reference to 'EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs' in 'EQVisual::EQV::ARCamUpdate'
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(252,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/IrrFw/eqv/EQV.cpp')
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
      D:\AProj\AppMainLib\src\DataRecorderBase.h(249,43):
      the template instantiation context (the oldest one first) is
          D:\AProj\AppMainLib\src\MatrixRecorder.h(17,31):
          see reference to class template instantiation 'EQVisual::DataRecorderBase<EQVisual::MatRecData>' being compiled
          D:\AProj\AppMainLib\src\DataRecorderBase.h(226,7):
          while compiling class template member function 'int EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs(EQVisual::DrTimeType,const DT *&,const DT *&,float &,size_t)'
          with
          [
              DT=EQVisual::MatRecData
          ]
              D:\AProj\AppMainLib\app\ArMmPLayer\AppMainAMP.cpp(2374,15):
              see the first reference to 'EQVisual::DataRecorderBase<EQVisual::MatRecData>::getDataPtrUs' in 'AppNameSpace::AppMainAMP::processVideo::<lambda_1>::operator ()'
  
D:\AProj\AppMainLib\src\DataRecorderBase.h(252,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/app/ArMmPLayer/AppMainAMP.cpp')
  
  AppMainLib.vcxproj -> D:\AProj\VkUpApp\x64\Release\AppMainLib.lib
