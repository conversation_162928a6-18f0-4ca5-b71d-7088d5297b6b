﻿#include "AppGlobal.h"
#include "MmdPhyAnimator.h"
#include "irrmmd.h"
#include "../../app/armmplayer/snarRoot.h"
#include "irrmmd/ccubegridscenenode.h"
#include "external/imgui/misc/cpp/imgui_stdlib.h"
#include "VulkanRenderer/base/VulkanUIOverlay.h"
#include <CSceneNodeAnimatorCameraTouchControl.h>
#include "ImGuiMmdHelper.h"
using namespace irr::scene;
using namespace ualib;
using namespace saba;
using namespace glm;


namespace
{
    struct customCmdNameItemsData {
        std::string name;
        int ic = 0, fc = 0, sc = 0;
    };
    static customCmdNameItemsData CCItems[] = { {"ccKinematic",1},{"ccPhyAnim",2},{"ccPhyAnimMul",2,2}, {"ccPhyToAnim",1}, {"ccTimeMul",0,1},  {"ccPhyTimeMul",0,1},{"ccCtrForce",1,1},{"ccKeyEvent",4,0},    {"ccGoToFrame",1,0} }; // Add your items here

}
#pragma region Save_Load
Json::Value MMDNodeInfo_to_Json(const MMDNodeInfo& info) {
    Json::Value jv;
    if (info.sbIdx != 0) jv["sbIdx"] = info.sbIdx;
    if (info.nodeName.size()) jv["nodeName"] = info.nodeName;
    jv["locate"] = info.locate;
    return jv;
}
void json_to_MMDNodeInfo(const Json::Value& jv, MMDNodeInfo& info) {
    if (!jv["sbIdx"].isNull()) info.sbIdx = jv["sbIdx"].asInt();
    if (!jv["nodeName"].isNull()) info.nodeName = jv["nodeName"].asString();
    if (!jv["locate"].isNull()) info.locate = jv["locate"].asInt();
}
void saveCommonAddPhyObjMembers(Json::Value& jv, const KeyPoseCmd::KpcAddPhyObj& pms, const KeyPoseCmd::KpcAddPhyObj& dft) {
    jv["TypeFlag"] = pms.TypeFlag;
    if (pms.modFlag != dft.modFlag) jv["modFlag"] = pms.modFlag;
    if (pms.genIntv != dft.genIntv) jv["genIntv"] = pms.genIntv;
    if (pms.objNum != dft.objNum) jv["objNum"] = pms.objNum;
    if (pms.emitR != dft.emitR) jv["emitR"] = pms.emitR;
    if (pms.maxGen != dft.maxGen) jv["maxGen"] = pms.maxGen;
    if (pms.poType != dft.poType) jv["poType"] = pms.poType;
    if (pms.mass != dft.mass) jv["mass"] = pms.mass;
    if (pms.sizeScale != dft.sizeScale) jv["sizeScale"] = pms.sizeScale;
	if (pms.opm.autoScale != dft.opm.autoScale) jv["autoScale"] = pms.opm.autoScale;
	if (pms.opm.ascDur != dft.opm.ascDur) jv["autoScaleDur"] = pms.opm.ascDur;
	if (pms.opm.ascVal[0] != dft.opm.autoScale) jv["autoScaleVal0"] = pms.opm.ascVal[0];
	if (pms.opm.ascVal[1] != dft.opm.autoScale) jv["autoScaleVal1"] = pms.opm.ascVal[1];

    if (pms.autoRemove != dft.autoRemove) jv["autoRemove"] = pms.autoRemove;
    if (pms.removeTimer != dft.removeTimer) jv["removeTimer"] = pms.removeTimer;
    if (pms.disableHitTime != dft.disableHitTime) jv["disableHitTime"] = pms.disableHitTime;
    if (pms.size != dft.size) jv["size"] = ualib::UaJson::makeVec3(pms.size);
    if (pms.src != glm::vec3(0.f)) jv["src"] = ualib::UaJson::makeVec3(pms.src);
    if (pms.tgt != glm::vec3(0.f)) jv["tgt"] = ualib::UaJson::makeVec3(pms.tgt);
    if (pms.rtt != glm::vec3(0.f)) jv["rtt"] = ualib::UaJson::makeVec3(pms.rtt);
    if (pms.vel != glm::vec3(0.f)) jv["vel"] = ualib::UaJson::makeVec3(pms.vel);

    if (pms.srcPosMode != dft.srcPosMode) jv["srcPosMode"] = pms.srcPosMode;
    if (pms.srcVelMode != dft.srcVelMode) jv["srcVelMode"] = pms.srcVelMode;
    if (pms.flyTime != dft.flyTime) jv["flyTime"] = pms.flyTime;
	if (pms.atkFlag != dft.atkFlag) jv["atkFlag"] = pms.atkFlag;
    jv["srcNd"] = MMDNodeInfo_to_Json(pms.srcNd);
    jv["tgtNd"] = MMDNodeInfo_to_Json(pms.tgtNd);

    if (pms.opm.vfg.sbAtkMode != dft.opm.vfg.sbAtkMode) jv["sbAtkMode"] = pms.opm.vfg.sbAtkMode;

	//if (pms.opm.maxSbLockNodes != dft.opm.maxSbLockNodes) jv["atkMaxLocks"] = pms.opm.maxSbLockNodes;
	//if (pms.opm.maxLockSbs != dft.opm.maxLockSbs) jv["maxLockSbs"] = pms.opm.maxLockSbs;
}
void loadCommonAddPhyObjMembers(const Json::Value& jv, KeyPoseCmd::KpcAddPhyObj& pm) {
    if (!jv["TypeFlag"].isNull()) pm.TypeFlag = jv["TypeFlag"].asUInt();
    if (!jv["modFlag"].isNull()) pm.modFlag = jv["modFlag"].asUInt();
    if (!jv["objNum"].isNull()) pm.objNum = jv["objNum"].asInt();
    if (!jv["emitR"].isNull()) pm.emitR = jv["emitR"].asFloat();
    if (!jv["maxGen"].isNull()) pm.maxGen = jv["maxGen"].asInt();
    if (!jv["genIntv"].isNull()) pm.genIntv = jv["genIntv"].asFloat();
    if (!jv["poType"].isNull()) pm.poType = jv["poType"].asInt();
    if (!jv["mass"].isNull()) pm.mass = jv["mass"].asFloat();
    if (!jv["sizeScale"].isNull()) pm.sizeScale = jv["sizeScale"].asFloat();
	if (!jv["autoScale"].isNull()) pm.opm.autoScale = jv["autoScale"].asInt();
	if (!jv["autoScaleDur"].isNull()) pm.opm.ascDur = jv["autoScaleDur"].asFloat();
	if (!jv["autoScaleVal0"].isNull()) pm.opm.ascVal[0] = jv["autoScaleVal0"].asFloat();
	if (!jv["autoScaleVal1"].isNull()) pm.opm.ascVal[1] = jv["autoScaleVal1"].asFloat();

    if (!jv["autoRemove"].isNull()) pm.autoRemove = jv["autoRemove"].asBool();
    if (!jv["removeTimer"].isNull()) pm.removeTimer = jv["removeTimer"].asFloat();
    if (!jv["disableHitTime"].isNull()) pm.disableHitTime = jv["disableHitTime"].asFloat();

    if (!jv["size"].isNull()) ualib::UaJson::getVec3(jv["size"], pm.size);
    if (!jv["src"].isNull()) ualib::UaJson::getVec3(jv["src"], pm.src);
    if (!jv["tgt"].isNull()) ualib::UaJson::getVec3(jv["tgt"], pm.tgt);
    if (!jv["rtt"].isNull()) ualib::UaJson::getVec3(jv["rtt"], pm.rtt);
    if (!jv["vel"].isNull()) ualib::UaJson::getVec3(jv["vel"], pm.vel);

    if (!jv["srcPosMode"].isNull()) pm.srcPosMode = jv["srcPosMode"].asInt();
    if (!jv["srcVelMode"].isNull()) pm.srcVelMode = jv["srcVelMode"].asInt();
    if (!jv["flyTime"].isNull()) pm.flyTime = jv["flyTime"].asFloat();
    if (!jv["atkFlag"].isNull()) pm.atkFlag = jv["atkFlag"].asUInt();

    if (!jv["srcNd"].isNull()) json_to_MMDNodeInfo(jv["srcNd"], pm.srcNd);
    if (!jv["tgtNd"].isNull()) json_to_MMDNodeInfo(jv["tgtNd"], pm.tgtNd);

	if (!jv["sbAtkMode"].isNull()) pm.opm.vfg.sbAtkMode = jv["sbAtkMode"].asInt();

    //if (!jv["atkMaxLocks"].isNull()) pm.opm.maxSbLockNodes = jv["atkMaxLocks"].asInt();
	//if (!jv["maxLockSbs"].isNull()) pm.opm.maxLockSbs = jv["maxLockSbs"].asInt();
}

void irr::scene::KeyPoseCmd::toJson(Json::Value& jv) const {
    jv["Type"] = typeToString(type);
    switch (type) {
    case KeyPoseCmdType::ectDistance:
    {
        KpcDistance dft;
        auto pms = std::get<KpcDistance>(cmdPm);
        if (pms.TypeFlag != dft.TypeFlag) jv["TypeFlag"] = pms.TypeFlag;
        if (pms.disType != dft.disType) jv["disType"] = pms.disType;
        if (pms.distance != dft.distance) jv["distance"] = pms.distance;
        if (pms.countMode != dft.countMode) jv["countMode"] = pms.countMode;
        if (pms.contition != dft.contition) jv["contition"] = pms.contition;

        // Save sub-commands if they exist
        if (!pms.onCmds.empty()) {
            Json::Value jvCmdsBelow(Json::arrayValue);
            for (const auto& cmd : pms.onCmds) {
                Json::Value jvCmd;
                cmd.toJson(jvCmd);
                jvCmdsBelow.append(jvCmd);
            }
            jv["onCmds"] = jvCmdsBelow;
        }


    }
    break;
    case KeyPoseCmdType::ectFw:
    {
        KpcFw dft;
        auto pms = std::get<KpcFw>(cmdPm);
        jv["TypeFlag"] = pms.TypeFlag;
        if (pms.fwType != dft.fwType) jv["fwType"] = pms.fwType;
        if (pms.name != dft.name) jv["name"] = pms.name;
        if (pms.rbPos != dft.rbPos) jv["rbPos"] = pms.rbPos;
        if (pms.vMul != dft.vMul) jv["vMul"] = pms.vMul;
        if (pms.vAdd != dft.vAdd) jv["vAdd"] = pms.vAdd;
        if (pms.fpm != dft.fpm) jv["fpm"] = ualib::UaJson::makeVec3(pms.fpm);
        if (pms.color != dft.color) jv["color"] = ualib::UaJson::makeVec4(pms.color);
    }
    break;
    case KeyPoseCmdType::ectTextFw:
    {
        KpcTextFw dft;
        auto pms = std::get<KpcTextFw>(cmdPm);
        saveCommonAddPhyObjMembers(jv, pms, dft);

        // Save TextFw specific members
        if (pms.name != dft.name) jv["name"] = pms.name;
        if (pms.text != dft.text) jv["text"] = ualib::WcstoUtf8(pms.text);
        if (pms.color1 != dft.color1) jv["color1"] = ualib::UaJson::makeVec4(pms.color1);
        if (pms.color2 != dft.color2) jv["color2"] = ualib::UaJson::makeVec4(pms.color2);
        if (pms.velOfs != dft.velOfs) jv["velOfs"] = ualib::UaJson::makeVec3(pms.velOfs);
        if (pms.allInOne != dft.allInOne) jv["hitCvt"] = pms.allInOne;
        if (pms.hitCvt != dft.hitCvt) jv["hitCvt"] = pms.hitCvt;
        if (pms.hitGroundCvt != dft.hitGroundCvt) jv["hitGroundCvt"] = pms.hitGroundCvt;
        if (pms.hitVelChgLimit != dft.hitVelChgLimit) jv["hitVelChgLimit"] = pms.hitVelChgLimit;
        if (pms.fwSpecular != dft.fwSpecular) jv["fwSpecular"] = pms.fwSpecular;
        if (pms.baseMass != dft.baseMass) jv["baseMass"] = pms.baseMass;
        if (pms.connectMode != dft.connectMode) jv["connectMode"] = pms.connectMode;
    }
    break;
    case KeyPoseCmdType::ectConnectRb:
    {
        KpcConnectRb dft;
        auto pms = std::get<KpcConnectRb>(cmdPm);
        jv["TypeFlag"] = pms.TypeFlag;
        if (pms.sbA != dft.sbA) jv["sbA"] = pms.sbA;
        if (pms.nodeNameA != dft.nodeNameA) jv["nodeNameA"] = pms.nodeNameA;
        if (pms.lockT != dft.lockT) jv["lockT"] = pms.lockT;
        if (pms.lockR != dft.lockR) jv["lockR"] = pms.lockR;
        if (pms.localPos != dft.localPos) jv["localPos"] = pms.localPos;
        if (pms.translate != dft.translate) jv["translate"] = ualib::UaJson::makeVec3(pms.translate);
        if (pms.rotate != dft.rotate) jv["rotate"] = ualib::UaJson::makeVec3(pms.rotate);
        if (pms.t2B != dft.t2B) jv["t2B"] = ualib::UaJson::makeVec3(pms.t2B);
        if (pms.r2B != dft.r2B) jv["r2B"] = ualib::UaJson::makeVec3(pms.r2B);
        if (pms.limMinT != dft.limMinT) jv["limMinT"] = ualib::UaJson::makeVec3(pms.limMinT);
        if (pms.limMaxT != dft.limMaxT) jv["limMaxT"] = ualib::UaJson::makeVec3(pms.limMaxT);
        if (pms.limMinR != dft.limMinR) jv["limMinR"] = ualib::UaJson::makeVec3(pms.limMinR);
        if (pms.limMaxR != dft.limMaxR) jv["limMaxR"] = ualib::UaJson::makeVec3(pms.limMaxR);
        if (pms.springT != dft.springT) jv["springT"] = pms.springT;
        if (pms.springR != dft.springR) jv["springR"] = pms.springR;
        if (pms.dampingT != dft.dampingT) jv["dampingT"] = pms.dampingT;
        if (pms.dampingR != dft.dampingR) jv["dampingR"] = pms.dampingR;
    }
    break;
    case KeyPoseCmdType::ectAddPhyObj:
    {
        KpcAddPhyObj dft;
        auto pms = std::get<KpcAddPhyObj>(cmdPm);
        saveCommonAddPhyObjMembers(jv, pms, dft);
    }
    break;
    case KeyPoseCmdType::ectCubeGrid:
    {
        auto pms = std::get<KpcCubeGrid>(cmdPm);
        jv["TypeFlag"] = pms.TypeFlag;
        jv["pos"] = ualib::UaJson::makeVec3(pms.pos);
        jv["rtt"] = ualib::UaJson::makeVec3(pms.rtt);
        jv["vel"] = ualib::UaJson::makeVec3(pms.vel);

        Json::Value jvGrid(Json::arrayValue);
        jvGrid.append(pms.grid.x);
        jvGrid.append(pms.grid.y);
        jvGrid.append(pms.grid.z);
        jv["grid"] = jvGrid;

        jv["brickSize"] = ualib::UaJson::makeVec3(pms.brickSize);
        jv["brickSpace"] = ualib::UaJson::makeVec3(pms.brickSpace);
        jv["reset"] = pms.reset;
        jv["density"] = pms.density;
        jv["restitution"] = pms.restitution;
        jv["friction"] = pms.friction;
        jv["centerOrigin"] = pms.centerOrigin;
        jv["connect"] = pms.connect;

		Json::Value imgPaths(Json::arrayValue);
		for (auto& p : pms.imgPaths) imgPaths.append(p);
        jv["imgPath"] = imgPaths;

        jv["color"] = ualib::UaJson::makeVec4(pms.color);

        jv["srcNd"] = MMDNodeInfo_to_Json(pms.srcNd);
        jv["tgtNd"] = MMDNodeInfo_to_Json(pms.tgtNd);
    }
    break;
    case KeyPoseCmdType::ectVoxGrid:
    {
        auto pms = std::get<KpcVoxGrid>(cmdPm);
        jv["TypeFlag"] = pms.TypeFlag;
        jv["voxPath"] = pms.voxPath;
        jv["pos"] = ualib::UaJson::makeVec3(pms.pos);
        jv["rtt"] = ualib::UaJson::makeVec3(pms.rtt);
        jv["vel"] = ualib::UaJson::makeVec3(pms.vel);


        jv["brickSize"] = ualib::UaJson::makeVec3(pms.brickSize);
        jv["brickSpace"] = ualib::UaJson::makeVec3(pms.brickSpace);
        jv["reset"] = pms.reset;
        jv["density"] = pms.density;
        jv["restitution"] = pms.restitution;
        jv["friction"] = pms.friction;
        jv["centerOrigin"] = pms.centerOrigin;
        jv["connect"] = pms.connect;


        jv["color"] = ualib::UaJson::makeVec4(pms.color);

        jv["srcNd"] = MMDNodeInfo_to_Json(pms.srcNd);
        jv["tgtNd"] = MMDNodeInfo_to_Json(pms.tgtNd);
    }
    break;
    case KeyPoseCmdType::ectMotion:
    {
        KpcMotion dft;
        auto pms = std::get<KpcMotion>(cmdPm);
        if (pms.filePath != dft.filePath) jv["filePath"] = pms.filePath;
        if (pms.fileType != dft.fileType) jv["fileType"] = pms.fileType;
		if (pms.speedMul != dft.speedMul) jv["speedMul"] = pms.speedMul;
		if (pms.startTime != dft.startTime) jv["startTime"] = pms.startTime;
		if (pms.swapLR != dft.swapLR) jv["swapLR"] = pms.swapLR;
        if (pms.iv != dft.iv) jv["iv"] = ualib::UaJson::makeIVec4(pms.iv);
        if (pms.fv != dft.fv) jv["fv"] = ualib::UaJson::makeVec4(pms.fv);
    }
    break;
    case KeyPoseCmdType::ectCameraKey:
    {
		auto pms = std::get<KpcCameraKey>(cmdPm);
		jv["anim"] = pms.anim;
        jv["local"] = pms.local;
		jv["vc.tgt"] = ualib::UaJson::makeVec3(pms.vc.m_interest);
        jv["vc.rtt"] = ualib::UaJson::makeVec3(pms.vc.m_rotate);
        jv["vc.dis"] = pms.vc.m_distance;
        jv["vc.fov"] = pms.vc.m_fov;
#if 0
		Json::Value bezierArray(Json::arrayValue);
        auto setBezier = [=](Json::Value& bezierArray, saba::VMDBezier& bezier)
            {
                bezierArray.append(bezier.m_cp1.x);
                bezierArray.append(bezier.m_cp1.y);
                bezierArray.append(bezier.m_cp2.x);
                bezierArray.append(bezier.m_cp2.y);
            };

		setBezier(bezierArray, pms.vc.m_ixBezier);
		setBezier(bezierArray, pms.vc.m_iyBezier);
		setBezier(bezierArray, pms.vc.m_izBezier);
		setBezier(bezierArray, pms.vc.m_rotateBezier);
        setBezier(bezierArray, pms.vc.m_distanceBezier);
        setBezier(bezierArray, pms.vc.m_fovBezier);

		jv["vc.interp"] = bezierArray;
#endif
    }
    break;
    case KeyPoseCmdType::ectCustom:
    {
        auto pms = std::get<KpcCustom>(cmdPm);
        if (pms.customCmdName.size()) jv["customCmdName"] = pms.customCmdName;
        if (pms.customCmdStringValue.size()) jv["customCmdValue"] = pms.customCmdStringValue;
        if (pms.iv != glm::ivec4(0)) jv["iv"] = ualib::UaJson::makeIVec4(pms.iv);
        if (pms.fv != glm::vec4(0)) jv["fv"] = ualib::UaJson::makeVec4(pms.fv);
    }
    break;
    case KeyPoseCmdType::ectModel:
    {
        KpcModel dft;
        auto pms = std::get<KpcModel>(cmdPm);
        if (pms.TypeFlag != dft.TypeFlag) jv["TypeFlag"] = pms.TypeFlag;
        if (pms.filePath != dft.filePath) jv["filePath"] = pms.filePath;
        if (pms.pos != dft.pos) jv["pos"] = ualib::UaJson::makeVec3(pms.pos);
        if (pms.rtt != dft.rtt) jv["rtt"] = ualib::UaJson::makeVec3(pms.rtt);
        if (pms.scale != dft.scale) jv["scale"] = pms.scale;
        if (pms.massMul != dft.massMul) jv["massMul"] = pms.massMul;
        if (pms.frictionMul != dft.frictionMul) jv["frictionMul"] = pms.frictionMul;
        if (pms.isCharacter != dft.isCharacter) jv["isCharacter"] = pms.isCharacter;
        if (pms.allRbActive != dft.allRbActive) jv["allRbActive"] = pms.allRbActive;
        if (pms.initVel != dft.initVel) jv["initVel"] = ualib::UaJson::makeVec3(pms.initVel);
    }
    break;
    default:

        break;
    }
}

void irr::scene::KeyPoseCmd::fromJson(const Json::Value& jv) {
    auto typeStr = jv["Type"].asString();
    type = stringToType(typeStr);

    switch (type) {
    case KeyPoseCmdType::ectDistance:
    {
        KpcDistance pm;
        if (!jv["TypeFlag"].isNull()) pm.TypeFlag = jv["TypeFlag"].asInt();
        if (!jv["disType"].isNull()) pm.disType = jv["disType"].asInt();
        if (!jv["distance"].isNull()) pm.distance = jv["distance"].asFloat();
        if (!jv["countMode"].isNull()) pm.countMode = jv["countMode"].asInt();
        if (!jv["contition"].isNull()) pm.contition = jv["contition"].asInt();
        // Load "cmdsOnBelow" array
        if (jv.isMember("onCmds") && jv["onCmds"].isArray()) {
            for (const auto& jvCmd : jv["onCmds"]) {
                KeyPoseCmd cmd;
                cmd.fromJson(jvCmd);
                pm.onCmds.push_back(cmd);
            }
        }
        cmdPm = pm;
    }
    break;
    case KeyPoseCmdType::ectFw:
    {
        KpcFw pm;
        if (!jv["fwType"].isNull()) pm.fwType = jv["fwType"].asInt();
        if (!jv["TypeFlag"].isNull()) pm.TypeFlag = jv["TypeFlag"].asUInt();
        if (!jv["name"].isNull()) pm.name = jv["name"].asString();
        if (!jv["rbPos"].isNull()) pm.rbPos = jv["rbPos"].asInt();
        if (!jv["vMul"].isNull()) pm.vMul = jv["vMul"].asFloat();
        if (!jv["vAdd"].isNull()) pm.vAdd = jv["vAdd"].asFloat();
        if (!jv["fpm"].isNull()) ualib::UaJson::getVec3(jv["fpm"], pm.fpm);
        if (!jv["color"].isNull()) ualib::UaJson::getVec4(jv["color"], pm.color);
        cmdPm = pm;
    }
    break;
    case KeyPoseCmdType::ectTextFw:
    {
        KpcTextFw pm;
        loadCommonAddPhyObjMembers(jv, pm);  // Load common members

        if (!jv["text"].isNull()) pm.text = ualib::Utf8toWcs(jv["text"].asString());
        if (!jv["name"].isNull()) pm.name = jv["name"].asString();
        if (!jv["color1"].isNull()) ualib::UaJson::getVec4(jv["color1"], pm.color1);
        if (!jv["color2"].isNull()) ualib::UaJson::getVec4(jv["color2"], pm.color2);
        if (!jv["velOfs"].isNull()) ualib::UaJson::getVec3(jv["velOfs"], pm.velOfs);
        // Load TextFw specific members
        if (!jv["allInOne"].isNull()) pm.allInOne = jv["allInOne"].asBool();
        if (!jv["hitCvt"].isNull()) pm.hitCvt = jv["hitCvt"].asBool();
        if (!jv["hitGroundCvt"].isNull()) pm.hitGroundCvt = jv["hitGroundCvt"].asBool();
        if (!jv["hitVelChgLimit"].isNull()) pm.hitVelChgLimit = jv["hitVelChgLimit"].asFloat();
        if (!jv["fwSpecular"].isNull()) pm.fwSpecular = jv["fwSpecular"].asInt();
        if (!jv["baseMass"].isNull()) pm.baseMass = jv["baseMass"].asFloat();
        if (!jv["connectMode"].isNull()) pm.connectMode = jv["connectMode"].asInt();

        cmdPm = pm;
    }
    break;
    case KeyPoseCmdType::ectConnectRb:
    {
        KpcConnectRb pm;
        if (!jv["TypeFlag"].isNull()) pm.TypeFlag = jv["TypeFlag"].asUInt();
        if (!jv["sbA"].isNull()) pm.sbA = jv["sbA"].asInt();
        if (!jv["nodeNameA"].isNull()) pm.nodeNameA = jv["nodeNameA"].asString();
        if (!jv["lockT"].isNull()) pm.lockT = jv["lockT"].asInt();
        if (!jv["lockR"].isNull()) pm.lockR = jv["lockR"].asInt();
        if (!jv["localPos"].isNull()) pm.localPos = jv["localPos"].asInt();
        if (!jv["translate"].isNull()) ualib::UaJson::getVec3(jv["translate"], pm.translate);
        if (!jv["rotate"].isNull()) ualib::UaJson::getVec3(jv["rotate"], pm.rotate);
        if (!jv["t2B"].isNull()) ualib::UaJson::getVec3(jv["t2B"], pm.t2B);
        if (!jv["r2B"].isNull()) ualib::UaJson::getVec3(jv["r2B"], pm.r2B);
        if (!jv["limMinT"].isNull()) ualib::UaJson::getVec3(jv["limMinT"], pm.limMinT);
        if (!jv["limMaxT"].isNull()) ualib::UaJson::getVec3(jv["limMaxT"], pm.limMaxT);
        if (!jv["limMinR"].isNull()) ualib::UaJson::getVec3(jv["limMinR"], pm.limMinR);
        if (!jv["limMaxR"].isNull()) ualib::UaJson::getVec3(jv["limMaxR"], pm.limMaxR);
        if (!jv["springT"].isNull()) pm.springT = jv["springT"].asFloat();
        if (!jv["springR"].isNull()) pm.springR = jv["springR"].asFloat();
        if (!jv["dampingT"].isNull()) pm.dampingT = jv["dampingT"].asFloat();
        if (!jv["dampingR"].isNull()) pm.dampingR = jv["dampingR"].asFloat();
        cmdPm = pm;
    }
    break;
    case KeyPoseCmdType::ectAddPhyObj:
    {
        KpcAddPhyObj pm;
        loadCommonAddPhyObjMembers(jv, pm);  // Load common members
        cmdPm = pm;
    }
    break;
    case KeyPoseCmdType::ectCubeGrid:
    {
        KpcCubeGrid pm;
        if (!jv["TypeFlag"].isNull()) pm.TypeFlag = jv["TypeFlag"].asUInt();
        if (!jv["pos"].isNull()) ualib::UaJson::getVec3(jv["pos"], pm.pos);
        if (!jv["rtt"].isNull()) ualib::UaJson::getVec3(jv["rtt"], pm.rtt);
        if (!jv["vel"].isNull()) ualib::UaJson::getVec3(jv["vel"], pm.vel);
        if (!jv["grid"].isNull()) {
            Json::Value jvGrid = jv["grid"];
            pm.grid.x = jvGrid[0].asInt();
            pm.grid.y = jvGrid[1].asInt();
            pm.grid.z = jvGrid[2].asInt();
        }
        if (!jv["brickSize"].isNull()) ualib::UaJson::getVec3(jv["brickSize"], pm.brickSize);
        if (!jv["brickSpace"].isNull()) ualib::UaJson::getVec3(jv["brickSpace"], pm.brickSpace);
        if (!jv["reset"].isNull()) pm.reset = jv["reset"].asInt();
        if (!jv["density"].isNull()) pm.density = jv["density"].asFloat();
        if (!jv["restitution"].isNull()) pm.restitution = jv["restitution"].asFloat();
        if (!jv["friction"].isNull()) pm.friction = jv["friction"].asFloat();
        if (!jv["centerOrigin"].isNull()) pm.centerOrigin = jv["centerOrigin"].asInt();
        if (!jv["connect"].isNull()) pm.connect = jv["connect"].asInt();
        if (!jv["imgPath"].isNull()) {
            if (jv["imgPath"].isArray())
            {
				for (int i = 0; i < jv["imgPath"].size(); i++)
					pm.imgPaths.push_back(jv["imgPath"][i].asString());
            }
            else
            pm.imgPaths.push_back(jv["imgPath"].asString());
        }
		if (!jv["voxPath"].isNull()) pm.voxPath = jv["voxPath"].asString();
        if (!jv["color"].isNull()) ualib::UaJson::getVec4(jv["color"], pm.color);
        if (!jv["srcNd"].isNull()) json_to_MMDNodeInfo(jv["srcNd"], pm.srcNd);
        if (!jv["tgtNd"].isNull()) json_to_MMDNodeInfo(jv["tgtNd"], pm.tgtNd);
        cmdPm = pm;
    }
    break;
    case KeyPoseCmdType::ectVoxGrid:
    {
        KpcVoxGrid pm;
        if (!jv["TypeFlag"].isNull()) pm.TypeFlag = jv["TypeFlag"].asUInt();
        if (!jv["voxPath"].isNull()) pm.voxPath = jv["voxPath"].asString();

        if (!jv["pos"].isNull()) ualib::UaJson::getVec3(jv["pos"], pm.pos);
        if (!jv["rtt"].isNull()) ualib::UaJson::getVec3(jv["rtt"], pm.rtt);
        if (!jv["vel"].isNull()) ualib::UaJson::getVec3(jv["vel"], pm.vel);

        if (!jv["brickSize"].isNull()) ualib::UaJson::getVec3(jv["brickSize"], pm.brickSize);
        if (!jv["brickSpace"].isNull()) ualib::UaJson::getVec3(jv["brickSpace"], pm.brickSpace);
        if (!jv["reset"].isNull()) pm.reset = jv["reset"].asInt();
        if (!jv["density"].isNull()) pm.density = jv["density"].asFloat();
        if (!jv["restitution"].isNull()) pm.restitution = jv["restitution"].asFloat();
        if (!jv["friction"].isNull()) pm.friction = jv["friction"].asFloat();
        if (!jv["centerOrigin"].isNull()) pm.centerOrigin = jv["centerOrigin"].asInt();
        if (!jv["connect"].isNull()) pm.connect = jv["connect"].asInt();


        if (!jv["color"].isNull()) ualib::UaJson::getVec4(jv["color"], pm.color);
        if (!jv["srcNd"].isNull()) json_to_MMDNodeInfo(jv["srcNd"], pm.srcNd);
        if (!jv["tgtNd"].isNull()) json_to_MMDNodeInfo(jv["tgtNd"], pm.tgtNd);
        cmdPm = pm;
    }
    break;
    case KeyPoseCmdType::ectMotion:
	{
		KpcMotion pm;
        if (!jv["filePath"].isNull()) pm.filePath = jv["filePath"].asString();
        if (!jv["fileType"].isNull()) pm.fileType = jv["fileType"].asString();
		if (!jv["speedMul"].isNull()) pm.speedMul = jv["speedMul"].asFloat();
		if (!jv["startTime"].isNull()) pm.startTime = jv["startTime"].asFloat();
        if (!jv["swapLR"].isNull()) pm.swapLR = jv["swapLR"].asInt();
        if (!jv["iv"].isNull()) ualib::UaJson::getIVec4(jv["iv"], pm.iv);
        if (!jv["fv"].isNull()) ualib::UaJson::getVec4(jv["fv"], pm.fv);
		cmdPm = pm;
	}
    break;
    case KeyPoseCmdType::ectCameraKey:
    {
        KpcCameraKey pm;
        pm.vc.init();
        if (!jv["anim"].isNull()) pm.anim = jv["anim"].asInt();
        if (!jv["local"].isNull()) pm.local = jv["local"].asInt();
        ualib::UaJson::getVec3(jv["vc.tgt"],pm.vc.m_interest);
        ualib::UaJson::getVec3(jv["vc.rtt"], pm.vc.m_rotate);
        if (!jv["vc.dis"].isNull()) pm.vc.m_distance = jv["vc.dis"].asFloat();
        if (!jv["vc.fov"].isNull()) pm.vc.m_fov = jv["vc.fov"].asFloat();


        cmdPm = pm;

    }
    break;
    case KeyPoseCmdType::ectCustom:
    {
        KpcCustom pm;
        if (!jv["customCmdName"].isNull()) {
            pm.customCmdName = jv["customCmdName"].asString();
			pm.cmdType = KeyPoseCmd::StringToCustomCmd(pm.customCmdName);
        }
        if (!jv["customCmdValue"].isNull()) pm.customCmdStringValue = jv["customCmdValue"].asString();
        if (!jv["iv"].isNull()) ualib::UaJson::getIVec4(jv["iv"], pm.iv);
        if (!jv["fv"].isNull()) ualib::UaJson::getVec4(jv["fv"], pm.fv);
        cmdPm = pm;
    }
    break;
    case KeyPoseCmdType::ectModel:
    {
        KpcModel pm;
        if (!jv["TypeFlag"].isNull()) pm.TypeFlag = jv["TypeFlag"].asUInt();
        if (!jv["filePath"].isNull()) pm.filePath = jv["filePath"].asString();
        if (!jv["pos"].isNull()) ualib::UaJson::getVec3(jv["pos"], pm.pos);
        if (!jv["rtt"].isNull()) ualib::UaJson::getVec3(jv["rtt"], pm.rtt);
        if (!jv["scale"].isNull()) pm.scale = jv["scale"].asFloat();
        if (!jv["massMul"].isNull()) pm.massMul = jv["massMul"].asFloat();
        if (!jv["frictionMul"].isNull()) pm.frictionMul = jv["frictionMul"].asFloat();
        if (!jv["isCharacter"].isNull()) pm.isCharacter = jv["isCharacter"].asBool();
        if (!jv["allRbActive"].isNull()) pm.allRbActive = jv["allRbActive"].asBool();
        if (!jv["initVel"].isNull()) ualib::UaJson::getVec3(jv["initVel"], pm.initVel);
        cmdPm = pm;
    }
    break;
    default:
        //assert(0);
        break;
    }
}


void irr::scene::MmdPhyAnimator::saveToFile(const std::string& filepath)
{
    using namespace ualib;
    UaJsonSetting jss;
    jss.SetFile(filepath, false);
    auto& root = jss.refRootValue();
    Json::Value jvKeyData;
    int idx = -1;

    KeyPoseData dfp;
    Json::Value jvNodes(Json::arrayValue);
    for (const auto&   rigNode : nodeMap) {
        auto& node = rigNode.node;
        Json::Value jvNode;
        jvNode["Node"] =  node->GetName();
        if (rigNode.alias.size()) jvNode["alias"] = rigNode.alias;
        if (rigNode.sbT != 0) jvNode["sbT"] = rigNode.sbT;
		if (rigNode.disabled != 0) jvNode["disabled"] = rigNode.disabled;
        Json::Value jvKeys(Json::arrayValue);
        for (const auto& key : rigNode.keys) {
            Json::Value jvKey;

            jvKey["frame"] = key.frame;
            jvKey["cumFrame"] = key.frameIdx;
			if (key.disabled) jvKey["disabled"] = 1;
            const auto& pose = key.pose;
            if (pose.saved) {
                jvKey["ndTmir"] = pose.ndTmir;
                if (pose.dndB >= 0)
                {
					jvKey["dndB"] = pose.dndB;
                }
                else if (pose.getB()) {
                    jvKey["ndB"] = ualib::WcstoUtf8(pose.ndBmir ? L"█" : L"") + pose.getB()->GetName();
                }                
                if (pose.sbB != 1) jvKey["sbB"] = pose.sbB;
                if (pose.baseMode != 0) jvKey["baseMode"] = pose.baseMode;
                if (pose.setRd ) jvKey["setRd"] = pose.setRd;
                if (pose.rdRatio < 0.999f) jvKey["rdRatio"] = pose.rdRatio;
                jvKey["pos"] = UaJson::makeVec3(pose.pos);
                jvKey["rtt"] = UaJson::makeQuat(pose.rtt);
                if (pose.fmul != 1.f) jvKey["fmul"] = pose.fmul;
                if (pose.amul != 1.f) jvKey["amul"] = pose.amul;
                if (pose.fsc != 1.f) jvKey["fsc"] = pose.fsc;
                //if (pose.time0 != 1.f) jvKey["time0"] = pose.time0;
                //if (pose.time1 != 1.f) jvKey["time1"] = pose.time1;
                if (pose.rttMode != dfp.rttMode) jvKey["rttMode"] = pose.rttMode;
                if (pose.amul != dfp.amul) jvKey["amul"] = dfp.amul;
                if (pose.front!= dfp.front) jvKey["front"] = UaJson::makeVec3(pose.front);

                // Save commands if they exist
                if (!pose.cmds.empty()) {
                    Json::Value jvCmds(Json::arrayValue);
                    for (const auto& cmd : pose.cmds) {
                        Json::Value jvCmd;
                        cmd.toJson(jvCmd);
                        jvCmds.append(jvCmd);
                    }
                    jvKey["cmds"] = jvCmds;
                }

                jvKeys.append(jvKey);
            }
        }
        jvNode["keys"] = jvKeys;
        jvNodes.append(jvNode);
    }
    root["nodes"] = jvNodes;
	Json::Value jvUI;
    jvUI["framePixelWidthTarget"] = rigSequencer->framePixelWidthTarget;
    root["ui"] = jvUI;

    jss.SaveFile();
}

bool irr::scene::MmdPhyAnimator::loadFromFile(const MpaLoadJsonParam& lf, bool clear)
{
    curFilePath = lf.filepath;
	mpaName = ualib::GetFileName(curFilePath);
    playStop();	setCurFrame(0);
    if (clear)     CCubeGridSceneNode::clearWalls();

    using namespace ualib;
    UaJsonSetting jss;
    jss.SetFile(curFilePath, true);
    auto& root = jss.refRootValue();

    nodeMap.clear();

    if (!root.isMember("nodes") || !root["nodes"].isArray()) {
        return false;
    }


	auto jv = root["ui"];
    if (!jv.isNull())
    {
        if (jv.isMember("framePixelWidthTarget")) {
            framePixelWidthTarget = jv["framePixelWidthTarget"].asFloat();

        }
    }
	auto sweapLRName([&](const std::wstring& name) {
		if (name.size() < 1) return name;
        bool isL = name[0] == L'左';
		if (isL || name[0] == L'右') {
			return (isL ? L"右" : L"左") + name.substr(1);
		}
		return name;
		});
    for (const auto& jvNode : root["nodes"]) {
        if (!jvNode.isMember("Node") || !jvNode.isMember("keys")) {
            continue;
        }
        auto jv = jvNode["sbT"];
        int sbT = jv.isNull() ? 0 : jvNode["sbT"].asInt();
        if (sbT >= sabas.size())
        {
            Ctx->getLib()->CurStage()->sendKeyEvent(KEY_KEY_M, 1, 0, 0, 0);
			sabas.push_back(mmd->sabas.back());
        }
        // Find target node
        auto name = jvNode["Node"].asString();
        assert(name.size());
        auto nameT = ualib::Utf8toWcs(name);
        if (lf.swapLR) {
            nameT = sweapLRName(nameT);
			name = ualib::WcstoUtf8(nameT);
        }
        auto* node = sabas[sbT]->findNode(nameT);
        bool extNode = nameT.size()>=5 && nameT[0] == L'$' && nameT[1] == L'x' && nameT[2] == L'_' ;
        if (!node) {
            if (!Pm.isEditor) {
                //throw "sub MPA should not ext node";
                DP(("SUB MPA EXTNODE!!!!!!!!!!!!!!!!!!!!!!!!!!!"));
                //continue;
            }
            if (!extNode)
            continue;
            else {
                std::string parentName;
                size_t pos = name.find('_', 4); // find the second underscore
                if (pos != std::wstring::npos) {
                    parentName = name.substr(pos + 1);
                }
                auto parentNode = sabas[sbT]->Pmx->GetNodeManager()->FindNode(parentName);
                node=addExtNode(parentNode,name);
            }
        }

        createNode(node);
        jv = jvNode["alias"]; if (jv.isString()) {
            nodeIt->alias = jvNode["alias"].asString();
            if (nodeIt->isCameraNode = (nodeIt->alias == "Camera"))
			    curCameraRigNode = nodeIt->node;
        }
        if (jvNode.isMember("disabled")) {
            nodeIt->disabled = jvNode["disabled"].asInt();
        }
        // Load all keys for this node
        for (const auto& jvKey : jvNode["keys"]) {
            RigKey key;

            key.frame = jvKey["frame"].asInt();
            key.frameIdx = jvKey["cumFrame"].asInt();

            //key.pose.ndT = node;
            key.pose.ndTmir = jvKey["ndTmir"].asBool();

            auto nameB = ualib::Utf8toWcs(jvKey["ndB"].asString());
            if (lf.swapLR) {
                nameB = sweapLRName(nameB);
               // name = ualib::WcstoUtf8(nameB);
            }
            if (nameB[0] == L'█') {
                key.pose.ndBmir = true;
                nameB = nameB.substr(1);
            }

            auto jv = jvKey["sbB"];
            if (!jv.isNull()) key.pose.sbB = std::min( (int)sabas.size()-1,jv.isNull() ? 1 : jv.asInt());
            if (key.pose.sbB>=sabas.size())
			{
				Ctx->getLib()->CurStage()->sendKeyEvent(KEY_KEY_M, 1, 0, 0, 0);
                resetSabas();
			}
            jv = jvKey["disabled"];
            key.disabled = jv.asInt()?true:false;
            jv = jvKey["baseMode"];
            key.pose.baseMode = jv.isNull() ? 0 : jv.asInt();
            jv = jvKey["setRd"];
            if (!jv.isNull()) key.pose.setRd = jv.asInt();
            jv = jvKey["rdRatio"];
            if (!jv.isNull()) key.pose.rdRatio = jv.asFloat();

            key.pose.saved = 1;
            key.pose.setB(sabas[key.pose.sbB]->findNode(nameB));
            jv = jvKey["dndB"];
            if (!jv.isNull()) key.pose.dndB =   jv.asInt();
            //if (!key.pose.getB()) continue;
            UaJson::getFloatsDZ(jvKey["pos"], (float*)&key.pose.pos, 3);
            UaJson::getFloatsDZ(jvKey["rtt"], (float*)&key.pose.rtt, 4);

            jv = jvKey["fmul"];
            key.pose.fmul = jv.isNull() ? 1.0f : jv.asFloat();
            jv = jvKey["amul"];
            key.pose.amul = jv.isNull() ? 1.0f : jv.asFloat();
            jv = jvKey["fsc"];
            key.pose.fsc = jv.isNull() ? 1.0f : jv.asFloat();
            //jv = jvKey["time0"];
            //key.pose.time0 = jv.isNull() ? 1.0f : jv.asFloat();
            //jv = jvKey["time1"];
            //key.pose.time1 = jv.isNull() ? 1.0f : jv.asFloat();
            jv = jvKey["rttMode"];
            if (!jv.isNull()) key.pose.rttMode = jv.asInt();
            jv = jvKey["front"];
            if (!jv.isNull()) UaJson::getVec3(jv, key.pose.front);
            jv = jvKey["amul"];
            if (!jv.isNull()) key.pose.amul = jv.asFloat();

            // Load commands if they exist
            if (jvKey.isMember("cmds") && jvKey["cmds"].isArray()) {
                for (const auto& jvCmd : jvKey["cmds"]) {
                    KeyPoseCmd cmd;
                    cmd.fromJson(jvCmd);
                    key.pose.cmds.emplace_back(cmd);
                }
            }

            nodeIt->keys.emplace_back(key);
        }
    }

    updateCumulativeTimes();
    for (auto&  node : nodeMap) {
        node.curKeyIdx = 0;
    }
    createUI();
    return true;
}

void irr::scene::MmdPhyAnimator::extendKeysWithRotation(float angle, const glm::vec3& center) {
    if (nodeMap.empty()) return;

    // Find the last frame
    int lastFrame = 0;
    for (const auto& rigNode : nodeMap) {
        if (!rigNode.keys.empty()) {
            int nodeLastFrame = rigNode.keys.back().frameIdx;
            lastFrame = std::max(lastFrame, nodeLastFrame);
        }
    }

    // Create rotation matrix
    glm::mat4 rotMatrix = glm::rotate(glm::mat4(1.0f), angle, glm::vec3(0, 1, 0));

    // For each node, copy its last key and apply rotation
    for (auto&   rigNode : nodeMap) {
        if (rigNode.keys.empty()) continue;

        // Get the last key
        RigKey lastKey = rigNode.keys.back();

        // Create new key
        RigKey newKey = lastKey;
        newKey.frame = MMD_PHA_FPS; // Default frame interval

        // Get the position relative to center
        glm::vec3 relativePos = lastKey.pose.pos - center;

        // Apply rotation
        glm::vec4 rotatedPos = rotMatrix * glm::vec4(relativePos, 1.0f);
        newKey.pose.pos = glm::vec3(rotatedPos) + center;

        // Combine rotations
        glm::mat4 rotMat = glm::mat4_cast(lastKey.pose.rtt) * rotMatrix;
        newKey.pose.rtt = glm::quat_cast(rotMat);

        // Add the new key
        rigNode.keys.push_back(newKey);
    }

    // Update cumulative frames
    updateCumulativeTimes();
}
#pragma endregion

#pragma region UI_Helper



void MmdPhyAnimator::editKeyCmd(std::vector<KeyPoseCmd>& cmds, int id)
{

    ImGui::PushID(id);
    //ImGui::Begin("Command Editor", nullptr, ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoTitleBar);


    if (ImGui::Button("C")) {

        copiedCmds = cmds;
    }ImGui::SameLine();
    if (ImGui::Button("V")) {
        if (!copiedCmds.empty()) {
            saveState();
            for (auto& key : rigSequencer->mSelectedKeys) {
                nodeMap[key.first].keys[key.second].pose.cmds = copiedCmds;
            }
        }
    }ImGui::SameLine(0, 10);



    // Command list
    // TODO: use a horizontal list to replace the list, and show item count on the right
    float lineHeight = ImGui::GetTextLineHeight();
    float listBoxHeight = (ImGui::GetTextLineHeight() + 4) * cmds.size() + 10;
    ImGui::SetNextWindowContentSize(ImVec2(0.0f, listBoxHeight));
    ImGui::BeginChild("CommandList", ImVec2(150, listBoxHeight), true);
    for (size_t i = 0; i < cmds.size(); i++) {
        char label[32];
        snprintf(label, sizeof(label), "%zu: %s", i, KeyPoseCmd::typeToString(cmds[i].type));
        if (ImGui::Selectable(label, i == selectedCmdIdx[id])) {
            selectedCmdIdx[id] = i;
        }
    }
    ImGui::EndChild();
    // Add/Remove buttons
    ImGui::SameLine(); if (ImGui::Button("+")) {
        KeyPoseCmd cmd; cmd.type = KeyPoseCmdType::ectCustom; cmd.cmdPm = KeyPoseCmd::KpcCustom{};
        cmds.push_back(cmd);
        selectedCmdIdx[id] = cmds.size() - 1;
    }
    ImGui::SameLine();
    if (selectedCmdIdx[id] >= 0 && selectedCmdIdx[id] < cmds.size()) {
        if (ImGui::Button("-")) {
            cmds.erase(cmds.begin() + selectedCmdIdx[id]);
            selectedCmdIdx[id] = cmds.empty() ? -1 : cmds.size() - 1;
        }
    }


    ImGui::PushItemWidth(180);
    // Show editor for selected command
    if (selectedCmdIdx[id] >= 0 && selectedCmdIdx[id] < cmds.size())
    {
        auto& cmd = cmds[selectedCmdIdx[id]];

        // Command type selector
        const char* currentType = KeyPoseCmd::typeToString(cmd.type);
        if (ImGui::BeginCombo("CmdType", currentType)) {
#define MACRO_X(name) \
    if (ImGui::Selectable(#name, cmd.type == KeyPoseCmdType::ect##name)) {\
            cmd.type = KeyPoseCmdType::ect##name; cmd.cmdPm = KeyPoseCmd::Kpc##name{};}

            KEYPOSE_CMD_TYPES(MACRO_X)
#undef MACRO_X

                ImGui::EndCombo();
        }
        ImGui::Separator();
        // Show command parameters based on type
        switch (cmd.type) {
        case KeyPoseCmdType::ectDistance:
        {
            auto& dist = std::get<KeyPoseCmd::KpcDistance>(cmd.cmdPm);
            ImGui::Checkbox("On", (bool*)&dist.TypeFlag);
            // Distance type selector
            const char* disTypeNames[] = { "No Scale", "Node Scale" };
            ImGui::Combo("Distance Type", &dist.disType, disTypeNames, IM_ARRAYSIZE(disTypeNames));
            // Distance value
            ImGui::InputInt("Count Mode", &dist.countMode);

            // Distance value
            ImGui::DragFloat("Distance", &dist.distance, 0.1f, 0.0f, 1000.0f, "%.1f");

            ImGui::Text("sub cmds");

            if (ImGui::BeginTabBar("DistanceCommandTabs"))
            {
                if (ImGui::BeginTabItem("on < "))
                {
                    editKeyCmd(dist.onCmds, id + 1);
                    ImGui::EndTabItem();
                }

                ImGui::EndTabBar();
            }
        }
        break;
        case KeyPoseCmdType::ectFw:
        {
            bool updateNow = false;
            auto& fw = std::get<KeyPoseCmd::KpcFw>(cmd.cmdPm);

            //TypeFlag can only be 0,1,2,4
            //TODO use combobox
            bool showOneFw = (fw.TypeFlag & 1) != 0;
            bool turnOnNodeFw = (fw.TypeFlag & 2) != 0;
            bool turnOffNodeFw = (fw.TypeFlag & 4) != 0;
            ImGui::Text("Flag ");  ImGui::SameLine();
            if (ImGui::Checkbox("1##FwTF", &showOneFw)) fw.TypeFlag = (showOneFw ? 1 : 0) | (fw.TypeFlag & 6); ImGui::SameLine();
            if (ImGui::Checkbox("+##FwTF", &turnOnNodeFw)) fw.TypeFlag = (turnOnNodeFw ? 2 : 0) | (fw.TypeFlag & 5); ImGui::SameLine();
            if (ImGui::Checkbox("-##FwTF", &turnOffNodeFw)) fw.TypeFlag = (turnOffNodeFw ? 4 : 0) | (fw.TypeFlag & 3);

            if (ImGui::SliderInt("fwType", &fw.fwType, 0, 2) || fwNames[fw.fwType].size() == 0) {
                fwNames[fw.fwType] = Sb0->Eqv->getPtrFwIdStrs(fw.fwType);
            };
            if (ImGui::BeginCombo("fwName", fw.name.c_str(), ImGuiComboFlags_HeightLarge)) {
                for (const auto& name : fwNames[fw.fwType]) {
                    if (ImGui::Selectable(name.c_str(), name == fw.name)) {
                        fw.name = name;
                        updateNow = true;
                    }
                }
                ImGui::EndCombo();
            }
            updateNow |= ImGui::ColorEdit4("color", &fw.color.x, ImGuiColorEditFlags_Float | ImGuiColorEditFlags_InputRGB | ImGuiColorEditFlags_AlphaBar);
            updateNow |= ImGui::Checkbox("rbPos", (bool*)&fw.rbPos);
            updateNow |= ImGui::InputFloat("vMul", &fw.vMul);
            updateNow |= ImGui::InputFloat("vAdd", &fw.vAdd);
            updateNow |= ImGui::InputFloat3("fpm", &fw.fpm.x);
            if (updateNow) nodeIt->curFw = fw;
        }break;

        case KeyPoseCmdType::ectConnectRb:
        {
            auto& pm = std::get<KeyPoseCmd::KpcConnectRb>(cmd.cmdPm);
            ImGui::Text("Flag ");  ImGui::SameLine();

            bool fCon = (pm.TypeFlag & 1) != 0;
            bool fConUntil = (pm.TypeFlag & 2) != 0;
            bool fDisCon = (pm.TypeFlag & 4) != 0;
			bool fBase = (pm.TypeFlag & 8) != 0;
            if (ImGui::Checkbox("C+", &fCon)) pm.TypeFlag = (fCon ? 1 : 0) | (pm.TypeFlag & ~1); ImGui::SameLine();
            //if (ImGui::Checkbox("Cu", &fConUntil)) pm.TypeFlag = (fConUntil ? 2 : 0) | (pm.TypeFlag & ~2); ImGui::SameLine();
            if (ImGui::Checkbox("C-", &fDisCon)) pm.TypeFlag = (fDisCon ? 4 : 0) | (pm.TypeFlag & ~4); ImGui::SameLine();
            if (ImGui::Checkbox("Bs", &fBase)) pm.TypeFlag = (fBase ? 8 : 0) | (pm.TypeFlag & ~8);
            // Model selection for node A
            std::string& name = pm.nodeNameA;
            int& sbIdx = pm.sbA;
            InputNode(sbIdx, name, "Node A");

            ImGui::Checkbox("Local Position", (bool*)&pm.localPos);

            // Transform settings
            ImGui::Text("Transform Settings:");
            ImGui::InputFloat3("T to A", &pm.translate.x);
            ImGui::InputFloat3("R to A", &pm.rotate.x);
            ImGui::InputFloat3("T to B", &pm.t2B.x);
            ImGui::InputFloat3("R to B", &pm.r2B.x);

            ImGui::SetNextItemWidth(100);
            // ImGui::InputInt(" Translation Limits", &pm.lockT);
            static const std::string motionNames[3] = { "FREE", "LIMITED", "LOCKED" };
            if (ImGui::BeginCombo(" Translation Limits", motionNames[pm.lockT].c_str())) {
                for (int i = 0; i < 3; i++) {
                    if (ImGui::Selectable(motionNames[i].c_str(), i == pm.lockT)) {
                        pm.lockT = i;
                    }
                }
                ImGui::EndCombo();
            }
            ImGui::InputFloat3("Min T", &pm.limMinT.x);
            ImGui::InputFloat3("Max T", &pm.limMaxT.x);
            ImGui::SetNextItemWidth(100);
            // ImGui::InputInt(" Rotation Limits (deg) ", &pm.lockR);
            if (ImGui::BeginCombo(" Rotation Limits", motionNames[pm.lockR].c_str())) {
                for (int i = 0; i < 3; i++) {
                    if (ImGui::Selectable(motionNames[i].c_str(), i == pm.lockR)) {
                        pm.lockR = i;
                    }
                }
                ImGui::EndCombo();
            }
            ImGui::InputFloat3("Min R", &pm.limMinR.x);
            ImGui::InputFloat3("Max R", &pm.limMaxR.x);

            // Spring and Damping
            ImGui::Text("Stiffness and Damping:");
            ImGui::SetNextItemWidth(90); ImGui::InputFloat("T.Spr", &pm.springT, 0, 0, "%.f"); ImGui::SameLine(0, 35);
            ImGui::SetNextItemWidth(80); ImGui::InputFloat("T.Dmp", &pm.dampingT, 0, 0, "%.f");
            ImGui::SetNextItemWidth(90); ImGui::InputFloat("R.Spr", &pm.springR, 0, 0, "%.f"); ImGui::SameLine(0, 35);
            ImGui::SetNextItemWidth(80); ImGui::InputFloat("R.Dmp", &pm.dampingR, 0, 0, "%.f");
        }
        break;
        case KeyPoseCmdType::ectAddPhyObj:
        {
            auto& apo = std::get<KeyPoseCmd::KpcAddPhyObj>(cmd.cmdPm);
            bool updateNow = imguiCommonAddPhyObjProperties(apo);
            if (updateNow) {
                curRigNode->curAPO = apo;
                onPlayKeyChanged(*curRigNode, curRigNode->keys[curRigNode->editKeyIdx].pose.cmds, true);
            }
        }
        break;
        case KeyPoseCmdType::ectTextFw:
        {
            auto& tfw = std::get<KeyPoseCmd::KpcTextFw>(cmd.cmdPm);
            bool updateNow = false;

            if (ImGui::BeginTabBar("TextFwTabs"))
            {
                if (ImGui::BeginTabItem("TextFw Properties"))
                {
                    ImGui::BeginGroup();

                    static char sz[1024];
                    snprintf(sz, sizeof(sz), "%s", ualib::WcstoUtf8(tfw.text).c_str());
                    if (ImGui::InputTextMultiline("Text", sz, sizeof(sz), ImVec2(200, 100))) {
                        tfw.text = ualib::Utf8toWcs(sz);
                        updateNow = true;
                    }

                    updateNow |= ImGui::ColorEdit4("Color1", &tfw.color1.x, ImGuiColorEditFlags_Float | ImGuiColorEditFlags_InputRGB | ImGuiColorEditFlags_AlphaBar);
                    updateNow |= ImGui::ColorEdit4("Color2", &tfw.color2.x, ImGuiColorEditFlags_Float | ImGuiColorEditFlags_InputRGB | ImGuiColorEditFlags_AlphaBar);
                    updateNow |= ImGui::DragFloat3("velOfs", &tfw.velOfs.x, 0.01f, -1, 1, "%.01f");
                    updateNow |= ImGui::Checkbox("All in One", &tfw.allInOne);
                    updateNow |= ImGui::Checkbox("Hit Convert", &tfw.hitCvt);
                    updateNow |= ImGui::Checkbox("Hit Ground Convert", &tfw.hitGroundCvt);
                    ImGui::SetNextItemWidth(100);
                    updateNow |= ImGui::InputFloat("Hit VelChgLimit", &tfw.hitVelChgLimit, 0, 0, "%.1f");
                    ImGui::PushItemWidth(150);
                    updateNow |= ImGui::DragInt("FW Specular", &tfw.fwSpecular, 1, 0, 1, "%d");
                    updateNow |= ImGui::DragFloat("Base Mass", &tfw.baseMass, 0.1f,0.1f, 1000.0f, "%.1f");

                    const char* connectModeItems[] = { "None", "Each Only","Each Full", "Base Rb"};
                    updateNow |= ImGui::Combo("Connect Mode", &tfw.connectMode, connectModeItems, IM_ARRAYSIZE(connectModeItems));

                    ImGui::Separator();
					updateNow |= ImGui::DragInt("durMs", &tfw.tp.charDurMs, 100, 1000, 10000);
                    ImGui::PopItemWidth();
                    ImGui::EndGroup();

                    ImGui::SameLine();
                    ImGui::BeginGroup();
                    ImGui::Text("T.Fw Style");
                    if (ImGui::BeginListBox("##fwName", ImVec2(150, 300)))
                    {
                        for (const auto& name : Sb0->Eqv->getPtrFwIdStrs(0))
                        {
                            if (ImGui::Selectable(name.c_str(), name == tfw.name))
                            {
                                tfw.name = name;
                                updateNow = true;
                            }
                        }
                        ImGui::EndListBox();
                    }
                    ImGui::EndGroup();
                    ImGui::EndTabItem();
                }
                if (ImGui::BeginTabItem("PO Properties"))
                {

					updateNow |= imguiCommonAddPhyObjProperties(tfw);//why tfw is not modified after this function?
                    ImGui::EndTabItem();
                }
                ImGui::EndTabBar();
            }

            if (updateNow) {
                curRigNode->curTFW = tfw;
                onPlayKeyChanged(*curRigNode, curRigNode->keys[curRigNode->editKeyIdx].pose.cmds);
            }
        }
        break;
        case KeyPoseCmdType::ectCubeGrid:
        {
            auto& cg = std::get<KeyPoseCmd::KpcCubeGrid>(cmd.cmdPm);
            bool updateNow = false;

            // Basic properties
            ImGui::InputInt("Flag", (int*)&cg.TypeFlag);
            ImGui::SameLine(); ImGui::TextDisabled("(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::BeginTooltip();
                ImGui::Text("1 = Add grid\n2 = Turn on\n4 = Turn off");
                ImGui::EndTooltip();
            }

            // Grid dimensions and properties
            ImGui::Text("Grid Settings");
            updateNow |= ImGui::InputInt3("Grid Size", &cg.grid.x);
            updateNow |= ImGui::InputFloat3("Brick Size", &cg.brickSize.x);
            updateNow |= ImGui::InputFloat3("Brick Space", &cg.brickSpace.x);

            // Physical properties
            ImGui::Separator();
            ImGui::Text("Physics Properties");
            updateNow |= ImGui::InputInt("Reset", &cg.reset);
            updateNow |= ImGui::InputFloat("Density", &cg.density);
            updateNow |= ImGui::InputFloat("Restitution", &cg.restitution);
            updateNow |= ImGui::InputFloat("Friction", &cg.friction);

            // Position and orientation
            ImGui::Separator();
            ImGui::Text("Transform");
            updateNow |= ImGui::InputFloat3("Position", &cg.pos.x);
            updateNow |= ImGui::InputFloat3("Rotation", &cg.rtt.x);
            updateNow |= ImGui::InputFloat3("Velocity", &cg.vel.x);

            // Additional settings
            ImGui::Separator();
            ImGui::Text("Additional Settings");
            updateNow |= ImGui::Checkbox("Center ", (bool*)&cg.centerOrigin);
            updateNow |= ImGui::InputInt("Connect ", &cg.connect);
            updateNow |= ImGui::ColorEdit4("Color", &cg.color.x);

            // Node settings
            ImGui::Separator();
            ImGui::Text("Source Settings");
            updateNow |= InputMMDNodeInfo(cg.srcNd, "S");

            ImGui::Text("Target Settings");
            updateNow |= InputMMDNodeInfo(cg.tgtNd, "T");

            updateNow |= vks::ImGui_PickFilePathControl("##VoxPath", cg.voxPath, true, false);

            // Image paths
            std::vector<std::string>& imgPaths = cg.imgPaths;

            ImGui::Text("Image Paths");
            if (ImGui::BeginListBox("##ImagePaths", ImVec2(-FLT_MIN, 3 * ImGui::GetTextLineHeightWithSpacing())))
            {
                for (size_t i = 0; i < imgPaths.size(); ++i)
                {
                    std::string label = "##Path " + std::to_string(i + 1);
                    updateNow |= vks::ImGui_PickImagePathControl(label.c_str(), imgPaths[i], 200.0f);
                }
                ImGui::EndListBox();
            }

            // Add a new path
            if (ImGui::Button("+Path"))
            {
                imgPaths.push_back("");
                updateNow = true;
            }

            ImGui::SameLine();

            // Remove the last path
            if (!imgPaths.empty() && ImGui::Button("-Last"))
            {
                curRigNode->curTexId = 0;
                imgPaths.pop_back();
                updateNow = true;
            }

            if (updateNow)
            {
                onPlayKeyChanged(*curRigNode, curRigNode->keys[curRigNode->editKeyIdx].pose.cmds);
            }

        }
        break;
        case KeyPoseCmdType::ectVoxGrid:
        {
            auto& pm = std::get<KeyPoseCmd::KpcVoxGrid>(cmd.cmdPm);
            bool updateNow = false;

            // Basic properties
            ImGui::InputInt("Flag", (int*)&pm.TypeFlag);
            ImGui::SameLine(); ImGui::TextDisabled("(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::BeginTooltip();
                ImGui::Text("1 = Add grid\n2 = Turn on\n4 = Turn off");
                ImGui::EndTooltip();
            }


            updateNow |= ImGui::InputFloat3("Brick Size", &pm.brickSize.x);
            updateNow |= ImGui::InputFloat3("Brick Space", &pm.brickSpace.x);

            // Physical properties
            ImGui::Separator();
            ImGui::Text("Physics Properties");
            updateNow |= ImGui::InputInt("Reset", &pm.reset);
            updateNow |= ImGui::InputFloat("Density", &pm.density);
            updateNow |= ImGui::InputFloat("Restitution", &pm.restitution);
            updateNow |= ImGui::InputFloat("Friction", &pm.friction);

            // Position and orientation
            ImGui::Separator();
            ImGui::Text("Transform");
            updateNow |= ImGui::InputFloat3("Position", &pm.pos.x);
            updateNow |= ImGui::InputFloat3("Rotation", &pm.rtt.x);
            updateNow |= ImGui::InputFloat3("Velocity", &pm.vel.x);

            // Additional settings
            ImGui::Separator();
            ImGui::Text("Additional Settings");
            updateNow |= ImGui::Checkbox("Center ", (bool*)&pm.centerOrigin);
            updateNow |= ImGui::InputInt("Connect ", &pm.connect);
            updateNow |= ImGui::ColorEdit4("Color", &pm.color.x);

            // Node settings
            ImGui::Separator();
            ImGui::Text("Source Settings");
            updateNow |= InputMMDNodeInfo(pm.srcNd, "S");

            ImGui::Text("Target Settings");
            updateNow |= InputMMDNodeInfo(pm.tgtNd, "T");

            updateNow |= vks::ImGui_PickFilePathControl("##VoxPath", pm.voxPath, true, false);


            ImGui::SameLine();



            if (updateNow)
            {
                onPlayKeyChanged(*curRigNode, curRigNode->keys[curRigNode->editKeyIdx].pose.cmds);
            }

        }
        break;
        case KeyPoseCmdType::ectMotion:
        {
            bool updateNow = false;
            auto& pm = std::get<KeyPoseCmd::KpcMotion>(cmd.cmdPm);

            updateNow |= vks::ImGui_PickFilePathControl("##Path", pm.filePath, true, false);
            //ImGui::InputText("Type##loadfile", &pm.fileType);
            updateNow |= ImGui::InputFloat("Play Speed", &pm.speedMul, 0.1, 1, "%.2f");
            updateNow |= ImGui::InputFloat("Start Time", &pm.startTime, 1, 10, "%.g");
            updateNow |= ImGui::InputInt("Swap LR", &pm.swapLR);
            if (ImGui::IsItemHovered()) {
                ImGui::BeginTooltip();
                ImGui::Text("-1: from curTime");
                ImGui::EndTooltip();
            }

            ImGui::InputInt4("IV", &pm.iv.x);
            ImGui::InputFloat4("FV", &pm.fv.x);

            if (updateNow) {
                onPlayKeyChanged(*curRigNode, curRigNode->keys[curRigNode->editKeyIdx].pose.cmds);
            }
        }
        break;
        case KeyPoseCmdType::ectCameraKey:
        {
            bool updateNow = false;
            auto& kpm = std::get<KeyPoseCmd::KpcCameraKey>(cmd.cmdPm);
            static auto backPm = kpm, * pLast = &kpm;
            if (ImGui::IsKeyPressed(ImGuiKey_F))
            {
                camPreview = !camPreview;
                if (camPreview) {
                    backPm = kpm;
                    pLast = &kpm;
                }
                else {
                    Ctx->setViewCamera(Ctx->gd.CamRtt);
                    Ctx->getCamTCAnimator()->tld.rtt.z = 0;
                }
            }
            if (camPreview && pLast != &kpm) {
                backPm = kpm;
                pLast = &kpm;
            }
            auto& pm = kpm;
            ImGui::Checkbox("Preview", (bool*)&camPreview);

            ImGui::SameLine();
            if (ImGui::Button("Reset")) {
                pm = backPm;
                updateNow = true;
            }ImGui::SameLine();
            if (ImGui::Button("ToRttCam")) {
                irrCamToRttCam(curMmdNode->model->saba);
            }
            if (camPreview && !vks::IsDraggingAnyImGuiItem()) {
                // Draw fullscreen overlay to indicate camera control mode
                ImGui::GetForegroundDrawList()->AddRect(
                    ImVec2(0, 0),
                    ImGui::GetIO().DisplaySize,
                    IM_COL32(255, 0, 0, 128), 0, 0, 32 // Semi-transparent green
                );
                if (ImGui::IsMouseDragging(0)) {
                    auto d = ImGui::GetMouseDragDelta(0);				ImGui::ResetMouseDragDelta(0);
                    if (abs(d.x) > 0.01f) pm.vc.m_rotate.y -= d.x * 0.01f, updateNow = true;
                    if (abs(d.y) > 0.01f) pm.vc.m_rotate.x -= d.y * 0.01f, updateNow = true;
                }
                if (ImGui::IsMouseDragging(1)) {
                    auto d = ImGui::GetMouseDragDelta(1);				ImGui::ResetMouseDragDelta(1);
                    if (abs(d.y) > 0.01f) pm.vc.m_distance -= d.y * 0.01f, updateNow = true;
                }
                if (ImGui::IsMouseDragging(2)) {
                    auto d = ImGui::GetMouseDragDelta(2);				ImGui::ResetMouseDragDelta(2);
                    if (abs(d.x) > 0.01f) pm.vc.m_interest.x += d.x * 0.01f, updateNow = true;
                    if (abs(d.y) > 0.01f) pm.vc.m_interest.y -= d.y * 0.01f, updateNow = true;
                }
            }
            ImGui::InputInt("Anim##cam", &pm.anim);
            ImGui::InputInt("Local##cam", &pm.local);
            updateNow |= ImGui::DragFloat3("##m_interest", &pm.vc.m_interest.x, 0.1f); ImGui::SameLine();
            if (ImGui::Button("T=0##m_interest", ImVec2(0, 20))) pm.vc.m_interest = vec3(0), updateNow = true;
            updateNow |= ImGui::DragFloat3("##m_rotate", &pm.vc.m_rotate.x, 0.01f); ImGui::SameLine();
            if (ImGui::Button("R=0##m_rotate", ImVec2(0, 20))) pm.vc.m_rotate = vec3(0), updateNow = true;
            ImGui::SetNextItemWidth(100.f);
            float dis = -pm.vc.m_distance;
            if (ImGui::DragFloat("Distance", &dis, 0.1f, 0, 100.f, "%.2f")) pm.vc.m_distance = -dis;

#if 1
            VMDCameraKeyEditor::Draw("Curve", pm.vc, ImVec2(300, 300));
#else
            static bool Cx{}, Cy{}, Cz{}, Cr{}, CA{};
            if (BezierEditor::Draw("Curve", pm.vc.m_ixBezier.m_cp1, pm.vc.m_ixBezier.m_cp2, ImVec2(150, 150)))
            {
                pm.vc.m_iyBezier = pm.vc.m_ixBezier;
                pm.vc.m_izBezier = pm.vc.m_ixBezier;
                updateNow = true;
            } ImGui::SameLine();
            ImGui::BeginGroup();
            ImGui::Text("");
            ImGui::Checkbox("X", &Cx);
            ImGui::Checkbox("Y", &Cy);
            ImGui::Checkbox("Z", &Cz);
            ImGui::Checkbox("R", &Cr);
            ImGui::Checkbox("A", &CA);
            ImGui::EndGroup();
#endif
            if (updateNow) {
                curEditKey()->pose.pos = pm.vc.m_interest;
                curEditKey()->pose.rtt = quat(pm.vc.m_rotate);
                onPlayKeyChanged(*curRigNode, curRigNode->keys[curRigNode->editKeyIdx].pose.cmds);
            }
        }
        break;
        case KeyPoseCmdType::ectCustom:
        {
            bool updateNow = false;
            auto& cg = std::get<KeyPoseCmd::KpcCustom>(cmd.cmdPm);
            updateNow |= ImGui::InputText("Name##1", &cg.customCmdName); ImGui::SameLine();
            static int curIdx = 0;
            if (ImGui::BeginCombo("##NameCmb", CCItems[curIdx].name.c_str(), ImGuiComboFlags_NoPreview))
            {
                for (int i = 0; i < sizeof(CCItems) / sizeof(CCItems[0]); i++)
                {
                    if (ImGui::Selectable(CCItems[i].name.c_str(), i == curIdx))
                    {
                        updateNow = true;
                        curIdx = i;
                        cg.customCmdName = CCItems["D:/MMD/PMX/CB/srs/1.pmx", i].name;
                        cg.cmdType = KeyPoseCmd::StringToCustomCmd(cg.customCmdName);
                    }
                }
                ImGui::EndCombo();
            }
            int ci = -1;
            for (int i = 0; i < sizeof(CCItems) / sizeof(CCItems[0]); i++)
                if (CCItems[i].name == cg.customCmdName) { ci = i; break; }
            if (ci >= 0) {
                if (CCItems[ci].ic == 1) updateNow |= ImGui::InputInt("Int", &cg.iv.x);
                else if (CCItems[ci].ic > 1) updateNow |= ImGui::InputInt4("Integer ", &cg.iv.x);
                if (CCItems[ci].fc == 1) {
                    updateNow |= ImGui::InputFloat("Float", &cg.fv.x, 0.1f, 0.5f);
                    updateNow |= ImGui::SliderFloat("Float##1", &cg.fv.x, 0.f, 1.f, "%g");
                }
                else if (CCItems[ci].fc > 1) updateNow |= ImGui::InputFloat4("Float", &cg.fv.x);
                if (CCItems[ci].sc > 0) updateNow |= ImGui::InputText("String", &cg.customCmdStringValue);
            }
            else {
                updateNow |= ImGui::InputInt4("Integer ", &cg.iv.x);
                updateNow |= ImGui::InputFloat4("Float", &cg.fv.x);
                updateNow |= ImGui::InputText("String", &cg.customCmdStringValue);
            }
            if (cg.cmdType == eccKeyEvent) {
                // Display current hotkey settings
                bool ctrl = cg.iv.y != 0;
                bool shift = cg.iv.z != 0;
                bool alt = cg.iv.w != 0;

                // Create a string representation of the current hotkey
                std::string hotkeyStr;
                if (ctrl) hotkeyStr += "Ctrl+";
                if (shift) hotkeyStr += "Shift+";
                if (alt) hotkeyStr += "Alt+";

                // Add the key name
                if (cg.iv.x > 0) {
                    // Get human-readable key name using our helper function
                    hotkeyStr += IrrGetKeyName((irr::EKEY_CODE)cg.iv.x);
                } else {
                    hotkeyStr += "[Press a key]";
                }

                // Create a button that looks like a text field
                ImGui::Text("Hotkey:");
                ImGui::SameLine();

                // Use a button that will capture the next key press when clicked

                if (ImGui::Button(hotkeyStr.c_str(), ImVec2(150, 0))) {
                    inputingKey = true;
                    ImGui::SetWindowFocus(); // Ensure this window has focus
                }

                // Show tooltip when hovering
                if (ImGui::IsItemHovered()) {
                    ImGui::BeginTooltip();
                    ImGui::Text("Click to set a new hotkey");
                    ImGui::EndTooltip();
                }

                // If we're waiting for a key press, check for key inputs
                if (inputingKey) {
                    ImGui::SameLine();
                    ImGui::TextColored(ImVec4(1.0f, 0.3f, 0.3f, 1.0f), "Press any key...");

                    // Check for key presses using the newer ImGui approach
                    ImGuiIO& io = ImGui::GetIO();

                    // Check for common keys that might be pressed
                    for (int key = ImGuiKey_Tab; key <= ImGuiKey_KeypadEqual; key++) {
                        if (ImGui::IsKeyPressed((ImGuiKey)key, false)) {
                            // Convert ImGui key to Irrlicht key code using our helper function
                            irr::EKEY_CODE irrKey = ImGuiKeyToIrrlichtKey((ImGuiKey)key);

                            if (irrKey != irr::KEY_KEY_CODES_COUNT) {
                                // Store the key and modifiers
                                cg.iv.x = irrKey;
                                cg.iv.y = io.KeyCtrl ? 1 : 0;
                                cg.iv.z = io.KeyShift ? 1 : 0;
                                cg.iv.w = io.KeyAlt ? 1 : 0;
                                inputingKey = false;
                                updateNow = true;
                                break;
                            }
                        }
                    }

                    // Also check for additional keys not covered in the main loop
                    // (e.g., media keys, navigation keys, etc.)
                    for (int key = ImGuiKey_GamepadStart; key < ImGuiKey_COUNT; key++) {
                        if (ImGui::IsKeyPressed((ImGuiKey)key, false)) {
                            irr::EKEY_CODE irrKey = ImGuiKeyToIrrlichtKey((ImGuiKey)key);

                            if (irrKey != irr::KEY_KEY_CODES_COUNT) {
                                // Store the key and modifiers
                                cg.iv.x = irrKey;
                                cg.iv.y = io.KeyCtrl ? 1 : 0;
                                cg.iv.z = io.KeyShift ? 1 : 0;
                                cg.iv.w = io.KeyAlt ? 1 : 0;
                                inputingKey = false;
                                updateNow = true;
                                break;
                            }
                        }
                    }

                    // Also check for mouse clicks to cancel
                    if (ImGui::IsMouseClicked(0) && !ImGui::IsItemHovered()) {
                        inputingKey = false;
                    }
                }

                // Add a clear button
                ImGui::SameLine();
                if (ImGui::Button("Clear")) {
                    cg.iv = glm::ivec4(0);
                    updateNow = true;
                }

                // Add checkboxes for modifiers
                bool modifierChanged = false;
                modifierChanged |= ImGui::Checkbox("Ctrl", &ctrl);
                ImGui::SameLine();
                modifierChanged |= ImGui::Checkbox("Shift", &shift);
                ImGui::SameLine();
                modifierChanged |= ImGui::Checkbox("Alt", &alt);

                if (modifierChanged) {
                    cg.iv.y = ctrl ? 1 : 0;
                    cg.iv.z = shift ? 1 : 0;
                    cg.iv.w = alt ? 1 : 0;
                    updateNow = true;
                }
            }
            if (updateNow) onPlayKeyChanged(*curRigNode, curRigNode->keys[curRigNode->editKeyIdx].pose.cmds);
        };
        break;
        case KeyPoseCmdType::ectModel:
        {
            bool updateNow = false;
            auto& pm = std::get<KeyPoseCmd::KpcModel>(cmd.cmdPm);

            // Basic properties
            ImGui::InputInt("Flag", (int*)&pm.TypeFlag);
            ImGui::SameLine(); ImGui::TextDisabled("(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::BeginTooltip();
                ImGui::Text("1 = Add model\n2 = Turn on\n4 = Turn off");
                ImGui::EndTooltip();
            }

            // Model file path
            updateNow |= vks::ImGui_PickFilePathControl("Model Path", pm.filePath, true, false);

            // Transform properties
            ImGui::Separator();
            ImGui::Text("Transform");
            updateNow |= vks::DragFloat3Ext("Local Pos", &pm.pos.x, 0.1f);
            updateNow |= vks::DragFloat3DegToRad("Local Rtt", &pm.rtt.x);
            updateNow |= ImGui::DragFloat("Scale", &pm.scale, 0.1f, 0.1f, 10.0f);

            // Physics properties
            ImGui::Separator();
            updateNow |= ImGui::DragFloat("Mass Mul", &pm.massMul, 0.1f, 0.1f, 10.0f, "%.2f");
            updateNow |= ImGui::DragFloat("Friction Mul", &pm.frictionMul, 0.1f, 0.1f, 10.0f, "%.2f");

            // Additional settings
            ImGui::Separator();
            updateNow |= ImGui::Checkbox("Is Character", &pm.isCharacter);
            updateNow |= ImGui::Checkbox("All Rb Active", &pm.allRbActive);
            updateNow |= vks::DragFloat3Ext("Initial Velocity", &pm.initVel.x, 0.1f);

            if (updateNow) {
                onPlayKeyChanged(*curRigNode, curRigNode->keys[curRigNode->editKeyIdx].pose.cmds);
            }
        }
        break;
        }
    }
    ImGui::PopItemWidth();
    // ImGui::End();
    ImGui::PopID();
}



bool MmdPhyAnimator::InputNode(int& sbIdx, std::string& name, const std::string& label)
{
    bool changed = false;
    ImGui::SetNextItemWidth(30);
    changed |= ImGui::DragInt(label.c_str(), &sbIdx, 0.1f, 0, sabas.size() - 1, "%d", ImGuiSliderFlags_AlwaysClamp);
    //ImGui::InputInt((std::string("##") + sbIdxLabel).c_str(), &sbIdx	,1,10, ImGuiInputTextFlags_AutoSelectAll);
    char buf[256];
    strncpy(buf, name.c_str(), sizeof(buf));
    ImGui::SetNextItemWidth(80); ImGui::SameLine();
    if (changed |= ImGui::InputText((std::string("##") + label).c_str(), buf, sizeof(buf))) {
        name = buf;
    }ImGui::SameLine();
    auto picked = (changed |= ImGui::Button((std::string("Pick##") + label).c_str(), ImVec2(50, 0)));
    auto pickedNode = arRoot->curPickNode.sbNode;
    if (picked && pickedNode) {
        name = pickedNode->GetName();
        for (size_t i = 0; i < sabas.size(); i++)
            if (pickedNode->model->saba == sabas[i]) { sbIdx = i;  break; }
    }
    return changed;
}

bool MmdPhyAnimator::InputMMDNodeInfo(MMDNodeInfo& info, std::string label)
{
    bool changed = false;

    // Show locate input first
    ImGui::SetNextItemWidth(100);
#if 1
    changed |= ImGui::InputInt((label + " Locate").c_str(), &info.locate, 1, 1);
#else
    const char* items[] = { "World", "Node", "RigidBody", "RB Node" };
    changed |= ImGui::Combo((label + " Locate").c_str(), &info.locate, items, IM_ARRAYSIZE(items));
#endif
    // Only show node inputs if locate > 0
    if (info.locate > 0) {
        changed |= InputNode(info.sbIdx, info.nodeName, (label + " Node").c_str());

        // Update node pointer if needed
        if (changed && info.nodeName.size() > 0) {
            info.node = sabas[info.sbIdx]->Pmx->GetNodeManager()->FindNode(info.nodeName);
        }
    }

    return changed;
}



#include "irrmmd/CMidiPlateSceneNode.h"
extern bool gPhyRttSetAV;
extern bool gPhyUseAddForce;
extern float gPhyRttMul;
void irr::scene::MmdPhyAnimator::uiOtherTabs()
{
    using namespace ImGui;

    if (BeginTabItem("Scene")) {
        BeginGroup();
        PushItemWidth(300);
        if (DragFloat3("srcOfs", &Ctx->gd.baseLightPos.x, 0.1f, -100, 100)) {
            Ctx->gd.lightPosChanged = true;
        };
        Text("PhyRtt");
        Checkbox("SAV", &gPhyRttSetAV);
        SameLine(); DragFloat("Mul", &gPhyRttMul, 0.1f, 0.0f, 10.f, "%.1f");
        DragFloat("lookOnVel", &Ctx->gd.lookOnVel, 0.1f, 0.0f, 10.f, "%.1f");
        DragFloat("tmpPm", &Ctx->gd.tmpPm, .01f, 0.0f, 100.f, "%.2f");
        Checkbox("AdF", &gPhyUseAddForce);

        PopItemWidth();

        EndGroup();
        SameLine();
        BeginGroup();

        PushItemWidth(200);
 		Checkbox("Camera RttOnSbVel", &Ctx->gd.camRttOnSbVel); SameLine();
        PopItemWidth();

        EndGroup();

        SameLine();
        BeginGroup();

        PushItemWidth(200);
		Checkbox("tone", (bool*) & Ctx->gd.toneForHdr); SameLine();
        DragFloat("toneGamma", &Driver->dsd.hdrGamma, 0.01f, 0.0f, 2.2f, "%.2f");
        DragFloat("toneExposure", &Driver->dsd.hdrExposure, 0.01f, 0.0f, 10.f, "%.2f");
        extern float gFloatTestPm ;
        DragFloat("testPm", &gFloatTestPm, 0.01f, 0, 1);
        PopItemWidth();

        EndGroup();

        EndTabItem();
    }


    if (BeginTabItem("MMD")) {
        BeginGroup();
        PushItemWidth(300);
  		DragFloat("shadowRat", &mmd->shdpm.shadowRat, 0.01f, 0.0f, 1.f, "%.2f");
  		DragFloat("whiteRat", &mmd->shdpm.whiteRat, 0.01f, 0.0f, 1.f, "%.2f");
  		DragFloat("NdotLRat", &mmd->shdpm.NdotLRat, 0.01f, 0.0f, 1.f, "%.2f");
  		DragFloat("NdotEyeRat", &mmd->shdpm.NdotEyeRat, 0.01f, 0.0f, 1.f, "%.2f");
  		SliderInt("toonMulCount", &mmd->shdpm.toonMulCount, 0, 5);
        PopItemWidth();

        EndGroup();
        SameLine();
        BeginGroup();

        PushItemWidth(200);
        Checkbox("trgSetVelP2P", &mmd->trgSetVelP2P);
        PopItemWidth();

        EndGroup();

        SameLine();
        BeginGroup();

        PushItemWidth(200);


        PopItemWidth();

        EndGroup();

        EndTabItem();
    }

    if (BeginTabItem("AddObj",nullptr, setTabAddObjSelected?  ImGuiTabItemFlags_SetSelected : 0)) {
        setTabAddObjSelected = false;
		bool updated = false;
        BeginGroup();
        PushItemWidth(300);
        updated |= Checkbox("Auto", &Ctx->gd.apm.autoShot); SameLine();
        updated |= Checkbox("toChara", &Ctx->gd.apm.toMMD); SameLine();
        updated |= Checkbox("p2p", &Ctx->gd.apm.p2p); SameLine();
        updated |= Checkbox("noRtt", &Ctx->gd.apm.noRtt);
        updated |= Checkbox("fromEmitter", &Ctx->gd.apm.fromEmitter);
        if (Ctx->gd.apm.autoShot && Ctx->gd.apm.countRt > 10) Ctx->gd.apm.countRt = 10;
        updated |= SliderInt("countRt", &Ctx->gd.apm.countRt, 1, Ctx->gd.apm.autoShot ? 10 : 50, "%d", ImGuiSliderFlags_AlwaysClamp);
        updated |= DragFloat("scale", &Ctx->gd.apm.scale, 0.05f, 0.1f, 20.f, "%g");
        updated |= DragFloat("angle", &Ctx->gd.apm.angle, 0.1f, 0.f, 90.f, "%g");
        updated |= DragFloat("density", &Ctx->gd.apm.density, 0.1f, 0.1f, 100.f, "%g");
        updated |= DragFloat("spdMul", &Ctx->gd.apm.spdMul, 0.1f, 0.f, 100.f, "%g");
        updated |= DragFloat3("srcOfs", &Ctx->gd.apm.camOfsCtr.x, 0.1f, -10, 20);
        updated |= DragFloat3("dirOfs##s", &Ctx->gd.apm.dirOfs.x, 0.01f, -1, 1);
        updated |= vks::DragFloatXYZ("dirOfs##c", &Ctx->gd.apm.dirOfs.x, &Ctx->gd.apm.dirOfs.y, &Ctx->gd.apm.dirOfs.z, 0.01f, true);
        PopItemWidth();
        EndGroup();

        SameLine();
        BeginGroup();
        PushItemWidth(200);
		auto& apm = Ctx->gd.apm;
        updated |= SliderInt("dupCount", &apm.dupCount, 1, 100);
        updated |= vks::DragFloat3Ext("dupTrs", &apm.dupTrsInc[0], 0.001f, 0, 1.f, "%.2f");
        updated |= vks::DragFloat3Ext("dupRtt", &apm.dupRttInc[0], 0.1f, -180.f, 180.f,"%.1f");
        updated |= DragFloat("dupScale", &apm.dupScaleInc, 0.001f, -0.5f, 1.f, "%.2f");
		updated |= DragInt("combine", &apm.combineMode, 0.1f, 0, 10);
		updated |= DragInt("dupModel", &apm.dupModelId, 0.1f, 0, 10);

        if (Button("0")) {
			apm.dupTrsInc = vec3(0);
			apm.dupRttInc = vec3(0);
			apm.dupScaleInc = 0;
        }; SameLine();
        if (Button("dup 1")) {
            apm.dupTrsInc = vec3(0, 0.02f, 0);
            apm.dupRttInc = vec3(1.5f, 50, 0);
            apm.dupScaleInc = -0.01;
        }; SameLine();
        if (Button("30")) {
            apm.combineMode = 0;
            apm.dupCount = 30;
            apm.dupTrsInc = vec3(0, 0.02f, 0);
            apm.dupRttInc = vec3(3, 50, 0);
            apm.dupScaleInc = -0.01;
            apm.spdMul = 2;
        }; SameLine();
        if (Button("G")) {
            apm.combineMode = 0;
            apm.dupCount = 60;
            apm.dupTrsInc = vec3(0, 0.02f, 0);
            apm.dupRttInc = vec3(1.5f, 50, 0);
            apm.dupScaleInc = -0.01;
            apm.spdMul = 0;
			apm.camOfsCtr = vec3(0, -10, 10);
        }; SameLine();
        if (Button("W")) {
            apm.combineMode = 0;
            apm.dupCount = 36;
            apm.dupTrsInc = vec3(0, 0, 0);
            apm.dupRttInc = vec3(10, 0, 0);
            apm.dupScaleInc =0;
            apm.spdMul = 0;
            apm.camOfsCtr = vec3(0, -10, 10);
        }; SameLine();
        if (Button("C")) {
            apm.dupCount = 60;
            apm.combineMode = 1;
            apm.dupTrsInc = vec3(0, 0.1f, 0);
            apm.dupRttInc = vec3(0, 30, 0);
            apm.dupScaleInc = 0;
            apm.spdMul = 0;
            apm.camOfsCtr = vec3(0,  10, 10);
        }; SameLine();
        if (Button("L")) {
            apm.dupCount = 30;
            apm.combineMode = 1;
            apm.dupTrsInc = vec3(0, 1, 0);
            apm.dupRttInc = vec3(0, 0, 0);
            apm.dupScaleInc = 0;
            apm.spdMul = 0;
            apm.camOfsCtr = vec3(0, 10, 10);
        };
        updated |= Checkbox("check", &Ctx->gd.apm.checkMode); SameLine();
        updated |= Checkbox("Rcmb", &Ctx->gd.apm.RCombine);
        if (Button("Save")) {
			Ctx->gd.apm.dupToSave =1;

        };
        SameLine();
        if (Button("AddObj")) {
            Ctx->getLib()->CurStage()->sendKeyEvent(KEY_KEY_R, 1, 1, 0, 1);
        }
        //SliderInt("lsXC", &Ctx->gd.apm.lsXC, 1, 16.f); SliderInt("lsYC", &Ctx->gd.apm.lsYC, 1, 8.f);
        PopItemWidth();
        EndGroup();
        SameLine();
        BeginGroup();
        PushItemWidth(200);
		auto& gpm = gPmxGenPm;
        updated |= vks::DragFloat3Ext("gpPos", &gpm.position[0], 0.01f, 0, 100.f, "%.2f");
        //updated |= DragFloat("ropeRadiusAdd", &gpm.ropeRadiusAdd, 0.001f, 0, 2.f, "%.2f");
        PopItemWidth();
        EndGroup();

        EndTabItem();
        if (updated) {
            apm.toUpdate = 1;
            gPmxGenPm.chainScale = 1.f + apm.dupScaleInc;
			gPmxGenPm.chainMassScale = gPmxGenPm.chainScale* gPmxGenPm.chainScale ;
            gPmxGenPm.chainCount = apm.dupCount;
        }
    }

    if (BeginTabItem("MidiObj"))
    {
        auto& nbp = CMidiPlateSceneNode::nbp;
        BeginGroup();
        PushItemWidth(300);
        SetNextItemWidth(100); Checkbox("srcMode", (bool*)&nbp.srcMode); SameLine();
        SetNextItemWidth(100); Checkbox("snIm", (bool*)&nbp.snIM); SameLine();
        SetNextItemWidth(100); Checkbox("footB", (bool*)&nbp.footButt); SameLine();
        SetNextItemWidth(100); Checkbox("headB", (bool*)&nbp.headButt);
        SliderInt("angleN", &nbp.angleN, 1, 10);
        SliderFloat("flyTime", &nbp.extraFlyTime, 0.5f, 2.f);
        DragFloat("fMul", &nbp.fMul,0.1f, 0.5f, 5.f);
        DragFloat("rMul", &nbp.rMul, 0.01f, 0.0f, 1.f);
        vks::DragFloatXYZExt("srcPos", &nbp.srcPos.x,0.1f);
        DragFloat("size", &nbp.radiusX2, 0.1f, 0.5f, 5.f);
        DragFloat("mass", &nbp.initMass, 0.1f, 1.f, 100.f);
		InputInt("sbMmdOfs", &nbp.sbMmdOfs);
        vks::DragFloatXYZExt("ballOfs", &nbp.ballMmdOfs.x, 0.1f);
        SetNextItemWidth(100); SliderFloat("upMul", &nbp.upMul, 20.f, 50.f); SameLine();
        SetNextItemWidth(100); SliderFloat("downMul", &nbp.downMul, 0.f, 20.f);
        PopItemWidth();
        EndGroup();  SameLine();

        BeginGroup();
        PushItemWidth(300);
        SliderFloat("nbTimer", &nbp.ballTimer, 3.0f, 60.f);
        SliderInt("nbIdx", &nbp.nbIdx, 0, 10);
        PopItemWidth();
        EndGroup();



        EndTabItem();
    }
    if (BeginTabItem("FW"))
    {
        BeginGroup();
        PushItemWidth(300);
        auto Eqv = Sb0->Eqv;
		InputInt("curFw0", &Eqv->curPtrFwIds[0]); Eqv->curPtrFwIds[0] = Eqv->curPtrFwIds[0] % Eqv->ptrFws[0].size();
        SameLine(); Text("%s", Sb0->Eqv->getPtrFwIdStrs(0)[Eqv->curPtrFwIds[0]].c_str());
        InputInt("curFw1", &Eqv->curPtrFwIds[1]); Eqv->curPtrFwIds[1] = Eqv->curPtrFwIds[1] % Eqv->ptrFws[1].size();
        SameLine(); Text("%s",Sb0->Eqv->getPtrFwIdStrs(1)[Eqv->curPtrFwIds[1]].c_str());
		InputInt("curFw2", &Eqv->curPtrFwIds[2]); Eqv->curPtrFwIds[2] = Eqv->curPtrFwIds[2] % Eqv->ptrFws[2].size();
        SameLine(); Text("%s", Sb0->Eqv->getPtrFwIdStrs(2)[Eqv->curPtrFwIds[2]].c_str());
        PopItemWidth();
        EndGroup();
        EndTabItem();
    }
    if (BeginTabItem("VoxScene"))
    {
        BeginGroup();
        PushItemWidth(300);

        auto& vspm = Ctx->gd.vspm;
        SetNextItemWidth(100); Checkbox("OIT", (bool*)&vspm.oit);  SameLine();
        SliderFloat("Alpha", &vspm.alpha, 0.1f, 1.f,"%.1f");    SameLine();
        SliderFloat("Scale", &vspm.scale, 0.1f, 1.2f,"%.1f");
		SetNextItemWidth(100); Checkbox("AutoReset", (bool*)&vspm.autoReset);  SameLine();
		SetNextItemWidth(100); Checkbox("SpeedDelay", (bool*)&vspm.speedDelay); // SameLine();
        DragFloat("ResetTime", &vspm.resetTime, 0.1f,0.1f, 10.f);
        DragFloat("BtShell", &vspm.btShell, 100.f, 100.f, 10000.f); SameLine();
        DragFloat("BtInner", &vspm.btInner, 100.f, 100.f, 10000.f);
        SliderInt("FwFrame", &vspm.fwFrame, 0,10);
        PopItemWidth();
        EndGroup();
        EndTabItem();
    }
}


#pragma endregion