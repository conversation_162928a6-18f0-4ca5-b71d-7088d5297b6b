// Copyright (C) 2002-2012 <PERSON><PERSON>
// This file is part of the "Irrlicht Engine".
// For conditions of distribution and use, see copyright notice in irrlicht.h
#include "AppGlobal.h"
#include "SnPhyFluid.h"

#include "IVideoDriver.h"
#include "ISceneManager.h"
#include "S3DVertex.h"
#include "SMeshBuffer.h"
#include "os.h"
#include "CShadowVolumeSceneNode.h"
#include "SMesh.h"
#include "IMesh.h"
#include "PxPhysicsAPI.h"
#include "extensions/PxParticleExt.h"
#include "extensions/PxCudaHelpersExt.h"
#include "cudamanager/PxCudaContext.h"
#include "cudamanager/PxCudaContextManager.h"

using namespace physx;
using namespace physx::ExtGpu;

namespace saba {
	extern PxPhysics* gPxPhysics;
	extern PxScene* gPxScene;
}
using namespace saba;

static PxPBDParticleSystem*			gFluidParticleSystem	= NULL;
static PxParticleAndDiffuseBuffer*	gFluidBuffer			= NULL;

namespace irr
{
namespace scene
{
	using namespace irr::video;

void SnPhyFluid::initFluid(const u32 numX, const u32 numY, const u32 numZ, const float3& position)
{
	if (gFluidParticleSystem)
		return;

	PxCudaContextManager* cudaContextManager = gPxScene->getCudaContextManager();
	if (cudaContextManager == NULL)
		return;

	const PxU32 maxParticles = numX * numY * numZ;
	const PxReal restOffset = 0.5f * particleSpacing / 0.6f;

	// Material setup - fluid specific properties
	PxPBDMaterial* fluidMat = gPxPhysics->createPBDMaterial(0.05f, 0.05f, 0.f, 0.001f, 0.5f, 0.005f, 0.01f, 0.f, 0.f);

	// Set fluid-specific material properties
	fluidMat->setViscosity(0.001f);
	fluidMat->setSurfaceTension(0.00704f);
	fluidMat->setCohesion(0.0704f);
	fluidMat->setVorticityConfinement(10.f);

	PxPBDParticleSystem *particleSystem = gPxPhysics->createPBDParticleSystem(*cudaContextManager, 96);
	gFluidParticleSystem = particleSystem;

	// General particle system setting
	const PxReal solidRestOffset = restOffset;
	const PxReal fluidRestOffset = restOffset * 0.6f;
	const PxReal particleMass = fluidDensity * 1.333f * 3.14159f * particleSpacing * particleSpacing * particleSpacing;

	particleSystem->setRestOffset(restOffset);
	particleSystem->setContactOffset(restOffset + 0.01f);
	particleSystem->setParticleContactOffset(fluidRestOffset / 0.6f);
	particleSystem->setSolidRestOffset(solidRestOffset);
	particleSystem->setFluidRestOffset(fluidRestOffset);
	particleSystem->setParticleFlag(PxParticleFlag::eENABLE_SPECULATIVE_CCD, false);
	particleSystem->setMaxVelocity(solidRestOffset*100.f);

	gPxScene->addActor(*particleSystem);

	// Diffuse particles setting
	PxDiffuseParticleParams dpParams;
	dpParams.threshold = 300.0f;
	dpParams.bubbleDrag = 0.9f;
	dpParams.buoyancy = 0.9f;
	dpParams.airDrag = 0.0f;
	dpParams.kineticEnergyWeight = 0.01f;
	dpParams.pressureWeight = 1.0f;
	dpParams.divergenceWeight = 10.f;
	dpParams.lifetime = 1.0f;
	dpParams.useAccurateVelocity = false;

	// Create particles and add them to the particle system
	const PxU32 particlePhase = particleSystem->createPhase(fluidMat, PxParticlePhaseFlags(PxParticlePhaseFlag::eParticlePhaseFluid | PxParticlePhaseFlag::eParticlePhaseSelfCollide));

	PxU32* phase = PX_EXT_PINNED_MEMORY_ALLOC(PxU32, *cudaContextManager, maxParticles);
	PxVec4* positionInvMass = PX_EXT_PINNED_MEMORY_ALLOC(PxVec4, *cudaContextManager, maxParticles);
	PxVec4* velocity = PX_EXT_PINNED_MEMORY_ALLOC(PxVec4, *cudaContextManager, maxParticles);

	PxReal x = position.x;
	PxReal y = position.y;
	PxReal z = position.z;

	// Initialize particles in 3D grid
	for (PxU32 i = 0; i < numX; ++i)
	{
		for (PxU32 j = 0; j < numY; ++j)
		{
			for (PxU32 k = 0; k < numZ; ++k)
			{
				const PxU32 index = i * (numY * numZ) + j * numZ + k;

				PxVec4 pos(x, y, z, 1.0f / particleMass);
				phase[index] = particlePhase;
				positionInvMass[index] = pos;
				velocity[index] = PxVec4(0.0f);

				z += particleSpacing;
			}
			z = position.z;
			y += particleSpacing;
		}
		y = position.y;
		x += particleSpacing;
	}

	ExtGpu::PxParticleAndDiffuseBufferDesc bufferDesc;
	bufferDesc.maxParticles = maxParticles;
	bufferDesc.numActiveParticles = maxParticles;
	bufferDesc.maxDiffuseParticles = maxDiffuseParticles;
	bufferDesc.maxActiveDiffuseParticles = maxDiffuseParticles;
	bufferDesc.diffuseParams = dpParams;

	bufferDesc.positions = positionInvMass;
	bufferDesc.velocities = velocity;
	bufferDesc.phases = phase;

	gFluidBuffer = physx::ExtGpu::PxCreateAndPopulateParticleAndDiffuseBuffer(bufferDesc, cudaContextManager);
	gFluidParticleSystem->addParticleBuffer(gFluidBuffer);

	PX_EXT_PINNED_MEMORY_FREE(*cudaContextManager, positionInvMass);
	PX_EXT_PINNED_MEMORY_FREE(*cudaContextManager, velocity);
	PX_EXT_PINNED_MEMORY_FREE(*cudaContextManager, phase);
}

SnPhyFluid::SnPhyFluid(f32 size, ISceneNode* parent, ISceneManager* mgr,
		s32 id, const core::vector3df& position,
		const core::vector3df& rotation, const core::vector3df& scale)
	: IMeshSceneNode(parent, mgr, id, position, rotation, scale),
	Mesh(0), Shadow(0), Size(size)
{
	#ifdef _DEBUG
	setDebugName("SnPhyFluid");
	#endif

	// Setup Fluid
	initFluid(numPointsX, numPointsY, numPointsZ, { -0.5f * numPointsX * particleSpacing, 3.f, -0.5f * numPointsZ * particleSpacing });
}

SnPhyFluid::~SnPhyFluid()
{
	if (Shadow)
		Shadow->drop();
	if (Mesh)
		Mesh->drop();
}

void SnPhyFluid::updateMesh()
{
	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	PxParticleAndDiffuseBuffer* userBuffer = gFluidBuffer;
	if (!userBuffer) return;

	PxVec4* positions = userBuffer->getPositionInvMasses();
	const PxU32 numParticles = userBuffer->getNbActiveParticles();

	PxScene* scene;
	PxGetPhysics().getScenes(&scene, 1);
	PxCudaContextManager* cudaContextManager = scene->getCudaContextManager();

	cudaContextManager->acquireContext();
	PxCudaContext* cudaContext = cudaContextManager->getCudaContext();

	if (!Mesh) {
		SMeshBuffer* buffer = new SMeshBuffer();
		Mb = buffer;
		buffer->Vertices.set_used(numParticles);
		buffer->Indices.set_used(numParticles); // For point rendering

		Mesh = new SMesh;
		Mesh->addMeshBuffer(buffer);
		buffer->drop();
		IsVisible = true;

		// Initialize vertices for point rendering
		for (u32 i = 0; i < numParticles; i++) {
			auto& v = buffer->Vertices[i];
			v.Color.set(255, 100, 150, 255); // Blue-ish fluid color
			v.TCoords.set(0.0f, 0.0f);
		}

		// Set indices for point rendering
		for (u32 i = 0; i < numParticles; i++) {
			buffer->Indices[i] = i;
		}

		video::SMaterial& mt = Mesh->getMeshBuffer(0)->getMaterial();
		mt.Lighting = false;
		mt.MaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL;
		mt.DiffuseColor = mt.AmbientColor = 0x8F0096FF; // Semi-transparent blue
		mt.PointSize = particleSize * 1000.0f; // Scale for visibility
	}

	if (numParticles > 0) {
		SMeshBuffer* buffer = (SMeshBuffer*)Mb;

		if (!ptcBuf) ptcBuf = new float4[numParticles];
		cudaContext->memcpyDtoH(ptcBuf, CUdeviceptr(positions), sizeof(PxVec4) * numParticles);

		for (u32 i = 0; i < numParticles; i++) {
			buffer->Vertices[i].Pos.set(ptcBuf[i].x, ptcBuf[i].y, -ptcBuf[i].z);
		}

		// Update bounding box
		Box.reset(buffer->Vertices[0].Pos);
		for (u32 i = 1; i < numParticles; i++) {
			Box.addInternalPoint(buffer->Vertices[i].Pos);
		}
		buffer->setBoundingBox(Box);
	}

	// Handle diffuse particles if enabled
	if (showDiffuseParticles && userBuffer->getNbActiveDiffuseParticles() > 0) {
		PxVec4* diffusePositions = userBuffer->getDiffusePositions();
		const PxU32 numDiffuseParticles = userBuffer->getNbActiveDiffuseParticles();

		if (!diffusePtcBuf) diffusePtcBuf = new float4[maxDiffuseParticles];
		cudaContext->memcpyDtoH(diffusePtcBuf, CUdeviceptr(diffusePositions), sizeof(PxVec4) * numDiffuseParticles);

		// Store diffuse particle data for rendering
		// (Implementation would depend on how you want to render foam/bubbles)
	}

	cudaContextManager->releaseContext();
}

void SnPhyFluid::OnAnimate(u32 timeMs)
{
	ISceneNode::OnAnimate(timeMs);
	updateMesh();
}

//! renders the node.
void SnPhyFluid::render()
{
	if (!gFluidParticleSystem || !Mesh)
		return;

	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	driver->setTransform(video::ETS_WORLD, AbsoluteTransformation);

	video::SMaterial& mat = Mesh->getMeshBuffer(0)->getMaterial();
	driver->setMaterial(mat);

	auto mb = Mesh->getMeshBuffer(0);

	if (renderAsPoints) {
		// Render as points
		driver->drawVertexPrimitiveList(mb->getVertices(), mb->getVertexCount(),
			mb->getIndices(), mb->getVertexCount(), mb->getVertexType(),
			scene::EPT_POINTS, mb->getIndexType());
	} else {
		// Alternative: render as small spheres or other primitives
		// This would require generating sphere geometry for each particle
	}
}

//! returns the axis aligned bounding box of this node
const core::aabbox3d<f32>& SnPhyFluid::getBoundingBox() const
{
	return Box;
}

//! Removes a child from this scene node.
bool SnPhyFluid::removeChild(ISceneNode* child)
{
	if (child && Shadow == child)
	{
		Shadow->drop();
		Shadow = 0;
	}

	return ISceneNode::removeChild(child);
}

//! Creates shadow volume scene node as child of this node
IShadowVolumeSceneNode* SnPhyFluid::addShadowVolumeSceneNode(
		const IMesh* shadowMesh, s32 id, bool zfailmethod, f32 infinity)
{
#if _SHADOW_VOLUME_SCENE_NODE_
	if (!SceneManager->getVideoDriver()->queryFeature(video::EVDF_STENCIL_BUFFER))
		return 0;

	if (!shadowMesh)
		shadowMesh = Mesh; // if null is given, use the mesh of node

	if (Shadow)
		Shadow->drop();

	Shadow = new CShadowVolumeSceneNode(shadowMesh, this, SceneManager, id,  zfailmethod, infinity);
#endif
	return Shadow;
}

void SnPhyFluid::OnRegisterSceneNode()
{
	if (IsVisible)
		SceneManager->registerNodeForRendering(this);
	ISceneNode::OnRegisterSceneNode();
}

//! returns the material based on the zero based index i.
video::SMaterial& SnPhyFluid::getMaterial(u32 i)
{
	static video::SMaterial s;
	if (!Mesh) return s;
	return Mesh->getMeshBuffer(0)->getMaterial();
}

//! returns amount of materials used by this scene node.
u32 SnPhyFluid::getMaterialCount() const
{
	return 1;
}

//! Creates a clone of this scene node and its children.
ISceneNode* SnPhyFluid::clone(ISceneNode* newParent, ISceneManager* newManager)
{
	if (!newParent)
		newParent = Parent;
	if (!newManager)
		newManager = SceneManager;

	SnPhyFluid* nb = new SnPhyFluid(Size, newParent,
		newManager, ID, RelativeTranslation);

	nb->cloneMembers(this, newManager);
	nb->getMaterial(0) = getMaterial(0);
	nb->Shadow = Shadow;
	if ( nb->Shadow )
		nb->Shadow->grab();

	if ( newParent )
		nb->drop();
	return nb;
}

void SnPhyFluid::setSize()
{
	// Implementation for setSize if needed
}

} // end namespace scene
} // end namespace irr
