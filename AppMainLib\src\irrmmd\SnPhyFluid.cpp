// Copyright (C) 2002-2012 <PERSON><PERSON>
// This file is part of the "Irrlicht Engine".
// For conditions of distribution and use, see copyright notice in irrlicht.h
#include "AppGlobal.h"
#include "SnPhyFluid.h"

#include "IVideoDriver.h"
#include "ISceneManager.h"
#include "S3DVertex.h"
#include "SMeshBuffer.h"
#include "os.h"
#include "CShadowVolumeSceneNode.h"
#include "SMesh.h"
#include "IMesh.h"
#include "PxPhysicsAPI.h"
#include "extensions/PxParticleExt.h"
#include "extensions/PxCudaHelpersExt.h"
#include "cudamanager/PxCudaContext.h"
#include "cudamanager/PxCudaContextManager.h"

using namespace physx;
using namespace physx::ExtGpu;

namespace saba {
	extern PxPhysics* gPxPhysics;
	extern PxScene* gPxScene;
}
using namespace saba;

static PxPBDParticleSystem*			gFluidParticleSystem	= NULL;
static PxParticleAndDiffuseBuffer*	gFluidBuffer			= NULL;

namespace irr
{
namespace scene
{
	using namespace irr::video;

void SnPhyFluid::initFluid(const u32 numX, const u32 numY, const u32 numZ, const float3& position)
{
	if (gFluidParticleSystem)
		return;

	PxCudaContextManager* cudaContextManager = gPxScene->getCudaContextManager();
	if (cudaContextManager == NULL)
		return;

	const PxU32 maxParticles = numX * numY * numZ;
	const PxReal restOffset = 0.5f * particleSpacing / 0.6f;

	// Material setup - fluid specific properties
	PxPBDMaterial* fluidMat = gPxPhysics->createPBDMaterial(0.05f, 0.05f, 0.f, 0.001f, 0.5f, 0.005f, 0.01f, 0.f, 0.f);

	// Set fluid-specific material properties
	fluidMat->setViscosity(0.001f);
	fluidMat->setSurfaceTension(0.00704f);
	fluidMat->setCohesion(0.0704f);
	fluidMat->setVorticityConfinement(10.f);

	PxPBDParticleSystem *particleSystem = gPxPhysics->createPBDParticleSystem(*cudaContextManager, 96);
	gFluidParticleSystem = particleSystem;

	// General particle system setting
	const PxReal solidRestOffset = restOffset;
	const PxReal fluidRestOffset = restOffset * 0.6f;
	const PxReal particleMass = fluidDensity * 1.333f * 3.14159f * particleSpacing * particleSpacing * particleSpacing;

	particleSystem->setRestOffset(restOffset);
	particleSystem->setContactOffset(restOffset + 0.01f);
	particleSystem->setParticleContactOffset(fluidRestOffset / 0.6f);
	particleSystem->setSolidRestOffset(solidRestOffset);
	particleSystem->setFluidRestOffset(fluidRestOffset);
	particleSystem->setParticleFlag(PxParticleFlag::eENABLE_SPECULATIVE_CCD, false);
	particleSystem->setMaxVelocity(solidRestOffset*100.f);

	gPxScene->addActor(*particleSystem);

	// Diffuse particles setting
	PxDiffuseParticleParams dpParams;
	dpParams.threshold = 300.0f;
	dpParams.bubbleDrag = 0.9f;
	dpParams.buoyancy = 0.9f;
	dpParams.airDrag = 0.0f;
	dpParams.kineticEnergyWeight = 0.01f;
	dpParams.pressureWeight = 1.0f;
	dpParams.divergenceWeight = 10.f;
	dpParams.lifetime = 1.0f;
	dpParams.useAccurateVelocity = false;

	// Create particles and add them to the particle system
	const PxU32 particlePhase = particleSystem->createPhase(fluidMat, PxParticlePhaseFlags(PxParticlePhaseFlag::eParticlePhaseFluid | PxParticlePhaseFlag::eParticlePhaseSelfCollide));

	PxU32* phase = PX_EXT_PINNED_MEMORY_ALLOC(PxU32, *cudaContextManager, maxParticles);
	PxVec4* positionInvMass = PX_EXT_PINNED_MEMORY_ALLOC(PxVec4, *cudaContextManager, maxParticles);
	PxVec4* velocity = PX_EXT_PINNED_MEMORY_ALLOC(PxVec4, *cudaContextManager, maxParticles);

	PxReal x = position.x;
	PxReal y = position.y;
	PxReal z = position.z;

	// Initialize particles in 3D grid
	for (PxU32 i = 0; i < numX; ++i)
	{
		for (PxU32 j = 0; j < numY; ++j)
		{
			for (PxU32 k = 0; k < numZ; ++k)
			{
				const PxU32 index = i * (numY * numZ) + j * numZ + k;

				PxVec4 pos(x, y, z, 1.0f / particleMass);
				phase[index] = particlePhase;
				positionInvMass[index] = pos;
				velocity[index] = PxVec4(0.0f);

				z += particleSpacing;
			}
			z = position.z;
			y += particleSpacing;
		}
		y = position.y;
		x += particleSpacing;
	}

	ExtGpu::PxParticleAndDiffuseBufferDesc bufferDesc;
	bufferDesc.maxParticles = maxParticles;
	bufferDesc.numActiveParticles = maxParticles;
	bufferDesc.maxDiffuseParticles = maxDiffuseParticles;
	bufferDesc.maxActiveDiffuseParticles = maxDiffuseParticles;
	bufferDesc.diffuseParams = dpParams;

	bufferDesc.positions = positionInvMass;
	bufferDesc.velocities = velocity;
	bufferDesc.phases = phase;

	gFluidBuffer = physx::ExtGpu::PxCreateAndPopulateParticleAndDiffuseBuffer(bufferDesc, cudaContextManager);
	gFluidParticleSystem->addParticleBuffer(gFluidBuffer);

	PX_EXT_PINNED_MEMORY_FREE(*cudaContextManager, positionInvMass);
	PX_EXT_PINNED_MEMORY_FREE(*cudaContextManager, velocity);
	PX_EXT_PINNED_MEMORY_FREE(*cudaContextManager, phase);
}

SnPhyFluid::SnPhyFluid(f32 size, ISceneNode* parent, ISceneManager* mgr,
		s32 id, const core::vector3df& position,
		const core::vector3df& rotation, const core::vector3df& scale)
	: IMeshSceneNode(parent, mgr, id, position, rotation, scale),
	Mesh(0), DiffuseMesh(0), Shadow(0), Size(size)
{
	#ifdef _DEBUG
	setDebugName("SnPhyFluid");
	#endif
	setScale(MMD_SABA_SCALE);
	// Setup Fluid
	initFluid(numPointsX, numPointsY, numPointsZ, { -0.5f * numPointsX * particleSpacing, 3.f, -0.5f * numPointsZ * particleSpacing });
}

SnPhyFluid::~SnPhyFluid()
{
	if (Shadow)
		Shadow->drop();
	if (Mesh)
		Mesh->drop();
	if (DiffuseMesh)
		DiffuseMesh->drop();
}

void SnPhyFluid::updateMesh()
{
	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	PxParticleAndDiffuseBuffer* userBuffer = gFluidBuffer;
	if (!userBuffer) return;

	PxVec4* positions = userBuffer->getPositionInvMasses();
	const PxU32 numParticles = userBuffer->getNbActiveParticles();

	PxScene* scene;
	PxGetPhysics().getScenes(&scene, 1);
	PxCudaContextManager* cudaContextManager = scene->getCudaContextManager();

	cudaContextManager->acquireContext();
	PxCudaContext* cudaContext = cudaContextManager->getCudaContext();

	if (!Mesh) {
		SMeshBuffer* buffer = new SMeshBuffer();
		Mb = buffer;
		// Each particle needs 4 vertices (quad) and 6 indices (2 triangles)
		buffer->Vertices.set_used(numParticles * 4);
		buffer->Indices.set_used(numParticles * 6);

		Mesh = new SMesh;
		Mesh->addMeshBuffer(buffer);
		buffer->drop();
		IsVisible = true;

		// Initialize quad vertices for each particle
		for (u32 i = 0; i < numParticles; i++) {
			u32 baseVertex = i * 4;
			u32 baseIndex = i * 6;

			// Create quad vertices (will be positioned later in updateMesh)
			for (u32 j = 0; j < 4; j++) {
				auto& v = buffer->Vertices[baseVertex + j];
				v.Color.set(255, 100, 150, 255); // Blue-ish fluid color
				v.Pos.set(0, 0, 0); // Will be set in updateMesh
			}

			// Set texture coordinates for quad
			buffer->Vertices[baseVertex + 0].TCoords.set(0.0f, 0.0f); // Top-left
			buffer->Vertices[baseVertex + 1].TCoords.set(1.0f, 0.0f); // Top-right
			buffer->Vertices[baseVertex + 2].TCoords.set(1.0f, 1.0f); // Bottom-right
			buffer->Vertices[baseVertex + 3].TCoords.set(0.0f, 1.0f); // Bottom-left

			// Set indices for two triangles forming a quad
			buffer->Indices[baseIndex + 0] = baseVertex + 0; // Triangle 1
			buffer->Indices[baseIndex + 1] = baseVertex + 1;
			buffer->Indices[baseIndex + 2] = baseVertex + 2;

			buffer->Indices[baseIndex + 3] = baseVertex + 0; // Triangle 2
			buffer->Indices[baseIndex + 4] = baseVertex + 2;
			buffer->Indices[baseIndex + 5] = baseVertex + 3;
		}

		video::SMaterial& mt = Mesh->getMeshBuffer(0)->getMaterial();
		mt.Lighting = false;
		mt.MaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL;
		mt.DiffuseColor = mt.AmbientColor = 0x8F0096FF; // Semi-transparent blue
		mt.BackfaceCulling = false; // Show both sides of quads
	}

	if (numParticles > 0) {
		SMeshBuffer* buffer = (SMeshBuffer*)Mb;

		if (!ptcBuf) ptcBuf = new float4[numParticles];
		cudaContext->memcpyDtoH(ptcBuf, CUdeviceptr(positions), sizeof(PxVec4) * numParticles);

		// Get camera position for billboard orientation
		core::vector3df cameraPos = SceneManager->getActiveCamera() ?
			SceneManager->getActiveCamera()->getAbsolutePosition() :
			core::vector3df(0, 0, 0);

		// Update quad vertices for each particle
		for (u32 i = 0; i < numParticles; i++) {
			core::vector3df particlePos(ptcBuf[i].x, ptcBuf[i].y, -ptcBuf[i].z);
			u32 baseVertex = i * 4;

			// Calculate billboard vectors (facing camera)
			core::vector3df toCamera = (cameraPos - particlePos).normalize();
			core::vector3df up(0, 1, 0);
			core::vector3df right = toCamera.crossProduct(up).normalize();
			up = right.crossProduct(toCamera).normalize();

			// Scale by particle size
			right *= particleSize;
			up *= particleSize;

			// Set quad vertices
			buffer->Vertices[baseVertex + 0].Pos = particlePos - right + up;  // Top-left
			buffer->Vertices[baseVertex + 1].Pos = particlePos + right + up;  // Top-right
			buffer->Vertices[baseVertex + 2].Pos = particlePos + right - up;  // Bottom-right
			buffer->Vertices[baseVertex + 3].Pos = particlePos - right - up;  // Bottom-left
		}

		// Update bounding box using first vertex of each quad
		if (numParticles > 0) {
			Box.reset(buffer->Vertices[0].Pos);
			for (u32 i = 0; i < numParticles; i++) {
				u32 baseVertex = i * 4;
				for (u32 j = 0; j < 4; j++) {
					Box.addInternalPoint(buffer->Vertices[baseVertex + j].Pos);
				}
			}
			buffer->setBoundingBox(Box);
		}
	}

	// Handle diffuse particles if enabled
	if (showDiffuseParticles && userBuffer->getNbActiveDiffuseParticles() > 0) {
		PxVec4* diffusePositions = userBuffer->getDiffusePositionLifeTime();
		const PxU32 numDiffuseParticles = userBuffer->getNbActiveDiffuseParticles();

		if (!diffusePtcBuf) diffusePtcBuf = new float4[maxDiffuseParticles];
		cudaContext->memcpyDtoH(diffusePtcBuf, CUdeviceptr(diffusePositions), sizeof(PxVec4) * numDiffuseParticles);

		// Create or update diffuse particle mesh
		if (!DiffuseMesh && numDiffuseParticles > 0) {
			SMeshBuffer* diffuseBuffer = new SMeshBuffer();
			DiffuseMb = diffuseBuffer;
			// Each diffuse particle needs 4 vertices (quad) and 6 indices (2 triangles)
			diffuseBuffer->Vertices.set_used(maxDiffuseParticles * 4);
			diffuseBuffer->Indices.set_used(maxDiffuseParticles * 6);

			DiffuseMesh = new SMesh;
			DiffuseMesh->addMeshBuffer(diffuseBuffer);
			diffuseBuffer->drop();

			// Initialize quad vertices for each diffuse particle
			for (u32 i = 0; i < maxDiffuseParticles; i++) {
				u32 baseVertex = i * 4;
				u32 baseIndex = i * 6;

				// Create quad vertices (will be positioned later)
				for (u32 j = 0; j < 4; j++) {
					auto& v = diffuseBuffer->Vertices[baseVertex + j];
					v.Color.set(255, 255, 255, 200); // White foam/bubble color
					v.Pos.set(0, 0, 0); // Will be set below
				}

				// Set texture coordinates for quad
				diffuseBuffer->Vertices[baseVertex + 0].TCoords.set(0.0f, 0.0f); // Top-left
				diffuseBuffer->Vertices[baseVertex + 1].TCoords.set(1.0f, 0.0f); // Top-right
				diffuseBuffer->Vertices[baseVertex + 2].TCoords.set(1.0f, 1.0f); // Bottom-right
				diffuseBuffer->Vertices[baseVertex + 3].TCoords.set(0.0f, 1.0f); // Bottom-left

				// Set indices for two triangles forming a quad
				diffuseBuffer->Indices[baseIndex + 0] = baseVertex + 0; // Triangle 1
				diffuseBuffer->Indices[baseIndex + 1] = baseVertex + 1;
				diffuseBuffer->Indices[baseIndex + 2] = baseVertex + 2;

				diffuseBuffer->Indices[baseIndex + 3] = baseVertex + 0; // Triangle 2
				diffuseBuffer->Indices[baseIndex + 4] = baseVertex + 2;
				diffuseBuffer->Indices[baseIndex + 5] = baseVertex + 3;
			}

			video::SMaterial& diffuseMt = DiffuseMesh->getMeshBuffer(0)->getMaterial();
			diffuseMt.Lighting = false;
			diffuseMt.MaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL;
			diffuseMt.DiffuseColor = diffuseMt.AmbientColor = 0xC8FFFFFF; // Semi-transparent white
			diffuseMt.BackfaceCulling = false; // Show both sides of quads
		}

		// Update diffuse particle positions
		if (DiffuseMesh && numDiffuseParticles > 0) {
			SMeshBuffer* diffuseBuffer = (SMeshBuffer*)DiffuseMb;
			currentDiffuseParticles = numDiffuseParticles;

			// Get camera position for billboard orientation
			core::vector3df cameraPos = SceneManager->getActiveCamera() ?
				SceneManager->getActiveCamera()->getAbsolutePosition() :
				core::vector3df(0, 0, 0);

			// Update quad vertices for each active diffuse particle
			for (u32 i = 0; i < numDiffuseParticles; i++) {
				core::vector3df particlePos(diffusePtcBuf[i].x, diffusePtcBuf[i].y, -diffusePtcBuf[i].z);
				
				float lifetime = diffusePtcBuf[i].w; // Lifetime information
				u32 baseVertex = i * 4;

				// Calculate billboard vectors (facing camera)
				core::vector3df toCamera = (cameraPos - particlePos).normalize();
				core::vector3df up(0, 1, 0);
				core::vector3df right = toCamera.crossProduct(up).normalize();
				up = right.crossProduct(toCamera).normalize();

				// Scale by particle size (smaller for diffuse particles)
				float diffuseSize = particleSize * 0.3f; // 30% of main particle size
				right *= diffuseSize;
				up *= diffuseSize;

				// Set quad vertices
				diffuseBuffer->Vertices[baseVertex + 0].Pos = particlePos - right + up;  // Top-left
				diffuseBuffer->Vertices[baseVertex + 1].Pos = particlePos + right + up;  // Top-right
				diffuseBuffer->Vertices[baseVertex + 2].Pos = particlePos + right - up;  // Bottom-right
				diffuseBuffer->Vertices[baseVertex + 3].Pos = particlePos - right - up;  // Bottom-left

				// Adjust alpha based on lifetime (fade out over time)
				u8 alpha = static_cast<u8>(200 * lifetime); // Assuming lifetime is 0-1
				for (u32 j = 0; j < 4; j++) {
					diffuseBuffer->Vertices[baseVertex + j].Color.setAlpha(alpha);
				}
			}
		}
	}

	cudaContextManager->releaseContext();
}

void SnPhyFluid::OnAnimate(u32 timeMs)
{
	ISceneNode::OnAnimate(timeMs);
	updateMesh();
}

//! renders the node.
void SnPhyFluid::render()
{
	if (!gFluidParticleSystem || !Mesh)
		return;

	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	driver->setTransform(video::ETS_WORLD, AbsoluteTransformation);

	// Render main fluid particles
	video::SMaterial& mat = Mesh->getMeshBuffer(0)->getMaterial();
	driver->setMaterial(mat);

	auto mb = Mesh->getMeshBuffer(0);

	// Render as triangulated quads (billboards)
	driver->drawVertexPrimitiveList(mb->getVertices(), mb->getVertexCount(),
		mb->getIndices(), mb->getIndexCount() / 3, mb->getVertexType(),
		scene::EPT_TRIANGLES, mb->getIndexType());

	// Render diffuse particles (foam/bubbles) if enabled and available
	if (showDiffuseParticles && DiffuseMesh && currentDiffuseParticles > 0) {
		video::SMaterial& diffuseMat = DiffuseMesh->getMeshBuffer(0)->getMaterial();
		driver->setMaterial(diffuseMat);

		auto diffuseMb = DiffuseMesh->getMeshBuffer(0);

		// Only render the active diffuse particles
		u32 diffuseTriangleCount = currentDiffuseParticles * 2; // 2 triangles per particle
		driver->drawVertexPrimitiveList(diffuseMb->getVertices(), currentDiffuseParticles * 4,
			diffuseMb->getIndices(), diffuseTriangleCount, diffuseMb->getVertexType(),
			scene::EPT_TRIANGLES, diffuseMb->getIndexType());
	}
}

//! returns the axis aligned bounding box of this node
const core::aabbox3d<f32>& SnPhyFluid::getBoundingBox() const
{
	return Box;
}

//! Removes a child from this scene node.
bool SnPhyFluid::removeChild(ISceneNode* child)
{
	if (child && Shadow == child)
	{
		Shadow->drop();
		Shadow = 0;
	}

	return ISceneNode::removeChild(child);
}

//! Creates shadow volume scene node as child of this node
IShadowVolumeSceneNode* SnPhyFluid::addShadowVolumeSceneNode(
		const IMesh* shadowMesh, s32 id, bool zfailmethod, f32 infinity)
{
#if _SHADOW_VOLUME_SCENE_NODE_
	if (!SceneManager->getVideoDriver()->queryFeature(video::EVDF_STENCIL_BUFFER))
		return 0;

	if (!shadowMesh)
		shadowMesh = Mesh; // if null is given, use the mesh of node

	if (Shadow)
		Shadow->drop();

	Shadow = new CShadowVolumeSceneNode(shadowMesh, this, SceneManager, id,  zfailmethod, infinity);
#endif
	return Shadow;
}

void SnPhyFluid::OnRegisterSceneNode()
{
	if (IsVisible)
		SceneManager->registerNodeForRendering(this);
	ISceneNode::OnRegisterSceneNode();
}

//! returns the material based on the zero based index i.
video::SMaterial& SnPhyFluid::getMaterial(u32 i)
{
	static video::SMaterial s;
	if (!Mesh) return s;
	return Mesh->getMeshBuffer(0)->getMaterial();
}

//! returns amount of materials used by this scene node.
u32 SnPhyFluid::getMaterialCount() const
{
	return 1;
}

//! Creates a clone of this scene node and its children.
ISceneNode* SnPhyFluid::clone(ISceneNode* newParent, ISceneManager* newManager)
{
	if (!newParent)
		newParent = Parent;
	if (!newManager)
		newManager = SceneManager;

	SnPhyFluid* nb = new SnPhyFluid(Size, newParent,
		newManager, ID, RelativeTranslation);

	nb->cloneMembers(this, newManager);
	nb->getMaterial(0) = getMaterial(0);
	nb->Shadow = Shadow;
	if ( nb->Shadow )
		nb->Shadow->grab();

	if ( newParent )
		nb->drop();
	return nb;
}

void SnPhyFluid::setSize()
{
	// Implementation for setSize if needed
}

} // end namespace scene
} // end namespace irr
