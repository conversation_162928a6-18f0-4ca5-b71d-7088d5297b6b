#pragma once

 

#include <irrlicht.h>
#include <map>
#include <functional>
#include "irrfw/eqv/EQV.h"
#include "irrsaba.h"
#include "../../app/ArMmPLayer/FL_ArEditor.h"
#include "MmdMidiPlayer.h"
#include "irrmmd/PhyObjMan.h"
#include "MmdPhyAnimator.h"

namespace EQVisual {
}
namespace ualib {
	class UaLibContext;
}
namespace irr::scene 
{
	class SnArRoot;
	class SnPhyFluid;
	struct IrrMmdParam {
		irrSabaParam spm;
		bool mmdInfinity = false;
		SnArRoot *arRoot=nullptr;
	};
	class IrrMMD
	{
	public:
		IrrMMD(IrrMmdParam pm);
		~IrrMMD();
		void createSabas(int num=1);
		void releaseSabas();
		void preUpdate();
		void postUpdate();
		void reloadMMD();
		void loadPoseId(int id, int frameNum);
		void loadMiscs(irr::scene::IrrSaba* saba);
		void setAnimating(bool aning); 
		bool getAnimating() { return animating; }
		void doAttach0(IrrSaba* p);
		void toggleLnv(int mode);
		void setOverrideTIme(int64_t us, float setGameTime, int64_t deltaUs);
		void freeAllRes();
		IrrSaba* getSaba(int id);
		IrrSaba* getAnmGlbNearestSaba(glm::vec3 tgt);
		std::vector<IrrSaba*> sabas, players;
		void addSaba(IrrSaba* p){
			sabas.push_back(p);
	}
		IrrSaba* nextSaba(int id,int ofs=1);
		IrrSaba* nextSaba(IrrSaba* cursb);
		PhyObjManager* Pom{};
		IrrSaba* sb0{}, * curAiSb{}, * lastAiSb{};
		MMDNode* lastPickNode{};
		irr::scene::ISceneManager *SceneManager;
		MmdMidiPlayer mdplr;
		IrrMmdParam Pm;
		saba::EPhysicsEngine curPhyType= saba::EPhysicsEngine::PhysX;
		irr::scene::ISceneNode* RootNode;

		void resetAni(float startS=0.f);
		bool switchSabas(int ofs);
		IrrSaba *curAiSaba();
		//void setOWFrame(int fr) { owFrame = fr; }
		void setCamToTgt(irr::scene::ICameraSceneNode* cam, IrrSaba* saba=nullptr);
		void setPmxFile(irr::io::path fp);
		void resetPmxFiles(int count=0);
		void connectSabas(int type, int nextInc);
		struct MMDInfo {
			saba::PMXInfo info;
		};
		void getInfo(io::path fp,MMDInfo &mi);
		//debug
		irr::core::vector3df dbgAddRt;
 
		irr::io::path vmdPaths[16];
		std::vector<irr::io::path> pmxPaths;
		irr::io::path camPath;
		int64_t owMmdTimeUs = -1, owMmdTimeUsLast=-1, owDeltaUs=-1;


		float owSetTime = -1.f;
		int owTimeDiv = 1, owTimeMul=100;
		bool autoPause=false;
		bool pauseDemoing = false;
		int  pauseIdx = 0;
		int pauseDemoFr = 0;
		bool updating = true;
		bool drawPauseDemo(irr::video::ITexture* texDemo);
		bool paraRender = MULTI_THREAD_RENDERING;
		bool LookOverride = false;
		int ikVisable = 0;
		bool hasBloom=false;
		core::vector3df lookAtPos;
		int outNormalMap = 0;
		irr::core::matrix4 baseMatrix;
		bool shaderNoColor = false;
		CsEditorState* cs{};
		saba::MMDNode* tempIKRoot{};
		irr::video::MmdDynamicParam shdpm;
		bool trgSetVelP2P = true;

		irr::scene::IrrSaba* camSb{};
		irr::scene::SabaHolder  camSbTgtSb;
		saba::MMDNode* camSbTgtNd{};
		bool renderingByCamSb = false; // discard too close fragment 
		ualib::UaLibContext* Ctx;
		glm::vec3 camPos{}, camPosLast{}, camVel;
		glm::mat4 camMat;
		bool syncMovNode = false;
		saba::MMDJoint* camRbFpvDmyJt{};
		saba::MMDRigidBody  * camRbFpvDmy{};

		//catcher
		IrrSaba* sbCatcher{}, * sbLastCatcher{};
		int catcherId = 0, catcherCount = 0;
		IrrSaba* sbTracker{};
		irr::scene::SnArRoot* arRoot{};
		MmdPhyAnimator* MPA{}, * mainMPA{};
		float rigForceMul = 100.f, rigPlaybackTimeMul=1.f;
		void createMrk();

		bool onKeyEvent(bool pressed, const irr::SEvent::SKeyInput& ke);
		bool onPointerEvent(const irr::SEvent::SPointInput& pe);
		void curCtrlSb(std::function<void(irr::scene::IrrSaba*)> cb);
		void createFluid();
	private:
		

		void rigSaveFile() { 
			MPA->saveToFile("data/rigFrSave.json"
			);
		}
 

		void rigUpdate();
		void rigSetInitNodes();
		void loadRigNode();

		bool animating = true;
		bool bRecreate = false;	
		int mmdMaxId = 0;




		
	};

	Json::Value mmdPoseToJson(saba::MMDModel* model);

	int mmdPoseFromJson(saba::MMDModel* model, const Json::Value& jsv);
}
