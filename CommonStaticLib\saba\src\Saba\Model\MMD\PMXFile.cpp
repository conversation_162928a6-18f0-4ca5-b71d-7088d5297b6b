﻿#include "PMXFile.h"
//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//
#include "mmdPCH.h"
#include "PMXFile.h"

#include <Saba/Base/Log.h>
#include <Saba/Base/File.h>
#include <Saba/Base/UnicodeUtil.h>

#include <vector>
#include <glm/ext/scalar_constants.hpp>
 
#include <jsoncpp/json5cpp.h>
#include <Helpers/glmUtils.h>

//see MMD_ADD_FINGER_RBS
 
// $`{json}
// $[name0.name1]   // defined in file english memo
namespace saba
{
	static size_t getFloats(const Json::Value& v, float* fv, uint32_t arrSize, float* defaultValue = nullptr)
	{
		uint32_t len = v.size();  
		if (len > arrSize) len = arrSize;
		for (uint32_t i = 0; i < arrSize; i++)
			if (i < len) fv[i] = v[i].asFloat();
			else if (defaultValue) fv[i] = *defaultValue;
		return len;
	}

	namespace
	{

		template <typename T>
		bool Read(T* val, File& file)
		{
			return file.Read(val);
		}

		template <typename T>
		bool Read(T* valArray, size_t size, File& file)
		{
			return file.Read(valArray, size);
		}

		bool ReadString(PMXFile* pmx, std::string* val, File& file)
		{
			uint32_t bufSize;
			if (!Read(&bufSize, file))
			{
				return false;
			}

			if (bufSize > 0)
			{
				if (pmx->m_header.m_encode == 0)
				{
					// UTF-16
					std::u16string utf16Str(bufSize / 2, u'\0');
					if (!file.Read(&utf16Str[0], utf16Str.size()))
					{
						return false;
					}
					if (!ConvU16ToU8(utf16Str, *val))
					{
						return false;
					}
				}
				else if (pmx->m_header.m_encode == 1)
				{
					// UTF-8
					std::string utf8Str(bufSize, '\0');
					file.Read(&utf8Str[0], bufSize);

					*val = utf8Str;
				}
				return !file.IsBad();
			}
			else 
				return false;			
		}

		bool ReadIndex(int32_t* index, uint8_t indexSize, File& file, int add=0)
		{
			switch (indexSize)
			{
			case 1:
			{
				uint8_t idx;
				Read(&idx, file);
				if (idx != 0xFF)
				{
					*index = (int32_t)idx;
				}
				else
				{
					*index = -1;
				}
			}
				break;
			case 2:
			{
				uint16_t idx;
				Read(&idx, file);
				if (idx != 0xFFFF)
				{
					*index = (int32_t)idx;
				}
				else
				{
					*index = -1;
				}
			}
				break;
			case 4:
			{
				uint32_t idx;
				Read(&idx, file);
				*index = (int32_t)idx;
			}
				break;
			default:
				return false;
			}
			*index += add;
			return !file.IsBad();
		}

		bool ReadBindIndex(int32_t* index, uint8_t indexSize, File& file, int add = 0)
		{
			switch (indexSize)
			{
			case 1:
			{
				uint8_t idx;
				Read(&idx, file);
				if (idx != 0xFF)
				{
					*index = (int32_t)idx;
				}
				else
				{
					*index = -1;
				}
			}
			break;
			case 2:
			{
				uint16_t idx;
				Read(&idx, file);
				if (idx != 0xFFFF)
				{
					*index = (int32_t)idx;
				}
				else
				{
					*index = -1;
				}
			}
			break;
			case 4:
			{
				uint32_t idx;
				Read(&idx, file);
				*index = (int32_t)idx;
			}
			break;
			default:
				return false;
			}
			if (*index>=0)
				*index += add;
			return !file.IsBad();
		}

		bool ReadHeader(PMXFile* pmxFile, File& file)
		{
			auto& header = pmxFile->m_header;

			Read(&header.m_magic, file);
			Read(&header.m_version, file);

			Read(&header.m_dataSize, file);

			Read(&header.m_encode, file);
			Read(&header.m_addUVNum, file);

			Read(&header.m_vertexIndexSize, file);
			Read(&header.m_textureIndexSize, file);
			Read(&header.m_materialIndexSize, file);
			Read(&header.m_boneIndexSize, file);
			Read(&header.m_morphIndexSize, file);
			Read(&header.m_rigidbodyIndexSize, file);

			return !file.IsBad();
		}

		bool ReadFace(PMXFile* pmx, File& file)
		{
			int32_t faceCount = 0;
			if (!Read(&faceCount, file))
			{
				return false;
			}
			faceCount /= 3;

			pmx->m_faces.resize(faceCount);

			switch (pmx->m_header.m_vertexIndexSize)
			{
			case 1:
			{
				std::vector<uint8_t> vertices(faceCount * 3);
				Read(vertices.data(), vertices.size(), file);
				for (int32_t faceIdx = 0; faceIdx < faceCount; faceIdx++)
				{
					pmx->m_faces[faceIdx].vtxId[0] = vertices[faceIdx * 3 + 0];
					pmx->m_faces[faceIdx].vtxId[1] = vertices[faceIdx * 3 + 1];
					pmx->m_faces[faceIdx].vtxId[2] = vertices[faceIdx * 3 + 2];
				}
			}
				break;
			case 2:
			{
				std::vector<uint16_t> vertices(faceCount * 3);
				Read(vertices.data(), vertices.size(), file);
				for (int32_t faceIdx = 0; faceIdx < faceCount; faceIdx++)
				{
					pmx->m_faces[faceIdx].vtxId[0] = vertices[faceIdx * 3 + 0];
					pmx->m_faces[faceIdx].vtxId[1] = vertices[faceIdx * 3 + 1];
					pmx->m_faces[faceIdx].vtxId[2] = vertices[faceIdx * 3 + 2];
				}
			}
				break;
			case 4:
			{
				std::vector<uint32_t> vertices(faceCount * 3);
				Read(vertices.data(), vertices.size(), file);
				for (int32_t faceIdx = 0; faceIdx < faceCount; faceIdx++)
				{
					pmx->m_faces[faceIdx].vtxId[0] = vertices[faceIdx * 3 + 0];
					pmx->m_faces[faceIdx].vtxId[1] = vertices[faceIdx * 3 + 1];
					pmx->m_faces[faceIdx].vtxId[2] = vertices[faceIdx * 3 + 2];
				}
			}
				break;
			default:
				return false;
			}

			return !file.IsBad();
		}

		bool ReadTexture(PMXFile* pmx, File& file)
		{
			int32_t texCount = 0;
			if (!Read(&texCount, file))
			{
				return false;
			}

			pmx->m_textures.resize(texCount);

			for (auto& tex : pmx->m_textures)
			{
				ReadString(pmx, &tex.m_textureName, file);
			}

			return !file.IsBad();
		}

		bool ReadMaterial(PMXFile* pmx, File& file)
		{
			int32_t matCount = 0;
			if (!Read(&matCount, file))
			{
				return false;
			}

			pmx->m_materials.resize(matCount);

			for (auto& mat : pmx->m_materials)
			{
				ReadString(pmx, &mat.m_name, file);
				ReadString(pmx, &mat.m_englishName, file);

				Read(&mat.m_diffuse, file);
				Read(&mat.m_specular, file);
				Read(&mat.m_specularPower, file);
				Read(&mat.m_ambient, file);

				Read(&mat.m_drawMode, file);

				Read(&mat.m_edgeColor, file);
				Read(&mat.m_edgeSize, file);

				ReadIndex(&mat.m_textureIndex, pmx->m_header.m_textureIndexSize, file);
				ReadIndex(&mat.m_sphereTextureIndex, pmx->m_header.m_textureIndexSize, file);
				Read(&mat.m_sphereMode, file);

				Read(&mat.m_toonMode, file);
				if (mat.m_toonMode == PMXToonMode::Separate)
				{
					ReadIndex(&mat.m_toonTextureIndex, pmx->m_header.m_textureIndexSize, file);
				}
				else if (mat.m_toonMode == PMXToonMode::Common)
				{
					uint8_t toonIndex;
					Read(&toonIndex, file);
					mat.m_toonTextureIndex = (int32_t)toonIndex;
				}
				else
				{
					return false;
				}

				ReadString(pmx, &mat.m_memo, file);

				Read(&mat.m_numFaceVertices, file);
			}

			return !file.IsBad();
		}


		bool ReadDisplayFrame(PMXFile* pmx, File& file)
		{
			int32_t displayFrameCount;
			if (!Read(&displayFrameCount, file))
			{
				return false;
			}

			pmx->m_displayFrames.resize(displayFrameCount);

			for (auto& displayFrame : pmx->m_displayFrames)
			{
				ReadString(pmx, &displayFrame.m_name, file);
				ReadString(pmx, &displayFrame.m_englishName, file);

				Read(&displayFrame.m_flag, file);
				int32_t targetCount;
				if (!Read(&targetCount, file))
				{
					return false;
				}
				displayFrame.m_targets.resize(targetCount);
				for (auto& target : displayFrame.m_targets)
				{
					Read(&target.m_type, file);
					if (target.m_type == PMXDispalyFrame::TargetType::BoneIndex)
					{
						ReadIndex(&target.m_index, pmx->m_header.m_boneIndexSize, file, (pmx->fcp.addRootBone));
					}
					else if (target.m_type == PMXDispalyFrame::TargetType::MorphIndex)
					{
						ReadIndex(&target.m_index, pmx->m_header.m_morphIndexSize, file);
					}
					else
					{
						return false;
					}
				}
			}

			return !file.IsBad();
		}


		bool ReadSoftbody(PMXFile* pmx, File& file)
		{
			int32_t sbCount;
			if (!Read(&sbCount, file))
			{
				return false;
			}

			pmx->m_softbodies.resize(sbCount);

			for (auto& sb : pmx->m_softbodies)
			{
				ReadString(pmx, &sb.m_name, file);
				ReadString(pmx, &sb.m_englishName, file);

				Read(&sb.m_type, file);

				ReadIndex(&sb.m_materialIndex, pmx->m_header.m_materialIndexSize, file);

				Read(&sb.m_group, file);
				Read(&sb.m_collisionGroup, file);

				Read(&sb.m_flag, file);

				Read(&sb.m_BLinkLength, file);
				Read(&sb.m_numClusters, file);

				Read(&sb.m_totalMass, file);
				Read(&sb.m_collisionMargin, file);

				Read(&sb.m_aeroModel, file);

				Read(&sb.m_VCF, file);
				Read(&sb.m_DP, file);
				Read(&sb.m_DG, file);
				Read(&sb.m_LF, file);
				Read(&sb.m_PR, file);
				Read(&sb.m_VC, file);
				Read(&sb.m_DF, file);
				Read(&sb.m_MT, file);
				Read(&sb.m_CHR, file);
				Read(&sb.m_KHR, file);
				Read(&sb.m_SHR, file);
				Read(&sb.m_AHR, file);

				Read(&sb.m_SRHR_CL, file);
				Read(&sb.m_SKHR_CL, file);
				Read(&sb.m_SSHR_CL, file);
				Read(&sb.m_SR_SPLT_CL, file);
				Read(&sb.m_SK_SPLT_CL, file);
				Read(&sb.m_SS_SPLT_CL, file);

				Read(&sb.m_V_IT, file);
				Read(&sb.m_P_IT, file);
				Read(&sb.m_D_IT, file);
				Read(&sb.m_C_IT, file);

				Read(&sb.m_LST, file);
				Read(&sb.m_AST, file);
				Read(&sb.m_VST, file);

				int32_t arCount;
				if (!Read(&arCount, file))
				{
					return false;
				}
				sb.m_anchorRigidbodies.resize(arCount);
				for (auto& ar : sb.m_anchorRigidbodies)
				{
					ReadIndex(&ar.m_rigidBodyIndex, pmx->m_header.m_rigidbodyIndexSize, file);
					//ar.m_rigidBodyIndex += INS_RB_NUM;
					ReadIndex(&ar.m_vertexIndex, pmx->m_header.m_vertexIndexSize, file);
					Read(&ar.m_nearMode, file);
				}

				int32_t pvCount;
				if (!Read(&pvCount, file))
				{
					return false;
				}
				sb.m_pinVertexIndices.resize(pvCount);
				for (auto& pv : sb.m_pinVertexIndices)
				{
					ReadIndex(&pv, pmx->m_header.m_vertexIndexSize, file);
				}
			}

			return !file.IsBad();
		}


	}


	//=========================================================================

	PMXFile::~PMXFile()
	{
		pJvRoot = nullptr;
		//free baMap
 
		baMap.clear();
	}

	bool PMXFile::ReadInfo(File& file)
	{
		PMXFile* pmx = this;
		auto& info = pmx->m_info;

		ReadString(pmx, &info.m_modelName, file);
		ReadString(pmx, &info.m_englishModelName, file);
		ReadString(pmx, &info.m_comment, file);	
		ReadString(pmx, &info.m_englishComment, file);
		if (extInfoTxt.size() > 10)
			info.m_englishComment=extInfoTxt;
		if (info.m_englishComment.size() > 10 && info.m_englishComment[0] == '$' && info.m_englishComment[1] == '{') //ALT+227
		{
			pJvRoot = std::make_shared<Json::Value>();
			if (!Json5::parse(info.m_englishComment.substr(1), *pJvRoot, nullptr))
			{
				pJvRoot = nullptr;
				throw std::runtime_error("PMXFile json5  error");
			}
		}


		{
			//if (info.m_englishComment.find("[allDynRb]") != std::string::npos)	pmx->allDynRb = 1;
		}

		return !file.IsBad();
	}




	void PMXFile::ScalePmxFile(float scale)
	{
		for (auto& v : m_vertices)
		{
			v.m_position *= scale;
		}
		for (auto& b : m_bones)
		{
			b.m_position *= scale;
			b.m_positionOffset *= scale;
		}
		for (auto& b : m_rigidbodies)
		{
			b.m_translate *= scale;
			b.m_shapeSize *= scale;		
		}
		for (auto& b : m_joints)
		{
			b.translate *= scale;
			b.limitMinT *= scale;
			b.limitMaxT *= scale;
		}
	}
	void PMXFile::ScalePmxFile(glm::vec3 scale)
	{
		auto absscale = abs(scale);
		glm::mat4 view;// = glm::translate(glm::mat4(1.0f), glm::vec3(0.0f, 0.0f, -2.0f));
		view = glm::scale(glm::mat4(1.0f), glm::vec3(-1.0f, 1.0f, 1.0f));

		for (auto& v : m_vertices)
		{
			v.m_position *= scale;
			//v.m_normal *= scale;
			if (v.m_weightType == PMXVertexWeight::SDEF)
			{
				v.m_sdefC  *= scale; v.m_sdefR0 *= scale; v.m_sdefR1 *= scale;
			}
		}
		for (auto& b : m_bones)
		{
			b.m_position *= scale;
			b.m_positionOffset *= scale;		
		//	b.m_fixedAxis *= scale;			b.m_localXAxis *= scale;			b.m_localZAxis *= scale;
		
		}
		for (auto& b : m_rigidbodies)
		{
			b.m_translate *= scale;
			b.m_shapeSize *= absscale;
			//b.m_rotate.y = glm::pi<float>() - b.m_rotate.y;
			//b.m_rotate.z = glm::pi<float>() - b.m_rotate.z;
		}
		for (auto& b : m_joints)
		{
			b.translate *= scale;
			b.limitMinT *= scale;  //to check
			b.limitMaxT *= scale;
			b.springT *= scale;
		}

		for (auto& m : m_morphs)
		{
			for (auto& pm : m.m_positionMorph)
			{
				pm.m_position *= scale;
			}
			for (auto& pm : m.m_boneMorph)
			{
				pm.m_position *= scale;
			}
		}
	}

	bool saba::PMXFile::ReadBone(File& file)
	{
		PMXFile* pmx = this;
		const static char headUtf8[4] = { char(256 - 23),char(256 - 96),char(256 - 83),'\0' };

#define BNAME3_D(X, S) const static int64_t X = *(int64_t*)ToUtf8String(S).c_str() & 0xFFFFFF
#define BNAME6_D(X, S) const static int64_t X = *(int64_t*)ToUtf8String(S).c_str() & 0xFFFFFFFFFFFF 
#define BNAME6_IS(X)  ((*(int64_t*)bone.m_name.c_str() & 0xFFFFFFFFFFFF) == X )
		//2个汉字
		BNAME6_D(eyes, L"両目");
		BNAME6_D(eyeL, L"左目");
		BNAME6_D(eyeR, L"右目");
		BNAME6_D(footL, L"左足");
		BNAME6_D(footR, L"右足");
		BNAME6_D(upper, L"上半");
		BNAME6_D(handL, L"左手");
		BNAME6_D(handR, L"右手");
		//BNAME3_D(chZhi, L"指");

		int32_t boneCount;
		if (!Read(&boneCount, file))
		{
			return false;
		}
		if (fcp.addRootBone)
			boneCount += 1;

		pmx->m_bones.resize(boneCount);

		if (fcp.addRootBone) {
			PMXBone& root = pmx->m_bones[0];
			root = PMXBone{};
			root.m_name = "newRoot";
			root.m_position = glm::vec3(0);
			root.m_parentBoneIndex = -1;
			root.m_boneFlag = AllowControl | AllowRotate | AllowTranslate | Visible;
			pmx->m_bones[0] = root;
		}
		Json::Value* pjbone{};
		if (pJvRoot) pjbone = &(*pJvRoot)["NodeStyles"];
		
		for (size_t i = fcp.addRootBone; i < pmx->m_bones.size(); i++)
		{
			PMXBone& bone = pmx->m_bones[i];
			ReadString(pmx, &bone.m_name, file);
			bone.m_nameU = ToWString(bone.m_name);
 
			if (ReadString(pmx, &bone.m_englishName, file)) {
				auto rn = bone.m_englishName;
				if (rn.size() > 2 && rn[0] == '$' && rn[1] == '`') {
						bone.pjv = new Json::Value();
						if (!Json5::parse(bone.m_englishName.substr(2), *bone.pjv, nullptr))
						{
							delete bone.pjv; bone.pjv = nullptr;
						}
					}
				if (pJvRoot)	setBoneJson(bone, pjbone);
			}
			Read(&bone.m_position, file);
			ReadIndex((int32_t*)&bone.m_parentBoneIndex, pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
 
			Read(&bone.m_deformDepth, file);
			//bone.m_deformDepth += defAdd;

			Read(&bone.m_boneFlag, file);

			if (((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::TargetShowMode) == 0)
			{
				Read(&bone.m_positionOffset, file);
			}
			else
			{
				ReadIndex(&bone.m_linkBoneIndex, pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				if (bone.m_linkBoneIndex-fcp.addRootBone==-1)
					bone.m_linkBoneIndex = -1; 
			}

			if (((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::AppendRotate) ||
				((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::AppendTranslate))
			{
				ReadIndex(&bone.m_appendBoneIndex, pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				Read(&bone.m_appendWeight, file);
			}

			if ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::FixedAxis)
			{
				Read(&bone.m_fixedAxis, file);
			}

			if ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::LocalAxis)
			{
				Read(&bone.m_localXAxis, file);
				Read(&bone.m_localZAxis, file);
			}

			if ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::DeformOuterParent)
			{
				Read(&bone.m_keyValue, file);
			}

			if ((uint16_t)bone.m_boneFlag & (uint16_t)PMXBoneFlags::IK)
			{
				ReadIndex(&bone.m_ikTargetBoneIndex, pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				Read(&bone.m_ikIterationCount, file);
				Read(&bone.m_ikLimit, file);

				int32_t linkCount;
				if (!Read(&linkCount, file))
				{
					return false;
				}

				bone.m_ikLinks.resize(linkCount);
				for (auto& ikLink : bone.m_ikLinks)
				{
					ReadIndex(&ikLink.m_ikBoneIndex, pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
					Read(&ikLink.m_enableLimit, file);

					if (ikLink.m_enableLimit != 0)
					{
						Read(&ikLink.m_limitMin, file);
						Read(&ikLink.m_limitMax, file);
					}
				}

				if (fcp.ikHandToArm1 && bone.m_ikLinks.size() > 3)
				{
					bone.m_ikTargetBoneIndex = pmx->m_bones[bone.m_ikTargetBoneIndex].m_parentBoneIndex;
					bone.m_ikLinks.erase(bone.m_ikLinks.begin());
				}

				//if (bone.m_name == "IKRightArm")
				//{
				//	ikArmRI = i;
				//}
			}


			if (!hdI && *(int*)bone.m_name.c_str() == *(int*)headUtf8) {
				hdI = i;
				rootI = i; 
				auto pi = m_bones[rootI].m_parentBoneIndex;
				while ( pi != -1)
				{
					rootI = pi; 
					pi = m_bones[rootI].m_parentBoneIndex;
				}
			}
			else
			{
				if (!eyesI && BNAME6_IS(eyes)) {
					eyesI = i;
				}
				else if (!eyeLI && BNAME6_IS(eyeL))
					eyeLI = i;
				else if (!eyeRI && BNAME6_IS(eyeR))
					eyeRI = i;
				else if (BNAME6_IS(footL)) {
					if (!footLI && bone.m_nameU == L"左足首") {
						footLI = i;
						leg1LI = m_bones[footLI].m_parentBoneIndex;
						legLI = m_bones[leg1LI].m_parentBoneIndex;
					}
					else if (!footLDI && bone.m_nameU == L"左足首D") {
						footLDI = i;
						leg1LDI = m_bones[footLDI].m_parentBoneIndex;
						legLDI = m_bones[leg1LDI].m_parentBoneIndex;
					}
				}
				else if (BNAME6_IS(footR)) {
					if (!footRI && bone.m_nameU == L"右足首") {
						footRI = i;
						leg1RI = m_bones[footRI].m_parentBoneIndex;
						legRI = m_bones[leg1RI].m_parentBoneIndex;
					}
					else if (!footRDI && bone.m_nameU == L"右足首D") {
						footRDI = i;
						leg1RDI = m_bones[footRDI].m_parentBoneIndex;
						legRDI = m_bones[leg1RDI].m_parentBoneIndex;
					}				
				}
				else if (BNAME6_IS(handL)) {
					if (!handLI && bone.m_nameU == L"左手首") {
						handLI = i;			
						bone.isHand = true;
					}
				}
				else if (BNAME6_IS(handR)) {
					if (!handRI && bone.m_nameU == L"右手首") {
						handRI = i;
						bone.isHand = true; bone.isHdR = 1;
					}
				}
				else if (BNAME6_IS(upper)) {
					if (!upperI && bone.m_nameU == L"上半身") {
						upperI = i; 
					}
					else if (!upper2I && bone.m_nameU == L"上半身2") {
						upper2I = i; 
					} 
				}
				else if (bone.m_parentBoneIndex<m_bones.size() && m_bones[bone.m_parentBoneIndex].isHand)
				{
					auto &pbone=m_bones[bone.m_parentBoneIndex];
					bone.isHand = true; bone.isHdR = pbone.isHdR;
					auto nu=&bone.m_nameU[0];
					bone.isFinger = bone.m_name.size()==12 && nu[2] == L'指' && uint32_t(nu[3]-L'０')<4u;
					
					if (bone.isFinger)
					{
						bone.fgI =  (nu[1] == L'親' ? 0 : nu[1] == L'人' ? 1 : nu[1] == L'中' ? 2 : nu[1] == L'薬' ?  3 : 4);
						bone.fgN = nu[3] - L'０' + (bone.fgI == 0 ? 1 : 0);
						assert(bone.fgI < 5 && bone.fgN < 4);
						fgIds[bone.isHdR][bone.fgI][bone.fgN] = i;
					}
					if (pbone.isFinger && pbone.child0==0) {
						pbone.child0 = i;
					}
					DPWCS((L"FG %s  %d , %d %d %d", nu, bone.isFinger, bone.isHdR,bone.fgI,bone.fgN));
				}
			}
		}

		//if (pmx->m_bones.size() > 20 && pmx->m_bones[pmx->m_bones.size() - 1].m_name != "subRoot")
		//{
		//	PMXBone b = pmx->m_bones[1];
		//	b.m_name = "subRoot";
		//	b.m_nameU = ToWString(b.m_name);
		//	b.m_position.y = 25;
		//	b.m_parentBoneIndex = -1;
		//	pmx->m_bones.push_back(b);
		//}

		if (hdI > 1) {// 頭IK
			int nextni = *(int*)pmx->m_bones[hdI + 1].m_englishName.c_str();
			if (nextni != 'daeH') {//'HeadTarget"

				PMXBone hdtgt = pmx->m_bones[hdI];
				hdtgt.m_name = "headTarget"; hdtgt.m_nameU=ToWString(hdtgt.m_name);
				hdtgt.m_position.z -= 1;// hdtgt.m_position.y += 0.2;
				hdtgt.m_parentBoneIndex = hdI;
				pmx->headTgtId = pmx->m_bones.size();
				pmx->headTgtBone = hdtgt;
				pmx->m_bones.push_back(hdtgt);

				PMXBone hdIK = pmx->m_bones[hdI];
				hdIK.m_name = "headIK";//"頭IK" 
				hdIK.m_nameU = ToWString(hdIK.m_name);
				hdIK.m_position.z -= 2;
				hdIK.m_ikTargetBoneIndex = pmx->m_bones.size() - 1;
				hdIK.m_boneFlag = (PMXBoneFlags)((uint16_t)hdIK.m_boneFlag | (uint16_t)PMXBoneFlags::IK);
				hdIK.m_ikIterationCount = 20;
				hdIK.m_ikLimit = 3 * 3.1415927 / 180;
				PMXIKLink ikLink{}; auto lki = hdI;
				for (int i = 0; i < 1; i++)
				{
					ikLink.m_ikBoneIndex = lki;
					hdIK.m_ikLinks.insert(hdIK.m_ikLinks.begin(), ikLink);
					lki = pmx->m_bones[lki].m_parentBoneIndex;
				}

				pmx->m_bones.push_back(hdIK);
			}

			if (eyesI>1 && eyeLI && eyeRI) 
			{
				pmx->m_bones[eyeLI].m_boneFlag |= PMXBoneFlags::DeformAfterPhysics;
				pmx->m_bones[eyeRI].m_boneFlag |= PMXBoneFlags::DeformAfterPhysics;
				PMXBone&  b = pmx->m_bones[eyesI];
				b.m_position.y = pmx->m_bones[eyeLI].m_position.y;
				//b.m_position.z = 0;
			}
 
			if (fcp.addSubRbBase && upper2I)
			{
				PMXBone b = pmx->m_bones[upper2I];
				b.m_name = "subRbBase";
				b.m_nameU = ToWString(b.m_name);
				//b.m_boneFlag |= PMXBoneFlags::DeformAfterPhysics;		b.m_parentBoneIndex = upperI;
				b.m_boneFlag |= PMXBoneFlags::DeformAfterPhysics | PMXBoneFlags::AppendTranslate | PMXBoneFlags::AppendRotate;				b.m_appendBoneIndex = upperI;
				subRbBaseI = pmx->m_bones.size(); pmx->m_bones.push_back(b);
			}
 
#if ADD_PINNODE
			if (upper2I)
			{	//handle
				PMXBone b = pmx->m_bones[rootI];
				b.m_boneFlag = (PMXBoneFlags)(
					PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate
					| PMXBoneFlags::AllowControl | PMXBoneFlags::Visible//| PMXBoneFlags::IK | PMXBoneFlags::Visible
					);

				b.m_name = "pinHandL";
				b.m_position = glm::vec3(0, 0, 0); 
				b.m_parentBoneIndex = rootI;	
				hdlLI = pmx->m_bones.size(); pmx->m_bones.push_back(b);				
				b.m_name = "pinHandR";
				b.m_position = glm::vec3(-0, 0, 0);
				b.m_parentBoneIndex = rootI;
				hdlRI = pmx->m_bones.size(); pmx->m_bones.push_back(b);
			}
#endif

			
		}
		//if (ikArmRI > 1)
		//{
		//	int fri = pmx->findBoneIdx(ToUtf8String(L"右人指３"));
		//	PMXBone hdtgt = pmx->m_bones[ikArmRI];
		//	hdtgt.m_position = pmx->m_bones[fri].m_position;
		//	hdtgt.m_name = "IKFgTgtR";				
		//	hdtgt.m_parentBoneIndex = ikArmRI - 1;
		//	hdtgt.m_boneFlag = (PMXBoneFlags)((uint16_t)hdtgt.m_boneFlag & (~(uint16_t)(PMXBoneFlags::IK | (uint16_t)PMXBoneFlags::AllowTranslate)));
		//	hdtgt.m_ikLinks.clear();
		//	pmx->m_bones.push_back(hdtgt);
		//	int idx = pmx->m_bones.size() - 1;
		//	hdtgt.m_name = "IKFingerR";
		//	hdtgt.m_parentBoneIndex = 1;
		//	hdtgt.m_boneFlag = (PMXBoneFlags)((uint16_t)hdtgt.m_boneFlag | (uint16_t)PMXBoneFlags::IK|(uint16_t)PMXBoneFlags::AllowTranslate);
		//	hdtgt.m_ikTargetBoneIndex = idx;
		//	hdtgt.m_ikLinks.push_back({ pmx->m_bones[ikArmRI].m_ikTargetBoneIndex });
		//	for (auto a: pmx->m_bones[ikArmRI].m_ikLinks)
		//	hdtgt.m_ikLinks.push_back(a);
		//	pmx->m_bones.push_back(hdtgt);
		//}

		return !file.IsBad();
	}

	void PMXFile::setBoneJson(saba::PMXBone& bone, Json::Value* pjbone)
	{		
		auto& rn = bone.m_englishName;
		if (pjbone && rn.size() > 2 && rn[0] == '$') {
			if (rn[1] == '=') {
				auto& jbone = *pjbone;
				auto styleName = rn.substr(2);
				if (baMap.find(styleName) == baMap.end()) {
					baMap[styleName] = std::make_shared<PMXBoneAnim>();
					auto& vb = jbone[styleName];

					PMXBoneAnim& ba = *baMap[styleName];
					if (!vb.isNull()) {
						float def = 0.f;
						auto& vbrtt = vb["rttSpeed"];
						if (vbrtt.isArray() && vbrtt.size() == 3) {
							ba.aniRtt = true;
							ba.rttSpeed = glm::radians(glm::vec3(vbrtt[0].asFloat(), vbrtt[1].asFloat(), vbrtt[2].asFloat()));
						}

						auto& vbflap = vb["flapAxis"];
						if (vbflap.isArray() && vbflap.size() == 3) {
							ba.aniFlap = true;

							getFloats(vbflap, (float*)&ba.flapAxis, 3, &def); ba.flapAxis = glm::fastNormalize(ba.flapAxis);
							getFloats(vb["flapAngTmul"], (float*)&ba.flapAngTmul, 3, &def);
							getFloats(vb["flapPM"], ba.flapPM, 8);
						}

						auto& vbTrsType = vb["trsType"];
						if (vbTrsType.isString()) {
							ba.aniTrs = true;
							//bone.aniJellyfish = true;
							ba.trsType = bttDefault;
							if (vbTrsType.asString() == "jellyfish") ba.trsType = bttJellyfish;
							else if (vbTrsType.asString() == "src2tgt") ba.trsType = bttSrcTgt;
							else if (vbTrsType.asString() == "track") ba.trsType = bttTrack;
							ba.trsTps = vb["trsTps"].asFloat();
							ba.trsFlag = vb["trsFlag"].asUInt();
							getFloats(vb["trsV0"], (float*)&ba.trsV0, 3, &def);
							getFloats(vb["trsV1"], (float*)&ba.trsV1, 3, &def);
							getFloats(vb["trsPM"], ba.trsPM, 8);
						}

						if (auto v = vb["ndFwCount"])	ba.ndFwCount = v.asInt();
						if (auto v = vb["ndFwName"])	ba.ndFwName = v.asString();
						if (auto v = vb["ndFwType"])	ba.ndFwType = v.asInt();
						if (auto v = vb["ndMatFw"])		ba.ndMatFw = v.asInt();
						if (auto v = vb["ndFwIntv"])	ba.ndFwIntv = v.asFloat();
						if (auto v = vb["ndFwVel"])		getFloats(vb["ndFwVel"], (float*)&ba.ndFwVel, 3, &def);
						if (auto v = vb["ndFwEmtSize"])	getFloats(vb["ndFwEmtSize"], (float*)&ba.ndFwEmtSize, 3, &def);

					}
					
				}
				bone.anim = baMap[styleName].get(); // is this ok?
			}
		}
		//else if (rn[1] == '`') {

		//	Json::Value pjv;
		//	if (Json5::parse(bone.m_englishName.substr(2), pjv, nullptr))
		//	{
		//		if (auto v=pjv["ndFwName"]) bone.ndFwName = v.asString(); 

		//	}

		//}
	}

	bool saba::PMXFile::ReadRigidbody(File& file)
	{
		PMXFile* pmx = this;
		int32_t rbCount;
		if (!Read(&rbCount, file))
		{
			return false;
		}

		pmx->m_rigidbodies.resize(rbCount);// + headTarget
		Json::Value* pjrb{};
		if (pJvRoot) pjrb = &(*pJvRoot)["RigidBodyStyles"];

		for (size_t i=0;i<m_rigidbodies.size();i++)
		{
			auto& rb = m_rigidbodies[i];
			ReadString(pmx, &rb.m_name, file);
			if (ReadString(pmx, &rb.m_englishName, file))	setRbByJson(rb, pjrb);			

			ReadBindIndex((int32_t*) & rb.m_boneIndex, pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));

			Read(&rb.m_group, file);
			uint16_t	collideMaskU16;
			Read(&collideMaskU16, file);
			rb.m_collideMask32 = collideMaskU16;
			Read(&rb.m_shape, file);
			Read(&rb.m_shapeSize, file);

			Read(&rb.m_translate, file);
			Read(&rb.m_rotate, file);

			Read(&rb.m_mass, file);
			Read(&rb.m_translateDimmer, file);
			Read(&rb.m_rotateDimmer, file);
			Read(&rb.m_repulsion, file);
			Read(&rb.m_friction, file);

			Read(&rb.m_op, file);
			if (rb.m_boneIndex > 0) {
				if (rb.m_boneIndex == hdI)
					hd_RbI = (int)i;
				else if (rb.m_boneIndex == eyesI) // model already has eye rb				
					pmx->eyesI = pmx->eyeRI = pmx->eyeLI = 0;
				else if (rb.m_boneIndex == footLI && footLDI)
					rb.m_boneIndex = footLDI;
				else if (rb.m_boneIndex == footRI && footRDI)
					rb.m_boneIndex = footRDI;
				else if (rb.m_boneIndex == legLI && legLDI)
					rb.m_boneIndex = legLDI;
				else if (rb.m_boneIndex == legRI && legRDI)
					rb.m_boneIndex = legRDI;
				else if (rb.m_boneIndex == leg1LI && leg1LDI)
					rb.m_boneIndex = leg1LDI;
				else if (rb.m_boneIndex == leg1RI && leg1RDI)
					rb.m_boneIndex = leg1RDI;				
				else if (rb.m_boneIndex<m_bones.size() && m_bones[rb.m_boneIndex].isHand)					
				{
					if (m_bones[m_bones[rb.m_boneIndex].m_parentBoneIndex].isHand)
						hasFingerRB = true; 
					if (m_bones[rb.m_boneIndex].isHdR) handR_RbI = (int)i; else handL_RbI = (int)i;
				}
			}
			//DPWCS((L"RB %s  td=%f", m_bones[rb.m_boneIndex].m_nameU.c_str(),rb.m_translateDimmer));
			//if (rb.m_englishName[0] == 'ã') //ALT+227
			//{
			//	if (rb.m_englishName.find("[dyn]") != std::string::npos) rb.flag |= EPmxRbFlag::forceDynRb;
			//}
		}
		


		if (fcp.addEyeRb &&eyesI ) 
		{
			PMXRigidbody rb{
			.m_name = "eyes",
			.m_boneIndex = (uint32_t)pmx->eyesI,
			.m_collideMask32 = 0,
			.m_shape = PMXRigidbody::Shape::Sphere,
			.m_shapeSize = glm::vec3(0.2f),
			.m_translate = pmx->m_bones[pmx->eyesI].m_position ,
			.m_rotate = glm::vec3(0),
			.m_mass = 0.2f,
			.m_repulsion = 0,
			.m_friction = 0.f,
			.m_op = PMXRigidbody::Operation::Static,// DynamicAndBoneMerge,
			.isEye = true,
			.dynMass= 0.1f,
			};
			//eyeL_RbI = (int)m_rigidbodies.size();
			m_rigidbodies.push_back(rb);
			//rb.m_name = "eyeR";
			//rb.m_boneIndex = (int)pmx->eyeRI;
			//rb.m_translate = pmx->m_bones[pmx->eyeRI].m_position;
			//eyeR_RbI = (int)m_rigidbodies.size();
			//pmx->m_rigidbodies.push_back(rb);
		}
 
		if (fcp.addSubRbBase && subRbBaseI)
		{
			PMXRigidbody rb{
				.m_name = "rb_subRbBase",
				.m_boneIndex =  (uint32_t)subRbBaseI,
				.m_collideMask32 = 0,
				.m_shape = PMXRigidbody::Shape::Sphere,
				.m_shapeSize = glm::vec3(1.f),
				.m_translate = pmx->m_bones[subRbBaseI].m_position,
				.m_rotate = glm::vec3(0),
				.m_mass = 0.f,
				.m_repulsion = 0,
				.m_friction = 0.f,
				.m_op = PMXRigidbody::Operation::Static,// DynamicAndBoneMerge,
				.dynMass= 0.f,
			};
			m_rigidbodies.push_back(rb);
		}
 
 
#if ADD_PINNODE
		if (hdlLI)
		{
			PMXRigidbody rb{
				.m_name = "rb_pinHandL",
				.m_boneIndex =  (uint32_t)hdlLI,
				.m_collideMask32 = 0,
				.m_shape = PMXRigidbody::Shape::Sphere,
				.m_shapeSize = glm::vec3(1.0f),
				.m_rotate = glm::vec3(0),
				.m_mass = 10000.f,
				.m_repulsion = 0,
				.m_friction = 0.f,
				.m_op = PMXRigidbody::Operation::Static,// DynamicAndBoneMerge,

				.dynMass = 0,
			};
			rb.m_translate = m_bones[rb.m_boneIndex].m_position;
			m_rigidbodies.push_back(rb);
		}
		if (hdlRI)
		{
			PMXRigidbody rb{
				.m_name = "rb_pinHandR",
				.m_boneIndex = (uint32_t)hdlRI,
				.m_collideMask32 = 0,
				.m_shape = PMXRigidbody::Shape::Sphere,
				.m_shapeSize = glm::vec3(1.0f),
				.m_rotate = glm::vec3(0),
				.m_mass = 10000.f,
				.m_repulsion = 0,
				.m_friction = 0.f,
				.m_op = PMXRigidbody::Operation::Static,// DynamicAndBoneMerge,

				.dynMass = 0,
			};
			rb.m_translate = m_bones[rb.m_boneIndex].m_position;
			m_rigidbodies.push_back(rb);
		}
#endif

		if (fcp.addFingerRbs && !hasFingerRB)
		{
			for (int i = 0; i < 2; i++) for (int j = 0; j < 5; j++) for (int k = 1; k <= 3; k++)
			{
				int boneId = fgIds[i][j][k];
				if (boneId > 0) fingerNdCount++;
				else continue;
				PMXBone& bone=m_bones[boneId]; 
				glm::vec3 tgt;
				if (bone.child0 > 0) {
					PMXBone& childBone = m_bones[bone.child0];
					tgt = childBone.m_position;
				}
				else {
					tgt = bone.m_position+bone.m_positionOffset;
				}
				
				//create rigidbody for bone;
				PMXRigidbody rb= m_rigidbodies[i==1? handR_RbI: handL_RbI];
				rb.m_shape = PMXRigidbody::Shape::Capsule;
				rb.m_boneIndex = boneId;
				rb.m_shapeSize.x = 0.1f - bone.fgN * 0.01f;
				rb.m_shapeSize.y = glm::length(bone.m_position - tgt)*1;
				rb.m_translate = (bone.m_position + tgt) * 0.5f;

				glm::quat rotationQuat =
					glm::rotation(glm::vec3(1.0f, 0.0f, 0.0f), glm::normalize(tgt - bone.m_position));

				rb.m_rotate = glm::eulerAngles(rotationQuat);

				glm::quat qr = rb.m_rotate;				
				qr = qr* glm::angleAxis(glm::half_pi<float>(), glm::vec3(0.0f, 0.0f,1.0f)) ;
				rb.m_rotate = glm::eulerAngles(qr);

				m_rigidbodies.push_back(rb);
			}
		}

		return !file.IsBad();
	}

	void PMXFile::setRbByJson(saba::PMXRigidbody& rb, Json::Value* pjrb)
	{
		auto& rn = rb.m_englishName;
		if (rn.size() > 2 && rn[0] == '$') {
			if (rn[1] == '`') {

				rb.pjv = new Json::Value();
				if (!Json5::parse(rb.m_englishName.substr(2), *rb.pjv, nullptr))
				{
					delete rb.pjv; rb.pjv = nullptr;
				}

			}
			else if (pjrb && rn[1] == '=') {

				auto& jbone = *pjrb;
				auto& vb = jbone[rn.substr(2)];
				if (!vb.isNull()) {
					rb.attachFlag = vb["anchorFlag"].asInt();
				}

					 
			}
		}
	}

	bool PMXFile::ReadMorph(File& file)
	{
		PMXFile* pmx = this;
#define ADD_MORPH 1
		int32_t morphCount;
		if (!Read(&morphCount, file))
		{
			return false;
		}


		pmx->m_morphs.resize(morphCount);

		for (auto& morph : pmx->m_morphs)
		{
			ReadString(pmx, &morph.m_name, file);
			ReadString(pmx, &morph.m_englishName, file);

			Read(&morph.m_controlPanel, file);
			Read(&morph.m_morphType, file);

			int32_t dataCount;
			if (!Read(&dataCount, file))
			{
				return false;
			}

			if (morph.m_morphType == PMXMorphType::Position)
			{
				morph.m_positionMorph.resize(dataCount);
				for (auto& data : morph.m_positionMorph)
				{
					ReadIndex(&data.m_vertexIndex, pmx->m_header.m_vertexIndexSize, file);
					Read(&data.m_position, file);
				}
			}
			else if (morph.m_morphType == PMXMorphType::UV ||
				morph.m_morphType == PMXMorphType::AddUV1 ||
				morph.m_morphType == PMXMorphType::AddUV2 ||
				morph.m_morphType == PMXMorphType::AddUV3 ||
				morph.m_morphType == PMXMorphType::AddUV4
				)
			{
				morph.m_uvMorph.resize(dataCount);
				for (auto& data : morph.m_uvMorph)
				{
					ReadIndex(&data.m_vertexIndex, pmx->m_header.m_vertexIndexSize, file);
					Read(&data.m_uv, file);
				}
			}
			else if (morph.m_morphType == PMXMorphType::Bone)
			{
				morph.m_boneMorph.resize(dataCount);
				for (auto& data : morph.m_boneMorph)
				{
					ReadBindIndex(&data.m_boneIndex, pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
					Read(&data.m_position, file);
					Read(&data.m_quaternion, file);
				}
			}
			else if (morph.m_morphType == PMXMorphType::Material)
			{
				morph.m_materialMorph.resize(dataCount);
				for (auto& data : morph.m_materialMorph)
				{
					ReadIndex(&data.m_materialIndex, pmx->m_header.m_materialIndexSize, file);
					Read(&data.m_opType, file);
					Read(&data.m_diffuse, file);
					Read(&data.m_specular, file);
					Read(&data.m_specularPower, file);
					Read(&data.m_ambient, file);
					Read(&data.m_edgeColor, file);
					Read(&data.m_edgeSize, file);
					Read(&data.m_textureFactor, file);
					Read(&data.m_sphereTextureFactor, file);
					Read(&data.m_toonTextureFactor, file);
				}
			}
			else if (morph.m_morphType == PMXMorphType::Group)
			{
				morph.m_groupMorph.resize(dataCount);
				for (auto& data : morph.m_groupMorph)
				{
					ReadIndex(&data.m_morphIndex, pmx->m_header.m_morphIndexSize, file);
					Read(&data.m_weight, file);
				}
			}
			else if (morph.m_morphType == PMXMorphType::Flip)
			{
				morph.m_flipMorph.resize(dataCount);
				for (auto& data : morph.m_flipMorph)
				{
					ReadIndex(&data.m_morphIndex, pmx->m_header.m_morphIndexSize, file);
					Read(&data.m_weight, file);
				}
			}
			else if (morph.m_morphType == PMXMorphType::Impluse)
			{
				morph.m_impulseMorph.resize(dataCount);
				for (auto& data : morph.m_impulseMorph)
				{
					ReadIndex(&data.m_rigidbodyIndex, pmx->m_header.m_rigidbodyIndexSize, file);
					//data.m_rigidbodyIndex += INS_RB_NUM;
					Read(&data.m_localFlag, file);
					Read(&data.m_translateVelocity, file);
					Read(&data.m_rotateTorque, file);
				}
			}
			else
			{
				SABA_ERROR("Unsupported Morph Type:[{}]", (int)morph.m_morphType);
				return false;
			}
		}
		if (ADD_MORPH)
		{
			PMXMorph morph{};
			morph.m_name = "fw1";
			morph.m_morphType = PMXMorphType::Group;
			pmx->m_morphs.push_back(morph);
		}
		return !file.IsBad();
	}


	bool PMXFile::ReadVertex(File& file)
	{
		PMXFile* pmx = this;
		int32_t vertexCount;
		if (!Read(&vertexCount, file))
		{
			return false;
		}

		auto& vertices = pmx->m_vertices;
		vertices.resize(vertexCount);
		for (auto& vertex : vertices)
		{
			Read(&vertex.m_position, file);
			Read(&vertex.m_normal, file);
			Read(&vertex.m_uv, file);

			for (uint8_t i = 0; i < pmx->m_header.m_addUVNum; i++)
			{
				Read(&vertex.m_addUV[i], file);
			}

			Read(&vertex.m_weightType, file);

			switch (vertex.m_weightType)
			{
			case PMXVertexWeight::BDEF1:
				ReadIndex(&vertex.m_boneIndices[0], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				break;
			case PMXVertexWeight::BDEF2:
				ReadIndex(&vertex.m_boneIndices[0], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				ReadIndex(&vertex.m_boneIndices[1], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				Read(&vertex.m_boneWeights[0], file);
				break;
			case PMXVertexWeight::BDEF4:
				ReadIndex(&vertex.m_boneIndices[0], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				ReadIndex(&vertex.m_boneIndices[1], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				ReadIndex(&vertex.m_boneIndices[2], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				ReadIndex(&vertex.m_boneIndices[3], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				Read(&vertex.m_boneWeights[0], file);
				Read(&vertex.m_boneWeights[1], file);
				Read(&vertex.m_boneWeights[2], file);
				Read(&vertex.m_boneWeights[3], file);
				break;
			case PMXVertexWeight::SDEF:
				ReadIndex(&vertex.m_boneIndices[0], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				ReadIndex(&vertex.m_boneIndices[1], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				Read(&vertex.m_boneWeights[0], file);
				Read(&vertex.m_sdefC, file);
				Read(&vertex.m_sdefR0, file);
				Read(&vertex.m_sdefR1, file);
				break;
			case PMXVertexWeight::QDEF:
				ReadIndex(&vertex.m_boneIndices[0], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				ReadIndex(&vertex.m_boneIndices[1], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				ReadIndex(&vertex.m_boneIndices[2], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				ReadIndex(&vertex.m_boneIndices[3], pmx->m_header.m_boneIndexSize, file, (fcp.addRootBone));
				Read(&vertex.m_boneWeights[0], file);
				Read(&vertex.m_boneWeights[1], file);
				Read(&vertex.m_boneWeights[3], file);
				Read(&vertex.m_boneWeights[4], file);
				break;
			default:
				return false;
			}
			Read(&vertex.m_edgeMag, file);
		}

		return !file.IsBad();
	}

	bool saba::PMXFile::ReadJoint( File& file)
	{
		PMXFile* pmx = this;
		int32_t jointCount;
		if (!Read(&jointCount, file))
		{
			return false;
		}

		pmx->m_joints.resize(jointCount);

		for (int i = 0; i < jointCount; i++)
		{

			auto& joint = pmx->m_joints[i];
			joint.breakMassMul = 0.f;

			ReadString(pmx, &joint.m_name, file);
			ReadString(pmx, &joint.m_englishName, file);

			Read(&joint.m_type, file);
			ReadIndex(&joint.m_rigidbodyAIndex, pmx->m_header.m_rigidbodyIndexSize, file);
			ReadIndex(&joint.m_rigidbodyBIndex, pmx->m_header.m_rigidbodyIndexSize, file);
			//joint.m_rigidbodyAIndex += INS_RB_NUM;
			//joint.m_rigidbodyBIndex += INS_RB_NUM;
			Read(&joint.translate, file);
			Read(&joint.rotate, file);

			Read(&joint.limitMinT, file);
			Read(&joint.limitMaxT, file);
			Read(&joint.limitMinR, file);
			Read(&joint.limitMaxR, file);

			Read(&joint.springT, file); 
			Read(&joint.springR, file); 
			joint.springT *= 100.f;			joint.springR *= 100.f;
			float dampDivT = 32.f, dampDivR=100.f;
			if (joint.m_englishName[0] == '$' && joint.m_englishName[1] == '`') //ALT+227
			{
				Json::Value v;
				if (Json5::parse(joint.m_englishName.substr(2), v, nullptr))
				{
					if (v.isMember("breakMassMul"))
						joint.breakMassMul = v["breakMassMul"].asFloat();
					auto v1 = v["lockPos"]; if (!v1.isNull()) joint.lockPos = v1.asInt();
					if (v.isMember("dampDivT")) 
						dampDivT = v["dampDivT"].asFloat();
					if (v.isMember("dampDivR")) dampDivR = v["dampDivR"].asFloat();

				}
				//if (joint.m_englishName.find("[fix]") != std::string::npos)	joint.m_type = PMXJoint::JointType::Fixed;
			}

			joint.dampingT = dampDivT > 9999.f?glm::vec3(0):glm::vec3(
				joint.springT.x > 1.f ? joint.springT.x / dampDivT : 0.f,
				joint.springT.y > 1.f ? joint.springT.y / dampDivT : 0.f,
				joint.springT.z > 1.f ? joint.springT.z / dampDivT : 0.f
			);

			joint.dampingR = dampDivR > 9999.f ? glm::vec3(0) : glm::vec3(
				joint.springR.x > 1.f ? joint.springR.x / dampDivR : 0.f,
				joint.springR.y > 1.f ? joint.springR.y / dampDivR : 0.f,
				joint.springR.z > 1.f ? joint.springR.z / dampDivR : 0.f
			);
			//joint.dampingR = glm::vec3(10);
			
		}

		
#if 0 //dyn auto add
		if (fcp.addEyeRb && pmx->eyeL_RbI && pmx->eyeR_RbI) {
			PMXJoint jt{
			.m_name = "eyeL_hd",
			.m_rigidbodyAIndex = (int)hd_RbI,
			.m_rigidbodyBIndex = eyeL_RbI,
			.m_translate = pmx->m_bones[pmx->eyeLI].m_position,
			.m_rotate = glm::vec3(0),
			.m_translateLowerLimit{0.f,0.f,0.f},
			.m_translateUpperLimit{0.f,0.f,0.f},
			.m_rotateLowerLimit{glm::radians(vec3(-6, -19, 0))},
			.m_rotateUpperLimit{glm::radians(vec3(-6, 19, 0))},
			.springTranslateFactor{0,0,0},
			.m_springR{0,0,0},
			};
			
			pmx->m_joints.push_back(jt);
			jt.m_name = "eyeR_hd";
			jt.m_rigidbodyBIndex = (int)eyeR_RbI;
			jt.m_translate = pmx->m_bones[pmx->eyeRI].m_position;
			pmx->m_joints.push_back(jt);
		}
#endif
		return !file.IsBad();
	}

 
	void PMXFile::copyVertexBoneInfoFrom(const PMXFile* src, float maxDis) {
		if (!src || src->m_vertices.empty() || m_vertices.empty()) {
			SABA_WARN("Cannot copy vertex bone info - invalid source or empty vertices");
			return;
		}
		bool *needCopy = new bool[m_vertices.size()];
		memset(needCopy, 0, sizeof(bool) * m_vertices.size());

		// Set needCopy flags based on material memo
		size_t faceOffset = 0;
		for (const auto& mat : m_materials) {
			if (mat.m_memo=="{copyWeight}") {
				// Get vertex indices for this material's faces
				for (size_t i = 0; i < mat.m_numFaceVertices / 3; ++i) { // Divide by 3 for triangle count
					size_t faceIdx = faceOffset + i;
					if (faceIdx < m_faces.size()) {
						// Mark all three vertices of the triangle
						for (int j = 0; j < 3; j++) {
							size_t vertexIndex = m_faces[faceIdx].vtxId[j];
							if (vertexIndex < m_vertices.size()) {
								needCopy[vertexIndex] = true;
							}
						}
					}
				}
			}
			faceOffset += mat.m_numFaceVertices / 3; // Advance by number of triangles
		}


		const size_t numThreads = std::thread::hardware_concurrency();
		const size_t verticesPerThread = m_vertices.size() / numThreads;
		std::vector<std::thread> threads;
		float maxDis2 = maxDis * maxDis;
		auto processVertexRange = [this, src,maxDis2, needCopy](size_t start, size_t end) {
			for (size_t vi = start; vi < end; vi++) {				
				auto& dstVertex = m_vertices[vi];
				if (!needCopy[vi]) continue;
				float minDist = std::numeric_limits<float>::max();
				const PMXVertex* closestVertex = nullptr;

				// Find closest vertex in source PMX
				for (const auto& srcVertex : src->m_vertices) {
					float dist = glm::distance2(dstVertex.m_position, srcVertex.m_position);
					if (dist < minDist) {
						minDist = dist;
						closestVertex = &srcVertex;
					}
				}

				if (minDist> maxDis2) {
					continue;
				}

				// Copy bone weights and indices
				dstVertex.m_weightType = closestVertex->m_weightType;
				
				switch (dstVertex.m_weightType) {
				case PMXVertexWeight::BDEF1:
					dstVertex.m_boneIndices[0] = closestVertex->m_boneIndices[0];
					break;

				case PMXVertexWeight::BDEF2:
					dstVertex.m_boneIndices[0] = closestVertex->m_boneIndices[0];
					dstVertex.m_boneIndices[1] = closestVertex->m_boneIndices[1];
					dstVertex.m_boneWeights[0] = closestVertex->m_boneWeights[0];
					break;

				case PMXVertexWeight::BDEF4:
					for (int i = 0; i < 4; i++) {
						dstVertex.m_boneIndices[i] = closestVertex->m_boneIndices[i];
						dstVertex.m_boneWeights[i] = closestVertex->m_boneWeights[i];
					}
					break;

				case PMXVertexWeight::SDEF:
					dstVertex.m_boneIndices[0] = closestVertex->m_boneIndices[0];
					dstVertex.m_boneIndices[1] = closestVertex->m_boneIndices[1];
					dstVertex.m_boneWeights[0] = closestVertex->m_boneWeights[0];
					dstVertex.m_sdefC = closestVertex->m_sdefC;
					dstVertex.m_sdefR0 = closestVertex->m_sdefR0;
					dstVertex.m_sdefR1 = closestVertex->m_sdefR1;
					break;

				default:
					SABA_WARN("Unknown vertex weight type: {}", (int)dstVertex.m_weightType);
					break;
				}
			}
		};

		// Create and launch threads
		for (size_t i = 0; i < numThreads - 1; i++) {
			size_t start = i * verticesPerThread;
			size_t end = (i + 1) * verticesPerThread;
			threads.emplace_back(processVertexRange, start, end);
		}

		// Process remaining vertices in the main thread
		processVertexRange((numThreads - 1) * verticesPerThread, m_vertices.size());

		// Wait for all threads to complete
		for (auto& thread : threads) {
			thread.join();
		}

		SABA_INFO("Copied vertex bone information from source PMX ({} vertices) using {} threads", 
			src->m_vertices.size(), numThreads);
	}
 
	bool ReadPMXFile(PMXFile* pmxFile, File& file)
	{
		if (!ReadHeader(pmxFile, file))
		{
			SABA_ERROR("ReadHeader Fail.");
			return false;
		}

		if (!pmxFile->ReadInfo( file))
		{
			SABA_ERROR("ReadInfo Fail.");
			return false;
		}

		if (!pmxFile->ReadVertex(file))
		{
			SABA_ERROR("ReadVertex Fail.");
			return false;
		}

		if (!ReadFace(pmxFile, file))
		{
			SABA_ERROR("ReadFace Fail.");
			return false;
		}

		if (!ReadTexture(pmxFile, file))
		{
			SABA_ERROR("ReadTexture Fail.");
			return false;
		}

		if (!ReadMaterial(pmxFile, file))
		{
			SABA_ERROR("ReadMaterial Fail.");
			return false;
		}

		if (!pmxFile->ReadBone(file))
		{
			SABA_ERROR("ReadBone Fail.");
			return false;
		}

		if (!pmxFile->ReadMorph(file))
		{
			SABA_ERROR("ReadMorph Fail.");
			return false;
		}

		if (!ReadDisplayFrame(pmxFile, file))
		{
			SABA_ERROR("ReadDisplayFrame Fail.");
			return false;
		}

		if (!pmxFile->ReadRigidbody(file))
		{
			SABA_ERROR("ReadRigidbody Fail.");
			return false;
		}

		if (!pmxFile->ReadJoint(file))
		{
			SABA_ERROR("ReadJoint Fail.");
			return false;
		}

		if (file.Tell() < file.GetSize())
		{
			if (!ReadSoftbody(pmxFile, file))
			{
				SABA_ERROR("ReadSoftbody Fail.");
				return false;
			}
		}

		return true;
	}
	bool ReadPMXFile(PMXFile* pmxFile, const char* filename)
	{
		File file;
		if (!file.Open(filename))
		{
			SABA_INFO("PMX File Open Fail. {}", filename);
			return false;
		}
		File fileJson5;
		if (fileJson5.Open(std::string(filename) + ".json5"))
		{
			
			auto bufSize=fileJson5.GetSize();
			std::string utf8Str(bufSize+1, '\0');
			fileJson5.Read(&utf8Str[0], bufSize);
			pmxFile->extInfoTxt= utf8Str;
		}
		pmxFile->realPath = file.openedPath;
		pmxFile->fileArchive = file.getArchive();
		if (!ReadPMXFile(pmxFile, file))
		{
			SABA_INFO("PMX File Read Fail. {}", filename);
			return false;
		}
		SABA_INFO("PMX File Read Successed. {}", filename);

		if (pmxFile->fcp.duplicateAndRotatePass) {
			pmxFile->DuplicateCombine();

		}
		 
		 
		return true;
	}

	// Add this implementation after the ScalePmxFile methods

 
	
}
