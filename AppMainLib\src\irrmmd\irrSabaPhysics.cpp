﻿#include "AppGlobal.h"
#include "irrSaba.h"
//AMP  single thread render  31fps 2ms 10 xl.zip  paused=770fps 


#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtx/matrix_decompose.hpp>
#include <glm/gtx/euler_angles.hpp>
#include <glm/gtx/vector_angle.hpp>

#include <stlUtils.h>
#include <Saba/Model/MMD/MMDNode.h>
#include <Saba/Model/MMD/MMDModel.h>
#include <Saba/Model/MMD/MMDPhysics.h>
#include <Saba/Base/File.h>
#include "cppIncDefine.h"
#include <UaUtils.h>
#include <helpers/UpUtils.h>
#include <irrfw/eqv/EQV.h>
#include "IrrMMD.h"
#include "PhyObjMan.h"

#define MMD_PHYSICAL_ANIMATION  1

#define RAG_DOLL				0

#if IS_WIN_DBG
#define MMDFWD(T,N,P, V, C) 		do{ static int fid = Eqv->getFwIdxByFwIdStr(T,N); mmdFw(P, fid, V , C);}while(0) //FID will not change at runtime
#else
#define MMDFWD( ) 
#endif
#define MMDFW(T,N,P, V, C) 		do{ static int fid = Eqv->getFwIdxByFwIdStr(T,N); mmdFw(P, fid, V , C);}while(0) //FID will not change at runtime
#define MMDFWID(fid,P, V, C) 		do{  mmdFw(P, fid, V , C);}while(0) //FID will not change at runtime

using namespace glm;
using namespace uu;
using namespace irr;
using namespace irr::scene;
using namespace irr::video;
using namespace irr::core;
using namespace saba;
using namespace ualib;
using namespace EQVisual;

void irr::scene::IrrSaba::startRagDoll(RagDollParam pm)
{
	if (!RAG_DOLL) return;
	curRdp = pm;
	rd.ratio = 0.f;
	if (rd.stage == -1) rd.totalTime = 0;
	rd.stage = 0;
	rd.timer = curRdp.dur0;
	rd.stageTime = 0.f;
	rd.upt0 = curRdp.dur0 >= 1.5f ? 0.2 : 0.0;
	rd.fint = curRdp.dur0 >= 1.5f ? 0.5 : 0.035;
	rd.moveLeg = 0;
	MMDFWD(2, "sw21s", ndYao->rbPos(), vec3(0, 0, 30), SColorf(1, curRdp.dur0 / 3, 0, 1));
}


void irr::scene::IrrSaba::phyAnimationUpdate(float stepTime, int step, int stepCount)
{
	auto phyStepTime = stepTime;// *MMDPhysics::phyTimeMul;
#if MMD_PHYSICAL_ANIMATION	
	//if (!Pmx->isCharacter) return;

	const float upT = rd.upt0, finT = rd.fint;
	rd.totalTime += phyStepTime;
	if (rd.stage == 0) {  //ragdoll
		rd.timer -= phyStepTime; rd.stageTime += phyStepTime;
		if (rd.timer <= 0) { //起身动作
			float durOnDis = ndYao->rb0->disTo(ndYao->getRbAnimGlobalPos()) / 30.f;
			curRdp.dur1 = glm::max(curRdp.dur1, rd.fint + durOnDis);
			rd.stage = 1;	rd.timer = curRdp.dur1;	rd.stageTime = 0.f;
		}
		else
		{
			rd.ratio = (rd.stageTime = curRdp.dur0 - rd.timer) / curRdp.dur0;
			float standT = 0.1f;
			if (rd.totalTime > 0.02f && rd.totalTime < 0.2f)
			{
				if (rd.moveLeg == 0) {
					vec3 yav = ndYao->rb0->vel;
					float aL = glh::angleRadBetweenVec((ndFootL->rb0->pos - ndYao->rb0->pos) * vec3(1, 0, 1), yav);
					float aR = glh::angleRadBetweenVec((ndFootR->rb0->pos - ndYao->rb0->pos) * vec3(1, 0, 1), yav);
					rd.moveLeg = (aL > aR) ? 2 : 1;
				}
				bool moveR = rd.moveLeg == 2;
				auto ndf = moveR ? ndFootR : ndFootL, ndo = moveR ? ndFootL : ndFootR;
				vec3 tr = ndf->mGlobalInit[3];
				tr.y = 0; tr *= 3.97f; tr = (ndYao->transformVec(tr));
				MMDFWD(2, "sw1s", tr, vec3(0, 0, 0), SColorf(1, rd.ratio * 3, 0, 1));
				vec3 dir = glm::normalize(ndYao->rb0->vel) * 5.f + tr;
				auto tgt = ndYao->rb0->pos + dir * ndYao->absScale.x; tgt.y = rd.ratio < standT ? (0.1f - rd.totalTime) * 10 : 0;
				ndf->rb0->scaleVel(0.3f, 3); ndf->rb0->addLinearVelToPos(tgt, 60 * (1 - rd.ratio));
				mat4 rtm = glm::toMat4(glm::quat(glm::eulerAngles(ndf->rb0->getRotation()) * vec3(0, 1, 1)));

				ndUpper2->rb0->addLinearVel(vec3(0, 10, 0));
				//ndf->rb0->parentRb->ReflectGlobalTransform();
				//ndf->rb0->ReflectGlobalTransform();
				if (ndf->GetChild() && ndf->GetChild()->rb0) ndf->GetChild()->rb0->setRotateTo(rtm);
				//ndf->rb0->setAngVelToRotateOnNode(rtm, 100);
				//if (rd.ratio >= standT)			ndf->GetParent()->rb0->addLinearVelToPos(tgt+vec3(0.f,3.f*ndYao->absScale.x,0.f), 3);
				MMDFWD(2, "sw1s", tgt, vec3(0, 0, 0), SColorf(moveR, rd.ratio * 3, 0, 1));
			}
		}
	}
	if (rd.stage == 1)
	{
		rd.timer -= phyStepTime;
		if (rd.timer <= 0) { rd.stage = -1;	rd.ratio = 1.f; }
		else
		{
			rd.ratio = (rd.stageTime = curRdp.dur1 - rd.timer) / curRdp.dur1;
		}
	}

	if (rd.ratio < 1.f)
	{
		if (curRdp.getUp && rd.stage == 1 && rd.stageTime >= 0.f && rd.stageTime < finT)
		{
			//if (ndYao->rbPos().y > (ndYao->mGlobalAnim * ndYao->rb0->getOfsMat())[3].y * 3.0f && rd.stageTime < upT) rd.stageTime = finT;

			ndYao->rb0->scaleVel(0.97, 3);
			if (rd.stageTime > upT) {
				MMDFWD(2, "sw21s", ndYao->rbPos(), vec3(0, 100, 0), SColorf(1, curRdp.dur0 / 3, 0, 1));
				Pmx->addBodyVel(vec3(0, 10, 0) + (ndYao->getRbAnimGlobalPos() - ndYao->rbPos()) * 0.3f);
				ndYao->rb0->addLinearVel(vec3(0, 25, 0));
				ndUpper2->rb0->addLinearVel(vec3(0, 15, 0));
			}

			ndHandL->rb0->addLinearVel(vec3(0, (rd.stageTime < upT ? 10 : -200), 0));
			ndHandR->rb0->addLinearVel(vec3(0, (rd.stageTime < upT ? 10 : -200), 0));
			ndFootL->rb0->addLinearVel(vec3(0, (rd.stageTime < upT ? 10 : -200), 0));
			ndFootR->rb0->addLinearVel(vec3(0, (rd.stageTime < upT ? 10 : -200), 0));
		}
	}

	int idYao = ndRbRoot->GetIndex();


	int i = itemIdx;
	auto& plr = Pm.mmd->mdplr;
	auto& cd = plr.chd[itemIdx];

	//if (ndYao->phyAnim) ndYao->rb0->SetCoMTransform(ndYao->mGlobalAnim * ndYao->rb0->getOfsMat());
	auto nm = Pmx->GetNodeManager();

	float animul = rd.ratio * rd.phyAniMul;
	float animulR = rd.ratio * rd.phyAniMul;
	if (Pmx->vmdAction && adAnmId< Pmx->vmdAddAnims.size())
	{
		auto& adanm = Pmx->vmdAddAnims[adAnmId];
		float at = gSceneTime - Pmx->vas[0].start;
		float dur = adanm.vmd->GetMaxKeyTimeFrameDeprecated() / 30.f;
		float mr = blendInOutAtTtoDmT(at, 0.3, dur);
		animulR = animul = animul + (1 - animul) * mr * 3;
		Pmx->phyActAnimating = at < dur;

		//if (at>adanm.vi.actB && at<adanm.vi.actE) animulR = animul = animul*0.75f;
#if 0
		phyLookMul = at < dur ? 0 : std::min(1.f, phyLookMul + 1 / 30.f);
#else
		phyLookHeadAngMul = at > adanm.vi.actT && at < dur + 0.5f ? 1.33 : 1;
		lookAt_mmdLookAt = at < dur;
		if (auto lo = Pom->getLastObj())	mmdLookAt = lo->getGlobalPos();

		if (lookAt_mmdLookAt) {
			sbFw2LineD("sw", ndHead->rbPos(), mmdLookAt, 0x2FFFFFFF, 30);
		}
#endif
	}

	for (int i = idYao + 0; i < nm->GetNodeCount(); i++) {
		bool isYao = i == idYao;
		auto n = nm->GetMMDNode(i);

		float amrat = std::min(1.f, animul);

		if (!(n == ndHead && (canLookAtCam || Pmx->vmdAction)))
			if (n->phyAnim == 1 && n->rb0 && n->rb0->dynRbType && (rd.stage<0 || rd.stage == 1 && rd.stageTime  > upT))
			{
				//DPWCS((L"MMD_PHYSICAL_ANIMATION %s", n->GetNameU().c_str()));
				//n->rb0->setRotateTo(n->rb0->parentRb->GetTransform()* n->rb0->parentRb->getOfsMatInv()* n->GetLocalTransform()* n->rb0->getOfsMat()
				mat4 mt = n->mGlobalAnim;
				auto rb = n->rb0;
				auto prb = ndRbRoot->rb0; //rb->parentRb; ////
				vec3 glbAniPos = (n->mGlobalAnim * n->rb0->getOfsMat())[3];
				if (!isYao && prb && rb->rbTreeLevel <= phyMatAniTreeLevel) {
					mt = prb->getNodeTransform();
					MMDNode* pnd = prb->node, * nd = rb->node->GetParent();
					mat4 m = nd->GetLocalTransform();
					while (nd != pnd) {
						m = nd->GetLocalTransform() * m; nd = nd->GetParent();
					}
					mt = mt * m;
					n->rb0->scaleVel(0.87, 2);
					n->rb0->addRotationToMatOnNode(mt, 100 * animulR);
					//n->rb0->setLinearVelToPos((mt * n->rb0->getOfsMat())[3], 20 * phyAniMul);
				}
				else
				{

					if (rd.ratio < 1.f) {
						n->rb0->addRotationToMatOnNode(n->mGlobalAnim, 200 * animulR * animulR);


						if (rd.stageTime > finT) {
							n->rb0->scaleVel(.5f + 0.5f * (1 - rd.ratio * amrat));
							n->rb0->addLinearVelToPos(glbAniPos, 10 * animul * animul);
						}
					}
					else {
						if (localPhyAnim)
						{

							if (isYao && (localPhyAnimFlag&1)) {
								core::matrix4 mm = n->mGlobalAnim;// rb0->getNodeTransform();
								auto t = mm.getTranslation();
								auto r = mm.getRotationDegrees();
								auto s = mm.getScale();
								DP((" %f %f s=%f", t.x, r.x, s.x));
								rb->addRotationToMatOnNode(n->mGlobalAnim, 20000 * animulR);
							}
							auto m = n->rb0->getNodeTransformByParentRB(0 ? ndRbRoot : 0);


#if JOINT_ANIM
							//auto j = n->rb0->jtsToParent[0];
							//glm::vec3 t, rtt;glm::quat r;
							//auto prn = (MMDNode*)n->rb0->parentRb->node;
							//auto pn = n->GetParent();
							//glm::mat4 qr(1); qr = n->GetLocalTransform();//;
							//while (pn != prn) { qr = (pn->GetLocalTransform()) * qr; pn = pn->GetParent(); }
							////qr = prn->localRtt * qr;
							//j->getLocalFrame(0, t, r);  r = qr;//r.w = -r.w;
							//j->setLocalFrame(0, t, r);
							//j->setDriveMul(1000);
#endif
							int lvl = rb->rbTreeLevel + (n->lowerNd ? 2 : 0);
							if (lvl > 3) {

								float rblvl = std::clamp(lvl / 3.f, 0.f, 1.f);
								float scrat = localPhyAnimToPos ? 0.1f : 0.01f;
								//n->rb0->scaleVel((1 - scrat) + rd.phyVelScaleOnAnim * scrat * (0.7f + 0.3f * rblvl) * (1 - amrat), 1);
								scrat = 0.5f;
								n->rb0->scaleVel((1 - scrat) + rd.phyVelScaleOnAnim * scrat * (0.7f + 0.3f * rblvl) * (1 - amrat), 2);

								m = m * n->rb0->getOfsMat();

								if (localPhyAnimToPos) {
									n->rb0->setLinearVelToPos(m[3], 100 * animul * n->phyAnimRatT);									
								}
								//n->rb0->addRotationToMatOnNode(m, 10000 * animulR * n->phyAnimRatR);
								
								//DPWCS((L"PA %10s	%d %d	 ", n->GetNameU().c_str(), rb->rbTreeLevel, n->lowerNd));
								auto jt = n->rb0->jtsToParent[0];
								jt->Pm.dampingT = vec3(100.f);
								jt->Pm.dampingR = vec3(100.f);
								jt->setDrivePose(n->mLocalAnim
									//*(n==ndArm1L || n == ndArm1R||1?glm::mat4_cast(glm::quat(jt->Pm.rotate)):glm::mat4(1))
									, 3, (lvl<2?500000.f:300000.f) * animulR * n->phyAnimRatR);
								//n->rb0->SetCoMTransform(m, 0);
							}
							else if (!phyObj) {
								DPWCS((L"RN %10s	 %d %d ", n->GetNameU().c_str(), rb->rbTreeLevel, n->lowerNd));
								//n->rb0->scaleVel(.95f,2);
								if (1) {
									float scr = rd.scRatioMin + rd.phyVelScaleOnAnim * (1 - rd.scRatioMin) * (0.7f) * (1 - std::min(1.f, amrat * n->phyAnimRatR));
									auto pos = n->rb0->getPosition(); pos.y = glbAniPos.y;
									//n->rb0->addLinearVelToPosLimitDis(glbAniPos, 1,0,10);
									// n->rb0->scaleVel3(vec3(1, scr, 1), 1);
									//n->rb0->scaleVel(0.f,2);
									//n->rb0->addLinearVel(vec3(0,glbAniPos.y-pos.y,0)*100.f );
									//n->rb0->SetCoMTranslate(pos);
									
									//n->rb0->addRotationToMatOnNode_MatRttResetXZ(n->rb0->getNodeTransform(), 2000 * animulR * n->phyAnimRatR, 0, 0); 
									//else n->rb0->addRotationToMatOnNode_MatRttResetXZ(n->rb0->getNodeTransform(), n->mGlobalAnim, 1000 * animulR * n->phyAnimRatR);

								}
								else if (rb->rbTreeLevel > 0) {
									m = n->mGlobalAnim;

									n->rb0->addRotationToMatOnNode(m, 1000 * animulR * n->phyAnimRatR * (1 - lvl * 0.1f));
									m = m * n->rb0->getOfsMat();
									if (rb->rbTreeLevel == 0)
										n->rb0->addLinearVelToPos(m[3], 90 * animul * n->phyAnimRatT * (1 - lvl * 0.1f));
									//n->rb0->SetCoMTransform(m, 0);
								}
							}
							//n->rb0->addLinearVelToPos(glbAniPos, 2 * animul);
						}
						else
						{
							if (rb->rbTreeLevel && n->rb0->jtsToParent.size()) n->rb0->jtsToParent[0]->setDriveMul(1);
							float rblvl = std::clamp(rb->rbTreeLevel / 3.f, 0.f, 1.f);
							if (n->isFoot) {
							//	DP(("f"));
							}
							float scr =  rd.scRatioMin + rd.phyVelScaleOnAnim * (1- rd.scRatioMin) * (0.7f + 0.3f * rblvl) * (1 - std::min(1.f, amrat * n->phyAnimRatR));
							n->rb0->scaleVel(scr);
							//n->rb0->addLinearVelToPos(glbAniPos, 25 * animul * n->phyAnimRatT);
							n->rb0->addLinearVelToPosLimitDis(glbAniPos, (n->isFoot ? 20 : 10) * animul * n->phyAnimRatT, 0.f, 10.f, 1.f);
							//n->rb0->addLinearVelToPosLimitDis2(glbAniPos, (n->isFoot ? 20 : 10)* animul* n->phyAnimRatT, 0.f, 10.f);
							n->rb0->addRotationToMatOnNode(n->mGlobalAnim, (n->isFoot ? 1000 : 300) * animulR * n->phyAnimRatR, n->isFoot);

							//if (rb->rbTreeLevel < 3) { DPWCS((L"ITEM %d %10s %.3f %.3f %.3f %.3f ", getItemIdx(), n->GetNameU().c_str(), animul, n->phyAnimRatT, animulR, n->phyAnimRatR)); }
						}

						//n->rb0->SetCoMTransform(n->mGlobalAnim * n->rb0->getOfsMat());

					}
				}
			}
	}

#endif
}

void irr::scene::IrrSaba::spring(uint32_t flag, int sprMode, float mul)
{
#define TO_HIT_LIKE		0
#define BEAT_MUL		3
#define SINE_MUL		0		//3
	float modelMul = 1.0f * mul;
	float deg = 30;// 30;
	bool ctrl = Ctx->getEvtRcv()->IsKeyDown(KEY_LCONTROL);
	bool shft = Ctx->getEvtRcv()->IsKeyDown(KEY_LSHIFT);

	//static int frcc = 0;	if (frcc++ % 2==1) return;
#if 1
	//if (!shft)
	//{
	//	ndUpper2->rb0->addLinearVel(vec3(0, 1, 0) * (20.f));
	//	Pmx->addBodyVel(vec3(0, 1, 0) * (3.0f));
	//}
	//ndHandL->rb0->addLinearVel(vec3(0, 1, 0) * (-30.f));
	//ndHandR->rb0->addLinearVel(vec3(0, 1, 0) * (-30.f));
	if (flag & 1) {

		float dis = ndYao->disTo(mmdLookAt), mul = std::clamp(dis / 20, 0.01f, 3.f) * modelMul;


		switch (springMode)
		{
		default: {
#if TO_HIT_LIKE
			vector3df yaopin(0, -1, 0), rhpin(0, 3, 2), lhpin(2, -1, 6), rfpin(-9, -1, -3), lfpin(9, -3, -3);
#else

			float t = gPhyTime * core::PI * 2;
			bool disFarY = ndYao->rbPos().y < mmdLookAt.y / 2;
			float disy = disFarY ? dis / 3 + 10 : dis / 10;
			vector3df yaopin(0, (dis / 10 - 1) * 3, 0), rhpin(-3, 3, 6), lhpin(3, 3, 6),
				rfpin(+1 - dis / 10, -1 - disy, 5 + sin(t) * SINE_MUL + cos(beatVal * core::PI) * BEAT_MUL),
				lfpin(-1 + dis / 10, -1 - disy, 5 - sin(t) * SINE_MUL - cos(beatVal * core::PI) * BEAT_MUL),
				toeR(+1 - dis / 10, 0 - disy, 3),
				toeL(-1 + dis / 10, 0 - disy, 3)
				;

			DP(("dis %f, disy %f", dis, disy));
#endif
			float leg1len = -ndFootL->GetTranslate().y, leg1a = glm::radians(10.f), leg1ay = sin(leg1a) * leg1len, leg1az = cos(leg1a) * leg1len;
			vector3df leg1L(lfpin + vec3(1, leg1ay, leg1az)), leg1R(rfpin + vec3(-1, leg1ay, leg1az));
			mtCam.transformVect(yaopin); mtCam.transformVect(rhpin); mtCam.transformVect(lhpin); mtCam.transformVect(rfpin); mtCam.transformVect(lfpin);
			mtCam.transformVect(leg1L); mtCam.transformVect(leg1R); //mtCam.transformVect(toeL); mtCam.transformVect(toeR);
#if 0
			getRb0()->scaleVel(0.9f);
			if (pinOnRoot(getRb0()->GetTransform())) {
				ndPinHandL->rb0->SetCoMTranslate(ndHandL->rb0->getPosition());
				ndPinHandR->rb0->SetCoMTranslate(ndHandR->rb0->getPosition());
				if (ndPinFootL) ndPinFootL->rb0->SetCoMTranslate(ndFootL->rb0->getPosition());
				if (ndPinFootR) ndPinFootR->rb0->SetCoMTranslate(ndFootR->rb0->getPosition());
			}
			else {
				ndPinHandL->rb0->setTranslateRate(vec3(lhpin), 0.16f, 0.1f, 1.5f);
				ndPinHandR->rb0->setTranslateRate(vec3(rhpin), 0.16f, 0.1f, 1.5f);
				if (ndPinFootL) ndPinFootL->rb0->setTranslateRate(vec3(lfpin), 0.16f, 0.1f, 1.f);
				if (ndPinFootR) ndPinFootR->rb0->setTranslateRate(vec3(rfpin), 0.16f, 0.1f, 1.f);
			}
			MMDFW(1, "sw", lhpin, {}, SColorf(1, 0, 0, 1));
			MMDFW(1, "sw", rhpin, {}, SColorf(0, 1, 0, 1));
#elif 1	
			ndUpper2->scaleVel(0.7f, 1, 6, 1);

#if TO_HIT_LIKE
			if (mul > 0) Pmx->scaleBodyVel(0.96f, 3);
			ndHandL->rb0->addLinearVel((lhpin - ndHandL->rb0->getPosition()) * std::max(10.f, mul * 20 / std::min(dis, 10.f)));
			ndHead->rb0->setAngVelToPos(mmdCamPos, 100, 1, 180.f);
			ndFootL->rb0->addLinearVelToPos(vec3(6, 0, 0), dis / 10);
			ndFootR->rb0->addLinearVelToPos(vec3(-6, 0, 0), dis / 10);
			ndUpper2->rb0->addLinearVelToPos(mmdCamPos, dis / 6);
			if (!shft && dis > 10) Pmx->addBodyVel((mmdCamPos - ndYao->rbPos()) * ((dis * (0.003f) + 0.01f) * modelMul));

#else

			//ndYao->rb0->addLinearVelToPos(yaopin, mul/10);
			ndYao->rb0->addRotationToMatOnNode(mtCam, 100.f);
			ndUpper2->rb0->addLinearVelToPos(mmdLookAt, mul * 2);
			ndHandL->rb0->addLinearVelToPos(lhpin, mul * 2);
			ndHandR->rb0->addLinearVelToPos(rhpin, mul * 2);
			ndLeg1L->rb0->addLinearVelToPos(leg1L, mul * (disFarY ? 6 : 1) * 100 / pow(ndFootL->disTo(lfpin), 2.f));
			ndLeg1R->rb0->addLinearVelToPos(leg1R, mul * (disFarY ? 6 : 1) * 100 / pow(ndFootR->disTo(rfpin), 2.f));

			ndFootL->rb0->addLinearVelToPos(lfpin, mul * (disFarY ? 6 : 1) * 200 / pow(ndFootL->disTo(lfpin), 2.f));
			ndFootR->rb0->addLinearVelToPos(rfpin, mul * (disFarY ? 6 : 1) * 200 / pow(ndFootR->disTo(rfpin), 2.f));
			auto ftmt = mtCam * glm::rotate(glm::mat4(1), glm::radians(90.f), glm::vec3(1, 0, 0));
			//ndLeg1L->rb0->addRotationToMatOnNode(mat3(ftmt), 2500.f/dis/dis);
			//ndLeg1R->rb0->addRotationToMatOnNode(mat3(ftmt), 2500.f/dis/dis);
			if (!disFarY) {

				ndFootL->rb0->addRotationToMatOnNode((ftmt), 2500.f / dis / dis);
				ndFootR->rb0->addRotationToMatOnNode((ftmt), 2500.f / dis / dis); ndFootL->scaleVel(0.8, 3); ndFootR->scaleVel(0.8, 3);
				if (ndToeL)
				{
					ndToeL->rb0->addRotationToMatOnNode(ftmt * glm::rotate(glm::mat4(1), glm::radians(sin(t) * 90), glm::vec3(1, 0, 0)), 200.f);
					ndToeR->rb0->addRotationToMatOnNode(ftmt * glm::rotate(glm::mat4(1), -glm::radians(sin(t) * 90), glm::vec3(1, 0, 0)), 200.f);
				}
			}

			//if (!shft) Pmx->addBodyVel((mmdCamPos - ndYao->rbPos()) * ((dis * (0.003f) + 0.003f) * modelMul));
#endif

#endif

#endif
		}break;
		case 1: {
			float t = gPhyTime * core::PI * 2;
			bool disFarY = ndYao->rbPos().y < mmdLookAt.y / 2;
			float disy = disFarY ? dis / 3 + 10 : dis / 10;
			vector3df yaopin(0, (dis / 10 - 1) * 3, 0),
				rhpin(+1 - dis / 10, -1 - disy, 3 + sin(t) * SINE_MUL + cos(beatVal * core::PI) * BEAT_MUL),
				lhpin(-1 + dis / 10, -1 - disy, 3 - sin(t) * SINE_MUL - cos(beatVal * core::PI) * BEAT_MUL)
				;

			DP(("dis %f, disy %f", dis, disy));

			float leg1len = -ndFootL->GetTranslate().y, leg1a = glm::radians(10.f), leg1ay = sin(leg1a) * leg1len, leg1az = cos(leg1a) * leg1len;
			vector3df arm1L(lhpin + vec3(1, leg1ay, leg1az)), arm1R(rhpin + vec3(-1, leg1ay, leg1az));
			mtCam.transformVect(yaopin); mtCam.transformVect(rhpin); mtCam.transformVect(lhpin);
			mtCam.transformVect(arm1L); mtCam.transformVect(arm1R); //mtCam.transformVect(toeL); mtCam.transformVect(toeR);

			ndUpper2->scaleVel(0.7f, 1, 6, 1);



			//ndYao->rb0->addLinearVelToPos(yaopin, mul/10);
			ndYao->rb0->addRotationToMatOnNode(mtCam, 100.f);
			ndUpper2->rb0->addLinearVelToPos(mmdLookAt, mul * 2);

			ndArm1L->rb0->addLinearVelToPos(arm1L, mul * (disFarY ? 6 : 3) * 100 / pow(dis, 2.f));
			ndArm1R->rb0->addLinearVelToPos(arm1R, mul * (disFarY ? 6 : 3) * 100 / pow(dis, 2.f));
			ndHandL->rb0->addLinearVelToPos(lhpin, mul * (disFarY ? 6 : 3) * 200 / pow(dis, 2.f));
			ndHandR->rb0->addLinearVelToPos(rhpin, mul * (disFarY ? 6 : 3) * 200 / pow(dis, 2.f));
			auto ftmt = mtCam * glm::rotate(glm::mat4(1), glm::radians(90.f), glm::vec3(1, 0, 0));
			//ndLeg1L->rb0->addRotationToMatOnNode(mat3(ftmt), 2500.f/dis/dis);
			//ndLeg1R->rb0->addRotationToMatOnNode(mat3(ftmt), 2500.f/dis/dis);
			if (!disFarY) {

				ndHandL->rb0->addRotationToMatOnNode((ftmt), 2500.f / dis / dis);
				ndHandR->rb0->addRotationToMatOnNode((ftmt), 2500.f / dis / dis); ndFootL->scaleVel(0.8, 3); ndFootR->scaleVel(0.8, 3);

			}
		}break;
		case 2: {
			float t = gPhyTime * core::PI * 2;
			bool disFarY = ndYao->rbPos().y < mmdLookAt.y / 2;
			float disy = disFarY ? dis / 3 + 10 : dis / 10;
			vector3df yaopin(0, (dis / 10 - 1) * 3, 0),
				rhpin(+1 - dis / 10 + cos(beatVal * core::PIx2) * BEAT_MUL / 2, 1 - disy + cos(beatVal * core::PI / 3), 3 + sin(t) * SINE_MUL + cos(beatVal * core::PI) * BEAT_MUL / 2),
				lhpin(-1 + dis / 10 - cos(beatVal * core::PIx2) * BEAT_MUL / 2, 1 - disy + cos(beatVal * core::PI / 3), 3 - sin(t) * SINE_MUL - cos(beatVal * core::PI) * BEAT_MUL / 2)
				;

			DP(("dis %f, disy %f", dis, disy));

			float leg1len = -ndFootL->GetTranslate().y, leg1a = glm::radians(10.f), leg1ay = sin(leg1a) * leg1len, leg1az = cos(leg1a) * leg1len;
			vector3df arm1L(lhpin + vec3(1, leg1ay, leg1az)), arm1R(rhpin + vec3(-1, leg1ay, leg1az));
			mtCam.transformVect(yaopin); mtCam.transformVect(rhpin); mtCam.transformVect(lhpin);
			mtCam.transformVect(arm1L); mtCam.transformVect(arm1R); //mtCam.transformVect(toeL); mtCam.transformVect(toeR);

			ndUpper2->scaleVel(0.7f, 1, 6, 1);



			//ndYao->rb0->addLinearVelToPos(yaopin, mul/10);
			ndYao->rb0->addRotationToMatOnNode(mtCam, 100.f);
			ndUpper2->rb0->addLinearVelToPos(mmdLookAt, mul * 2);

			ndArm1L->rb0->addLinearVelToPos(arm1L, mul * (disFarY ? 6 : 3) * 100 / pow(dis, 2.f));
			ndArm1R->rb0->addLinearVelToPos(arm1R, mul * (disFarY ? 6 : 3) * 100 / pow(dis, 2.f));
			ndHandL->rb0->addLinearVelToPos(lhpin, mul * (disFarY ? 6 : 3) * 200 / pow(dis, 2.f));
			ndHandR->rb0->addLinearVelToPos(rhpin, mul * (disFarY ? 6 : 3) * 200 / pow(dis, 2.f));
			auto ftmt = mtCam * glm::rotate(glm::mat4(1), glm::radians(90.f), glm::vec3(1, 0, 0));
			//ndLeg1L->rb0->addRotationToMatOnNode(mat3(ftmt), 2500.f/dis/dis);
			//ndLeg1R->rb0->addRotationToMatOnNode(mat3(ftmt), 2500.f/dis/dis);
			if (!disFarY) {

				ndHandL->rb0->addRotationToMatOnNode((ftmt), 2500.f / dis / dis);
				ndHandR->rb0->addRotationToMatOnNode((ftmt), 2500.f / dis / dis); ndFootL->scaleVel(0.8, 3); ndFootR->scaleVel(0.8, 3);

			}
		}break;
		case 3: {
			float t = gPhyTime * core::PI * 2;
			bool disFarY = ndYao->rbPos().y < mmdLookAt.y / 2;
			float disy = disFarY ? dis / 3 + 10 : dis / 10;
			vector3df yaopin(0, (dis / 10 - 1) * 3, 0),
				rhpin(+1 - dis / 10 + cos(beatVal * core::PIx2) * BEAT_MUL / 2, 1 - disy + cos(beatVal * core::PI / 3), 3 + sin(t) * SINE_MUL + cos(beatVal * core::PI) * BEAT_MUL / 2),
				lhpin(-1 + dis / 10 - cos(beatVal * core::PIx2) * BEAT_MUL / 2, 1 - disy + cos(beatVal * core::PI / 3), 3 - sin(t) * SINE_MUL - cos(beatVal * core::PI) * BEAT_MUL / 2)
				;

			DP(("dis %f, disy %f", dis, disy));

			float leg1len = -ndFootL->GetTranslate().y, leg1a = glm::radians(10.f), leg1ay = sin(leg1a) * leg1len, leg1az = cos(leg1a) * leg1len;
			vector3df arm1L(lhpin + vec3(1, leg1ay, leg1az)), arm1R(rhpin + vec3(-1, leg1ay, leg1az));
			mtCam.transformVect(yaopin); mtCam.transformVect(rhpin); mtCam.transformVect(lhpin);
			mtCam.transformVect(arm1L); mtCam.transformVect(arm1R); //mtCam.transformVect(toeL); mtCam.transformVect(toeR);

			ndUpper2->scaleVel(0.7f, 1, 6, 1);



			ndYao->rb0->addLinearVelToPos(yaopin, mul / 2);
			ndYao->rb0->addRotationToMatOnNode(mtCam, 100.f);
			ndUpper2->rb0->addLinearVelToPos(mmdLookAt, mul * 2);

			ndArm1L->rb0->addLinearVelToPos(arm1L, mul * (disFarY ? 6 : 3) * 100 / pow(dis, 2.f));
			ndArm1R->rb0->addLinearVelToPos(arm1R, mul * (disFarY ? 6 : 3) * 100 / pow(dis, 2.f));
			ndHandL->rb0->addLinearVelToPos(lhpin, mul * (disFarY ? 6 : 3) * 200 / pow(dis, 2.f));
			ndHandR->rb0->addLinearVelToPos(rhpin, mul * (disFarY ? 6 : 3) * 200 / pow(dis, 2.f));
			auto ftmt = mtCam * glm::rotate(glm::mat4(1), glm::radians(90.f), glm::vec3(1, 0, 0));
			//ndLeg1L->rb0->addRotationToMatOnNode(mat3(ftmt), 2500.f/dis/dis);
			//ndLeg1R->rb0->addRotationToMatOnNode(mat3(ftmt), 2500.f/dis/dis);
			if (!disFarY) {

				ndHandL->rb0->addRotationToMatOnNode((ftmt), 2500.f / dis / dis);
				ndHandR->rb0->addRotationToMatOnNode((ftmt), 2500.f / dis / dis); ndFootL->scaleVel(0.8, 3); ndFootR->scaleVel(0.8, 3);

			}
		}break;
		}
	}
	if (flag & 2)
	{
		mpA->appendMorph(1, 0, 0.3f);
		//ndHead->rb0->addLinearVelToPos(mmdCamPos, 10.f);		ndHead->rb0->addLinearVel(ndYao->rb0->getRotation() * vec3(1, 0, 0) * (-(mmdCamPos - ndHead->rbPos())) * 2.f);
		if (ndHead->rb0->pos.y > ndYao->rb0->pos.y + 2) {

			//ndHead->rb0->addLinearVel(-(mmdCamPos - ndHead->rbPos()) * 3.f * modelMul);
			//ndYao->rb0->addLinearVel((mmdCamPos - ndHead->rbPos()) * 1.f * modelMul);

			ndYao->rb0->setAngVelToPos(mmdLookAt, 50.f);
		}


		vec3 rtt(3.0, 0, 0);
		float f = float(-1000.f / Ctx->gd.timeMul);
		ndHead->rb0->addTorqueLocal(rtt * f);
		ndUpper2->rb0->addTorqueLocal(rtt * f * 2.f);
		ndYao->rb0->addTorqueLocal(rtt * f);


	}
	if (flag & 0x10) {
		auto a = springAng += stepTime * mul;
		quat r = ndYao->rb0->getNodeTransform();
		//ndFootL->rb0->scaleVel(0.9f); ndFootL->rb0->addLinearVel(((r)*vec3( 1, -0.0+sin(a)*0,  cos(a)*0) )* (10.f));
		//ndFootR->rb0->scaleVel(0.9f); ndFootR->rb0->addLinearVel(((r)*vec3(-1, -0.0+sin(a)*0,  cos(a)*0) )* (10.f));
		float ad = 60 * (sin(a)), ad1 = abs(3 * cos(a)), addX = -90;//-60
#if 0
		ndLegL->rb0->addRotationToMatOnNode(glm::rotate(mat4(1), glm::radians(addX - ad), vec3(1, 0, 0)), 200);
		ndLegR->rb0->addRotationToMatOnNode(glm::rotate(mat4(1), glm::radians(addX + ad), vec3(1, 0, 0)), 200);
#else
		ndLegL->joint2p()->setRttDegAdd(vec3{ addX - ad,0, -ad1 })->lockRtt(true);
		ndLegR->joint2p()->setRttDegAdd(vec3{ addX + ad,0, ad1 })->lockRtt(true);

		//ndLower->joint2p()->setRttDegAdd(vec3{-20,0,0 })->lockRtt(true);
		//ndUpper->joint2p()->setRttDegAdd(vec3{ 20,0,0 })->lockRtt(true);
		//ndUpper2->joint2p()->setRttDegAdd(vec3{ 20,0,0 })->lockRtt(true);

		//ndArmR->joint2p()->setRttDegAdd(vec3{ -30 - ad,60, 0 * sin(a) })->lockRtt(true);
		//ndArmL->joint2p()->setRttDegAdd(vec3{ -30 + ad,-60,-0 * sin(a) })->lockRtt(true);
#endif
		//ndLeg1L->joint2p()->setRttDegAdd(vec3{ 0,0,0 })->lockRtt(true);
		//ndLeg1R->joint2p()->setRttDegAdd(vec3{ 0,0,0 })->lockRtt(true);
		ndYao->rb0->setAngVelToRotateOnNode(glm::mat4_cast(glm::quat(vec3(PI / 2, 0, 0))), 30);
		ndUpper2->rb0->setAngVelToRotateOnNode(glm::mat4_cast(glm::quat(vec3(PI / 2, 0, 0))), 300);

	}
	if (flag & 0x20) {
		ndLegL->rb0->jtsToParent[0]->lockRtt(false);
		ndLegR->rb0->jtsToParent[0]->lockRtt(false);
		//ndArmR->rb0->jtsToParent[0]->lockRtt(false);
		//ndArmL->rb0->jtsToParent[0]->lockRtt(false);
		//ndLeg1L->joint2p()->lockRtt(false);
		//ndLeg1R->joint2p()->lockRtt(false);
	}

#if 0
	mpWa->appendMorph(1, 0.3f);
	mpO->appendMorph(1, 0.3f);
	if (mpPsy) mpPsy->appendMorph(1.1f, 0.3f);
	//Pmx->addBodyVel(float3( 0, 20, 0 )-getRb0()->getPosition());
	for (int i = 0; i < (shft ? 1 : 1); i++) {

		auto nd = ndYao;
		auto dir = (glm::quat(nd->GetGlobalTransform()) * glm::vec3(0, 0, ctrl ? 1 : -1));
		if (ndPussy && ctrl) {
			nd = ndPussy;  dir = glm::quat(ndPussy->GetGlobalTransform()) * (ndPussy->posOffset * (ctrl ? -1.f : 1.f));
		}
		ndYao->rb0->addLinearVel(dir * (28.f));
		if (shft) {
			ndFootL->rb0->addLinearVel(dir * 10.f + vec3(30, -38, -10));
			ndFootR->rb0->addLinearVel(dir * 10.f + vec3(-30, -38, -10));
		}
		else {
			ndFootL->rb0->addLinearVel(dir * 10.f + vec3(30, 0, 0));
			ndFootR->rb0->addLinearVel(dir * 10.f + vec3(-30, 0, 0));
		}
		//ndUpper2->scaleVel(0.1f, 2, 10);
		if (glm::length(getRb0()->vel) < 3.f) {
			//Pmx->addBodyVel(glm::normalize(float3(0, 20, 0) - getRb0()->getPosition()) * 200.f);
		}
		if (getRb0()->getPosition().y < 10) {
			//ndUpper2->rb0->addLinearVel(glm::vec3(0, 300, 0));
		}
		for (int i = 0; i < 16; i++) {
			if (ndPussy && !ctrl) {

				auto dir = glm::quat(ndPussy->GetGlobalTransform()) * glm::quat(vec3(deg * core::DEGTORAD * UaRandm1to1(), 0, 0)) * (ndPussy->posOffset * (ctrl ? -1.f : 1.f));
				mmdFw(ndPussy->getGlobalPos() + nd->rb0->vel * Ctx->gd.deltaTime, Eqv->getFwIdxByFwIdStr(1, "springFw"), dir * 20.f + nd->rb0->vel, SColorf(0xCFFFFFFF));
			}

		}
		//mmdFw(ndYao->getGlobalPos(), Eqv->getFwIdxByFwIdStr(1, "hitFw"), -dir * 60.f + ndYao->rb0->vel, SColorf(0xCFFFCF80));
	}
#endif
}



void irr::scene::IrrSaba::phyObjForceAttack(phyObjForceParam pm)
{

	int nearid = -1, farid = -1; float nearDis = 999999.f, farDis = -1.f;
	auto trb = ndUpper ?// ndUpper->rb0 
		ndUpper->rb0
		: Rb0();
	if (!trb) return;
	auto tgt = trb->getPosition() + pm.ofs;
	for (size_t i = 0; i < Pom->phyObjs.size(); i++)
	{
		auto& o = *Pom->phyObjs[i];

		if (!o.rb->GetActivation() || !(pm.objId == -1 || pm.objId == o.uid) || o.pm.tag != pm.tag) continue;

		//trb = (o.id % 2) ? ndLeg1L->rb0 : ndLeg1R->rb0;
		tgt = trb->getPosition();
		//tgt.x = tgt.z = 0; tgt.y = std::min(tgt.y, std::max(mmdCamPos.y - 20, 10.f));
		//tgt += (i % 100 / 100.f - 0.5f) * pm.ofs;

		float dis = trb->disTo(o.pos);
		if (dis < nearDis) {
			//if (dis > farDis) {
			nearDis = dis;
			nearid = i;
		}


		if (glm::length(o.dtvel) > 20.f) o.centerForceMul = -1.f;
		else if (o.centerForceMul < 1.f)
		{
			if (o.centerForceMul<=0 && o.centerForceMul+stepTime>0.f)
				actVoiceFx(o.id%8 + Pm.mmd->mdplr.mmdCount, 0,55+UaRand(10),L"",0.7f);
			o.centerForceMul += stepTime;
		}
		else
			o.centerForceMul = 1.f;

		float v = pm.mul * std::max(0.f, o.centerForceMul);
		if (v > 0.f) {
			vec3 vel(v * v);
			if (o.sb && o.sb->ndHandL) {
				if (charAtk.legGrab) pm.rst = 1;
				vel *= (tgt - o.rb->getPosition());
				glh::vecLimitLengthMinMax(vel, 0.f, 100.f);
				//o.sb->Pmx->addBodyVel(vel);

#if 0
				if (ndUpper2) {
					auto tgtUp2 = ndYao ->rb0->getPosition() * vec3(1, 1, 1) + vec3(0,   (  + 5 * sin(gSceneTime * piFloat*1.f)) * o.sb->absScale(), 0);
					tgtUp2.y = std::max(tgtUp2.y, o.sb->absScale()*10);
					o.sb->ndUpper2->rb0->addLinearVelToPosLimitDis(tgtUp2, 3, 1.f, 10.f);
					o.sb->ndUpper2->rb0->setAngVelToPos(tgt, 100.f);
					auto tgtNeck = ndNeck->rb0->getPosition() * vec3(1, 1, 1) + vec3(0, ( +6*sin(gSceneTime*piFloat*1.f))*o.sb->absScale(), 0);
					o.sb->ndArm1L->rb0->addLinearVelToPosLimitDis(tgtNeck, 6, 2, 10.f);
					o.sb->ndArm1R->rb0->addLinearVelToPosLimitDis(tgtNeck, 6, 2, 10.f);
				}	
#else
				if (glm::fract((o.age - gFrameTime) / 10) < 0.2f && glm::fract(o.age / 10) >= 0.2f) {
					MpaLoadJsonParam lf{};
					static size_t cc = 0;
					lf.filepath = "data/mpa/liftKick1.json"; lf.setSb0 = o.sb; lf.setSb1 = mmd->sabas[cc++ % mmd->sabas.size()];
					o.sb->startSbMPA(lf);
				}

#endif

			}
			//else if (pm.tag == 'cbgr')
			//{
			//	o.rb->addLinearVel(vel*vec3(0,2,0));
			//}
			else {
				//o.rb->scaleVel(0.677f, 3);
				vel *= (tgt - o.rb->getPosition());

				glh::vecLimitLengthMinMax(vel, 0.f, 100.f);
				o.rb->setLinearVel(vel);
				if (o.pm.vfg.flag & 0x2) {
					vel = glm::cross(vel, vec3(0, 0.9f, 0));
					o.rb->addLinearVel(vel);
				}
			}
			if (pm.rst) o.resetStatus();
			if (o.sb) { MMDFW(1, "jetRing", o.pos, -vel * 2.f, SColorf(1, 1, 1, 1)); }
		}

	}

	//if (nearid >= 0) {
	//	auto& o = *Pom->phyObjs[nearid];
	//	if (Rb0()->disTo(o.pos) < pm.maxDis && o.pos.y < pm.maxY) {
	//		if (pm.rst) { charAtk.reset(); o.resetStatus(); DP(("reset near %d", nearid)); }
	//		if (o.sb) {
	//			o.sb->Pmx->addBodyVel((tgt - o.sb->Rb0()->getPosition()) * pm.mul * 5.f);
	//		}
	//		else o.rb->addLinearVel((tgt - o.rb->getPosition()) * pm.mul * 5.f);
	//	}
	//}
}

void irr::scene::IrrSaba::phyLookAt(phyLAParam pm)
{ 
	float dis = ndHead->disTo(pm.pos) / ndHead->absScale.x ;
	float disRatio = glm::clamp((dis - pm.disMin) / (pm.disMax - pm.disMin), 0.f, 1.f);
	float am = pm.angleMul * (1 - pm.disRatioAngMul * disRatio);
	float powMul = (1 - pm.disRatioPowMul * disRatio);
	bool lk = ndHead->rb0->setAngVelToPos(pm.pos, std::min(pm.powHead * powMul, pm.powHeadMax), 1.f, 120.f * am);
	//DP(("phylook %f %f %f %f %d ", dis, disRatio, am, std::min(pm.powHead * powMul, pm.powHeadMax), lk));
	//sbFw2("sw", pm.pos, vec3(0, 0, 0), 0xFFFF0000);
	if ((!lastPhyLook /*|| mpO && itemIdx > 0 && mpO->m_weight < 0.1f*/) && lk) {

		if (mpXiao) mpXiao->appendMorph(1, 0, 2);
		if (mpWa) mpWa->appendMorph(1, 0, 2);

	}
	lastPhyLook = lk;
	if (ndEyes && ndEyes->rb0 && pm.powEye > 0.01f) {
		ndEyes->rb0->setAngVelToPos(pm.pos, pm.powEye * powMul, 0.5f, 60.f * am);
		//ndEyeR->rb0->setAngVelToPos(camPos, powEye *2.f,0.5f);
		//MMDFWD(2, "pt1s", ndEyes->rb0->getPosition(), ndEyes->rb0->getRotation() * vec3(0, 0, -30), SColorf(0.5, 1.0, 0.1, 1));
		//MMDFWD(2, "pt1s", ndEyeR->rb0->getPosition(), ndEyeR->rb0->getRotation() * vec3(0, 0, -30), SColorf(0.5, 1.0, 0.1, 1));
	}
	if (ndCatEarL && pm.powCatEar > 1)
	{
		ndCatEarL->rb0->setAngVelToPos(pm.pos, pm.powCatEar * powMul, 1.f, 160.f);
		ndCatEarR->rb0->setAngVelToPos(pm.pos, pm.powCatEar * powMul, 1.f, 160.f);
		//MMDFW(2, "sw1s", pm.pos, vec3{ 0.f }, SColorf(0,  1, 0, .2));
	}

}
void irr::scene::IrrSaba::renderPhysxDbgVisualLines(saba::MMDPhysics* ph)
{
#if 1
	{
#if SABA_USE_PHYSX
		if (itemIdx == 0)
		{
			static 		std::vector<irr::video::S3DVertex> lineBuf;
			if (ph->phyDbgLineNum > 0) {
				if (lineBuf.size() < ph->phyDbgLineNum * 2) lineBuf.resize(ph->phyDbgLineNum * 2);
#if 1			//2x speed
				const int numThreads = 8;// std::thread::hardware_concurrency();
				std::vector<std::future<void>> futures(numThreads);
				int workload = (ph->phyDbgLineNum + numThreads - 1) / numThreads;
				for (int i = 0; i < numThreads; ++i) {
					int start = i * workload;
					int end = std::min(start + workload, ph->phyDbgLineNum);
					futures[i] = std::async(std::launch::async, [=]() {
						for (int i = start; i < end; ++i) {
							auto& s = ph->phyDbgLines[i];
							{
								auto& t = lineBuf[i * 2]; t.Pos.set(s.pos0.x, s.pos0.y, -s.pos0.z);
								t.Color = s.color0; t.Normal.set(0, 1, 0);
							}
							{
								auto& t = lineBuf[i * 2 + 1]; t.Pos.set(s.pos1.x, s.pos1.y, -s.pos1.z);
								t.Color = s.color1; t.Normal.set(0, 1, 0);
							}
						}
						});
				}
				for (auto& future : futures) 	future.get();
#else
				for (int i = 0; i < num; i++) { auto& s = plines[i]; { auto& t = lineBuf[i * 2]; t.Pos.set(s.pos0.x, s.pos0.y, -s.pos0.z); t.Color = s.color0; t.Normal.set(0, 1, 0); } { auto& t = lineBuf[i * 2 + 1]; t.Pos.set(s.pos1.x, s.pos1.y, -s.pos1.z); t.Color = s.color1;						t.Normal.set(0, 1, 0); } }
#endif				
				auto& p0 = lineBuf[0]; //p0.Pos.set(0, 0, 0);
				auto& p1 = lineBuf[1]; //p1.Pos.set(0, 100, 0);
				SMaterial sm; sm.MaterialType = video::EMT_LINE;
				Driver->setMaterial(sm);
				Driver->setTransform(ETS_WORLD, mmdBaseMat);
				Driver->drawVertexPrimitiveList(lineBuf.data(), ph->phyDbgLineNum * 2, 0, 1, EVT_STANDARD, EPT_LINES, EIT_32BIT);
			}
		}

#else
		if (Dg && Dg->getDebugMode()) {
			Dg->begin();
			mmdPhysics->GetDynamicsWorld()->debugDrawWorld();
			Dg->end();
			SMaterial sm; sm.MaterialType = video::EMT_LINE;
			Driver->setMaterial(sm);
			Driver->setTransform(ETS_WORLD, mmdBaseMat);
			Driver->drawVertexPrimitiveList(Dg->lineVec.data(), Dg->lineVec.size(), 0, 1, EVT_STANDARD, EPT_LINES, EIT_32BIT);
		}
#endif
	}
#endif
}


void irr::scene::IrrSaba::resetPhyAnim()
{
	localPhyAnim = false; localPhyAnimToPos = false;
	ndYao->forEachSubNodes([=](saba::MMDNode* nd) {
		if (nd->rb0 && nd->rb0->dynRbType) {
			nd->phyAnimRatR = nd->phyAnimRatT = 1;
			nd->phyAnim = 0;
			if (nd->rb0->jtsToParent.size() > 0)
				if (auto jt = nd->rb0->jtsToParent[0]) {

					jt->setDrivePose(glm::mat4(1), 3, 0);//				jt->setDriveMul(1);
				}

		}
		}, true);
}


#if 1
void irr::scene::IrrSaba::footOnGround(saba::MMDNode* ndf) 
{
    // 常量定义提升可维护性
    constexpr float BASE_RTT_MUL = 900.f;
    constexpr float HIGH_FALL_RTT_MUL = 3000.f;
    constexpr float HEIGHT_THRESHOLD_FACTOR = 0.3f;
    constexpr float MIRROR_POS_FACTOR = 0.7f;
    constexpr float ANGLE_THRESHOLD_NORMAL = 60.f;
    constexpr float ANGLE_THRESHOLD_HIGH_FALL = 120.f;

    // 提前缓存常用数据
    auto* rbYao = ndYao->rb0;
    if (!rbYao || !ndf->rb0) return;

    float sc = ndYao->absScale.y;
    float sc2 = sc * sc;
    float ypScled = Pmx->yaoPos.y * sc;
    float footY = ypScled * HEIGHT_THRESHOLD_FACTOR;
    
    // 父节点处理
    auto parentNode = ndf->GetParent();
    if (!parentNode->rb0) return;
    
    // 物理数据缓存
    const auto& yaoVel = rbYao->vel;
    const auto& yaoPos = rbYao->pos;
    const float fy = ndf->rb0->pos.y;

    // 角度计算
    const mat4 mParent = parentNode->rb0->getNodeTransform();
    const quat qrP = glm::quat(mParent);
    const float angle = glm::degrees(glm::acos(
        glm::dot(vec3(0, -1, 0), qrP * vec3(0, -1, 0))
    ));

    // 状态判断
    const bool highFall = angle < ANGLE_THRESHOLD_HIGH_FALL && 
                          yaoVel.y < -sc * 3.f && 
                          yaoPos.y > ypScled && 
                          yaoPos.y < ypScled * 5.f;
    
    const bool shouldProcess = 
        (angle < ANGLE_THRESHOLD_NORMAL && 
         ((ndf->rb0->vel.y < -0.2f && fy < footY * 3.f) || 
          (yaoPos.y > ypScled / 2 && fy < footY))) || 
        highFall;

    if (!shouldProcess) return;

    // 空间关系计算
    const vec3 posP = yaoPos;
    const vec3 posL = ndFootL->rb0->pos;
    const vec3 posR = ndFootR->rb0->pos;

    // 使用投影平面距离比较
    const auto projDistSq = [](const vec3& a, const vec3& b) {
        const vec2 delta = vec2(a.x - b.x, a.z - b.z);
        return glm::dot(delta, delta);
    };

    const bool isL_far = projDistSq(posP, posL) > projDistSq(posP, posR);
    auto* ndFar = isL_far ? ndFootL : ndFootR;
    auto* ndNear = isL_far ? ndFootR : ndFootL;

    if (ndNear != ndf) return;

    // 物理调整逻辑
    const float mulBySpd = std::abs(yaoVel.y);
    const float farDis = 15.f * sc2;
    
    if (highFall) {
        rbYao->setAngVelToPos(yaoPos + vec3(0, -10, 0), 
                            std::min(100.f, mulBySpd / 2.f));
    }

    parentNode->rb0->addRotationToMatOnNode_MatRttResetXZ(mParent, 
                        highFall ? 200 : 100);

    // 镜像位置计算
    const vec3& posFar = isL_far ? posL : posR;
    vec3 posFarMirror = posP + (posP - posFar) * MIRROR_POS_FACTOR;
    posFarMirror.y = ndNear->rb0->pos.y;

    // 距离判断
    const bool tooFar = fy < footY && 
                       projDistSq(ndNear->rb0->pos, posFarMirror) > farDis;
    
    // 高度调整
    const float heightAdjust = highFall ? -0.3f : 
                              tooFar ? (footY - fy) * 0.6f : 0.1f;
    posFarMirror.y += heightAdjust * sc;

    // 足部位置修正
    const float applySpeed = highFall ? std::min(10.f, mulBySpd / 100.f) : 
                            tooFar ? 10.f : 5.f;
    
    if (highFall && projDistSq(posFar, posFarMirror) > 100.f * sc2) {
        const vec2 dir = glm::normalize(vec2(posFar.x - posFarMirror.x, 
                                           posFar.z - posFarMirror.z));
        const float targetDist = 10.f * sc;
        const vec3 midPoint = (posFar + posFarMirror) * 0.5f;

        vec3 newFar = midPoint + vec3(dir.x * targetDist * 0.5f, 
                                     posFarMirror.y, 
                                     dir.y * targetDist * 0.5f);
        vec3 newNear = midPoint - vec3(dir.x * targetDist * 0.5f, 
                                      posFarMirror.y, 
                                      dir.y * targetDist * 0.5f);

        ndFar->rb0->scaleVel3(vec3(0.75f, 1.f, 0.75f), 1);
        ndFar->rb0->addLinearVelToPos(newFar, applySpeed);
        posFarMirror = newNear;
    }

    ndNear->rb0->addLinearVelToPos(posFarMirror, applySpeed);
    ndFar->rb0->scaleVel3(vec3(0.75f, 1.f, 0.75f), 1);

    // 调试可视化
    sbFw2LineD("pt", ndNear->rb0->pos, posFarMirror, tooFar ? 0xFF00FF80 : 0x80008080, 10);

    // 足部旋转调整
    const bool needRotation = ndf->rb0->vel.y < -0.2f && fy < footY || 
                             fy < footY / 2.f;
    
    if (needRotation) {
        const mat4 mRb = ndf->rb0->GetTransform();
        const mat4 mNode = mRb * ndf->rb0->getOfsMatInv();
        const float finalRttMul = tooFar ? HIGH_FALL_RTT_MUL : BASE_RTT_MUL;
        const quat qr = ndf->rb0->addRotationToMatOnNode_MatRttResetXZ(mNode, finalRttMul);

        sbFw2LineD("pt", ndf->rb0->pos, 
                  ndf->rb0->pos + qr * vec3(0, -10, 0), 
                  0xFFFF0000, 60);
        sbFw2LineD("pt", ndf->rb0->pos, 
                  ndf->rb0->pos + quat(mNode) * vec3(0, -10, 0), 
                  0xFFFFFFFF, 60);
    }
}
#else
// Handle the foot interaction with ground
// Adjust foot position and rotation when foot is near ground or falling
// @param ndf - The foot node to process
void irr::scene::IrrSaba::footOnGround(saba::MMDNode* ndf) {
	float rttMul = 900.f;
	bool movingFoot = false;
    // Get parent node and validate
    auto parentNode = ndf->GetParent();
    if (!parentNode->rb0) return; // Null check

    // Calculate scale and distance parameters
	float sc = ndYao->absScale.y, sc2 = sc * sc, farDis = 15.f * sc2;
    float ypScled = Pmx->yaoPos.y * sc;
    float footY = ypScled * 0.3f; // Height threshold for foot
	
    if (ndf->rb0) {
        auto ndParent = ndf->GetParent();
        mat4 mParent = ndParent->rb0->getNodeTransform();
        quat qrP = glm::quat(mParent);

        // Calculate angle between parent and ground
        float angle = glm::degrees(glm::acos(glm::dot(quat(1, 0, 0, 0) * vec3(0, -1, 0), qrP * vec3(0, -1, 0))));

        float fy = ndf->rb0->pos.y;
        
        // Check if falling fast from high position
        bool highFall = angle < 120.f && ndYao->rb0->vel.y<-sc * 3.f && ndYao->rb0->pos.y>ypScled && ndYao->rb0->pos.y<ypScled*5.f;

        // Process foot positions when near ground or falling
        if ((angle < 60.f && ((ndf->rb0->vel.y < -0.2f && fy < footY * 3.f)
			|| ndYao->rb0->pos.y > ypScled / 2 && fy < footY)) || highFall) {
            
            // Get positions
            glm::vec3 posP = ndYao->rb0->pos; //center of body
            glm::vec3 posL = ndFootL->rb0->pos;
            glm::vec3 posR = ndFootR->rb0->pos;

            // Determine which foot is farther from body center
            bool isL_far = glm::length2(vec3(posP - posL) * vec3(1, 0, 1)) > glm::length2(vec3(posP - posR) * vec3(1, 0, 1));
			auto ndFar = isL_far ? ndFootL : ndFootR, ndNear = isL_far ? ndFootR : ndFootL;

            // Process the nearer foot
            if (ndNear == ndf) {
				float mulBySpd = abs(ndYao->rb0->vel.y);
                // Add angular velocity on high fall
                if (highFall) ndYao->rb0->setAngVelToPos(ndYao->rb0->pos + vec3(0, -10, 0), std::min(100.f, mulBySpd/2.f));
                
                ndParent->rb0->addRotationToMatOnNode_ZeroMatRttXZ(mParent, highFall ? 200 : 100);

                // Calculate target position for far foot based on mirror position
                auto posFar = isL_far ? posL : posR, posNear = isL_far ? posR : posL;
                auto posFarMirrorToP = posP + (posP - posFar) * 0.7f;
                posFarMirrorToP.y = posNear.y;

                // Check if feet are too far apart
                bool tooFar = fy < footY && glm::length2(posNear - posFarMirrorToP)>farDis;
				if (tooFar) rttMul = 3000.f;
                // Adjust height based on conditions
                posFarMirrorToP.y += (highFall ? -0.3f : (tooFar ? (footY - fy) * 0.6f : 0.1f)) * sc;
				posNear = posFarMirrorToP;
				if (highFall && glm::length2((posFar - posFarMirrorToP) * vec3(1, 0, 1)) > 100.f *sc2 ) {
                    vec3 dir = glm::normalize((posFar - posFarMirrorToP) * vec3(1, 0, 1));
                    float targetDist = 10.f * sc;
                    vec3 midPoint = (posFar + posFarMirrorToP) * 0.5f;
                    posFar = midPoint + dir * (targetDist * 0.5f);
                    posFar.y = posFarMirrorToP.y;
                    posNear = midPoint - dir * (targetDist * 0.5f);
                    posNear.y = posFarMirrorToP.y;
					ndFar->rb0->scaleVel3(vec3(0.75f,1.f,0.75f), 1);
					ndFar->rb0->addLinearVelToPos(posFar, highFall ? std::min(10.f,  mulBySpd / 100.f) : tooFar ? 10.f : 5.f);

				}
                // Apply velocity changes
				ndFar->rb0->scaleVel3(vec3(0.75f, 1.f, 0.75f), 1);
                ndNear->rb0->addLinearVelToPos(posNear, highFall ? std::min(10.f,  mulBySpd / 100.f) : tooFar ? 10.f : 5.f);
				movingFoot = true;
                // Visual debug line
                sbFw2LineD("pt", ndNear->rb0->pos, posFarMirrorToP, tooFar ? 0xFF00FF80 : 0x80008080, 10);
            }
        }

        // Return if angle too large
        if (angle > 60.f) return;

        // Adjust foot rotation when hitting ground
        if (ndf->rb0->vel.y < -0.2f && fy < footY || fy<footY/2.f || movingFoot)
        {
            mat4 mRb = ndf->rb0->GetTransform();
            mat4 mNode = mRb * ndf->rb0->getOfsMatInv();
            auto qr = ndf->rb0->addRotationToMatOnNode_ZeroMatRttXZ(mNode, rttMul);

            // Visual debug lines
            sbFw2LineD("pt", ndf->rb0->pos, ndf->rb0->pos + qr * vec3(0, -10, 0), 0xFFFF0000, 60);
            sbFw2LineD("pt", ndf->rb0->pos, ndf->rb0->pos + quat(mNode) * vec3(0, -10, 0), 0xFFFFFFFF, 60);
        }
    }
};
#endif