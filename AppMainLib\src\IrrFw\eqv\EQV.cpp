﻿#include "AppGlobal.h"
#define FW_IMG_BORDER 0
#define TODO_RTT 1
#define EQV_USE_WAVENODE 1
#define FWTEXT_DBGSTEP  0
#define HAS_HEIGHT_MAP 0
#define CAM_SPACE_DIS 0.5

#define ROTATE_TEXT_90 0
#define USE_AR_LAUNCHER 0
#define PFP_DELAY  0.1  //for mouth prepare 

 
#define EQ_MIN_BAND 1
#define EQ_MAX_BAND 32
#define USE_INPUT_PNG_COLOR 1



#define COLMAP_DEFAULT_TIMNEOFS 0.07f
#define VFG_GRID  -32  //def:-96


#include <string_view>
#include <algorithm>
#include <set>
 
#include "EQV.h"
#include "cppIncDefine.h"
#include "EqvNodeBand.h"
#include "EqvNodeWave.h"
#include "EqvFwNode.h"
#include "EqvNodeRT.h"
#include "apng/apngEnc.h"

#include "UaUtils.h"
#include "stlUtils.h"

#if HAS_FT2MAN
#include <FT/FT2Man.h>
extern ualib::FT2Man gFt2Man;
#elif HAS_FM_Android
#include "FT/FMAndroid.h"
#endif
using namespace EQVisual;
using namespace ualib;
using namespace irr;
using namespace irr::core;
constexpr size_t MaxPtrFw = 256;;
#include "EqvShapeFunc.h"
#include "irrmmd/irrMMD.h"
#include "irrmmd/irrSaba.h"
#include "Helpers/ThreadPool/ThreadPool.h"
#include <VulkanRenderer/VkMr2D.h>
#include "irrmmd/PhyObjMan.h"
static core::matrix4  mcrCAMY_ADDRTT;
#ifdef _WIN32
#include "DbgHelpers.h"
#define EQV_STYLE_PATH "D:/AProj/VkUpApp/eqv/mf"
FileChangeDetectorEx ScdPtsTxt(TEXT(EQV_STYLE_PATH));
#endif
EQV::EQV(ualib::UaLibContext* ctx)
	:Ctx(ctx)//, pvp(_vp)
{
	Ctx->eqv.eqv = this;
	rttYadj = AR_SRC_RTTY;
	mcrCAMY_ADDRTT.setRotationDegrees({ 0, CAMY_ADDRTT, 0 });

	EqvLdr.Eqv = this;
	SceneManager = ctx->getSceneManager();
	mSnRoot = SceneManager->addEmptySceneNode();
	snSbtItv = SceneManager->addEmptySceneNode();
	snSbtCur = SceneManager->addEmptySceneNode();
	snSbtTgt = SceneManager->addEmptySceneNode();
	snSbtSrc = SceneManager->addEmptySceneNode();
	snSbtCamMovS = SceneManager->addEmptySceneNode();
	snSbtCamMovT = SceneManager->addEmptySceneNode();
	//vmd = new mmd::VmdFile(ctx);

	//vmd->loadFile("d:/tmp/mmd/cam.vmd");
#if !DRONE_AR
	ar.camPosScale = 1000.f;
#endif
#if USE_SBT_ANIM3D
	ft.camSpeedZ = 100;
#endif
	Tlp = new TfListMgr();

	initAR();

#if DRAW_SHADER_TOY
	ShaderToyManParam sdtpm = { this,Ctx };
	SdToy = new ShaderToyMan(sdtpm);
#endif
#if USE_SVG 
	
	SvgRenderer::cbReadStkFile = [this](const char* path, uu::AlignedMemoryBuffer& buf)
	{
		io::IReadFile* rf = Ctx->getFileSystem()->createAndOpenFile(path);
		assert(rf);
		if (!rf) return;
		//std::ifstream is(GRAPHICS_FN "stk", std::ios::binary | std::ios::ate);
		int size = rf->getSize();
		buf.ensureSize(size);
		rf->read(buf.CurBufPtr, size);
		buf.appendData(nullptr, size);
		rf->drop();
	};
#if (!HAS_MMD || defined(__ANDROID__))

	cbOnSvFwCenterPt = [this](SvTimePt& pt, int fromId, float deltaS) {
		Drawing = pt.flag != 0xF0000000;
		drawFinished = (pt.flag & 2);
		if (pt.flag & 0x20) {
			drawStroking1 = drawStroking;
			drawStroking = !(pt.flag & 1);
			drawCtrPos = pt.ctr;
			if (drawStroking1 != drawStroking)
				drawPtId = 0;
			else drawPtId++;
		}
		 if (Drawing) {
			 
			 auto cam = svgCam ? svgCam : Ctx->gd.CamRtt ? Ctx->gd.CamRtt : Ctx->gd.CamNormal;
 
			 vector3df tp = drawUpdate(pt, deltaS, drawMX) *float3(500, 500, 50);
		 
			 
			 if (pt.sn) {
				 pt.sn->getAbsoluteTransformation().transformVect(tp);
			 }
			 else {
				 tp += svgPm.drawOfst;
				 core::matrix4 mt;
				 cam->getViewMatrix().getInverse(mt);
				 mt.transformVect(tp);
			 }
			 svgMan->drawFw(this, sin(drawRat * core::HALF_PI)*svgPm.scale , tp );
			 //LaunchFw3D(tp , getFwIdx(2, 1), {0,0,0}, drawScf,&pm);

			 //LaunchFw3D(drawPos * float3(100, 100, 1), getFwIdx(2, 2), { 0,0,0 },drawStroking ? SColorf(1, 0, 1, drawRat * .5f + 0.5) :				  SColorf(0, 0, 1, drawRat * .5f + 0.5) );

		}
		else {
			svgMan->useMat = drawStroking = false; //evtMoving = true;curSTP.pos.set(0, 30, 0);  
			drawStrokeRatio = 0;
		}

		return ;
	};
#endif
#endif
#ifdef _WIN32
	if (Ctx->isApp(APPID_WinTest))
	ScdPtsTxt.mCallBack = [this](irr::io::path fp) {
		static int cpy = 0;
		static int64_t tms = 0;
		if (ualib::NowMs() - tms < 1000)
			return;
		tms = ualib::NowMs();
		if (cpy == 0 && fp.pathGetExt() == ".eqvdef")
		{
			cpy = 1;

			ualib::SleepMs(100);
			if (fp != "!stkFws.eqvdef") {
				SetCurrentDirectory(L"D:\\AProj\\VkUpApp\\");
				Ctx->UaCopyFile(irr::io::path("eqv/mf").pathAppend(fp.c_str()), Ctx->getDataFilePath("style.eqvdef"));
			}
			Ctx->getDriver(0)->ClearOnBegin = true;

			ualib::SleepMs(10);
			recreateOnUpdate();
			//ualib::SleepMs(500);
			cpy = 0;
		}
	};
#endif
}


EQV::~EQV()
{
	delete MatRec;
	delete Tlp;
	if (pgMap) delete[]pgMap;
#if DRAW_SHADER_TOY
	if (SdToy) delete SdToy;
#endif
	if (svgMan) delete svgMan;
	//delete vmd;
	mSnRoot->remove();

}


bool EQV::CreateEQV(irr::io::path fp, int bandCount)	// or recreate
{
	Clear();
	needRecreateOnUpdate = false;
	if (bandCount > 0)
		eqvBandCount = bandCount;

	UaJsonSetting jssStkFws;
	//CPU_COUNT_B(MT);//r22.3 d229
	auto T1 = std::async(std::launch::async, [&]() {
		auto sjs = Ctx->readStringFromFile("eqv/mf/!stkFws.eqvdef");
		if (!sjs.empty()) {
			//mFWs.resize(staticFwCount);	mEmbers.resize(staticEmbCount);		 	 
			jssStkFws.LoadJsonString(sjs, true);
		}
		});
	// Refactored code to execute in separate threads

	EqvLdr.LoadDlPack();
	InitEQV(fp);

	T1.get();
	//CPU_COUNT_E(MT);
	if (jssStkFws.refRootValue().isObject())
		loadDynFw(jssStkFws);
	return true;
}

bool EQV::InitEQV(irr::io::path sJsFile)
{
	Clear();
	ualib::UaJsonSetting jss("mutex_EqvNodeFile");

	std::string file = "style.eqvdef";

	bool br = false;
	//CPU_COUNT_B(JSON);
	if (eqvDatStringToLoad.length() > 0)
	{
		eqvDatString = eqvDatStringToLoad;
		eqvDatStringToLoad.clear();
	}
	else if (sJsFile.size() > 10)
		eqvDatString = Ctx->readStringFromFile(sJsFile);
	if (eqvDatString.length()<1)
		eqvDatString = Ctx->readStringFromFile(file.c_str());

	br = jss.LoadJsonString(eqvDatString, 1);
	//CPU_COUNT_E(JSON);
	if (!br)
	{
		assert(0);
		return false;
	}
	if (!MatRec) MatRec=new MatrixRecorder();
	MatRec->drSetMaxKeyDuration(500LL);
	auto& dsd = Ctx->getDriver()->dsd;
	if (Ctx->getDriver(0)->dsd.initHDR) dsd.resetHDR(1.07f, 1.0f); //dsd.resetHDR(0.5f, 2.2f);
	else dsd.resetHDR(2.5f,0.5f);

	EqvParam tep;
	mPM = tep;
	DP(("CLEAR PTR"));
	for (int i=0;i<__crt_countof(ptrFws); i++) ptrFws[i] .clear();	
	ptrFws[0].reserve(256);
	tfdList.clear(); spsList.clear();
	dwWavMask = 1;
	mRotVec.set(0.f, 0.f, 0.f);
	cldFwId = 0;
	ar.camPosScale = 10 * MMD_SABA_SCALE;

	mFrameEmitCount = 1000;
	//Ctx->eqv.overrideBandCount = 0;
	Ctx->eqv.nextBandCount = 32;// BY eqvdef overrideBandCount
	auto cv = jss.copyRootValue();
	Json::Value v = cv["textFwDefault"];
	if (v.isObject())
	{
		TextFwParam a = TextFwParam::fromJsonValue(v,nullptr);
		DP(("Text %s\nSize %f", mTfpm.text.c_str(), mTfpm.fontSize));
#if AR_FW_TEXT_ON_CAM_PATH
		a.basePos = float3(0, 0, 0);
#endif
		mTfpmBase = a;
	}
	v = jss.copyRootValue()["tfdDefault"];
	if (v.isObject())
	{
		Tlp->getTfdFromJsonValue(mTfdDefault, v);
		mCurTfd = mTfdDefault;
	}

	jss.ProcessSubs([this, &jss, &dsd](std::string key, const Json::Value& v) {
		//DP(("%s = %s",vn.c_str(),v.toStyledString().c_str()));
		CeJs_Start(mPM);
		CeJs_Parse(tgtVer);
		CeJs_Parse(fxType);
		//CeJs_Parse(camType);
		CeJs_Parse(baseGX);
		CeJs_Parse(baseGY);
		CeJs_Parse(baseGZ);
		CeJs_Parse(dcrPw);
		CeJs_Parse(rttBaseY);
		CeJs_Parse(rttBaseX);
		CeJs_Parse(rttBaseZ);
		CeJs_Parse(rttSpdY);
		CeJs_Parse(rttSpdX);
		CeJs_Parse(rttSpdZ);
		CeJs_Parse(ptrFwMode);
		CeJs_Parse(ptrFwBaseDeg);
		CeJs_Parse(ptrFwKld);
		CeJs_Parse(ptrFw0Pct);
		CeJs_Parse(ptrFwDisDiv);
		CeJs_Parse(ptrZ);
		CeJs_Parse(ptrHueAdd);
	else if (key == "overrideBandCount")
	{
		//assert(0); //check acg if use it => kld0 used
		mPM.overrideBandCount = Ctx->eqv.nextBandCount = v.asInt();
	}
	//else if (key == "kaleido")	UaJson::ParseType(v, mPM.ptrFwKld);
	
	else if (key == "svg")
	{
		svgPm=SvgParam::fromJsonValue(v);
		if (!v["armIksDiv"].isNull())
		saba::MMDIkSolver::updateRateData(1, svgPm.armIksDiv, svgPm.armIksDivMul ,svgPm.armIksRat, svgPm.armIksRatMul);
	}
	else if (key == "Nodes" && v.isArray())
	{
		for (int i = 0; i < (int)v.size(); i++)
		{
			UaJson::ProcessSubs(v[i], [this, &v, &i](std::string key, const Json::Value& vs)
				{
					auto sss = key;
					//DP(("%S", sss.c_str()));
					if (key == "nodeType")
					{
						EqvNodeParam epm;
						epm.bandCount = eqvBandCount;
						epm.eqv = this;
						epm.sm = SceneManager;
						epm.ctx = Ctx;
						if (vs.asString() == "BarNode")
						{
							EqvNodeBandParam npm;
							npm.epm = epm;
							npm.onItemSnUpdated =  std::bind(&EQV::onBandItemSnUpdated, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);

							auto node = std::make_shared<EqvNodeBand>(npm);
							node->Deserialize(v[i]);
							this->AppendNode(node);
						}
#if EQV_USE_WAVENODE
						else if (vs.asString() == "WaveNode")
						{
							EqvNodeWaveParam npm;
							npm.epm = epm;
							auto node = std::make_shared<EqvNodeWave>(npm);
							node->Deserialize(v[i]);
							this->AppendNode(node);
						}
#endif
						else if (vs.asString() == "FwNode")
						{
							EqvFwNodeParam npm;
							auto subType = v[i]["subType"];
							if (!subType.empty())
							{
								npm.epm = epm;
								auto node = std::shared_ptr<EqvFwNode>(CreateEqvFwNode(subType.asString(), npm));
								if (node)
								{
									node->Deserialize(v[i]);
									this->AppendNode(node);
								}
							}
							else
							{
								assert(0);
							}
						}
						//else if (vs.asString() == "RtrNode")
						//{
						//	EqvNodeParam epm;
						//	epm.bandCount = PM.eqv->bvd->count;
						//	EqvNodeRTParam npm;
						//	npm.epm = epm;
						//	auto node = std::make_shared<EqvNodeRT>(npm);
						//	node->Deserialize(v[i]);
						//	this->AppendNode(node);
						//}

					}
				});
			//DP(("%d", v[i].size()));
		}

	}
	//else if (key == "pointerFw")
	//{
	//	pointerFw.gfd.FwId = FwMan.AddFw(v, L"eqvPointerFw");
	//}
	else if ((key == "ptrFws" || key == "ptrDownFws" || key == "ptrUpFws") && v.isArray())
	{
		DP(("Process ptrfw: %s",key.c_str()));
		int ptrId = -1;
		if (key == "ptrDownFws")	ptrId = 2;
		else if (key == "ptrUpFws") ptrId = 1;
		else if (ptrFws[0].size() < MaxPtrFw) ptrId = 0;
		else throw std::invalid_argument("err:ptrFws");
		for (unsigned int i = 0; i < v.size(); i++)
		{
			//DP(("+ ptrfw %d",i));
			if (i > 128) { assert(0); continue; }
			EqvFwStruct fws;
			FwMan.mFECD.reset();
			fws.tfp = mTfpmBase;
			auto vparent = v[i]["parent"];
			if (!vparent.isNull()) {
				int id= findPtrFwIdx(ptrId, vparent.asString());
				if (id >= 0)
					fws = ptrFws[ptrId][id];
			}
			UaJson::ProcessSubs(v[i], [this, i, &fws](std::string key1, const Json::Value& v)
				{
					CeJs_Start(fws);
			else if (key1 == "fw" || key1 == "cldFw")	fws.gfd.FwId = FwMan.AddFw(v, "ptrFw");
			else if (key1 == "textFw")	fws.tfp= TextFwParam::fromJsonValue(v,&mTfpmBase);
			else if (key1 == "vtxFw")	fws.vfp = VtxFwParam::fromJsonValue(v, nullptr);
			else if (key1 == "id") { fws.idStr = v.asString(); DP(("fws ID = %s", fws.idStr.c_str())); }
			else if (key1 == "cldFwDis") 	fws.cldFwDis = v.asInt();
			else if (key1 == "tfdId") 	fws.tfdId = v.asString();
			else if (key1 == "pianoMode") 	fws.pianoMode = v.asUInt();
			else if (key1 == "duration") 
				fws.durationMs = (int)(v.asFloat() * 1000);
			});
			fws.ecd = FwMan.mFECD;
			if (fws.tfp.ptScale != 1.f) {
				fws.tfp.srcScale *= fws.tfp.ptScale;
				fws.tfp.tgtScale *= fws.tfp.ptScale;
				 
				auto& fw = FwMan.mFWs[fws.gfd.FwId];
				for (int i = fw.startId; i < fw.startId + fw.count; i++)
					FwMan.mEmbers[i].r *= fws.tfp.ptScale;
			}
			ptrFws[ptrId].push_back(fws);
			
			//DP(("- ptrfw %d", i));
		}
		DP(("============= PTRFWS %d added", ptrId));
		if (ptrFws[0].size()> curPtrFwIds[0]) 		mTfpm = ptrFws[0][curPtrFwIds[0]].tfp;
	}
	else if (key == "ptrStrokePM1" && v.isArray()) {
		for (int i = 0; i < (int)v.size(); i++)
		{
			mPM.ptrStrokePM1[i] = v[i].asFloat();
		}
	}
	else if (key == "tfdList" && v.isArray())
	{
		for (unsigned int i = 0; i < v.size(); i++)
		{
			//if (i > 16) { assert(0);  }
			TfData tfd = mTfdDefault;
			Tlp->getTfdFromJsonValue(tfd, v[i]);
			tfdList.push_back(tfd);
		}
	}
	else if (key == "spsList" && v.isArray())
	{
		for (unsigned int i = 0; i < v.size(); i++)
		{
			if (i > 16) { assert(0); }
			spsList.push_back(SyncPointersParam::fromJsonValue(v[i]));
		}
	}
	else if (key == "hdr" && v.isObject())
	{
		auto v3 = v["gamma"];
		if (!v3.isNull()) dsd.hdrGamma =v3.asFloat();
		v3 = v["exposure"];
		if (!v3.isNull()) dsd.hdrExposure = v3.asFloat();
	}
	else if (key == "frameGenCount" ) {	mFrameEmitCount = v.asInt();	}
	else if (key == "mediaRelated") { 
		UaJson::ProcessSubs(v, [this](std::string key1, const Json::Value& v1)	{
			if (false) {}
			else if (key1 == "ar_camSrcOfs") {
				//UaJson::ParseFloat3(v1, ar.camSrcOfs.X, ar.camSrcOfs.Y, ar.camSrcOfs.Z);
			}
			else if (key1 == "ar_camPosScale") {
				//ar.camPosScale = v1.asFloat();
				//if (mmd) mmd->RootNode->setScale(ar.camPosScale / 10000);
			};		 
		});
	}
	else
	{

	}


	}); //Process nodes

	
	for (int i = 0; i < 3; i++)
	{
		auto &fws = ptrFws[i];
		auto& ids = ptrFwIdStrs[i];
		ids.clear();
		for (int j = fws.size() - 1; j >= 0; j--) {
			if (std::find(ids.begin(), ids.end(), fws[j].idStr) == ids.end()) {
				ids.insert(ids.begin(), fws[j].idStr);
			}
			else {
				ids.insert(ids.begin(), fws[j].idStr+="_" + std::to_string(j));
			}
			 
		}

	}

#if DRONE_AR
		ar.camPosScale = 1.0;// 609344;
		if (mmd) mmd->RootNode->setScale(1);
#endif
	g_epm.channelDataMask = this->dwWavMask;
	needTransform = false;
	hasRtt = false;
	if (mPM.rttSpdX != 0.f || mPM.rttSpdY != 0.f || mPM.rttSpdZ != 0.f)
	{
		hasRtt = true;
		mRotSpeed.set(mPM.rttSpdX, mPM.rttSpdY, mPM.rttSpdZ);
	}
	else mRotSpeed.set(0.f, 0.f, 0.f);
	if (mPM.rttBaseX != 0.f || mPM.rttBaseY != 0.f || mPM.rttBaseZ != 0.f)
	{
		hasRtt = true;
		mRttBase.set(mPM.rttBaseX, mPM.rttBaseY, mPM.rttBaseZ);
	}
	else mRttBase.set(0.f, 0.f, 0.f);
	if (hasRtt)
	{
		needTransform = true;
		//assert(mPM.ptrFwKld > 0);
	}

	if (mPM.ptrFwKld > 0 && mPM.tgtVer < 300)
		mPM.tgtVer = 300;
	mCreateTime = Ctx->gd.time;

	//compelete spsList data
	for (auto& sps : spsList)
	{
		sps.fwIdx = -1;
		for (int i=0;i< ptrFws[2].size();i++)
		{
			if (sps.fwId == ptrFws[2][i].idStr && ptrFws[2][i].idStr.length()>=1)
			{
				sps.fwIdx = i;
				break;
			}
		}
	}

	for (auto& fw : ptrFws[0])
	{
		memset(fw.tfp.sps,0,sizeof(fw.tfp.sps));
		ualib::StrVec sv;
		ualib::split(fw.tfp.spsId,',',sv);
		int sc = std::min(16, (int)sv.size());
		fw.tfp.spsCount = sc;
		for (int si=0; si<sc;si++)
		{
			fw.tfp.sps[si] = nullptr;
			for (int i = 0; i < spsList.size(); i++)
			{
				if (sv[si] == spsList[i].id && spsList[i].id.length() >= 1)
				{
					fw.tfp.sps[si] = &spsList[i];
					break;
				}
			}
		}
	}


	//Ctx->setCameraId(mPM.camType);
	FwMan.loadStaticFwFinish();

	inited = true;	

	//setCurPtrFwId(0);
	recreateIndex++;
	assert(!FwMan.embOverflow);
	DP(("EQV INITED"));
	return true;
}
int EQV::loadDynFw(ualib::UaJsonSetting& jssStkFws)
{
	std::vector<EQVisual::EqvFwStruct>& fws = ptrFws[eFtSTK];
	fwStrokes.clear();
	fws.clear();



	jssStkFws.ProcessSubs([&](std::string key, Json::Value v) {
		//DP(("%s = %s",vn.c_str(),v.toStyledString().c_str()));

		if (key == "fws") for (auto& fw : v) {
			EqvFwStruct efs;
			efs.idStr = fw["id"].asString();
			efs.gfd.FwId = FwMan.AddFw(fw["fw"], efs.idStr);
			fws.push_back(efs);
		}
		else if (key == "fwStrokes") for (auto& stk : v) {
			for (const Json::Value& item : v) {
				StrokeFwInfo fs;
				fs.id = item["id"].asString();
				fs.rbRadius = UaJson::getIfFloat(item, "rbRadius", 1.0f); // TODO:implement UaJson::getIfHas if has rbRadius get value, or use default
				fs.linkRMul = UaJson::getIfFloat(item, "linkRMul", 1.5f); // TODO:implement UaJson::getIfHas if has rbRadius get value, or use default
				fs.density = UaJson::getIfFloat(item, "density", 100.f);
				auto joint = item["joint"];
				UaJson::getFloats(joint["limitT"], fs.limitT, 3, nullptr);
				//UaJson::getFloats(joint["limitR"], fs.limitR, 3, nullptr);
				UaJson::getFloats(joint["springT"], fs.springT, 3, nullptr);
				//UaJson::getFloats(joint["springR"], fs.springR, 3, nullptr);
				UaJson::getFloats(joint["dampingT"], fs.dampingT, 3, nullptr);
				//UaJson::getFloats(joint["dampingR"], fs.dampingR, 3, nullptr);
				const Json::Value& fwvs = item["stkfws"]; // Use reference instead of copy
				if (fwvs.isArray()) {
					fs.stkfws.reserve(fwvs.size()); // Reserve memory for fwIds vector
					for (const Json::Value& fw : fwvs) {
						fs.stkfws.emplace_back(fw["fwid"].asString(), fw["multi"].asInt(), UaJson::getFloat4(fw["color0"]), UaJson::getFloat4(fw["color1"]), fw["skip"].asInt()+1 );
					}
				}
				fwStrokes.emplace_back(std::move(fs)); // Move fs into fwStrokes vector				
			}
		}



		});
	return 0;
}


const TfData* EQV::findTfdFromList(std::string_view id)
{
	for (auto& t : tfdList)
	{
		if (t.id == id)
			return &t;
	}
	return nullptr;
}

void EQV::AppendNode(std::shared_ptr<EqvNode> node)
{
	//node->SetEQV(this);
	mNodes.push_back(node);
}

void EQV::UpdateFrame(float stepTime)
{

	if (needRecreateOnUpdate || !inited) return;

	if (mEmitAll && ppm.ptCount>0 && pifFramePtrEvts.size() == 0)
	{
		irr::SEvent::SPointInput ev{};
		ev.act = EPA_Move;
		ev.X = 0000; ev.Y = -1000;
		pifFramePtrEvts.push_back(ev);
		if (ppm.id >= ppm.ptCount - 1)
			mEmitAll = false;
	}

#if USE_SVG && HAS_MMD
	if (svgMan) svgMan->updateFrame(this);
#endif

	if (pifFramePtrEvts.size() && pfwLock.try_lock())
	{
		PtrImFramegProcessPtrEvts(curPtrFwIds[0]);
		pifFramePtrEvts.clear();
		pfwLock.unlock();
	}

	mFrameBallFwCount = 0;


	if (TAMgr.getStatus() == EqvTouchActionManager::ES_Recording)
	{
		TAMgr.RecordCheck(TamNow());
	}
	else 	if (TAMgr.getStatus() == EqvTouchActionManager::ES_Replaying)
	{
		const PointerEventStruct* pe;
		int isEnd;
		auto& dsd = Ctx->getDriver()->dsd;
		int pt = 0; 
		if (dsd.downPtrCount < FW_MAX_POINTER - 1)
			pt=dsd.downPtrCount++;
		while (pe = TAMgr.ReplayGetEvent(TamNow(), isEnd))
		{

			dsd.pointerVel[pt]= pe->vel;
			DrawPointerInput(pe->pi,pt);
			if (isEnd)
			{

			}
		}
	}



	if (mIFppt) {
#if TODO_RTT
		if (hasRtt)
		{
			//if (g_sd.fwNeedResetTransform)
			//{
			//	g_sd.fwResetTrransformCD--;
			//	mRotVec *= 0.925;
			//	if (g_sd.fwResetTrransformCD < 1)
			//	{
			//		g_sd.fwResetTrransformCD = 60, g_sd.fwNeedResetTransform = false;
			//		mRotVec.set(0, 0, 0);
			//	}
			//}
			//else 
			{
				float mt = 1.f;
				float dt = std::max(0.0, Ctx->gd.time - mCreateTime);
				if (dt < 5.f)		mt = dt / 5.f;
				mRotVec += mRotSpeed * stepTime * mt;
			}
			auto Nrm180 = [=](float& x) {
				x = fmodf(x, 360.f); if (x > 180.f) x -= 360.f; else if (x < -180.f) x += 360.f;
			};
			Nrm180(mRotVec.X);// = fmodf(mRotVec.X, 360.f); if (mRotVec.X > 180.f) mRotVec.X -= 360.f; if (mRotVec.X > 180.f) mRotVec.X -= 360.f;
			Nrm180(mRotVec.Y);// = fmodf(mRotVec.Y, 360.f); if (mRotVec.Y > 180.f) mRotVec.Y -= 360.f; if (mRotVec.Y > 180.f) mRotVec.Y -= 360.f;
			Nrm180(mRotVec.Z);// = fmodf(mRotVec.Z, 360.f); if (mRotVec.Z > 180.f) mRotVec.Z -= 360.f; if (mRotVec.Z > 180.f) mRotVec.Z -= 360.f;
			//mIFppt->setRotation(mRttBase + mRotVec);
			//mIFppt->setRotation({ 90, 90, 0 });
		}
		//mIFppt->setRotation(0,sin(mRotVec.Y/60)*15,0);
		//mIFppt->updateAbsolutePosition();
#endif
	}

	for (auto& n : mNodes)
	{
		n->UpdateFrame(stepTime);
	}
	if (mIFppt) {

		mIFppt->SetGravity(core::vector3df(mPM.baseGX, mPM.baseGY, mPM.baseGZ));
		mIFppt->SetDeceleratePower(mPM.dcrPw);
#if USE_IFPPT_TXT
		mIFpptTxt->SetGravity(core::vector3df(mPM.baseGX, mPM.baseGY, mPM.baseGZ));
		mIFpptTxt->SetDeceleratePower(mPM.dcrPw);
#endif
#ifdef _DEBUG
		//LaunchFw3D(core::vector3df(Ctx->gd.screenX, Ctx->gd.screenY, 0), 1);
#endif
	}

	Tlp->tlpUpdate(TamNow());

	pfNodesUpdate(stepTime);


#if CAM_TIMELINE_ANIMATION
	if (Sap.timer > 0.f)
	{
		Sap.timer -= stepTime;
		if (Sap.timer <= 0.f)
		{
			snSbtTgt->setPosition(Sap.pos);
			snSbtTgt->setRotation(Sap.rtt);
			snSbtTgt->updateAbsolutePosition();
			//snSbt->updateAbsolutePosition();
			snSbtSrc->setPosition(snSbtItv->getPosition());
			snSbtSrc->setRotation(snSbtItv->getRotation());
			snSbtSrc->updateAbsolutePosition();
			sbtAnimating = true;
			sbtAniDur = Sap.durS;
			sbtAniTime = 0.f;
			Ctx->gd.CamNormal->setParent(snSbtItv);
			snSbtCamMovT->setPosition(mTfpm.plane == 0 ? vector3df(0,0,Sap.camZ): vector3df(0, -Sap.camZ, 0 ));
			snSbtCamMovS->setPosition(Ctx->gd.CamNormal->getPosition());

		}
	}
	if (sbtAnimating)
	{
		sbtAniTime += stepTime;
		float ratio = sbtAniTime / sbtAniDur;
		if (ratio > 0.996f)
		{
			sbtAnimating = false;
			ratio = 1.f;
			//if (Sap.timer > 0.001f) Sap.timer = 0.001f;
		}
		//ratio = uu::blendFun(uu::Ebf::easeInOutSine, ratio);;
		//ratio = 0.1;
		auto src = snSbtSrc;
		snSbtItv->setPosition(snSbtTgt->getPosition().getInterpolated(src->getPosition(), ratio));
#if !APP_CUBE_SPHERE
		snSbtItv->setRotation(snSbtTgt->getRotation().getInterpolated(src->getRotation(), ratio));
		Ctx->gd.CamNormal->setPosition(snSbtCamMovT->getPosition().getInterpolated(Ctx->gd.CamNormal->getPosition(), ratio));
		Ctx->getDriver()->dsd.stdDistance = Ctx->gd.CamNormal->getPosition().getLength();
#endif
		snSbtItv->updateAbsolutePosition();
		snSbtCamMovT->updateAbsolutePosition();
		
		//Ctx->gd.CamNormal->bindTargetAndRotation(true);
		vector3df up(mTfpm.plane == 0 ? vector3df(0, 1, 0) : vector3df(0, 0, 1));
		snSbtItv->getAbsoluteTransformation().rotateVect(up);
		Ctx->gd.CamNormal->setUpVector(up);
		Ctx->gd.CamNormal->setTarget(snSbtItv->getAbsolutePosition());
		Ctx->gd.CamNormal->updateTransform();
	}
#endif

#if USE_AR_DATA && USE_AR_LAUNCHER
	{
	auto cam = Ctx->gd.CamNormal;
	matrix4 mat; vector3df firePos;
	mIFppt->getAbsoluteTransformation().getInverse(mat);
	mat.transformVect(firePos, snGun->getAbsolutePosition());
	fireVec = (cam->getTarget() - cam->getAbsolutePosition()); fireVec.Y = 0;
	mat.transformVect(fireVec, fireVec);
	
	LaunchFw3D(V3dToFloat3(firePos), GetPtrFws_FwId(2,1), V3dToFloat3(fireVec));
	DP((""));
	}
#endif

#if DRAW_SHADER_TOY
	if (mNodes.size()) 
	for (int i = 0; i < eqvBandCount; i++) {
		auto bi = mNodes[0]->getBandData(i);
		if (bi)
			SdToy->fTexDat[i].x = core::clamp(bi->cy/100,0.f,1.f);
	}
	
#endif
}

void EQV::initAR() {
#if USE_AR_DATA
	if (!pifSnOrigin)
	{
		pifSnOrigin = Ctx->getSceneManager()->addCubeSceneNode(30.f,Ctx->gd.RootSn);
		pifSnOrigin->setVisible(false);
		auto cr1 = Ctx->getSceneManager()->addCubeSceneNode(10.f, pifSnOrigin, -1, { 0,0,0 });
		cr1->setScale(1, 1, 10 * MMD_SABA_SCALE);
		auto cr2 = Ctx->getSceneManager()->addCubeSceneNode(10.f, pifSnOrigin, -1, { 0,0,0 });
		cr2->setScale(10 * MMD_SABA_SCALE, 1, 1);
		//pifSnOrigin->getMaterial(0).EmissiveColor = 0x20FF0000;
		//pifSnOrigin->setMaterialType(EMT_TRANSPARENT_ALPHA_CHANNEL);
		//pifSnOrigin->getMaterial(0).DiffuseColor = 0x80FFFFFF;
		pifSnCamY = Ctx->getSceneManager()->addCubeSceneNode(10.1f);		//pifSn1->setVisible(false);
		pifSnCamY->getMaterial(0).EmissiveColor = 0xFFFF0080;
#if DRONE_AR
		pifSnCamX = Ctx->getSceneManager()->addCubeSceneNode(200, pifSnCamY);
		pifSnCamX->getMaterial(0).DiffuseColor = 0x80FFFF00;

		pifSnCam = Ctx->getSceneManager()->addCubeSceneNode(200, pifSnCamX);
		pifSnCam->getMaterial(0).DiffuseColor = 0x8000FFFF;
#else
		pifSnCam = pifSnCamY;
#endif
		pifSn1sub = Ctx->getSceneManager()->addCubeSceneNode(1.1f, pifSnCam);
		pifSn1sub->setRotation({ 0,0,0 });
		// Ctx->getSceneManager()->addCubeSceneNode(100.1, pifSn1sub, -1,{ 0,200,0 });
		//pifSn1sub->setMaterialType(EMT_TRANSPARENT_ALPHA_CHANNEL);
		pifSn1sub->getMaterial(0).DiffuseColor = 0x8000FF00;
		pifSn1sub->setMaterialFlag(EMF_LIGHTING, false);

#if USE_AR_LAUNCHER


		snGun = Ctx->getSceneManager()->addCubeSceneNode(0.1, pifSnCam, -1, { 0,-10,0 });
#endif

		pifUpOfSnCam = Ctx->getSceneManager()->addCubeSceneNode(0.1f, pifSnCam);
		pifUpOfSnCam->getMaterial(0).EmissiveColor = 0x800000FF;
		//if (Ctx->gd.scrWidth < Ctx->gd.scrHeight)			pifUpOfSnCam->setPosition(1000.f, 0.f, 0.f);		else 
			pifUpOfSnCam->setPosition(0.f, 10 * MMD_SABA_SCALE, 0.f);

		pifSnCamY->setVisible(false);


#if DRONE_AR
		{
			LevelWheelParam pm; pm.eqv = this; pm.ctx = Ctx;
			snLW = new SnLevelWheel(Ctx->gd.RootSn, SceneManager, -1, pm);
		}
#endif
		arInited = true;
	}
#endif
}
float EQV::JsonToDS(int& us, Json::Value& vcd,
	MatrixRecorder::DrDataStruct& ds, int datLen, int portrait)
{
	float dat[256];
	auto v = vcd["pt"];
	if (v.isNull())
		v = vcd.get("ptsUs", 0);
	us = v.asInt();
	auto vm = vcd["vm"];

	core::matrix4 matV, matP;

	
	for (int i = 0; i < datLen; i++)
		dat[i] = vm[i].asFloat();
	//for (int i = 0; i < 16; i++) matV[i] = dat[i];
	//for (int i = 0; i < 16; i++) matP[i] = pm[i].asFloat();
	//mv = mv.getTransposed();
	//auto rtt = mv.getRotationDegrees();
	//auto scl = mv.getTranslation();
	//DP(("rtt %3.2f,%3.2f,%3.2f     trs  %3.2f,%3.2f,%3.2f  ",rtt.X,rtt.Y,rtt.Z,scl.X,scl.Y,scl.Z));
	//m.setScale(m.getScale() * 0.01);
	//ds.d.mat = matV;// .getTransposed();

	float asW = dat[7], asH = dat[8];// asW = matP[0], asH = matP[5];
	float as = asH / asW;
	float fovH = (atan(1.f / asH)) * 2;//(atan(1.f /  ((portrait) ? asW : asH))) * 2;

	//float fovWD = (atan(1.f / asW)) * 2 *180/core::PI;//UE5
	//float fovHD = (atan(1.f / asH)) * 2 *180/core::PI;
	//float fovWDC = atan(tan(fovH / 2) * 16 / 9) * 2 * 180 / core::PI;
	//Ctx->gd.CamNormal->setNearValue(1);
	//Ctx->gd.CamNormal->setFarValue(10000* MMD_SABA_SCALE);
	//Ctx->gd.CamNormal->setPosition(0, -1000,0);

	ds.d.pos.set(-(dat[0]), dat[1], (dat[2]));
	irr::core::quaternion qtn(dat[3], dat[4], dat[5], dat[6]);
	ds.d.po1.set(-dat[9], dat[10], dat[11]);
	//ds.d.qt1.set(dat[12], dat[13], dat[14], dat[15]);
	ds.d.vec2.set(dat[16], dat[17], dat[18]);
	if (datLen > 20) {
		ds.d.rttYadj = dat[19];
		ds.d.scaleAdd = dat[20];
	}
	vector3df rtt; qtn.toEuler(rtt); rtt *= RADTODEG;
	rtt.set(-(rtt.X),
		-(rtt.Y),
		rtt.Z);
	//vector3df rtt1; ds.d.qt1.toEuler(rtt1); rtt1 *= RADTODEG;
	//rtt1.set(-(rtt1.X),
	//	-(rtt1.Y),
	//	-rtt1.Z);
	ds.d.rtt = rtt;
	// ds.d.rtt1 = rtt1;

#if CAMY_ADDRTT
	//mcrCAMY_ADDRTT.transformVect(ds.d.pos);
	//mcrCAMY_ADDRTT.transformVect(ds.d.po1);
	ds.d.pos = ds.d.pos - ds.d.po1;
	mcrCAMY_ADDRTT.transformVect(ds.d.pos);
#endif
#ifdef AR_DATA_POSMUL
	ds.d.pos *= AR_DATA_POSMUL;
#endif
	return fovH;
}
 
void EQV::JsonToAnc(Json::Value& vcd, AnchorStruct& as)
{
	float dat[256];
	as.timeS = vcd["timeS"].asFloat();
	auto vm = vcd["ad"];

	for (int i = 0; i < 10; i++)
		dat[i] = vm[i].asFloat();

	core::vector3df pos, rtt, po1;
	core::quaternion qtn;
	pos.set(-(dat[0]), dat[1], (dat[2]));
	qtn.set(dat[3], dat[4], dat[5], dat[6]);
	po1.set(-dat[7], dat[8], dat[9]);

	qtn.toEuler(rtt); rtt *= RADTODEG;
	rtt.set(-(rtt.X),
		-(rtt.Y),
		rtt.Z);

	as.rtt = rtt;

#if CAMY_ADDRTT
	//mcrCAMY_ADDRTT.transformVect(pos);
	//mcrCAMY_ADDRTT.transformVect(po1);
	as.pos = pos - po1;
	mcrCAMY_ADDRTT.transformVect(as.pos);
#endif
}

void EQV::JsonToDS_DJI(int& ms, Json::Value& vcd, MatrixRecorder::DrDataStruct& ds, int datLen, bool first)
{
#define  BASE_ON_FIRST 0
	int idx = 0;
	float rttBase = 0;
	vector3df	posBase;

	const float posMul = 10;  //scale scene  1/10 m = picel
	const float baseX = 0// 5500
		/ posMul, baseY =  AR_DRONE_ADDY  //12//
		/ posMul, baseZ = 0// -2360
		/ posMul;

	core::matrix4 mcv;

	float dat[32];
	ms = vcd["t"].asInt() - 600;
	auto dts = vcd["d"];
	for (int i = 0; i < datLen; i++)
		dat[i] = dts[i].asFloat();

	ds.d.pos.set((dat[2] - baseX) * posMul, (dat[1] - baseY) * posMul, (dat[0] - baseZ) * posMul);
#if BASE_ON_FIRST
	ds.d.pos.X -= posBase.X;
	ds.d.pos.Z -= posBase.Z;// = (ds.d.pos - posBase);

	if (!first) mcv.transformVect(ds.d.pos);
#endif
	float ry = dat[6], ay=dat[7];
	static float ory, oay,oDeltaYaw;
	static float ryAdd = 0.f,ayAdd = 0.f;
	//if (ry<0) ry += 360.f ;
	if (first) {
		ory = ry; ryAdd = 360.f;
		oay = ay; ayAdd = 360.f;
	}

	if (ory >= 90 && ry < -90)		ryAdd += 360.f;
	else if (ory < -90 && ry >= 90)	ryAdd -= 360.f;
	ory = ry;
	if (oay >= 90 && ay < -90)		ayAdd += 360.f;
	else if (oay < -90 && ay >= 90)	ayAdd -= 360.f;
	oay = ay;

	ds.d.rtt.set(-dat[5], (ry+ AR_DRONE_ADDRTTY + ryAdd), -dat[4]);
	ds.d.vec2.set(ds.d.rtt.X, ay + AR_DRONE_ADDRTTY + ayAdd, ds.d.rtt.Z);
	if (first)
	{
		oDeltaYaw =  ds.d.vec2.Y - ds.d.rtt.Y ;
	}
	DP(("YAWDELTA %f", ds.d.vec2.Y - ds.d.rtt.Y));
	ds.d.rtt.Y += oDeltaYaw;
	//DP(("DAT %f", ay + AR_DRONE_ADDRTTY + ayAdd));
	ds.d.scaleAdd = dat[3];
#if BASE_ON_FIRST
	if (first) {
		first = false;
		posBase = ds.d.pos; ds.d.pos.set(0, 0, 0);
		rttBase = ds.d.rtt.Y;
		ds.d.rtt.Y = 360;
		mcv.setRotationDegrees({ 0,180 - rttBase,0 });
	}
	else
		ds.d.rtt.Y = (ds.d.rtt.Y - rttBase) + 360;
#else
		//ds.d.rtt.Y = ds.d.rtt.Y ;
#endif
	//DP(("DRONE %f,%f,%f \n Rtt %f,%f,%f", ds.d.pos.X, ds.d.pos.Y, ds.d.pos.Z, ds.d.rtt.X, ds.d.rtt.Y,ds.d.rtt.Z));


}

void EQV::loadMatRecData(std::string strJson)
{
	ancDatVec.clear();
#if USE_AR_DATA

	ualib::UaJsonSetting jss("camdatjson");
#if DRONE_AR
	if (strJson.size() == 0)		jss.SetFile(arDatPath);
	else
#endif
		jss.LoadJsonString(strJson);
	auto v = jss.copyRootValue();
	Json::Value format = v["format"];
	if (format == "raw")
	{
		MatRec->raw = true;
		isColmapVideo = MatRec->raw;
#if USE_AR_DATA
		MatRec->drBeginRecord(0, false);
#endif
		timeDiv = 1;
		ar.camPosScale = 1;
		ar.camSrcOfs.set(0, 0, 0);
		Json::Value ver = v["ver"];
		float fovY = v["fovY"].asFloat();



		MatRec->timeOfs = !v["timeOfs"].isNull()?v["timeOfs"].asFloat() : COLMAP_DEFAULT_TIMNEOFS;
		Json::Value fps = v["fps"];
		MatRec->arRootScale = v["arRootScale"].asFloat();
		 
		if (fps.isInt())  MatRec->fps = fps.asInt();
		else MatRec->fps = 30;
		Json::Value vList = v["frames"];
		MatrixRecorder::DrDataStruct ds;
		Json::Value vMoldelList = v["models"];
		bool hasCmRoot = false;
		if (vMoldelList.isArray()) for (auto& vcd : vMoldelList DOT_AS_ARRAY) {
			std::string name = vcd[0].asString();
			if (name == "pointCloud") {
				core::matrix4 m;
				for (int i = 0; i < 16; i++) m[i] = vcd[i + 1].asFloat();
				Ctx->gd.CMCloudMat = m; 
				hasCmRoot = true;
			}
		}
		
		for (auto& vcd : vList DOT_AS_ARRAY) {
			ds.d.fid = vcd[0].asInt();
			core::matrix4 m;
			for (int i = 0; i < 16; i++)
			{
				m[i] = vcd[i + 1].asFloat();
			}
			//ds.d.pos.set(vcd[1].asFloat(), vcd[2].asFloat(), vcd[3].asFloat());
			//ds.d.rtt.set(vcd[4].asFloat(), vcd[5].asFloat(), vcd[6].asFloat());
			ds.d.mat = m;
			ds.d.pos = ds.d.mat.getTranslation();
			ds.d.rtt = ds.d.mat.getRotationDegrees();

			MatRec->drRecordData(ds, ds.d.fid * 1000000.0/MatRec->fps, false);

		}

		Ctx->gd.CamNormal->setFOV(fovY);
		return;
	}

	MatRec->raw = false;
#if USE_AR_DATA
	MatRec->drBeginRecord(0, true);
#endif

	Json::Value ver = v["ver"];
	int portrait = 0;
	int verNum = 0;
	if (ver.isInt()) {

		MatRec->ArDatVer = verNum = ver.asInt();
		portrait = v["portrait"].asInt();
		ivPortrait = portrait;
	}
	Json::Value camToDisplayRtt = v["camRotation"];
	ARCamRotation = 0;
	if (camToDisplayRtt.isInt())
		ARCamRotation = camToDisplayRtt.asInt();
	else if (portrait) ARCamRotation = 90;
#if !HAS_AR_EDITOR
	else if (ver < 20100 && Ctx->gd.scrWidth < Ctx->gd.scrHeight)	ARCamRotation = 90; //for old videos most by 10105
#endif
	Json::Value vList = v["cdList"];
	Json::Value vAncList = v["ancList"];
	if (verNum >= 5) {
		timeDiv = v["timeDiv"].asInt();
		//Ctx->setFixFrameTime(true,1.f / APP_FPS / timeDiv);
	}
	int idx = 0;
	float rttBase = 0;
	vector3df	posBase;
	bool first = true;
	const float posMul = 50;
	const float baseX = 0// 5500
		/ posMul, baseY = -110  //12//
		/ posMul, baseZ = 0// -2360
		/ posMul;
	int id = 0;
	float ryAdd = 360.f;
	core::matrix4 mcv;
#if DRONE_AR	
	Json::Value mdPos = v["mdDat"];
	if (!mdPos.isNull() && mmd) {
		int ms;
		JsonToDS_DJI(ms, mdPos, dsMdDat, 8, first);

	}
	if (mmd)
		snLight->setParent(mmd->RootNode);
#endif
	float fovY = 60;
	for (auto& vcd : vList DOT_AS_ARRAY) {
		MatrixRecorder::DrDataStruct ds;
#if DRONE_AR

		int ms;
		JsonToDS_DJI(ms, vcd, ds, 8, first);
		if (verNum < 3)
			ds.d.vec2 = ds.d.rtt;
		MatRec->drRecordData(ds, ms, false);


#else
		int us;
		fovY = JsonToDS(us, vcd, ds, (MatRec->ArDatVer >= 10103 ? 21 : 19), portrait);
		//us += 16000;
#if AR_CAM_PORTRIT
		std::swap(ds.pos.X, ds.pos.Z);
		std::swap(ds.po1.X, ds.po1.Z);
#endif
		//DP((" % 3.2f, % 3.2f, % 3.2f     P2  % 3.2f, % 3.2f, % 3.2f  ", mv[16], mv[17], mv[18], mv[23], mv[24], mv[25]));
		//DP(("pos %8.3f  %8.3f  %8.3f pos1 %8.3f  %8.3f  %8.3f  ",ds.pos.X, ds.pos.Y, ds.pos.Z, ds.po1.X, ds.po1.Y, ds.po1.Z));
		//ds.qt1 = { mv[26],mv[27], mv[28], mv[29] };
		//ds.d.mat.setScale(ds.d.mat[0]*10);

		MatRec->drRecordData(ds, us, false);// 1.0);
#endif

		id++;
		first = false;
	}

	Ctx->gd.CamNormal->setFOV(fovY);
	for (auto& vcd : vAncList DOT_AS_ARRAY) {
		AnchorStruct as;
		JsonToAnc(vcd, as);

		ancDatVec.push_back(as);
	}
	MatRec->drEndRecord();
	ar.camPosScale = 10 * MMD_SABA_SCALE;
#if AR_PATH_ON_STEP
	MatRec->initPathData(100.f);
#endif
	//vmd->saveFile(&MatRec);
#endif
}


int EQV::ARCamUpdate(int mediaIdx, int ofs,float timeS, bool overrideTime)
{
	float ratio; bool isNew;
	const MatrixRecorder::DrDataStruct* dsNext=0;
	//bool firstFrame = MatRec->isFirstFrame();
	MatrixRecorder::DrDataStruct dsi;
	dsi.ts = timeS * 1000000;
	const MatrixRecorder::DrDataStruct* ds;
	bool useFid = MatRec->FullFrame && (UE_PLUGIN && Ctx->getVkDrv(0)->getExtDevInfo() || mediaIdx >= 0);
	if (useFid) {
		ds = MatRec->drGetData(
#if DRONE_AR
			false,

			-10, ofs, timeS * 1000, true, &isNew, &dsNext, &ratio);
		isNew = true;
		MatrixRecorder::DrDataStruct dsi = *ds;

		if (dsNext) {
			dsi.d.rtt.interpolate(dsNext->d.rtt, ds->d.rtt, ratio);
			dsi.d.pos.interpolate(dsNext->d.pos, ds->d.pos, ratio);
			snLW->vel = (dsNext->d.pos - ds->d.pos) * .1f;
			snLW->acYaw = USE_AIRCRAFT_YAW ? dsi.d.vec2.interpolate(dsNext->d.vec2, ds->d.vec2, ratio) : dsi.d.rtt;//;
		}

		//static MatrixRecorder::DrDataStruct lastDs = dsi;	snLW->vel = (dsi.d.pos - lastDs.d.pos);	lastDs = dsi;
		UpdateCamState(&dsi);

		//snLW->setCamPYR(dsi.d.rtt); //no need? use cam
#else
			useFid,

			mediaIdx, ofs, timeS * 1000000, true, & isNew, & dsNext, & ratio);

		if (dsNext && MatRec->raw) {
			dsi.d.mat = ds->d.mat;
			dsi.d.mat = dsi.d.mat.interpolateTR(dsNext->d.mat, ratio);
			ds = &dsi;
		}
		arLastIdx = MatRec->curIdx();
	}
	else {
		const MatRecData* a, * b; float ratio;
		arLastIdx = MatRec->getDataPtrUs(
			mediaIdx >= 0?		(double(mediaIdx) / MatRec->fps) * 1000000			: 
			timeS*1000000,
			a, b, ratio, arLastIdx);
		DP(("arLastIdx %8d   %8d   %8f", mediaIdx, arLastIdx, ratio));
		core::matrix4 mt = a->mat;
		mt = mt.interpolateTR(b->mat, ratio);
		
		dsi.d.mat = mt;
		ds = &dsi;
	}
	
	 

	if (ds)// && isNew)
	{
		if (MatRec->raw) {    //colmap
			//DP(("AR idx=%d  Mid=%d", mediaIdx,MatRec->curIdx()));
			auto cam = Ctx->gd.CamNormal;
			auto mat = ds->d.mat;
			if (MatRec->rootNode) mat = MatRec->rootNode->getAbsoluteTransformation() * mat;
			else mat = Ctx->gd.CMCloudMat * mat;
			cam->setPosition(mat.getTranslation());
			
			cam->setRotation(mat.getRotationDegrees());
			cam->upNode->updateTransform();
			cam->setUpVector(cam->getUpVectorByNode());
			cam->updateAbsolutePosition();

		}
		else UpdateCamState(ds);// DP(("AR idx=%d",MatRec->curIdx()));
	
		if (overrideTime && mmd)
			mmd->setOverrideTIme(ds->ts , Ctx->gd.time, MatRec->getDeltaTs()  );
		
	}
#endif


	Ctx->gd.mediaSrcFrameIdDrawn = ds ? Ctx->gd.mediaSrcFrameId : -1;
	return arLastIdx;
}

void EQV::UpdateCamState(const MatrixRecorder::DrDataStruct* ds, bool portrait)
{
	if (!arInited)
		return;
	{

		vector3df rtt, pos, tmpRtt, tmpPos;

#if DRONE_AR
#if 0//DRONE_AR_FILTER
		if (dsNext) {
			rtt.interpolate(dsNext->rtt, ds->rtt, ratio);
			pos.interpolate(dsNext->pos, ds->pos, ratio);
		}
		else
#endif
		{
			rtt = ds->d.rtt;
			pos = ds->d.pos;
		}


		//pifSn->setPosition(0, 0, 0);
		pifSnCamY->setPosition(pos * ar.camPosScale);
#else
		//ds->d.qtn.toEuler(rtt); rtt *= RADTODEG; //rtt.Y += rttYadj;
		
		
		pifSnOrigin->setPosition(0, 0, 0);

		pifSnCamY->setPosition((ds->d.pos + ar.camSrcOfs) * ar.camPosScale);

#endif
		
		//vector3df up(0, 1, 0);


#if DRONE_AR			
		pifSnCamY->setRotation({
			0,
			rtt.Y ,
			0
			});

		pifSnCamX->setRotation({ rtt.X,0,0 });
		pifSnCam->setRotation({ 0,0,rtt.Z });

		//pifSnCamY->updateAbsolutePosition();
		//pifSnCamX->updateAbsolutePosition();
		//pifSnCam->updateAbsolutePosition();
		
#if USE_AR_LAUNCHER
		snGun->updateTransform();
#else
		pifSnCam->updateTransform();
#endif
		pifSn1sub->updateAbsolutePosition();
		pifUpOfSnCam->updateAbsolutePosition();
#else
		

		//pifSnCamY->setRotation({			(rtt.X + 180) ,			-(rtt.Y + CAMY_ADDRTT + recRtt.Y),			rtt.Z 			});

		pifSnCamY->setRotation(ds->d.rtt );
		//DP(("CAMRTT %.3f %.3f %.3f", ds->d.rtt.X, ds->d.rtt.Y, ds->d.rtt.Z));
		if (ScrRotation==0 && Ctx->gd.scrWidth < Ctx->gd.scrHeight)
			ScrRotation = 90;
		if (ARCamRotation || ScrRotation)
		{
			//ScrRotation = ARCamRotation;
			matrix4 mrtt; mrtt.setRotationDegrees({ 0, 0, ARCamRotation });
			mrtt = pifSnCamY->getRelativeTransformation() * mrtt;
			pifSnCamY->setRotation(mrtt.getRotationDegrees());
			//pifSnCamY->setPosition(mrtt.getTranslation());
		}
		//DP(("SCALEADD %f", ds->d.scaleAdd));
		Ctx->gd.RootSn->setScale(ds->d.scaleAdd* arCamPosMul);
		float rttY = rttYadj + ds->d.rttYadj;
		if (rttY !=0.f) {
			matrix4 mrtt; mrtt.setRotationDegrees({ 0, rttY, 0});
			mrtt = mrtt * pifSnCamY->getRelativeTransformation();
			pifSnCamY->setRotation(mrtt.getRotationDegrees());
			pifSnCamY->setPosition(mrtt.getTranslation());
		}

		if (pifSn1sub) {
			pifSn1sub->setPosition(0, 0, Ctx->getDriver()->dsd.stdDistance);
			pifSn1sub->updateTransform();
			
		}
		else
			pifSnCam->updateTransform();
		pifUpOfSnCam->updateAbsolutePosition();
		if (AR_USE_LIGHT  && (!MatRec || MatRec->ArDatVer >= 10001))
		{
			auto snl = Ctx->gd.Light[0];
			vector3df ld = ds->d.vec2; ld.rotateYZBy(rttLightAddX); ld.rotateXZBy(rttLightAddY);
			snl->setPosition(ld * 500 * MMD_SABA_SCALE);
		}
#endif
		

		//quaternion qt(pifSn1->getRotation() * DEGTORAD);
		//qt.getMatrix().rotateVect(up);

		//if (!Ctx->gd.CamMovie) Ctx->gd.CamMovie = Ctx->getSceneManager()->addCameraSceneNodeFPS();
		auto cam = Ctx->gd.CamNormal;
		//Ctx->getSceneManager()->setActiveCamera(cam);
		//Ctx->gd.CamNormal->setParent(pifSn1); 
		cam->setPosition(pifSnCam->getAbsolutePosition());
		cam->bindTargetAndRotation(true);
		
		cam->setUpVector(
			
			(pifUpOfSnCam->getAbsolutePosition() - pifSnCam->getAbsolutePosition())
		);
		auto rttcc = pifSnCam->getAbsoluteTransformation().getRotationDegrees();
		cam->setRotation(rttcc);// pifSnCamY->getRotation());
		cam->updateAbsolutePosition();
		//cam->updateMatrices();
		

#if 0
		{
			static int cc = 0; cc++;
			char buff[1000];
			auto pos = cam->getPosition();
			auto rtt = cam->getAbsoluteTransformation().getRotationDegrees();
			snprintf(buff, sizeof(buff), "matrec %d|%f,%f,%f\n%f,%f,%f\n%f\nSC=%f",cc, pos.X, pos.Y, pos.Z,
				rtt.X, rtt.Y, rtt.Z, cam->getAbsoluteTransformation().getRotationDegrees().Y, ar.camPosScale);
			dbgTxt = buff;
			DP((buff));
			pos.Y -= 5;
			LaunchFw3D(V3dToFloat3(pos), GetPtrFws_FwId(2, 1), { 0,0,0 });
		}
#endif
		//pifSn->setPosition(ds->po1 * 1000);// Ctx->gd.CamNormal->getTarget());
		//pifSn->updateAbsolutePosition();


		//mat.setTranslation(cpos *1000);

		//Ctx->gd.CamNormal->setOverrideViewMatrix(&mat);
		//Ctx->gd.CamNormal->setOverrideProjMatrix(&mp);

	}
}


void EQV::onUpdateEqvData(const EqvUpdateParam& pm)
{

	//DP(("UD %f %I64d",Ctx->gd.time , PM.eqv->bvd->ts));

	float sump3 = 0;
	float sum = 0;
	eqvBandCount = std::min(int(pm.eqv->bvd->count), EQ_MAX_BANDS);
	{//peak
		int evMax = std::min(EQ_WAV_BUF_LEN / 2, int(pm.eqv->bvd->count));
		float maxV =0.f;
		float* pwave = pm.eqv->bvd->val;
		evMax = std::min(evMax,EQ_MAX_BAND);
		for (int i = EQ_MIN_BAND; i < evMax; i += 1)
		{
			float v = pwave[i]/200.0 ;
			if (v > maxV) maxV = v;
		}
		CurWavePeak = maxV;
		
		for (int i = 7; i > 0; i--) lastPeaks[i] = lastPeaks[i-1];
		lastPeaks[0] = CurWavePeak;
		isPeakPeak = (lastPeaks[0]>0.0 && lastPeaks[0]<= lastPeaks[1] && lastPeaks[1]>=lastPeaks[2] && lastPeaks[2] > lastPeaks[3]);
		if (isPeakPeak)
		{
			int maxBand = 0;
			for (int i = 0; i < eqvBandCount; i++)
				if (pm.eqv->bvd->val[i] > pm.eqv->bvd->val[maxBand]) maxBand = i;
			peakBand = maxBand;
			peakFreq = pm.eqv->bandFreq[maxBand];
			peakVal = lastPeaks[1];
		}
		//DP(("max V %f",maxV));
#if 1 //worked but not so good

		{
			float lpk = peaks[curPeak];
			peaks[curPeak] = CurWavePeak;
			sumPeak += CurWavePeak;
			curPeak = (curPeak + 1) % WAV_PEAK_MAX;
			if (peakNum < WAV_PEAK_MAX) peakNum++;
			else
			{
				sumPeak -= lpk;
			}
			float avr = sumPeak / peakNum;
			//if (mIFppt) mIFppt->mPG->cb.mainPeakValue = 0.7+0.5*avr;
			AvrWavePeak = avr;
		}
		

#endif
#if DRAW_SHADER_TOY
		memset(SdToy->stc.freqs, 0, sizeof(SdToy->stc.freqs));
#endif
		float maxBV = 0;
		for (int i = 0; i < eqvBandCount; i++)
		{
			float v = pm.eqv->bvd->val[i]/128.f;
			sump3 += pow(v, 3);
			sum += v;
			if (v > maxBV) maxBV = v;
#if DRAW_SHADER_TOY
			SdToy->stc.freqs[i%4] = std::min(1.f,SdToy->stc.freqs[i % 4]+v/8) ;
			//SdToy->fTexDat[i].x = v;// clamp(v, 0.f, 1.f);
#endif
		}
#if DRAW_SHADER_TOY
		SdToy->stc.vol = std::min(1.f, maxBV);
		DP(("FREQ %f ", SdToy->stc.vol));
#endif
		bandSumP3 = sump3;
		bandSum = sum;

	}

	for (auto& n : mNodes)
	{
		n->UpdateData(pm);
	}

	 

	//if ( mPM.kaleido == 0 && AvrWavePeak > LastWavePeak*1.1f && ( GetPtrFwId() == 0 ||  GetPtrBeatFwId()>0))
	//{
	//	float3 v(0.f, 100 + AvrWavePeak * 100 + pow((AvrWavePeak - LastWavePeak)*3 , 2)*100, 0.f);
	//	g_pls->LaunchFwAtPointer(9, &v);
	//}

	if (mPM.ptrFwKld == 0 && sum > lastSum * 1.03f && (GetPtrFws_FwId() == 0 || GetPtrBeatFwId() > 0))
	{
		float3 v(0.f, (FW_BASE_VY * 0.27 + sum / eqvBandCount * 15 + pow((sum - lastSum) * 1.5f / eqvBandCount, 2) * 3) * 0.1f, 0.f);
#ifdef _WIN32
		//assert(0);///g_pls->LaunchFwAtPointer(9, &v);
#endif
	}

	//gun fire
	//if (AvrWavePeak > 0.01f && sum> lastSum*1.03f)

	{

#if 0
		LaunchFw3D(V3dToFloat3(firePos), GetPtrFws_FwId(0, ink.penPtrId),
			//{ 0,0,0 }
			V3dToFloat3(fireVec.normalize() * 10000 * AvrWavePeak * AvrWavePeak)
		);
#endif
	}



	lastSumP3 = sump3;
	lastSum = sum;;
	LastWavePeak = AvrWavePeak;
	mIFppt->SetAvgPeak(pow(lastSum /100.f,0.5)*0.2f);


}
#if HAS_MIDI
void EQV::UpdateMidiData(const midilib::MidiEventStruct& mes)
{
	for (auto& n : mNodes)
	{
		n->UpdateMidiData(mes);
	}
}
#endif
void EQV::DrawPointerInput(const irr::SEvent::SPointInput& pe, int fwptid)
{
	float prs = 0.f;
	static float minP = 1.f, maxP = 0.01f;
	auto driver = Ctx->getDriver();

	float camdis = driver->dsd.stdDistance ;
 
	vector3df vel = driver->dsd.pointerVel[fwptid];
	//float spd;

	bool isPen = pe.isPen;
	//isPen = false;
	if (isPen)
	{

		prs = pe.pressure;
		//spd = irr::core::clamp(10000 * (1.0f - pe.pressure), 10.f, 10000.f);;
	}
	else
	{

#if 0
		if (pe.pressure > 0.0f)
		{
			if (pe.pressure > maxP) maxP = pe.pressure;
			if (pe.pressure < minP) minP = pe.pressure;
			prs = pe.pressure;
			prs = (prs - minP) / (maxP - minP);
			if (prs <= 0.f) prs = 0.f;
		}
#else
		prs = 0.5;
#endif
		//spd = irr::core::clamp(1000.f + 9000 * prs, 1000.f, 10000.f);
	}


	auto cam = Ctx->getSceneManager()->getActiveCamera();
	irr::core::matrix4 mat,mv = cam->getViewMatrix();
	(mv).getInverse(mat);

	vector3df vec(vel.X * 100, -vel.Y * 100, vel.Z * 100);
	//DP(("PRS %f   %f", prs, spd));

	float x = pe.X, y = pe.Y, z = 0;// cz * 0.5*prs;
	
	if (Ctx->isNormalCamera()) srcTransScale = 1.f;
	else if (pe.Control)
	{
		plane3df pln(vector3df(0, 0, 0), vector3df(0, 1, 0));
		vector3df ipt,vecIn;
		vecIn = vector3df(Ctx->gd.screenX + x, Ctx->gd.screenY - y, camdis) ;
	
		mat.transformVect(vecIn); 
		//cam->getViewMatrix().transformPlane(pln);
	 
		if ((pe.act == irr::EPA_Down) && pln.getIntersectionWithLine(Ctx->gd.CamRtt->getPosition(),
			vecIn- Ctx->gd.CamRtt->getPosition(),
			//,
			ipt))
		{
			//static scene::ISceneNode* sn = Ctx->getSceneManager()->addSphereSceneNode(100);

			float tgtDis = (ipt- Ctx->gd.CamRtt->getPosition()).getLength();
			//mat.transformVect(ipt);		
			//sn->setPosition(vecIn);
			//Ctx->gd.CamRtt->getPosition().getLength();
			srcTransScale = tgtDis / camdis;
			//DP(("inters %f,%f,%f       %f / %f = %f", vecIn.X, vecIn.Y, vecIn.Z, tgtDis,camdis, srcTransScale));

		}
	}else srcTransScale= ( Ctx->gd.CamRtt->getPosition()).getLength() / camdis;
	//auto pos = ScrTo3D(x, y, z);

	LfwParam lp;
	lp.bid =  TAMgr.getStatus() == EqvTouchActionManager::ES_Recording ? TAMgr.getStrokeCount(): -1;
	lp.kldMode = mPM.ptrFwMode;
	lp.kldCount = mPM.ptrFwKld;
	lp.kldBaseDeg = mPM.ptrFwBaseDeg;

	
	
	float zWidth = mPM.ptrStrokePM1[0];
	float zBase = mPM.ptrStrokePM1[1];
#if 1
	float zRatio = (ink.useSpeed && !isPen ? pow(zBase, core::clamp(vel.getLength(), 0.01f, 16.0f)) : 1.0f)
			*
		(ink.usePressure || isPen? pe.pressure* (ink.weightPressurePct / 100.0f) : 1.0f);
	lp.pmz = zWidth * zRatio * pow(2.0f, ink.weightPct / 100.0f);
#else
	float zRatio = pow(zBase, core::clamp(vel.getLength(), 0.01f, 16.0f));
	lp.pmz = zWidth * zRatio;
#endif

	//DP(("zRatio=%f ",zRatio));
	static float lastPmz = 0.f;



	SColor sc;
	 

	// DP(("INK m=%d c=%X %X",ink.colorMode,ink.color1,ink.color2));
	switch (ink.colorMode)
	{
	case 1:sc = ink.color1; break;
	case 2:
	{
		float hsl_l = core::max_(1.f, mPM.ptrStrokePM1[5] - mPM.ptrStrokePM1[6] * pow(zRatio, mPM.ptrStrokePM1[7]));
		SColorHSL hsl(ink.hueAdd, 100, hsl_l);
		sc = hsl.toSColor();
	}break;
	case 3:
	{
		float hsl_l = core::max_(1.f, mPM.ptrStrokePM1[5] - mPM.ptrStrokePM1[6] * pow(zRatio, mPM.ptrStrokePM1[7]));
		SColorHSL hsl;
		hsl.fromRGB(SColorf(ink.color1));
		hsl.Hue += ink.hueAdd;
		sc = hsl.toSColor();
	}break;
	}
	switch (pe.act)
	{
	//case irr::EPA_Down:
	case irr::EPA_Move:
	{
		int ptrId = GetPtrFws_FwId(0, ink.penPtrId);
		if (ptrId == 0)
			break;
		//if (pe.isMouseBtnDownWhenMove(EMBSM_LEFT))
		
		vector2df last(pe.lastX, pe.lastY);
		vector2df curr(pe.X, pe.Y);
		float dis = (curr - last).getLength();

		//mIFppt->getAbsoluteTransformation().getInverse(mat);	

		//DP(("CAM %f,%f,%f ",cam->getPosition().X, cam->getPosition().Y, cam->getPosition().Z));
		if (dis>0.5// || pe.act== EPA_Down
			)
		{
			irr::core::vector3df pos1,vec1;
			//if (false)
			if (pe.lastX >= 0 && pe.lastY >= 0)
			{		
				float space = 1.f;
				if (dis >= space * 2.f && dis < 100.f)
				{
					int c = dis / space;
					LfwParam clp = lp;  clp.flag |= PFLAG_FORBID_CV1;
					bool intPmz = false;
					float be = lp.pmz > 0?abs(lastPmz / lp.pmz):0;
					if ( be>0.1 && be < 10  )
					{
						intPmz = true;
						//DP(("pmz  %f  %f", lastPmz, lp.pmz));
					}
					else {
						//DP(("pmz! %f  %f", lastPmz, lp.pmz));
					}
					for (int i = 1; i < c; i++)
					{
						float ra = float(i) / c;
						auto pos = curr.getInterpolated(last, ra);
						if (intPmz) {
							clp.pmz = lastPmz * (1.f - ra) + lp.pmz * ra;
							//DP(("pmzi %f  %f   %f", lastPmz, clp.pmz , lp.pmz));
						}

						ink.hueAdd += mPM.ptrHueAdd;
	
#if 1
						vector3df posCamSpace (pos.X, pos.Y, driver->dsd.stdDistance * CAM_SPACE_DIS);
						CamSpaceToSceneSpace(nullptr, &mat, vector2df(Ctx->gd.screenX,Ctx->gd.screenY), posCamSpace, vec, pos1, vec1, srcTransScale);
#else
						{		float camNZ = -Ctx->gd.CamNormal->getPosition().Z;
							pos1.set(Ctx->gd.screenX + pos.X, Ctx->gd.screenY - pos.Y,camNZ );//float4(0,-gg.scrHeight/2,0,0);
							mat.transformVect(pos1);
							vec1.set(Ctx->gd.screenX + pos.X+vec.X, Ctx->gd.screenY -( pos.Y+vec.Y), camNZ);
							mat.transformVect(vec1);
						}
#endif
						//clp.flag |= 1; 
						clp.pv.y = 10;
						LaunchFw3D(ualib::V3dToFloat3(pos1), ptrId, ualib::V3dToFloat3(vec1), sc, &clp);

					}
				}
			}
			ink.hueAdd += mPM.ptrHueAdd;

			vector3df posCamSpace(x, y, driver->dsd.stdDistance*CAM_SPACE_DIS);
			CamSpaceToSceneSpace(nullptr, &mat, vector2df(Ctx->gd.screenX,Ctx->gd.screenY), posCamSpace, vec, pos1, vec1, srcTransScale);
			driver->dsd.pointer3D[0] = pos1;
			driver->dsd.pointerVel[0] = vec1;
			//{		float camNZ = -Ctx->gd.CamNormal->getPosition().Z;
			//	pos1.set(Ctx->gd.screenX + x, Ctx->gd.screenY - y, camNZ);//float4(0,-gg.scrHeight/2,0,0);
			//	mat.transformVect(pos1);
			//	vec1.set(Ctx->gd.screenX + x + vec.X, Ctx->gd.screenY - (y+ vec.Y), camNZ);

			//	mat.transformVect(vec1);
			//}		
			lp.flag &= ~1;
			lp.pv.y = 10;
			LaunchFw3D(ualib::V3dToFloat3(pos1), ptrId, ualib::V3dToFloat3(vec1), sc, &lp);
		}
	}
	break;
#if 0
	case irr::EPA_Down:
	{
		static int cc = 0;
		int ptrId = GetPtrFwId(1, cc++);
		if (ptrId == 0)			break;
		LfwParam lp;
		lp.kldMode = mPM.ptrFwMode;
		lp.kldCount = mPM.ptrFwKld;
		lp.kldBaseDeg = mPM.ptrFwBaseDeg;
		LaunchFw3D(ualib::V3dToFloat3(pos), ptrId, vec, sc.color, &lp);
	}
	break;
	case irr::EPA_Up:
	{
		ink.hueAdd += 15;
		static int cc = 0;
		int ptrId = GetPtrFwId(2, cc++);
		if (ptrId == 0)			break;
		LfwParam lp;
		lp.kldMode = mPM.ptrFwMode;
		lp.kldCount = mPM.ptrFwKld;
		lp.kldBaseDeg = mPM.ptrFwBaseDeg;
		LaunchFw3D(ualib::V3dToFloat3(pos), ptrId, vec, sc.color, &lp);
	}
	break;
#endif
	default:
		break;
	}

	lastPmz = lp.pmz;
}

bool EQV::StageOnPointerEvent(const irr::SEvent::SPointInput& pe)
{
	if (ptrGrassMode) {
		if ( pe.act == EPA_Down || pe.act == EPA_Move && pe.isMouseBtnDownWhenMove(EMBSM_LEFT))
		{
			float groundY = 0.f;
			plane3df pln(vector3df(0, groundY, 0), vector3df(0, 1, 0));
			vector3df ipt;
			if (!pgMap) { pgMap = new char[PGWidth * PGWidth]; memset(pgMap, 0, PGWidth * PGWidth); }
			if (Ctx->getScrPtIntersectionOnPlane((int)pe.X, int(pe.Y), pln, ipt, SceneManager->getActiveCamera()))
			{				
				int  rad = 10, step = 10;
				int cx = ipt.X/step, cy = ipt.Z/step;
				for (int x = -rad; x <= rad ; x+=1) for (int y = -rad; y <= rad ; y+=1) {
					if (x * x + y * y <= rad * rad) {
						int gx = PGWidth/2 +cx + x, gy = PGWidth/2 + cy + y;
						int id = gy * PGWidth + gx;
						if (pgMap[id]==0 && gx >= 0 && gx < PGWidth && gy >= 0 && gy < PGWidth) {
							pgMap[id] = 1;
							LaunchFw3D(ipt + vector3df(x * step, 0, y * step), getCurPtrFwIdx(0), { 0,1000,0 }, SColorf(1, 1, 0, 1));
						}
					}
				}				
			}
		}
		return true;
	}

	if (TAMgr.getStatus() == EqvTouchActionManager::ES_Replaying)
		return false;

	float x = pe.X, y = pe.Y;
	
	const int vcc = 10;
	static vector3df vecs[vcc] {};
	static int vi = 0;
	auto driver = Ctx->getDriver();

	if (pe.act == EPA_Move || pe.act == EPA_Down)
	{
		float camdis = Ctx->getDriver()->dsd.stdDistance;// (SceneManager->getActiveCamera()->getPosition()).getLength();
		float x = pe.X, y = pe.Y, z = mPM.ptrZ!=0?mPM.ptrZ:camdis* mPtrDis;// cz * 0.5*prs;
		auto ScrTo3D = [=](float x, float y, float z) {
			return vector3df((Ctx->gd.screenX + x) * (camdis + z) / camdis, (Ctx->gd.screenY - y) * (camdis + z) / camdis, z);
		};
		vector3df pos = ScrTo3D(x, y, z);

		vecPtr.set(0, 0, 0);
		//if (pifSnOrigin) pifSnOrigin->setPosition(pos);
		driver->dsd.pointer3D[0] = pos;		//driver->dsd.pointerDown = true;
		if (pe.lastTimeUs > 0 )
		{
			if (pe.timeUs > pe.lastTimeUs + 100000)
				for (int i = 0; i < vcc; i++)	 vecs[i].set(0,0,0);
			vector3df posL = ScrTo3D(pe.lastX, pe.lastY, z);			
			if (pe.timeUs > pe.lastTimeUs && pe.timeUs - pe.lastTimeUs < 20000)
			{
				vecs[vi++] = (pos - posL) * 1000.0f / (float)std::max(int64_t(1000), pe.timeUs - pe.lastTimeUs);

				for (int i = 0; i < vcc; i++)	vecPtr += vecs[i];
				vecPtr /= vcc;
			}
			if (vi >= vcc) vi = 0;
			//DP(("VELADD [%I64d] %f,%f,%f    %f", pe.timeUs, vecPtr.X, vecPtr.Y, vecPtr.Z, (pos - posL).Y));
			driver->dsd.pointerVel[0].set(vecPtr.X,vecPtr.Y,vecPtr.Z);
		}
		if (driver->dsd.downPtrCount<1)		driver->dsd.downPtrCount = 1;
	}
	else
	{
		driver->dsd.pointer3D[0].set( -1000000.f,-1000000.f, 0.f);		//driver->dsd.pointerDown = false;
		driver->dsd.pointerVel[0].set(0, 0, 0);
	}
	PtrPos=driver->dsd.pointer3D[0];
	PtrInWindow = x > -1;



	if (TAMgr.getStatus() == EqvTouchActionManager::ES_Recording)
	{
		PointerEventStruct pes;
		pes.pi = pe;
		pes.time = TamNow();
		pes.vel = vecPtr;
		TAMgr.RecordEvent(pes);
	}


#if PTR_EVT_TO_MMD && !MMD_PLAY_GUQIN //  && 0
	if (cbOnSvFwCenterPt) {
		SvTimePt stp; stp.color = 0xFFFFFFFF;
		if (pe.act == EPA_Move || pe.act == EPA_Down)	stp.spFlag = 0x20;
		else stp.spFlag = 0x22;

		stp.pos.X = -1 + 2 * x / Ctx->gd.scrWidth; stp.pos.Y = 1 - 2 * y / Ctx->gd.scrHeight;
		cbOnSvFwCenterPt(stp, 3, 1 / 60.f);
	}
#else
	if (mPtrFwMode == 0 && (pe.act == EPA_Move || pe.act == EPA_Down))
		DrawPointerInput(pe, 0);
	else if (mPtrFwMode == 1)
		PtrImgFwLaunch(pe, 1);
#endif





	return false;
}

void EQV::Draw()
{
	if (Ctx->gd.CamRtt)
	{
		Ctx->gd.CamRtt->setOverrideViewMatrix(nullptr);
		
	}


	for (auto& n : mNodes)
	{
		n->Draw();
	}
}

void EQV::LaunchFw(float x, float y, float z, int id, irr::core::matrix4* mat, float3* vec, u32 BaseColor, u32 bid, float ratio, int ballN)  // 2D, WILL ADJUST X Y by Z
{
	assert(vec);
	if (x < -Ctx->gd.scrWidth || x > 2*Ctx->gd.scrWidth || y < -Ctx->gd.scrHeight || y > 2*Ctx->gd.scrHeight)
		return;
	float camdis = Ctx->getDriver()->dsd.stdDistance;
	irr::core::vector3df pos;
	if (mat)
	{
		pos = irr::core::vector3df(-0.5f + (x) / Ctx->gd.scrWidth, 0.5f - (y) / Ctx->gd.scrHeight, z);
		mat->transformVect(pos);
	}
	else
		pos = irr::core::vector3df((Ctx->gd.screenX + x) * (camdis + z) / camdis, (Ctx->gd.screenY - y) * (camdis + z) / camdis, z);//float4(0,-Ctx->gd.scrHeight/2,0,0);
	LfwParam lpm;
	lpm.bid = bid;
	lpm.ratio = ratio;
	lpm.ballN = ballN;
	LaunchFw3D(ualib::V3dToFloat3(pos), id, *vec,SColor(BaseColor), &lpm);
}


void EQV::LaunchFw3D(const float3& pos, int fwIdx, float3 vec, SColorf col, LfwParam* pm, float3* tcl)
{
 
	if (!Ctx->gd.supportFW || !mIFppt || !mIFppt->isVisible() //|| !mIFppt->canGen
		) return;
	static float lasttime = Ctx->gd.time;
	try
	{
		if (Ctx->gd.supportFW && mIFppt)
		{
			GenFirework gf;
			if (fwIdx >= FwMan.mFWs.size()) return;
			gf.embId = FwMan.mFWs[fwIdx].startId;//Fm.mFWs[Ctx->gd.rg.irand(Fm.mFWs.size()-1)+1].startId;
			Ember& emb = FwMan.mEmbers[gf.embId];
			
			float ratio = 0.f;
			int ballN = 0;
			gf.pmz = 1.0;// std::max(0.5f, std::min(2.0f, (float)std::min(Ctx->gd.scrHeight, Ctx->gd.scrWidth) / 720.f));
			if (pm)
			{
				gf.kldMode = pm->kldMode; //mPM.ptrFwMode;
				gf.kld = pm->kldCount; //mPM.ptrFwKld;
				gf.kldBaseDeg = pm->kldBaseDeg;// mPM.ptrFwBaseDeg;
				gf.setLife = pm->setLife;
				gf.bandId = pm->bid;
				ratio = pm->ratio;
				ballN = pm->ballN;
				gf.timerRatio = pm->timerRatio;
				gf.pmz = pm->pmz;
				gf.flag = pm->flag;
				gf.pv = pm->pv;
				gf.mid = pm->mid;
			}
			gf.vec = vec;
			gf.pos = pos;

			// 				if (z > 1 || z < -1)
// 				{
// 					DP((L"fw pos %10f,%10f,%10f   -   %10f,%10f,%10f",x,y,z,gf.pos.x,gf.pos.y,gf.pos.z));
// 				}
			

			u32 chue = (emb.cf & 0x7000) >> 12;
			if (chue)
			{
				SColorf bc(emb.fCol.x, emb.fCol.y, emb.fCol.z, emb.fCol.w);
				bool ocsl = false;
				float a = bc.getAlpha();
				float fad = UaRandm1to1() * 15;
				switch (chue)
				{
				case 2:bc = col; break;
				case 3:fad = -30 + 60 * ratio; break;
				case 4:bc = col; fad = -30 + 60 * ratio; break;
				case 7:bc = col; ocsl = true; fad = 0; break;
				default:
					break;
				}

				SColorHSL hsl; hsl.fromRGB(bc);

				hsl.Hue += fad;
				if (ocsl)
				{
					SColorHSL  hslo; hslo.fromRGB(SColorf(emb.fCol.x, emb.fCol.y, emb.fCol.z, emb.fCol.w));
					hsl.Saturation = hslo.Saturation;
					hsl.Luminance = hslo.Luminance;
				}
				hsl.toRGB(col);
				col.a = a;
			}
			gf.col = float4(col.r, col.g, col.b, col.a);
			gf.tcl = tcl? *tcl:gf.pos;
			gf.vec *= mIFppt->PtPow;
			mIFppt->FwLaunch(gf);

			lasttime = Ctx->gd.time;
		}
	}
	catch (...)
	{

	}
}





void EQV::Clear() {
	inited = false;
	mNodes.clear();
	pifFramePtrEvts.clear();
	pfNodes.clear(); pfPtrEvts.clear();
	CurWavePeak = AvrWavePeak = 0.f;
	sumPeak = 0.f;
	for (int i = 0; i < WAV_PEAK_MAX; i++) peaks[i] = 0.f;
	curPeak = 0;
	peakNum = 0;
	
#if USE_SVG
	if (svgMan) svgMan->reset();
#endif
}

void EQV::strokeUndo() 
{
	TAMgr.RemoveLastStroke(TamNow());
	int c = TAMgr.getStrokeCount();
	mIFppt->strokeMaxCount = c;
}

int EQV::GetPtrFws_FwId(int ptrType, int mod)
{
	auto& fws = ptrFws[ptrType];

	size_t pts = fws.size();
	if (pts < 1)	return 0;
	if (pts == 1)
	{
		return fws[0].gfd.FwId;
	}
	else
	{
		if (mod < 0)
		{
			if (UaRandF() < mPM.ptrFw0Pct)
				return fws[0].gfd.FwId;
			else
			{
				size_t idx = 1u + UaRand(pts - 1);
				if (idx >= pts) idx = pts - 1;
				return  fws[idx].gfd.FwId;
			}
		}
		else
		{
			size_t idx = mod % pts;
			return  fws[idx].gfd.FwId;

		}
	}
	throw;
	return 0;
}
int EQV::GetPtrBeatFwId()
{
	//todo

	return 0;
}
u32 EQV::GetBaseColor()
{
	u32 c = 0xFFFFFFFF;
	if (mNodes.size() > 0)
	{
		auto sch = mNodes[0]->mPtHSL;
		return sch.toSColor().color;
	}
	return c;
}

void EQV::SetBandId(GenFirework& gf)
{
	if (mNodes.size() > 0)
	{
		int bc = mNodes[0]->getBandCount();
		gf.bandId = std::min(bc, std::max(0, int(bc * (gf.pos.x - Ctx->gd.screenX) / Ctx->gd.scrWidth)));
	}

}

void EQV::UpdateBandCountOnScrSize()
{
	if (mPM.overrideBandCount == 0)
	{
		//Ctx->eqv.nextBandCount = Ctx->gd.scrWidth > Ctx->gd.scrHeight ? 32 : 16;
	}

}

int64_t EQV::TamNow() { 
	return (int64_t)(Ctx->gd.time * 1000); 
}


void EQV::recordMat(float timeMul) {

#if 0
	if (Ctx->gd.CamRtt && MatRec->drGetState() == DrState::DRS_Recording)
	{
		MatrixRecorder::DrDataStruct ds;
		ds.d.mat = Ctx->gd.CamRtt->getViewMatrix();
		MatRec->drRecordData(ds, TamNow(),timeMul);
	}
#endif
}

void EQV::addImgPfpNode(bool fire,bool cached)
{
	static int cc = 0; cc++;
	int pfwid = curPtrFwIds[0];

	pfp.idx = cc;
	pfp.time = pfp.delayS0b+PFP_DELAY+ptrFws[0][pfwid].tfp.spsOffset-ptrFws[0][pfwid].tfp.timeOffset;
	pfp.durMax = ptrFws[0][pfwid].tfp.durMax;
	pfp.cached = cached;
	if (cached) {
#if 1
		pfp.delayS0b = 0.0f;// std::max(0.f, pfp.delayS0b - 0.5f);// *= 0.5f;
#endif
		pfp.time += -pfp.delayS0b+ ptrFws[0][pfwid].tfp.timeOffset;
 
		pfp.ppm = pfpPpm;		
	}	
	pfp.fired = !fire;
	pfp.ptrFwIdx = pfwid;
	pfp.sdPosOfs = mTfPosOfs;
#if USE_SBT_ANIM3D
	pfp.mrTransTgt = snSbtCur->getAbsoluteTransformation() * pfp.mrTransTgt;
#endif
	DP(("PPM + [%3d] C %d ", pfp.idx, cached));
	pfNodes.insert({ cc ,pfp });
}

void EQV::pfAddForce(float time,irr::core::vector3df pos,
	irr::core::vector3df vel, int fwIdx,float rad )
{
	UP_RLOCK_GUARD(pfwLock);
	pfPtrEvts.insert({ time,{ pos,	vel ,++pfcc ,fwIdx,rad } });
}

void EQVisual::EQV::onBandItemSnUpdated(irr::scene::ISceneNode** sn, int count, int stage) 
{
	const float boxScale = 3.f;
	using namespace irr::scene;
	auto Pom = gPoMan[0]; if (!Pom || AvrWavePeak<0.00001f) return;
	
	count = std::min(count, 32);
	if (stage == 0) //new 
	{
		
		for (int i=0;i<count;i++) {
			DP(("onBandItemSnUpdated NEW"));
			float3 size = float3( 1 );
			PhyObjParam pm{ 0 ,111.f,size,{ 0,-10,0 },{},{ 0,0,0 },false,false,false };
			pm.operation =  1;  pm.parentKimSn = sn[i]; pm.autoKill = false;
			pm.hitSbTest = true; pm.ragDollPm = { .dur0 = 0.3f, .dur1 = 0.7f };
			pm.pmxrb.m_repulsion = 0.0f;
			pm.pmxrb.m_friction = 1.0f;
			pm.pmxrb.m_collideMask32 = -1; pm.tag = 'cam'; pm.pmxrb.noGravity = true;
			sn[i]->po = Pom->addObj(pm);
		}
	}
	else if (stage == 1) //update
	{
		for (int i = 0; i < count; i++) if (auto o = sn[i]->po) {
		
			float3 size = sn[i]->getAbsoluteTransformation().getScale()/100.f  ;
			o->rb->setScale(size);
		}
	}
	else if (stage == 2) //delete
	{
		for (int i = 0; i < count; i++)  if (auto o = sn[i]->po) {
			DP(("onBandItemSnUpdated DEL"));
			o->pm.parentKimSn = nullptr;
			o->pm.autoKill = true; o->timer = 0;
		}
	}
}
 
void EQV::pfNodesUpdate(float stepTime)
{
	 
	bool hasBlade = false;
	double now = Ctx->gd.time;
	auto driver = Ctx->getDriver();
	size_t pfc = pfNodes.size();
	if (ft.mmdThrowTimer > 0) {
		ft.mmdThrowTimer -=  stepTime;

		auto lastObj = ft.fwSaba->Pom->getLastObj();
		glm::vec3 velAdd{};;
		if (lastObj) {
			matrix4 mt = lastObj->rb->GetTransform(), ms = ft.forceNode->rb0->GetTransform();

			auto tgt = mt.getTranslation(), src = ms.getTranslation();
			auto vel = (tgt - src).normalize() * std::max(0.1f*MMD_SABA_SCALE, (tgt - src).getLength())*27 * MMD_SABA_SCALE;
			//velAdd = vel/100;
			ft.mmdThrowForce = vel;
		}

		//ft.forceNode->rb0->addForce(ft.mmdThrowForce+ velAdd);
		//ft.fwSaba->ndYao->rb0->addForce(-ft.mmdThrowForce- velAdd);
		ft.mmdThrowForce *= 0.9;

	}

	for (auto it= pfNodes.begin(); it!= pfNodes.end();)
	{
		PtrFwParam& pf = it->second;
		int fwid = pf.ptrFwIdx;
		pf.time -= stepTime;
		if (pf.time <= PFP_DELAY)
		{
			if (!pf.fired)
			{
				pf.fired = true;
				DP(("ppm - [%3d] C %d",it->second.idx, it->second.cached ));
				if (it->second.cached) 
				{
					ppm = it->second.ppm;
					pfp = it->second;				
					mTfpm = ptrFws[0][it->second.ptrFwIdx].tfp;
					 
					DP(("ppm %d ,    CACHE %d", ppm.imgW, it->second.cached));
					pfwDoEmitAll(it->second.ptrFwIdx);
				}
				static int scc[MaxPtrFw] = { 0 };
				const auto& mTfpm = ptrFws[0][fwid].tfp;
				const SyncPointersParam* sps = nullptr;
				if (mTfpm.spsCount > 0) {
					sps = mTfpm.sps[scc[fwid] % mTfpm.spsCount];
					scc[fwid] = (scc[fwid] + 1) % mTfpm.spsCount;
				}
				//if (cbOnTxtNode) cbOnTxtNode();
				if (sps)
				{
					int ptc = sps->count;
					int fwIdx = sps->fwIdx;
					double ptAddTime = 1.0 / sps->count * pf.delayS0dur * (sps->tsEnd - sps->tsBegin);
					float vfMulDTime = sps->vfcMul / ptAddTime;
					double tsBegin = now + pf.delayS0dur * sps->tsBegin;
					auto fv = sps->fv;
					auto fv1 = sps->fv1;

					matrix4 mt1 = pf.mrTransTgt;

					switch (sps->typeId)
					{
					case SyncPointersParam::eLine:
					{
						pfcc++;
						pf.corner2.Y = pf.corner1.Y = (pf.corner2.Y + pf.corner1.Y) / 2;
						pf.corner2.Z = pf.corner1.Z = (pf.corner2.Z + pf.corner1.Z) / 2 + fv[0];
						double time = tsBegin;
						auto posdt = pf.corner2 - pf.corner1;
						vector3df velforce;// = posdt / stepTime / ptc;
						vector3df lpos;
						for (int i = -1; i < ptc; i++)
						{
							float ratio = float(i) / (ptc - 1);
							vector3df pos = pf.corner1 + ratio * posdt;
							mt1.transformVect(pos);
							if (i >= 0)
							{
								velforce = (pos - lpos) * vfMulDTime;								
								pfPtrEvts.insert({ time,{ pos,	velforce ,pfcc ,fwIdx,1.f } });
								time += ptAddTime;
							}
							lpos = pos;
						}
					}
					break;
					case SyncPointersParam::eCircle:
					{
						pfcc++;
						float xlen = pf.corner2.X - pf.corner1.X;
						float rad = mTfpm.fontSize * fv[0];
						float y = (pf.corner1.Y + pf.corner2.Y) / 2 + mTfpm.fontSize * fv[1];// (pf.corner2.Y + pf.corner1.Y) / 2;
						float z = (pf.corner2.Z + pf.corner1.Z) / 2;
						float aBase = core::PIx2 * fv[2];
						float aLen = core::PIx2 * fv[3];
						double time = tsBegin;
						vector3df velforce, lpos;
						for (int i = -1; i < ptc; i++)
						{
							float ratio = float(i) / (ptc - 1);
							float ar = aBase + aLen * ratio;
							float ca = cos(ar), sa = sin(ar);
							vector3df pos(
								pf.corner1.X + ratio * xlen
								+ (ca * fv1[0] + sa * fv1[1]) * rad,
								y + (ca * fv1[2] + sa * fv1[3]) * rad,
								z + (ca * fv1[4] + sa * fv1[5]) * rad
							);
							mt1.transformVect(pos);
							if (i >= 0) {
								velforce = (pos - lpos) * vfMulDTime;								
								pfPtrEvts.insert({ time,{ pos,	velforce ,pfcc ,fwIdx,1.f  } });
								time += ptAddTime;
							}
							lpos = pos;
						}
					}
					break;
					case SyncPointersParam::eSwipe:
					{
						static u32 cc = 0;
						switch (cc % 4)	{
						//case 0: {}break;
						case 1: {	std::swap(pf.corner1, pf.corner2);}break;
						case 2: {	std::swap(pf.corner1.X, pf.corner2.X);}break;
						case 3: {	std::swap(pf.corner1.X, pf.corner2.X);std::swap(pf.corner1, pf.corner2);}break;
						}
						float zad = UaRandm1to1() * fv[4];
						pf.corner1.Z += zad;
						pf.corner2.Z -= zad;
						double time = tsBegin;
						auto posdt = pf.corner2 - pf.corner1;
						float raStart = -fv[0] + fv[2] * UaRandm1to1();
						float raLen = 1 + fv[1] + fv[3] * UaRandm1to1() - raStart;
						cc++;
						vector3df velforce, lpos;
						for (int i = -1; i < ptc; i++)
						{
							float ratio = raStart + float(i) / (ptc - 1) * raLen;
							vector3df pos = pf.corner1 + ratio * posdt;		
							mt1.transformVect(pos);
							if (i >= 0)
							{
								velforce = (pos - lpos) * vfMulDTime;						
								pfPtrEvts.insert({ time,{pos,velforce,cc % 32u,fwIdx,0.1f + sin(core::PI * ratio) } });
								time += ptAddTime;
							}
							lpos = pos;
						}
						//pfPtrEvts.insert({ time+0.1,{ pf.corner1 +  posdt,velforce,false } });
						hasBlade = true;
					}
					break;
					case SyncPointersParam::eLightning:
					{
						pfcc++;
						float xlen = pf.corner2.X - pf.corner1.X;
						float rad = mTfpm.fontSize * fv[0];
						float x = (pf.corner1.X + pf.corner2.X) / 2;
						float y = pf.corner2.Y;//) / 2 + mTfpm.fontSize * fv[1];// (pf.corner2.Y + pf.corner1.Y) / 2;
						float z = (pf.corner2.Z + pf.corner1.Z) / 2;
						float aBase = core::PIx2 * fv[2];
						float aLen = core::PIx2 * fv[3];
						double time = tsBegin;

						vector3df velforce, lpos;
						std::vector<vector3df> pts; pts.resize(ptc);
						vector3df lpt(x, y, z), ltv(0, 1, 0);
						int ic = 0;
						for (int i = 0; i < ptc; i++)
						{
							if (ic++ > 3 && ualib::UaRandF() > 0.5) {
								ltv += vector3df(fv[0] + UaRandm1to1() * fv[3], fv[1] + UaRandm1to1() * fv[4], fv[2] + UaRandm1to1() * fv[5]) * fv[6];
								ic = 0;
							}
							lpt = pts[i] = lpt + ltv;
						}

						for (int i = 0; i < ptc; i++)
						{
							float ratio = float(i) / (ptc - 1);
							vector3df pos(pts[ptc - i - 1]);
							mt1.transformVect(pos);
							if (i == 0) velforce.set(0, 0, 0);
							else velforce = (pos - lpos) * vfMulDTime;
							
							pfPtrEvts.insert({ time,{ pos,	velforce ,pfcc ,fwIdx,1.f  } });
							time += ptAddTime;
							lpos = pos;
						}
					}
					break;
					}
				}
				if (pfcc == -1)
					throw "overflow, too many pfp";
			}


			if (pf.time <= 0.f)
			{
#if MMD_MOUTH_FROM_LYRIC
				if (pf.speechId >= 0)
				{
					//DP(("pf =%d  %f", pf.speechId, pf.delayS0dur)); 
					onSetSpeechId(pf.speechId, std::max(0.035f,pf.delayS0dur- 0.1f));
				}
#endif
				it = pfNodes.erase(it);
			}
			else if (pf.time<0.1 && MMD_MOUTH_FROM_LYRIC && pf.speechId1 >= 0)
			{
				DP(("pf1=%d", pf.speechId1));
				onSetSpeechId(pf.speechId1, 0.1);
				pf.speechId1 = -1;
			}
			else
				it++;
		}
		else
			it++;
	}

	//CPU_COUNT_B(ptrcc);
	auto &dsd = Ctx->getDriver()->dsd;
	int pt = 0;
	//std::set<int> iset;
	int iflag[32] = { 0 };
	for (auto it = pfPtrEvts.begin(); it != pfPtrEvts.end();)
	{		
		if (it->first <= now)
		{
			auto& pd = it->second;
			uint32_t idxM32 = pd.idx % 32u;


			if (dsd.downPtrCount < FW_MAX_POINTER - 1 && //iset.find(pd.idx)==iset.end()
				iflag[idxM32]==0
				)
			{
				pt = dsd.downPtrCount++;
				dsd.pointer3D[pt] = pd.pos;
				dsd.pointerVel[pt] = pd.vel;

				ptrLights[idxM32].pos = pd.pos;
				ptrLights[idxM32].val = std::min(1.0f, ptrLights[idxM32].val+ 0.99f);

				iflag[idxM32] = 1;//iset.insert(pd.idx);
				//DP(("dsd- %d %f, %d %f",idxM32, it->first, dsd.downPtrCount, dsd.pointerVel[pt].X));
			}
			//driver->dsd.pointerDown[0] = pd.down;
			LfwParam  pm;
			pm.pmz = pd.rad;
			//if (pd.fwIdx>=0)					LaunchFw3D(V3dToFloat3(pd.pos), getFwIdx(2,pd.fwIdx), V3dToFloat3(pd.vel),SColorf(1,1,1,1),&pm);
			it = pfPtrEvts.erase(it);
		}
		else
			break;
	}

	int dpc = 0; 
	for (int i = 0; i < 32 && dpc <FW_MAX_PtrLight ; i++)
	{
		if (ptrLights[i].val > 0.001f)
		{
			dsd.lightPos[dpc] = ptrLights[i].pos;
			dsd.lightVal[dpc] = ptrLights[i].val;

			dpc++;

			ptrLights[i].val -= 0.0666f;
		}
	}
	dsd.ptrLightCount = dpc;
	
	//DP(("cc %d", dsd.downPtrCount));
	//CPU_COUNT_E(ptrcc);
	if (hasBlade) pfcc++;
	
}



int EQV::setPtrFwByIdStr(int ptrFwType, std::string_view idStr) {
	if (idStr.size() == 0) return curPtrFwIds[ptrFwType];
	auto& fws = ptrFws[ptrFwType];
	if (fws.size() < 1) throw;
	int id = -1;
	if (idStr.size() == 0 )
	{
		id = curPtrFwIds[ptrFwType];
	}
	else
	for (int i = (int)fws.size() - 1; i >= 0; i--)
		if (fws[i].idStr == idStr)
		{
			id = i;
			break;
		}
		
	if (id >= 0 && id < fws.size())
	{
		curPtrFwIds[ptrFwType] = id;
	}
	if (ptrFwType==0)
	setCurPtrFwId(curPtrFwIds[0]);

	return curPtrFwIds[ptrFwType];
}

float EQV::getPtrFwTimeOfsByFwId(std::string_view idStr)
{
	auto& fws = ptrFws[0];
	if (curPtrFwIds[0]>= fws.size()) curPtrFwIds[0]=0;
	int id = -1;
	if (idStr.size() > 0)
		for (int i = (int)fws.size() - 1; i >= 0; i--)
			if (fws[i].idStr == idStr)
			{
				return fws[i].tfp.timeOffset;
			}
	
	return fws[curPtrFwIds[0]].tfp.timeOffset;
	
}

int EQV::getFwIdx(int ptrFwType, int idxInPtrFw)
{
	auto& fws = ptrFws[ptrFwType];
	int size = (int)fws.size();
	if (idxInPtrFw < 0 || size<=0)
	{
		return 0;
	}
	else if (idxInPtrFw >= size) idxInPtrFw = size - 1;

	return fws[idxInPtrFw].gfd.FwId;
}

int EQV::findPtrFwIdx(int ptrType, std::string_view idStr)
{

	auto& fws = ptrFws[ptrType];
	int id = 0;
	if (idStr.size() > 0)
		for (int i = (int)fws.size() - 1; i >= 0; i--)
			if (fws[i].idStr == idStr)
			{
				id = i;
				break;
			}
	return id;
}
const std::vector<std::string>& EQVisual::EQV::getPtrFwIdStrs(int ptrFwType) 
{
	//auto& fws = ptrFws[ptrFwType];
	//std::vector<std::string> ids;
	//for (int i = 0; i < (int)fws.size(); i++)
	//{
	//	ids.push_back(fws[i].idStr);
	//}
	//return ids;
	return ptrFwIdStrs[ptrFwType];

}
int EQV::getFwIdxByFwIdStr(int ptrType, std::string_view idStr)
{
	int id=findPtrFwIdx(ptrType, idStr);
	return getFwIdx(ptrType, id>=0?id:0 );
}


void EQVisual::EQV::launchTextFw(ualib::SubtitleData& sd, bool show, bool cachePPM)
{
	if (sd.isSVG && mmd->sb0)
	{
		mmd->sb0->stkLineAdd(sd.ws);	return;
	}

	ualib::FT2TextParam a;
	int oldFid = getCurPtrFwId(0);	
	int fid = oldFid;
	if (!sd.fwIdStr.empty()) fid = setPtrFwByIdStr(0, sd.fwIdStr);
	else setCurPtrFwId(fid);
	if (sd.color0) {
		mCurTfd.color0 = sd.color0;
		mCurTfd.color1 = sd.color1;
	}
	//DP(("L+++++++++++++++"));
#if HAS_FT2MAN || HAS_FM_Android
	UP_LOCK_GUARD(lockGenText);
	std::wstring ws = sd.ws;
	if (ws.size() < 1)
		return;  
	//DP(("[%f]Sub: %S ", sd.startMs/1000.f, ws.c_str()));
#if 0
	void SpeakText(std::wstring preStr, std::wstring text, int fontSize);
	std::wstring pretxt = L"<rate absspeed=\"-5\"/><volume level=\"100\"/><voice required='Gender = Female; Age != Child'>";
	SpeakText(pretxt, ws, mTfpm.fontSize);
#else
	//generateTextImage(WcstoUtf8(ws),100,&sd);

	a.imgFile = inputPng;
	a.txt = ws;
	a.isMouth = (sd.flag & SDF_IsMouth) == SDF_IsMouth;//style is "MouthMorph"
#if AUTO_FONT_SIZE
	a.w = CANVAS_WIDTH;
	a.h = Ctx->gd.scrHeight;
	a.fontSize = std::max(MIN_FwFontSize, vp.mFF->sbtLoaded() ? vp.mFF->getScaledFontSize(sd.fsize, std::min(a.w, a.h)) : (int)sd.fsize);
#else
	a.w = std::max(CANVAS_WIDTH, Ctx->gd.scrWidth/2);
	a.h = std::max(CANVAS_WIDTH / 4, Ctx->gd.scrHeight);
	a.fontSize = int(sd.fsize * FontSizeMul + 0.5);
#endif
#if HAS_FM_Android
	a = FtG->mTextPm;
#endif
	a.show = show;
	a.fontId = 0;// sd.fontId;
	if (mTfpm.outlineData)
	{
		a.outlineType = mTfpm.outlineData % 10;
		a.outlineThickness = mTfpm.outlineData / 10 / 100.f;
	}
	else if (dmFtTxtPm.outlineType)
	{
		a.outlineType = dmFtTxtPm.outlineType;
		a.outlineThickness = dmFtTxtPm.outlineThickness;
	}
	a.bgColor = mTfpm.ftBgColor;

	a.fid = fid;
	a.fidTmp = -1;

	a.start = 0;
	a.len = (int)a.txt.length();
	a.reset = true;
	a.evtType = 1;
	a.stringIdx = 0;
	a.fwEndTime = (sd.endMs - sd.startMs) / 1000.f;
	if (sd.hasMatrix && !sd.vfg.useCamMat) a.wordSpaceRatio = 0.1f;

	curCacheFw = cachePPM;	generateTextFwImage(a,sd);

	float3 oldpos = mTfPosOfs;
	mTfPosOfs = sd.pos;
	const TextFwParam& tfp = getCurTextFwParam();

	//assert(Ft->Oii.hasLineWrap == false || sd.ex.ranges.size() < 2); //TODO: WRONG RECT (END+lINE2START)
	int rgc = a.imgFile.size() > 0 ? 1 : (int)sd.ex.ranges.size();
	//if (!vp.mFF) return;

#if CAM_TIMELINE_ANIMATION && (!CAM_MOVE_FORWARD)
	static int rcc = 0; rcc++;

	bool oRange0 = sd.oRangeId == 0;

	float cz = -std::max(270.f, 2.f * FtG->Oii.rect.w / 2 / tan(90 * core::DEGTORAD / 2));



	vector3df posadd = oRange0 ?
		//vp.mFF->subPos += snSbtTgt->getRotation().rotationToDirection({ 1,0,0 }) * FtG->Oii.rect.w * (2);

		snSbtCur->getRotation().rotationToDirection({ 0,0,1 }) * FtG->Oii.rect.h * (0) :

		snSbtCur->getRotation().rotationToDirection({ 0,1,0 }) * FtG->Oii.rect.h * (-1.5);// 2000;

	//posadd.set(FtG->Oii.rect.w,0,0);

	static int turnY[] = { 1,1,1,-1,-1,-1 };
	int tu = turnY[rcc % (sizeof(turnY) / sizeof(int))];
	EQV::SbtAniParam sap = { 0.f,posadd, vector3df(
		0,//(oRange0 ? -10.f : -10.f) ,
		30,//(oRange0 ? 30.f*tu : 30.f * tu),
		0// 30.f * tu
	),
		(oRange0 ? 0.999f : 0.999f),		cz };
	setSbtTarget(sap);
#endif

	//vector3df fwpos(snSbtCur->getPosition() +	vector3df(ualib::UaRandm1to1()*500, ualib::UaRandm1to1() * 500, ualib::UaRandm1to1() * 500));
	//LaunchOneFW(30, &fwpos);

	auto lineModFunc = [&]() {
		if (sd.flag & SDF_IsPosOfs) a.lineModAddY = 0.f;
		else a.lineModAddY = ((ptrFwCC[a.fid] % tfp.lineMod) * FtG->getLineHeight() * tfp.lineHMul);
		if (a.fid >= 0 && show)
			ptrFwCC[a.fid]++;
	};

	a.centerView = AR_FW_TEXT_CENTER;
	if (!a.centerView && !sd.hasMatrix)
		lineModFunc();

	for (int i = 0; i < rgc; i++)
	{
		auto rg = sd.ex.ranges[i];
		if (rg.fxFwStr.size() > 0)
			setPtrFwByIdStr(1, rg.fxFwStr);

		bool setFidTmp = false;
		int added = 0;
		if (RAP_LAST_WORD_ADD_FX && (i == rgc - 1)) {
			added = 1;
			setCurPtrFwId(curPtrFwIds[0]+added); 
		}
		if (setFidTmp = rg.tempFwStr.size() > 0)
			if (rg.tempFwStr[0] == '!')
				setCurPtrFwId(a.fid, 0);
			else
				setPtrFwByIdStr(0, rg.tempFwStr);

		if (onDamakuCmd && a.cmd.length() > 0)
			 onDamakuCmd(std::string("<null>|") + a.cmd, false);

		a.txt = rg.txt;
		a.start = rg.start;
		a.len = rg.len;
		a.reset = false;
		a.evtType = 0;
		a.stringIdx = 0;
		a.fwTimeOffset = rg.timeOfs / 1000.f;// +ADD_SUB_OFS;
		a.fwDurTime = rg.timeDur / 1000.f;

		//a.fidFx = getPtrUpFwByFwId(rg.fxFwStr);
		a.draw = sd.draw;
		

		if (sd.hasMatrix)
		{
			core::matrix4 m; m.setTranslation(sd.pos);
			a.subIdx = -12781;
			a.vfg = sd.vfg;
			static int cc = -1; cc++; 
			sd.sbid = i;// cc% mmd->sabas.size();
			a.vfg.sbItemIdx = sd.sbid % mmd->sabas.size();
			a.vfg.txt = sd.ws;
			a.vfg.key = sd.key;
			a.vfg.grid = VFG_GRID;

			a.centerView = 0;			
		}
		else a.vfg.grid = 0;
		if (a.centerView)
			lineModFunc();

		generateTextFwImage(a,sd);
		if (setFidTmp)
			setCurPtrFwId(a.fid, 0);

		if (RAP_LAST_WORD_ADD_FX && added) 	setCurPtrFwId(curPtrFwIds[0] - added);
	}
	mTfPosOfs = oldpos;

	//DP(("L---------------"));
#endif
#endif
	setCurPtrFwId( oldFid,0);
}

void EQVisual::EQV::genTextFw(std::wstring txt,   const char* fwName, bool oneshot, VtxFwGrid* vfg, ualib::FT2TextParam *tp) {
	static int cc = -1; cc++;
	if (fwName  ) {
		setPtrFwByIdStr(0, fwName);
	}

	ualib::SubtitleData sd;

	// txt = std::to_wstring(hdrBit) + L" bit";
	sd.ws = txt.size() ? ualib::wcsReplaceAll(txt, L"\r\n", L"\n") : L"♪";// L"大威天龍";//L"镜花水月";

	sd.fsize = getTfd()->fontSize;
	sd.startMs = 0;
	if (tp)
		dmFtTxtPm = *tp;
	ualib::ass::AcTextRange rg;
	int ofsMs = dmFtTxtPm.beginWaitMs, timeDur = dmFtTxtPm.charDurMs;
	

	if (oneshot)
	{
		rg.txt = sd.ws;
		rg.len = sd.ws.length();	rg.start = 0;
		rg.timeOfs = 0;
		rg.timeDur = timeDur;
		sd.ex.ranges.push_back(rg);
	}
	else
		for (int i = 0; i < sd.ws.length(); i++)
		{
			auto ch = sd.ws[i];
			if (ch == L' ' || ch == L'\n') continue;
			rg.txt = ch;
			rg.len = 1;	rg.start = i;
			rg.timeOfs = ofsMs;
			rg.timeDur = timeDur;
			ofsMs += timeDur;

			sd.ex.ranges.push_back(rg);
		}
	sd.sbid = cc % mmd->sabas.size();
	sd.endMs = ofsMs;
	sd.fontId = dmFtTxtPm.fontId;  
	sd.fsize = 200;
	if (vfg) {		
		sd.hasMatrix = true;
		sd.vfg = *vfg;
		if (sd.vfg.srcPosMode != 2) {
			mTfpmUE.srcPos = tp->srcPos;
			mTfpmUE.tgtPos = tp->tgtPos;
			mTfpmUE.srcVel = tp->srcVel;
		}
		sd.pos = core::vector3df(0, 0., 0.);
	}
	else sd.pos = core::vector3df(0, 1, -1);
	launchTextFw(sd, true);
}

void EQVisual::EQV::generateTextFwImage(ualib::FT2TextParam a, const ualib::SubtitleData& sd)
{
	if (!FtG) {
#if HAS_FT2MAN
		gFt2Man.initFT2();
		FtG = new ualib::FT2Glyph();
		Ft = FtG;
#endif
	}
	a.s = sd.s;
#if 0
	if (a.reset)
	{
		ft.txt = a.txt;
		ft.pos = 0;
		a.w = Ft->rtSize;
		a.h = Ft->rtSize;
		ft.stringIdx = a.stringIdx;
		if (ft.img) ft.img->drop();		ft.img = Ft->getTextImage(a, Driver);
		resetFwParam();

#ifdef _DEBUG
		//DrvOfs->writeImageToFile(ft.img, "out/FtFull.png");
#endif
		return;
	}
#else
	ft.pos = 0;
#endif
	if (ft.stringIdx != a.stringIdx)
		return;

	//ft.xMax = std::max(FtG->mX, ft.xMax);
	//ft.yMax = std::max(abs(FtG->mY), 500);

	static int cc = 1;


	if (a.evtType == 1)
	{
		cc++;
		ft.txt = a.txt;
		ft.curStcStart = a.start;
		if (ft.img) ft.img->drop();
		ft.img = FtG->getTextImage(a, Ctx->getDriverOnThread());
#ifdef _DEBUGx

		if (texBg) Driver->freeTexture(texBg);
		texBg = Driver->addTexture("rr", ft.img);
#endif
		pfp.resetFwParam();
#ifdef _DEBUGX 
		char fn[512];
		sprintf_s(fn, 512, "out/FT2Img%d.png", a.start);
		Ctx->getDriverOnThread()->writeImageToFile(ft.img, fn);
#endif


#if 0
		auto anm = ((irr::scene::CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());

		anm->aniTl_Reset(Ctx->gd.scrHeight / 1024.0);
		typedef AnimationTimeLine AT;

		anm->aniTl_Add(11, { 0.0,2,subPos.Z,subPos.X
			//- FtG->Oii.rect.w / 2 / tan(X_FOV * core::DEGTORAD / 2)
			,		1,	uu::Ebf::easeInOutSine });
		anm->aniTl_start();
#endif

		return;
	}

	if (a.start < ft.pos || a.len < 1)
		return;
	//DPWCS((L"OUT %s %f", a.txt.c_str(), a.fwDurTime));

	//float olofs = (a.outlineType == 1) ? a.outlineThickness*2 : 0;
	ft.pos = a.start + a.len;
	//a.w = Ctx->gd.scrWidth;
	//a.h = 512;
	//pfp.scrPosOfs.set(Ft->mX + (ft.curLineW > 0 ? (a.w - ft.curLineW) / 2 : 0),	 Ft->mY + (ft.yMax > 0 ? ft.yMax / 2 : 0), 0);
	int charId = a.start - ft.curStcStart;
	auto cla = FtG->Oii.rect;
	auto cl = FtG->getCharsLayout(a.start, a.len);

	if (cl.w <= 0 || cl.h <= 0)
		return;
	
	pfp.centerView = a.centerView;
	pfp.mid = a.subIdx;//TXT_SUB_MATRIX ? a.subIdx : 0;
 
	pfp.vfg = a.vfg;
	pfp.absPos = sd.absPos;
	pfp.centerByImageW = false;
	pfp.delayS0b = std::max(0.0f, a.fwTimeOffset);	//gm:10000
	pfp.delayS0dur = std::max(0.0f, a.fwDurTime);
	pfp.delayS1 = std::max(0.0f, a.fwEndTime - a.fwTimeOffset); //gm:20000
	//pfp.hueOfs =  Ctx->gd.time * 15;
	if (cl.w * cl.h == 0) return;
#if 1
	
	int border = 1;
	cl.x -= border;
	cl.y -= border;
	cl.w += border * 2;
	cl.h += border * 2;
	//if (cl.x < 0) {cl.w += cl.x; cl.x = 0;}
	//cl.y -= a.outlineThickness *300; cl.h += a.outlineThickness * 300;
#endif
	//DP(("G1"));
	float wordx = float(cl.x - cla.x), wordy = float(cl.y - cla.y);
	pfp.wordSize.set((float)cl.w, (float)cl.h, 0.f);
	pfp.centerRatio.set(0.5f, 0.5f, 0.f);
	pfp.imgSize.set((float)cla.w, (float)cla.h, 0.f);

	//if (0)	pfp.scrPosOfs.set(	UaRandm1to1() * Ctx->gd.scrWidth / 3,
	//		cla.h / 2 - (cl.y - cla.y) - (ptrFwCC[a.fid] % getCurLineMod()) * Ft->lnH, a.fwTimeOffset * camSpeedZ);
	//else 


	pfp.scrPosOfs.set(
		wordx,		//-cla.w/2 + wordx +cl.w/2 ,
		wordy  		//cla.h/2 - wordy - cl.h/2 
		+ a.lineModAddY,
		a.fwTimeOffset * ft.camSpeedZ+a.s.zOffset);
	//DP(("OFS %f,%f,%f", pfp.scrPosOfs.X, pfp.scrPosOfs.Y, pfp.scrPosOfs.Z));
	irr::SEvent evt{};
	//DP(("G2"));
	evt.EventType = irr::EET_CMD_INPUT_EVENT;
 

	if (a.isMouth || MMD_MOUTH_FROM_LYRIC)	processMouth(pfp, a);	else pfp.speechId = -1;
	//DP(("G3"));

	IImage* img = Ctx->getDriverOnThread()->createImage(ECF_A8R8G8B8, core::dimension2du(cl.w, cl.h));
	ft.img->copyTo(img, { 0,0 }, core::recti(cl.x, cl.y, cl.x + cl.w, cl.y + cl.h));

#if 0  //will change rb?
	char fn[512];
	sprintf_s(fn, 512, "out/ftimg%d.png", a.start);
	Ctx->getDriverOnThread()->writeImageToFile(ft.img, fn);
	sprintf_s(fn, 512, "out/test%d.png", a.start);
	Ctx->getDriverOnThread()->writeImageToFile(img, fn);
#endif
	evt.CmdInput.unI64 = (int64_t)img;
	//if (Ft->x >= Ctx->gd.scrWidth - 120) Ft->x = 0;


	{
		setPfpBase();
		//DP((L"ASS TXT = %s    %f,%f,%f", a.txt.c_str(), pfp.delayS0b, pfp.delayS0dur, pfp.delayS1));
		if (a.draw) process1282101_OnTextImage(evt.CmdInput, 1282103, a.show);
		addImgPfpNode(a.draw,curCacheFw);
	}
	//pfp.resetFwParam();
	if (a.fidFx >= 0)
	{
		vector3df pos1, vec1, posCamSpace(0.f - Ctx->gd.screenX, Ctx->gd.screenY - 0.f, 0 + Ctx->getDriver(0)->dsd.stdDistance * 1.5f);
		vector3df vec(0, FW_BASE_VY, 0);
		CamSpaceToSceneSpace(Ctx->getSceneManager()->getActiveCamera(), nullptr, vector2df(float(Ctx->gd.screenX), float(Ctx->gd.screenY)), posCamSpace, vec, pos1, vec1);

		LaunchFw3D(ualib::V3dToFloat3(pos1), getFwIdx(1, a.fidFx), ualib::V3dToFloat3(vec1), SColor(0xFFFFFFFF));
	}
}

void EQV::process1282101_OnTextImage(const irr::SEvent::SCmdInput& ce, int cmd, bool show)
{
	auto fun = [=]() {
		
		IVideoDriver* drv = Ctx->getDriver(1); 
		UP_LOCK_GUARD(drv->dsd.driverLock);
#if TEST_FW_OVER_TEXT
		zLaunchF_Fw();
		return;
#endif
		IImage* img, * img0;

		if (cmd == 1282103)
		{
			img0 = (IImage*)ce.unI64;
		}
		else if (Fsd.imgByD->imgSize > 0)
		{
			img0 = drv->createImageFromData(ECF_A8R8G8B8,
				core::dimension2du(Fsd.imgByD->imgW, Fsd.imgByD->imgH), Fsd.imgByD->pbImg, true, false);

			img0->isRGBA = drv->isRGBA(); // and mostly is white img
#ifdef __ANDROID__
			img0->isRGBA = true;// from flutter 
#endif
		}
		else
		{
			io::IReadFile* mrf = Ctx->getFileSystem()->createMemoryReadFile((void*)ce.pm1, (int)ce.pm2, "memFile.mem.bmp", true);
			img0 = drv->createImageFromFile(mrf);
			mrf->drop();
		}
		//drv->writeImageToFile(img0, "out/genFromimg0.png");
		img = ProcessInputImage(drv,img0);//trim
		//drv->writeImageToFile(img, "out/genFromimg1.png");

		if (ft.texTextImage) drv->freeTexture(ft.texTextImage);
		ft.texTextImage = drv->addTexture("<TsfSrcDbg>memFile_mem_png", img);
		//DP(("TEX %d,%d", texTextImage->getSize().Width, texTextImage->getSize().Height));



		if (cmd == 1282102) {
			img->drop();
			return;
		}

		if (ft.texRT)
			drv->freeTexture(ft.texRT);

		ft.texRT = drv->addRenderTargetTexture(ft.texTextImage->getSize());// drv->getTexture("s1.png");
		//texShadowHM = drv->addRenderTargetTexture(texTextImage->getSize());
		//texTmp = drv->addRenderTargetTexture(texTextImage->getSize());

		ft.tiNeedRender = 1;


		cmdImageToFw(ft.texTextImage, img, show);
		//drv->saveTexture(ft.texTextImage, "out/genFromimgTex.png");
		img->drop();
	};
#if 0
	if (std::this_thread::get_id() != renderThreadId)
	{
		UP_LOCK_GUARD(*mLib->getLock());

		fun();
	}
	else
#endif
		fun();

}

void EQVisual::EQV::setFwMmdSrc(irr::scene::IrrSaba* sb)
{
	ft.fwSaba = sb; 
	

}

void EQV::cmdImageToFw(irr::video::ITexture* tex, irr::video::IImage* imgOrig, bool show)
{
#define NEED_HMIMG 1
	auto drv = tex->getDriver();
	if (ft.tiNeedRender <= 0)		return;
	ft.tiNeedRender--;


#if NEED_HMIMG
	//if (texHM) drv->freeTexture(texHM);
	ft.texHM = drv->addRenderTargetTexture(tex->getSize());
	drawTextImgToFwImg(tex, ft.texRT, ft.texHM);
#else
	drawTextImgToFwImg(tex, texRT, nullptr);
#endif
	IImage* imgOut = drv->createImage(ft.texRT, vector2di(0, 0), ft.texRT->getSize());

	//DP(("ImgOut Size %d,%d   Scale=%f", imgOut->getDimension().Width, imgOut->getDimension().Height, mImgScale));
#if NEED_HMIMG
	IImage* imgHM = drv->createImage(ft.texHM, vector2di(0, 0), ft.texHM->getSize());
	drv->freeTexture(ft.texHM);
#endif
	EQV::PtrImgFwParam pm{};
	pm.imgCanvasWidth = std::max(CANVAS_WIDTH, Ctx->gd.scrWidth);
	pm.img = imgOut;
	if (imgOrig && inputPng.size() > 0 && USE_INPUT_PNG_COLOR) {
		pm.img = imgOrig;
		void* pSrc = imgOrig->lock();
		uu::swapRBinRGBA(pSrc, pSrc, imgOrig->getImageDataSizeInBytes());
		imgOrig->unlock();
		//Driver->writeImageToFile(imgOrig, "out/imgOrig.png");
	}
	pm.imgOrig = imgOrig;
#if NEED_HMIMG
	pm.imgHM = imgHM;
#endif
	pm.scale = ft.mImgScale;
	pfwSetImage(pm, show,curCacheFw);
	imgOut->drop();
#if NEED_HMIMG
	imgHM->drop();
#endif

	ft.titleHueBegin += 15; mBaseHue = ft.titleHueBegin;


}

void EQV::drawTextImgToFwImg(irr::video::ITexture* inTex, irr::video::ITexture* rt, irr::video::ITexture* hm)
{

	auto drv = inTex->getDriver();
	auto size = inTex->getSize();

	//SColorHSL hsl(ft.titleHueBegin, 80, 50);	//upper
	SColor col = 0, col1 = 0;

	SColor colors[4] = { col,col,col1,col1 };
	const EQVisual::TfData* td = getTfd();
	auto texHM = hm;
	if (!texHM) texHM = drv->addRenderTargetTexture(inTex->getSize());


#if 1
	//1 step blur
	drv->setRenderTarget(texHM, true, true, 0);
	//drv->draw2DImageStretch(inTex);
	video::SMaterial sm;
	sm.MaterialType = DRV_GET_MT(drv, Fx2DIdEnum::Fx2D_BlurHM);
	sm.setTexture(0, inTex);
	irr::video::CbMr2D* cb = VKDRV_CAST(drv)->getCB(Fx2DIdEnum::Fx2D_BlurHM);

	if (td) {
		const float* f = td->hmBlurPm;
		cb->blur.setParam(int(f[0] + 0.5f), f[1], f[2], f[10]);
		if (f[4] > 0.5f)
		{
			cb->blur.addPattern = int(f[4] + .5f);
			cb->blur.apLenMul = f[5];
			cb->blur.apHeightMul = f[6];
			cb->blur.apHeightAdd = f[7];
			cb->blur.apf1 = f[8];
			cb->blur.apf2 = f[9];
		}
	}
	else
		cb->blur.setParam(8);
	//drv->draw2DImageMr(mr, core::position2di(0, 0));
	//DP(("FwImg  Blur"));
	drv->draw2DImageMr(sm, inTex->getRectI(), inTex->getRectI());
	//drv->flush();drv->saveTexture(texHM, "out/HM.png");
#else
	//2 steps blur
	drv->setRenderTarget(texTmp, true, false, 0x00000000);
	//drv->draw2DImageStretch(inTex);
	video::SMaterial mr;
	mr.MaterialType = DRV_GET_MT(drv, Fx2DIdEnum::Fx2D_BlurV);
	mr.setTexture(0, inTex);
	//drv->draw2DImageMr(mr, core::position2di(0, 0));
	drv->draw2DImageMr(mr, tex->getRectI(), tex->getRectI());
	//To HM
	drv->setRenderTarget(texHM, true, false);
	mr.MaterialType = DRV_GET_MT(drv, Fx2DIdEnum::Fx2D_BlurH);
	mr.setTexture(0, texTmp);
	drv->draw2DImageMr(mr, tex->getRectI(), tex->getRectI());
#endif

	ITexture* texImgP1{};
#if 1
	auto mtGradient = DRV_GET_MT(drv, Fx2DIdEnum::Fx2D_Gradient);
	auto mr = drv->getMaterialRenderer(mtGradient);
	//mr->ReloadShaders();
	//DP(("."));
	drv->setRenderTarget(rt, true, true, 0);
	//DP((".."));
	sm.MaterialType = mtGradient;
	sm.setTexture(0, inTex);
	auto& gr = ((VkDriver*)drv)->getCB(Fx2DIdEnum::Fx2D_Gradient)->gradient;

	if (td)
	{
		ft.txtBaseHue += td->gradHueAdd;
		for (int i = 0; i < 4; i++)
		{
			gr.type[i] = td->gradType[i];

			gr.anti[i] = gr.type[i] >= 0 ? 0 : 1; gr.type[i] = abs(gr.type[i]);
			gr.func[i] = td->gradFunc[i];
		}
		auto g0 = td->gradHslAMM[0];  g0[0] += ft.txtBaseHue;
		auto g1 = td->gradHslAMM[1];  g1[0] += ft.txtBaseHue;

		gr.fv0 = g0 * float4(1.0 / 360, 1.0, 1.0, 1);
		gr.fv1 = g1 * float4(1.0 / 360, 1.0, 1.0, 1);
		SColorf cf(SColor(td->color0)), c1(SColor(td->color1));
		gr.color0 = float4(cf.r, cf.g, cf.b, cf.a);
		gr.color1 = float4(c1.r, c1.g, c1.b, c1.a);
		gr.xmin =ft.xyminmax[0];
		gr.xmax =ft.xyminmax[1];
		gr.ymin =ft.xyminmax[2];
		gr.ymax =ft.xyminmax[3];

		auto wh = inTex->getSize();

		for (int i = 0; i < 4; i++)
			if (abs(td->gradType[i]) == 9)
			{

				vector2df ctr(wh.Width / 2.f, wh.Height / 2.f);
				float a = core::PI * 2 * td->gradAngle[i] / 360;
				float r = vector2df((gr.xmax - gr.xmin) * wh.Width, (gr.ymax - gr.ymin) * wh.Height).getLength() / 2;
				vector2df pt1 = ctr + r * vector2df(cos(a), sin(a));
				vector2df pt2 = ctr + r * vector2df(cos(a + core::PI), sin(a + core::PI));
				gr.septs[i].x = pt2.X;
				gr.septs[i].y = pt2.Y;
				gr.septs[i].z = pt1.X;
				gr.septs[i].w = pt1.Y;
			}
			else
			{
				gr.septs[i].x = gr.xmin * wh.Width;
				gr.septs[i].y = gr.ymin * wh.Height;
				gr.septs[i].z = gr.xmax * wh.Width;
				gr.septs[i].w = gr.ymax * wh.Height;
			}
		gr.texWH = float2(wh.Width, wh.Height);
		//memcpy(gr.angles,td->gradAngle,sizeof(gr.angles));

	}
	drv->draw2DImageMr(sm, inTex->getRectI(), inTex->getRectI());

	//DP(("Draw > RT"));
	drv->flush();
	texImgP1 = rt->Clone("<TsfSrcDbg>texImgP1");
	//DP(("Draw > RT > P1"));
#else
	texImgP1 = inTex->Clone("texImgP1");
#endif


#if 0
	drv->saveTexture(texHM, "out/texHM.png");
	drv->saveTexture(inTex, "out/tex.png");
	drv->saveTexture(texImgP1, "out/p1.png");
#endif

#if 1

	//To tex
#if 1
	u32 bgColor = 0;
#else
	u32 bgColor = mis.bgColor & 0x00FFFFFF;
#endif
#define TESTFULLBG 0
#if TESTFULLBG
	bgColor = -1;
#endif
	drv->setRenderTarget(rt, true, true, bgColor);

	//drv->draw2DImage(texHM, core::recti(0, 0, size.Width, size.Height), inTex->getRectI(),		nullptr, colors);

	//drv->draw2DImage(inTex, tex->getRectI(), tex->getRectI());
#if 0
	mr.MaterialType = mtShadow;
	mr.setTexture(0, texHM);
	mr.setTexture(1, nullptr);
	drv->draw2DImageMr(mr, SColor(0xFF000000));
#endif


#if 1
	sm.MaterialType = DRV_GET_MT(drv, Fx2DIdEnum::Fx2D_NormalMap);;
	sm.setTexture(0, texImgP1);
	sm.setTexture(1, texHM);

	cb = VKDRV_CAST(drv)->getCB(Fx2DIdEnum::Fx2D_NormalMap);

	if (td)
	{
		cb->normSpec.colorMul = td->nmLightPm[0];
		cb->normSpec.intenMul = td->nmLightPm[1];
		cb->normSpec.specFactor = td->nmLightPm[2];
		cb->normSpec.specPow = td->nmLightPm[3];
		cb->normSpec.lightDir = glm::normalize(float3(td->nmLightPm[4], td->nmLightPm[5], td->nmLightPm[6]));
	}
	else
	{
		cb->normSpec.colorMul = 0.3f;
		cb->normSpec.intenMul = 0.0f;
		cb->normSpec.specFactor = 0.5f;
		cb->normSpec.specPow = 16;
		cb->normSpec.lightDir = glm::normalize(float3(1, 1, 1));
	}
	cb->normSpec.colorMode = mTfpm.colorMode;
	//SColor cs[4] = { 0x80000000,0x80000000,0x80000000 ,0x80000000 };
#if TESTFULLBG
#else
	drv->draw2DImageMr(sm, core::recti(0, 0, size.Width, size.Height), inTex->getRectI());
#endif
#else

	drv->draw2DImage(texImgP1, core::recti(0, 0, size.Width, size.Height), inTex->getRectI(),
		nullptr, colors);
#endif
	//DP(("Draw > RT fin"));
	//drv->flush();
	//drv->draw2DImage(texHM, tex->getRectI(), tex->getRectI());
	//drv->draw2DImage(inTex, tex->getRectI(), tex->getRectI());
	//drv->setRenderTarget(tex, false, false, 0x0);
	//drv->draw2DImage(inTex, core::recti(0 + ccx % 256, 0, 256 + ccx % 256, 256), nullptr);

	drv->flush();
#endif
	if (!hm) drv->freeTexture(texHM);
	drv->freeTexture(texImgP1);
 
	//VkDrv->waitDriverIdle(1);
}

irr::video::IImage* EQVisual::EQV::ProcessInputImage(irr::video::IVideoDriver *Driver, irr::video::IImage* img0)
{

	IImage* img;
	

	int cOpcity = 0;
	{
		u8* pb = (u8*)img0->lock();
		u32* pu = (u32*)pb;
		u32 w = img0->getDimension().Width, h = img0->getDimension().Height;
		pb += 3;
		for (u32 x = 0; x < w; x++)
			for (u32 y = 0; y < h; y++)
			{
				if (*(pb += 4) > 0) cOpcity++;

			}
		img0->unlock();
	}


	if (cOpcity > 20000)
	{
		DP(("IMG0 %d,%d", img0->getDimension().Width, img0->getDimension().Height));
		int w, h;
		auto size0 = img0->getDimension();

		float scale = sqrt(20000.f / cOpcity);
		w = int(size0.Width * scale);
		h = int(size0.Height * scale);
		DP(("IMG> %d,%d", w, h));
		img = Driver->createImage(ECF_A8R8G8B8, core::dimension2du(w, h));
		img0->copyToScalingBoxFilter(img);
		img0->drop();
		//Driver->writeImageToFile(img, "out/genFromimg.png");

		ft.mImgScale = std::min(inputPng.size() > 0 ? 1.0f : 8.0f, 1.f / scale);
	}
	else
	{
		img = img0;
		ft.mImgScale = 1.0f;
	}
	//DP(("IMG %d,%d", img->getDimension().Width, img->getDimension().Height));
	//Driver->writeImageToFile(img, "out/img.png");
	{
		u8* pb = (u8*)img->lock();
		u32* pu = (u32*)pb;
		int w = img->getDimension().Width, h = img->getDimension().Height;
		pb += 3;
		int xmin = -1, xmax = w;
		int ymin = -1, ymax = h;
		for (int x = 0; x < w && xmin == -1; x++)
			for (int y = 0; y < h; y++)
				if (*(pb + (y * w + x) * 4) > 0)
				{
					xmin = x;
					break;
				}
		for (int x = w - 1; x > xmin && xmax == w; x--)
			for (int y = 0; y < h; y++)
				if (*(pb + (y * w + x) * 4) > 0)
				{
					xmax = x;
					break;
				}
		for (int y = 0; y < h && ymin == -1; y++)
			for (int x = 0; x < w; x++)
				if (*(pb + (y * w + x) * 4) > 0)
				{
					ymin = y;
					break;
				}
		for (int y = h - 1; y > ymin && ymax == h; y--)
			for (int x = 0; x < w; x++)
				if (*(pb + (y * w + x) * 4) > 0)
				{
					ymax = y;
					break;
				}

		xmin = std::max(0, xmin - 1);
		ymin = std::max(0, ymin - 1);
		xmax = std::min((int)w - 1, xmax + 1);
		ymax = std::min((int)h - 1, ymax + 1);
		//DP(("XYMINMAX  %d,%d,%d,%d", xmin, xmax, ymin, ymax));
#if 1
		ft.xyminmax[0] = (float)xmin / (w - 1);
		ft.xyminmax[1] = (float)xmax / (w - 1);
		ft.xyminmax[2] = (float)ymin / (h - 1);
		ft.xyminmax[3] = (float)ymax / (h - 1);
		img->unlock();
#else
		u32 nW = xmax - xmin + 1, nH = ymax - ymin + 1;
		IImage* imgNew = Driver->createImage(ECF_A8R8G8B8, core::dimension2du(nW, nH));
		u32* puN = (u32*)imgNew->lock();

		for (int y = 0; y < nH; y++)
			memcpy(puN + y * nW, pu + (ymin + y) * w + xmin, nW * 4);
		imgNew->unlock();

		img->unlock();
		img->drop();
		img = imgNew;
#endif
	}
	return img;
}

void EQV::processMouth(EQVisual::EQV::PtrFwParam& pfp, ualib::FT2TextParam& a)
{
	pfp.speechId = 0;
	for (int i = 0; i < a.txt.size(); i++)
	{
		switch (tolower(a.txt[i])) {
		case L'a': pfp.speechId = 1; break;
		case L'i': pfp.speechId = 2; break;
		case L'u': pfp.speechId = 3; break;
		case L'e': pfp.speechId = 4; break;
		case L'o': pfp.speechId = 5; break;
		}
		if (pfp.speechId) break;
	}
	//DPWCS(("t = %s",a.txt.c_str()));
	std::wstring txtTrim = a.txt;
	ualib::wcsTrim(txtTrim);
	pfp.speechId1 = -1;
	auto len = txtTrim.size();
	if (len == 1) {
		if (pfp.speechId == 0)
		{
			wchar_t wc = txtTrim[0];
			if (wc == L'n') pfp.speechId = 2;
			else if (wc == L'C') pfp.speechId = 2;
			else if (wc == L'I') pfp.speechId = 1;
			else if (wc == L'B') pfp.speechId = 2;
		}
	}
	else if (len > 1) {
		wchar_t wc = tolower(txtTrim[0]);
		
		if (wc == L'b' || wc == 'f' || wc == L'm'||wc=='p' || wc == 'v') pfp.speechId1 = 0;
		else if (wc == L't' || wc == 'y')  pfp.speechId1 = 2;
		else if (wc == L'q' || wc=='w')  pfp.speechId1 = 3;

	}
	 
}

void EQV::setTfShow(bool isOn)
{ 
	//DP(("TFShow On = %d",isOn));
	isTfShowOn = isOn; 
	mIFppt->tfsPlayId = -1; 
}

void EQV::setTfd(const TfData* tfd)
{
	if (tfd)
		mCurTfd = *tfd;
	else
		mCurTfd = mTfdDefault;

}

void EQV::setTfdJson(std::string jsstr)
{
	ualib::UaJsonSetting jss("mutex_tfdFromJsonString");
	DP(("JSSTR %s", jsstr.c_str()));
	jss.LoadJsonString(jsstr); 

	Tlp->getTfdFromJsonValue(mTfdDefault, jss.copyRootValue());
	mCurTfd = mTfdDefault;
}

void EQV::initComponents(irr::scene::IrrMMD* _mmd)
{
	mmd = _mmd;
//#if USE_SVG
//	svgMan = new SvgManager(this, Ctx,this);
//	if (_mmd && _mmd->sb0) svgMan->setSaba(_mmd->sb0);
//#endif
}

void EQV::pfwSetImage(const PtrImgFwParam& pm, bool launch, bool cached)
{
	if (!inited)
		return;
	IImage* image = pm.img;
	if (!image) return;
	IImage* imgOrig = pm.imgOrig;
	auto driver = Ctx->getDriver();
	int step = 2;


	//UP_RLOCK_GUARD(pfwLock);
	pfpPpm = pm;
	auto &lpm = pfpPpm;

	float scale = lpm.scale;

	//if (!imgTxtFw) imgTxtFw = Driver->createImageFromFile(L"txtfw.png");
	int imgW = image->getDimension().Width;
	int imgH = image->getDimension().Height;
#if FWTEXT_DBGSTEP
	mPixelStep = FWTEXT_DBGSTEP;
#endif
	if (mPixelStep == 0)
	{
		if (mTfpm.pixelStep > 0) step = mTfpm.pixelStep;
		else
		if (imgW > 100 || imgH > 100)			step = std::max(1, int((sqrt((imgW * imgH) / 50000))));
	}
	else step = mPixelStep;
	//DP(("ImgFw step %d", step));
	float scrW = (float)driver->getScreenSize().Width, scrH = (float)driver->getScreenSize().Height;


	//float3 ct;// (UaRandm1to1() * scrW * 0.3, scrH * 0.25f + scrH * 0.1f * UaRandm1to1(), 0.f);

	float width = (float)std::max(2.f, std::min(lpm.imgCanvasWidth * 1.0f, (imgW * scale))), height = width * imgH / imgW;
	float pth = height / imgH;

	//float minY = (-scrH + height) / 2.f;
	//float maxY = (scrH - height) / 2.f;
	//float minX = (-scrW + width) / 2.f;
	//float maxX = (scrW - width) / 2.f;

	//if (ct.x > maxX) ct.x = maxX;
	//if (ct.x < minX) ct.x = minX;
	//if (ct.y < minY) ct.y = minY;
	//if (ct.y > maxY) ct.y = maxY;

	//ct.x = 0;// scrW / 2 - width / 2 - 50;
	//ct.y = 0;// scrH / 2 - height / 2 - 50;

	float camdis = Ctx->getDriver()->dsd.stdDistance;;
	auto campos = SceneManager->getActiveCamera()->getPosition();

	int ptrId = GetPtrFws_FwId(0, 0);
	auto& fwDat = FwMan.mFWs[ptrId];
	auto& emb = FwMan.mEmbers[fwDat.startId];

	//pifBuf.ensureSize(imgW * imgH * sizeof(PixelData));
	lpm.pd = new PixelData[imgW * imgH];
	lpm.ptInLine = imgH;
	lpm.line = 0;
	//ppm.ct = ct;
	lpm.width = width; lpm.height = height; lpm.ptScale = pth;
	lpm.imgW = imgW; lpm.imgH = imgH;
	lpm.lineMax = imgW;
	lpm.ptCount = 0;
	lpm.id = 0;
	lpm.stepr = step;
	//DP(("IMAGE FW %d,%d => %f,%f  step=%d", imgW, imgH, width, height, step));

	//int step = max(1, imgW / 90 + 0.5);
	PixelData* pd = lpm.pd;// = (PixelData*)pifBuf.CurBufPtr;
	int ix = 0, iy = 0;
	//bool hasStartGenOfs = (emb.gm & PFLAG_GenOfsMask) != 0;

	lpm.tl.set(FW_IMG_BORDER, FW_IMG_BORDER, 0);
	lpm.br.set(imgW - FW_IMG_BORDER, imgH - FW_IMG_BORDER, 0);
	for (int tx = lpm.tl.x; tx < lpm.br.x; tx += step)
	{
#if !HAS_HEIGHT_MAP
		for (int ty = lpm.tl.y; ty < lpm.br.y ; ty += step)
		{
			SColor sc = image->getPixelBGRA(tx, ty);
			bool bC = sc.getAlpha() > 0;
#if 0
			if (!bC) { bC = true; sc.color=0x8000FF00; }
#endif
			if (bC)
			{
	
				pd->color = sc.color;
				//pd->startOfs = bC ? 0 : 1;
				pd->x = tx; pd->y = ty;
				bool centerPt = ((ix % 3 == 1) && (iy % 3 == 1));
				pd->flag = (centerPt ? 0 : PFLAG_GenOfs);
				if (imgOrig)
				{
					SColor sc=imgOrig->getPixelBGRA(tx, ty);
					pd->flag |= ((sc.getGreen() > 127) ? PFLAG_GenOfsNull : (sc.getBlue() > 127) ? PFLAG_GenOfsOL : 0);
				}
				auto imgHM = pm.imgHM;

				float a=imgHM->getPixelBGRA(tx, ty).getAlpha();

				pd->dx = tx == 0 ?
					imgHM->getPixelBGRA(tx + 1, ty).getAlpha() - a:
					a - imgHM->getPixelBGRA(tx-1, ty).getAlpha();
				pd->dy = ty == 0 ?
					imgHM->getPixelBGRA(tx, ty + 1).getAlpha() -a:
					a - imgHM->getPixelBGRA(tx, ty-1).getAlpha();

				//if  (imgOrig && imgOrig->getPixelBGRA(tx, ty).getBlue() > 127)

				//pd->flag = centerPt ? 0 : PFLAG_FORBID_CV1;
				pd++;
				lpm.ptCount++;
			}
			iy++;
		}
#else
		for (int ty = 0; ty < imgH; ty += step)
		{
			const SColor& sc = image->getPixelBGRA(tx, ty);
			const SColor& hm = ppm.imgHM->getPixelBGRA(tx, ty);
			bool bC = sc.getAlpha() > 0;
			bool bH = hm.getAlpha() > 0;

			if (bC || (hasStartGenOfs && bH))
			{
				pd->color = bC ? sc.color : (0xFFFFFF | (std::min(0xFFu, hm.getRed() * 5) << 24));
				pd->hmVal = hm.color;
				pd->startOfs = bC ? 0 : 1;
				pd->x = tx; pd->y = ty;
				bool centerPt = ((ix % 3 == 1) && (iy % 3 == 1));
				pd->flag = centerPt ? 0 : 1;
				//pd->flag = centerPt ? 0 : PFLAG_FORBID_CV1;
				pd++;
				ppm.ptCount++;
			}
			iy++;
		}
#endif
		ix++;
	}
	

	//DP(("PIXEL %d/%d", ppm.ptCount, ppm.imgW * ppm.imgH));

	if (launch)
	{
		if (!cached) {
			ppm = pfpPpm;
			pfwDoEmitAll(curPtrFwIds[0]);
		}
	}
	else
	{
		mEmitAll = false;
		ppm.id = ppm.ptCount;
	}

}

void EQV::pfwDoEmitAll(int ptrFwId)
{
	UP_RLOCK_GUARD(pfwLock);
	irr::SEvent::SPointInput ev{};
	ev.act = EPA_Move; 
	ev.X = 0000; ev.Y = -1000;
	pifLastPe = ev;
	pifFramePtrEvts.clear();
	pifFramePtrEvts.push_back(ev);
	PtrImFramegProcessPtrEvts(ptrFwId);
	delete[] ppm.pd;
}

void EQV::PtrImgFwLaunch(const irr::SEvent::SPointInput& pe, int count)
{
	switch (pe.act)
	{
	case irr::EPA_Down:
	{
		//if (pifSnOrigin) pifSnOrigin->setVisible(true);
		pifFramePtrEvts.push_back(pe);
		pifLastPe = pe;
	}
	break;
	case irr::EPA_Move:
	{
		pifFramePtrEvts.push_back(pe);
		pifLastPe = pe;
	}
	break;
	case irr::EPA_Up:
	{
		//if (pifSnOrigin) pifSnOrigin->setVisible(false);
		pifLastPe = pe;
	}
	break;
	}
}

void EQV::PtrImFramegProcessPtrEvts(int ptrFwId)
{
	size_t peC=(uint32_t)pifFramePtrEvts.size();
	if (ppm.id >= ppm.ptCount) return;
	if (peC < 1)
	{
		if (pifLastPe.act == EPA_Down || pifLastPe.act == EPA_Move)
		{
			pifFramePtrEvts.push_back(pifLastPe); pifVelLen += 10;
			peC = 1;
		}
		else
		{
			pifVelLen = 10;
			return;
		}
	}
	size_t count = 0;
	size_t ptCount = mFrameEmitCount;//now large value , ALL OUT in one frame (51200)
	size_t ptPerPeC = ptCount / peC;

	vector3df tgtTrans, srcTrans;
 
	matrix4 matTgtTrans, matSrcTrans;
	if (pfp.absPos) {
		srcTrans = mTfpmUE.srcPos;
		tgtTrans = mTfpmUE.tgtPos;
		matSrcTrans.setTranslation(srcTrans );
		matTgtTrans.setTranslation(tgtTrans );
		pfp.mrTransTgt = matUtoI * matTgtTrans; // offset pos
		pfp.mrTransSrc = matUtoI * matSrcTrans;
	}
	else if (pfp.vfg.useCamMat==0)
	{
		float sx = (float)std::min(1920, Ctx->gd.scrWidth);
		float sy = (float)std::min(1080, Ctx->gd.scrHeight);
		float sz= std::min(1080.f, Ctx->getDriver()->dsd.stdDistance);
		srcTrans.set((mTfpm.srcPos.x + pfp.sdPosOfs.X) * sx, (MIRABS(mTfpm.srcPos.y) + pfp.sdPosOfs.Y) * sy, sz * (mTfpm.srcPos.z + pfp.sdPosOfs.Z));
		tgtTrans.set((mTfpm.tgtPos.x + pfp.sdPosOfs.X) * sx, (MIRABS(mTfpm.tgtPos.y) + pfp.sdPosOfs.Y) * sy, sz * (mTfpm.tgtPos.z + pfp.sdPosOfs.Z));

	}

	//DP(("PtrImFramegProcessPtrEvts  PE=%d  pt=%d",peC, ptCount));
	for (size_t ei=0;ei<peC;ei++)
	{
		auto& pe = pifFramePtrEvts[ei];
		uint32_t lc;	//launch count
		if (ei < peC - 1)
			count += (lc = ptPerPeC);
		else
			lc = ptCount - count;   //now run this , all
		//DP(("C=%d",c));
		size_t maxId = std::min(ppm.id + lc, ppm.ptCount); 

		//DP(("PRS %f   %f", prs, spd));

		float camdis = Ctx->getDriver()->dsd.stdDistance / MMD_SCALE_OLD;
		float x = pe.X, y = pe.Y, z = camdis * (mPtrDis)/MMD_SCALE_OLD;// cz * 0.5*prs;

		auto ScrTo3D = [=](float x, float y, float z) {
			return vector3df((Ctx->gd.screenX + x) * (camdis + z) / camdis, (Ctx->gd.screenY - y) * (camdis + z) / camdis, z);
		};

		vector3df posL = ScrTo3D(pe.lastX, pe.lastY, z);
		vector3df pos = ScrTo3D(x, y, z);//ptr src

		vector3df vecPtr;
		if (pe.lastTimeUs > 0)
		{

			if (pe.timeUs > pe.lastTimeUs && pe.timeUs - pe.lastTimeUs < 20000)
				vecPtr = (pos - posL) * 1000.0 * 200 / (float) std::max(int64_t(1000), pe.timeUs - pe.lastTimeUs);
			//DP(("VECADD %f,%f,%f", vecPtr.X, vecPtr.Y, vecPtr.Z));

		}

		{
			float _pmz = ppm.width / (ppm.imgW / ppm.stepr) * 0.5f;// *pfp.power;// r(half width)
			if (true)
			{
				if (pfp.absPos)
				{
					//core::matrix4 pfp.mrTransSrc;
					//pfp.mrTransSrc.setRotationDegrees(Float3ToV3d(mTfpm.tgtRtt));
				//	pfp.mrTransSrc.setTranslation(mTfpm.srcPos);
					pos = Float3ToV3d(mTfpm.srcPos);
					//mTfpm.srcScale = 0;
					//pfp.mrTransSrc.transformVect(pos);
				}
				else
				{
					pos.X = Ctx->gd.scrWidth * mTfpm.srcPos.x;
					pos.Y = Ctx->gd.scrHeight * mTfpm.srcPos.y;
					pos.Z = Ctx->getDriver()->dsd.stdDistance * mTfpm.srcPos.z;
				}
			}

			switch (pe.act)
			{
			case irr::EPA_Down:
			case irr::EPA_Move:
			{
				int ptrId = GetPtrFws_FwId(0, ptrFwId);
				if (ptrId == 0)
					break;

				auto& fwDat = FwMan.mFWs[ptrId];
				auto& emb = FwMan.mEmbers[fwDat.startId];

				if (ppm.line >= ppm.lineMax)
				{
					mPtrFwMode++;	break;
					//pifPm.line = 0;
				}

				f32  hueStepR = 0.1;

				vector3df scrSize(Ctx->gd.scrWidth, Ctx->gd.scrHeight, 0);
				vector3df scrTlS=   (scrSize - pfp.imgSize) / 2 + (pfp.scrPosOfs);
				if (pfp.centerView)		scrTlS.X = (scrSize.X - pfp.wordSize.X) / 2;
				
				vector3df scrPosBaseS(scrTlS + (pfp.centerRatio - pfp.centerRatio) * pfp.wordSize);
				//.Z = Ctx->getDriver()->dsd.stdDistance * mTfpm.srcPos.z;
				vector3df scrPosBaseT(scrTlS + (pfp.centerRatio - pfp.centerRatio) * pfp.wordSize);
				//scrPosBaseT.Z = Ctx->getDriver()->dsd.stdDistance * mTfpm.tgtPos.z;
				core::matrix4 invcammat;
				auto mv = Ctx->getSceneManager()->getActiveCamera()->getViewMatrix();
				(mv).getInverse(invcammat);

				//vector3df scrPosT(scrPosBaseT + uv * mTfpm.tgtScale /**pfp.power*/ * pfp.wordSize);
				//vector3df scenePosT = vector3df(Ctx->gd.screenX + scrPosT.X, Ctx->gd.screenY - scrPosT.Y, scrPosT.Z) * ppm.ptScale;
				vector3df scrPosT1(scrPosBaseT);
				pfp.corner1 = vector3df(Ctx->gd.screenX + scrPosT1.X, Ctx->gd.screenY - scrPosT1.Y, scrPosT1.Z) * ppm.ptScale;
				vector3df scrPosT2(scrPosBaseT + /* pfp.power * */ pfp.wordSize);
				pfp.corner2 = vector3df(Ctx->gd.screenX + scrPosT2.X, Ctx->gd.screenY - scrPosT2.Y, scrPosT2.Z) * ppm.ptScale;
				vector3df scrPosS1(scrPosBaseS);
				pfp.corner1s = vector3df(Ctx->gd.screenX + scrPosS1.X, Ctx->gd.screenY - scrPosS1.Y, scrPosS1.Z) * ppm.ptScale;
				vector3df scrPosS2(scrPosBaseS +/** pfp.power*/ pfp.wordSize);
				pfp.corner2s = vector3df(Ctx->gd.screenX + scrPosS2.X, Ctx->gd.screenY - scrPosS2.Y, scrPosS2.Z) * ppm.ptScale;

				//pfp.corner1b = vector3df(Ctx->gd.screenX + scrPosT2.X, Ctx->gd.screenY - scrPosT1.Y, scrPosT1.Z) * ppm.ptScale;
				//pfp.corner2c = vector3df(Ctx->gd.screenX + scrPosT1.X, Ctx->gd.screenY - scrPosT2.Y, scrPosT2.Z) * ppm.ptScale;
#if CAM_MOVE_FORWARD
				//float addZ = Ctx->getSceneManager()->getActiveCamera()->getPosition().Z + Ctx->getDriver()->dsd.stdDistance * FW_TEXT_STD_DIS_MUL;
				//pfp.corner1.Z += addZ;	pfp.corner2.Z += addZ;
#endif		

				if (pfp.absPos)
				{
					

				}
				else
				{
					pfp.mrTransTgt.setRotationDegrees(mTfpm.tgtRtt);
					pfp.mrTransTgt.setTranslation(tgtTrans);
					////core::matrix4 mr, mt;	mr.setRotationDegrees({ 0,90,0 });	mt.setTranslation(tgtTrans);	pfp.mrTransTgt = mr * mt;
					pfp.mrTransSrc.setRotationDegrees(mTfpm.tgtRtt);
					pfp.mrTransSrc.setTranslation(srcTrans);
					pfp.mrTransSrc.setScale(mTfpm.srcScale);
					pfp.mrTransTgt.setScale(mTfpm.tgtScale);
					/// SWAP fY Z
					if (mTfpm.plane)
					{
						//static int r = 0; r += 10;
						core::matrix4 mR; mR.setRotationDegrees({mTfpm.rotateX,0,0 }); 
						if (mTfpm.plane & 1) 
							pfp.mrTransSrc = mR * pfp.mrTransSrc;

						if (mTfpm.plane & 2)
							pfp.mrTransTgt = mR * pfp.mrTransTgt ;

						if (mTfpm.plane & 0x10)
						{
							assert(0);//todo use pfp.mrTransSrc
							//gf.pos = V3dToFloat3(firePos);
							//gf.vec = V3dToFloat3(fireVec);
						}
					}

				}


#if AR_FW_TEXT_ON_CAM_PATH
				//#error pfp.mrTransSrc todo
				auto cam = SceneManager->getActiveCamera();// Ctx->gd.CamNormal;
				auto crt = cam->getRotation(); crt.X = crt.X;  crt.Y = 360 + crt.Y;
				//pfp.mrTransTgt.setRotationDegrees(crt);
				irr::core::vector3df vel;
				MatrixRecorder::DrDataStruct ds;
				int mrid = 0;
				bool foundDS = MatRec->drGetDataTimeOfs(3.0f + pfp.delayS0b, ds, &vel, mrid);
				vector3df posTxt;

				matrix4 mt;  
				if (foundDS && mmd)
				{
					auto sb = mmd->sb0;
#if DRONE_AR
					crt.set((ds.d.rtt.X), ds.d.rtt.Y, ds.d.rtt.Z);  //future face
					posTxt = ds.d.pos + crt.rotationToDirection() * 600;// front of future pos
					//crt.X = crt.Z = 0; crt.Y = crt.Y + 180;  // turn face 180 to cam?
					//pfp.mrTransTgt.setRotationDegrees({ 180,180,0 });

					mt.setRotationDegrees(crt);
					mt.setTranslation(posTxt);
#else
#if 0
					crt = MatRec->dirAt(mrid);
					matrix4 m;
					m.buildRotateFromTo({ 0,0,1 }, crt);
					crt = m.getRotationDegrees();
					posTxt = ((ds.d.pos) * ar.camPosScale)  ;
#else
					matrix4 m; float dis = 800.f;
					auto& vm = cam->getViewMatrix();
					auto headPos = sb->headPos;
					vm.translateVect(headPos);
					vm.getInverse(mt);
					dis = cam->getAbsolutePosition().getDistanceFrom(sb->headPos); //headPos.Z/2;// cam->getAbsolutePosition().getDistanceFrom(mmd->sb0->camTgt->getAbsolutePosition());
					dis -= std::max(80.f, dis * 0.2f);
					cam->getViewMatrix().getInverse(mt);
					m.setTranslation({ 0, 0, dis });
					mt = mt * m;
#endif
#endif

				}
				else
				{
					matrix4 m; float dis,stdDis = Ctx->getDriver()->dsd.stdDistance;
#if HAS_MMD
					
					if (mmd) {
						auto sb = mmd->sb0;
						if (cam == sb->irrCam) sb->updateCameraAtOffsetTime( -ptrFws[0][ptrFwId].tfp.timeOffset);
						auto &vm = cam->getViewMatrix();
						auto headPos = sb->headPos;
						vm.translateVect(headPos);
						vm.getInverse(mt);
						dis = cam->getAbsolutePosition().getDistanceFrom(sb->headPos); //headPos.Z/2;// cam->getAbsolutePosition().getDistanceFrom(mmd->sb0->camTgt->getAbsolutePosition());
						dis -= std::max(80.f, dis * 0.5f);
						if (cam == sb->irrCam) sb->updateCameraAtOffsetTime(0.f);
					}
					else
#endif
					{
						dis = stdDis;
						cam->getViewMatrix().getInverse(mt);
					}
					m.setTranslation({ 0, 0, dis });
					//posTxt = cam->getAbsolutePosition();

					mt =  mt*m;
				}

				

				
				pfp.mrTransTgt = mt * pfp.mrTransTgt;
				pfp.mrTransSrc = mt * pfp.mrTransSrc;
#endif
				matrix4 mttgt = pfp.mrTransTgt;
				matrix4 mtsrc = pfp.mrTransSrc;

#if TXT_FROM_MMD
				if (auto saba = ft.fwSaba)
				{					
					mtsrc = saba->fwtMat;					
					sabaVel = saba->fwtAbsVel;
					mttgt = saba->fwtMat * glm::translate(glm::mat4(1), {0,-10,-300});
					//mttgt.setTranslation(mtSrc.getTranslation() + dir);
					mttgt.setScale(mTfpm.tgtScale);
					mtsrc.setScale(mTfpm.srcScale);					
				}
#endif
#if PHYSICS_MMD_FW
				if (HAS_AR_EDITOR && mTfpm.srcType == 1 && ei==0 && ft.fwSaba) {
					saba::MMDNode* nd; static int cc = 0; cc += 1;// UaRand(3) + 1;
					switch (cc % 2 +0) {
					default:nd = ft.fwSaba->ndHandL; break;
					case 1:nd = ft.fwSaba->ndHandR; break;
					case 2:nd = ft.fwSaba->ndFootL; break;
					case 3:nd = ft.fwSaba->ndFootR; break;
					}
					if (nd) {
						core::matrix4 m = nd->GetGlobalTransform();
						auto pos = m.getTranslation();
						ft.fwSaba->mmdBaseMat.transformVect(pos);
						ft.forceNode = nd;
						ft.mmdSrcPos = pos;
						//nd->rb0->Reset(ft.fwSaba->Pmx->GetMMDPhysics());
						//nd = nd->GetParent();
						//if (nd) nd->rb0->Reset(ft.fwSaba->Pmx->GetMMDPhysics());
						//nd = nd->GetParent();
						//if (nd) nd->rb0->Reset(ft.fwSaba->Pmx->GetMMDPhysics());
						matrix4 mt1 = pfp.mrTransTgt; vector3df tgt = (pfp.corner1 + pfp.corner2) / 2;
						mt1.transformVect(tgt);



						vector3df vel = (tgt - pos).normalize();
						ft.mmdSrcVel = vel * 10 * MMD_SABA_SCALE;

						auto mmdpos = pos, mmdtgt = tgt;

						ft.fwSaba->irrToMmdPos(mmdpos);
						ft.fwSaba->irrToMmdPos(mmdtgt);
						vel = (tgt - pos).normalize();
						auto force = vel * 102 * MMD_SABA_SCALE;
						ft.mmdThrowTimer = std::min(0.3f, pfp.delayS1);
						ft.mmdThrowForce = force;
					}
				}
#endif



				assert(!isTfShowOn || !isPfpIdxOn);
				uint32_t bandId = isPfpIdxOn ? pfp.chIdx : (isTfShowOn ? mIFppt->tfsPlayId : -1);
				//										LOOP

				ivi.vfg.grcount = 0;
				{
					
					//CPU_COUNT_BEGIN("EQVCCC");
					size_t count = maxId - ppm.id;
					float gr=0.f;
					
					if (pfp.mid ==-12781 && count>1) //calc vtx grid
					{
						
						GenFirework gfs[2];
						PixelData* pds = (PixelData*)ppm.pd;
						PixelData pd[2] = { pds[0],pds[1] }; 
						pd[0].x = pfp.ppm.tl.x; pd[0].y = pfp.ppm.tl.y;
						pd[1].x = pfp.ppm.br.x; pd[1].y = pfp.ppm.br.y;
						PtrImgLoop(this, 0, 2, gfs, fwDat, _pmz, 0, pos, pfp.mrTransSrc, pfp.mrTransTgt, pd);
						 
						vector3df box = { gfs[1].pos.x - gfs[0].pos.x, gfs[0].pos.y - gfs[1].pos.y,0 };						
						pfp.boxSize = box; 
						gr = box.y /  pfp.vfg.grid;
						if (pfp.vfg.grid < 0) gr = -pfp.vfg.grid;
						pfp.gr = gr;
						vector3di grSize(ceil(box.x / gr) , ceil(box.y / gr) , 1);
						pfp.vfg.r = gr;	pfp.vfg.xc = grSize.x; pfp.vfg.yc = grSize.y;  pfp.vfg.calcCount();
						DP(("box %fx%f -> %dx%d -> %fx%f =  %f,%f  -  %f,%f ", box.x, box.y,grSize.x,grSize.y,gr*grSize.x,gr*grSize.y, gfs[0].pos.x, gfs[0].pos.y, gfs[1].pos.x, gfs[1].pos.y));

						ivi.vfg = pfp.vfg; 
						auto useCamMat = ivi.vfg.useCamMat;
						core::matrix4 m;
						if (useCamMat == 2 && ivi.vfg.baseNode)
							m = (mmd->sb0->mmdBaseMat * core::matrix4(ivi.vfg.baseNode->GetGlobalTransform() * ivi.vfg.nodeLocalMat)).getTransformTR();
						else if (useCamMat == 3) {
							m.makeIdentity();
							m.setTranslation(mTfpmUE.srcPos);
						}							
						else m=Ctx->getViewCamera()->getAbsoluteTransformation();
						 
 
						vector3df bt =  gfs[1].pos; m.transformVect(bt);
						 
 
						ivi.rtt = glm::eulerAngles(glm::quat( glm::mat4(m)));
						
						for (int y = 0; y < ivi.vfg.yc; y++) for (int x = 0; x < ivi.vfg.xc; x++){
							int xc= ivi.vfg.xc, yc= ivi.vfg.yc;
							int i = y * xc + x; 
							if (i >= MaxVtxGrid) 
								break; 
							
							auto& ofs = ivi.ofs[i]; 
							auto& vel = ivi.dir[i];  // fw rb vel
							if (useCamMat) {
								if (useCamMat == 3) vel = mTfpmUE.srcVel/ MMD_SABA_SCALE;
								else vel.set(0, 0, 1);
								ofs.set(gfs[0].pos.x + gr * (x + 0.5), gfs[1].pos.y + gr * (y + 0.5) - std::min(bt.y, 00.f), (useCamMat == 2?3:6) * MMD_SABA_SCALE);
							}
							else {
								vel.set(0, 0, 0);
								ofs.set((gfs[0].pos.x+ gfs[1].pos.x)/2 + ((xc % 2 ? 0 : 0.5f) - xc / 2 + x) * gr , (gfs[0].pos.y + gfs[1].pos.y) / 2 + ((yc%2?0:0.5f) - yc / 2 + y) * gr, gfs->pos.z);
							}
							vel += ivi.vfg.velOfs;
							//ofs.x -= gr / 2;
							//ofs.y += gr / 2;
							//ofs += {0,1790,-2000};
							//ofs /= MMD_SABA_SCALE;// sb->irrToMmdPos(ofs);
							float timer = 0;
							if (mTfpm.durType < 2)
							{
								float durTime = mTfpm.durType == 1 ? (pfp.delayS0dur ) : (0.f);
								timer = std::max(0.f, pfp.delayS0b + durTime);
								timer += std::max(0.f, pfp.delayS1 - durTime);
							}
							else
							{
								timer = std::max(0.f, pfp.delayS0b);
								timer += std::max(0.f, pfp.delayS0dur);								
							}
							ivi.vfg.poTimer = timer+ pfp.durMax;
							ivi.irrOfs[i] = float3(ofs.x,ofs.y, gfs->pos.z);
							//ofs.y -= 16 *MMD_SABA_SCALE;
							if (useCamMat) ofs += Ctx->gd.apm.camOfsCtr * MMD_SABA_SCALE;
							m.transformVect(ofs);
							if (useCamMat != 3) m.rotateVect(vel);
							
							ofs /= MMD_SABA_SCALE;//irr to mmd
							
							ivi.vfg.grcount++;
						}
						
						cbIncVtxMat[pfp.vfg.sbItemIdx](ivi); 						
						pfp.mid = ivi.ids[0]; //need >0  
						
						
					assert(ivi.vfg.grcount <= MaxVtxGrid);
					ivi.vfg.grcount =std::min(ivi.vfg.grcount, (int) MaxVtxGrid);
					for (int i = 0; i < ivi.vfg.grcount; i++)			ivi.gridFwCount[i] = 0;
						 
					}

					if (count>gfArr.size())
					gfArr.resize(count);
					GenFirework* gfs = gfArr.data();
					PixelData* pd = (PixelData*)ppm.pd;
#if 0
					PtrImgLoop(this, 0, count, gfs, fwDat, _pmz, bandId, pos, pfp.mrTransSrc, pfp.mrTransTgt, pd);
#else //about 3X faster
					std::vector< std::future<void> > results;

					int h1 = count / Ctx->mpThreadNum; 
					int hLeft = count % Ctx->mpThreadNum;

					for (int i = Ctx->mpThreadNum - 1; i >= 0; i--)
					{
						size_t start = i * h1;
						size_t countd = (i == Ctx->mpThreadNum - 1 ? h1 + hLeft : h1);
						DP(("t %d-%d (%d)", start, start + countd, countd));
						results.emplace_back(Ctx->mpTPool->enqueue(&EQV::PtrImgLoop, this,
							start, countd, gfs+ start, fwDat, _pmz, bandId, pos, mtsrc, mttgt,pd));
					}

					for (auto&& result : results)
					{
						result.get();
					}
#endif
				
					mIFpptTxt->FwBulkLaunch(gfs, count);
					//CPU_COUNT_END("EQVCCC");
					ppm.id = maxId;


					
					if (ivi.vfg.grcount > 0) {
						connectTextFwGridRbs(gr);
					}

					//for (int y = 0; y < ivi.vfg.yc; y++) for (int x = 0; x < ivi.vfg.xc; x++) {
					//	int xc = ivi.vfg.xc, yc = ivi.vfg.yc;
					//	int i = y * xc + x;					
					//}


				}
			}
			break;
			}
		}
	}
	pifFramePtrEvts.clear();
}

void EQVisual::EQV::connectTextFwGridRbs(float gr)
{
	//remove unused grid
	//if (ivi.vfg.connectMode == 0)		return;
	if (ivi.vfg.connectMode != 2)
		for (int i = 0; i < ivi.vfg.grcount; i++) {
			if (ivi.gridFwCount[i] < 1 && ivi.po[i]->rb) {
				ivi.po[i]->rb->setCollideFilterMask(1, 0);
				ivi.po[i]->timer = 0;
			}
		}

	switch (ivi.vfg.connectMode)
	{
	case 1: //each other has FW (by AI)
		for (int y = 0; y < ivi.vfg.yc; y++) for (int x = 0; x < ivi.vfg.xc; x++) {
			int i = y * ivi.vfg.xc + x; if (i >= MaxVtxGrid) { break; }

			// Skip if the current object's timer is zero
			if (ivi.po[i]->timer == 0) continue;

			auto& o = *ivi.po[i];
			saba::PMXJoint jt{}; jt.setLocalPos = 0;
			jt.springT = glm::vec3(100000);
			jt.springR = glm::vec3(10000);
			jt.rotate = ivi.rtt;
#if 1
			// Connect to the nearest non-zero timer object above
			for (int dy = 1; y - dy >= 0; dy++) {
				int iu = (y - dy) * ivi.vfg.xc + x;
				if (ivi.po[iu]->timer != 0) {
					auto& ou = *ivi.po[iu];
					jt.translate = (ivi.ofs[i] + ivi.ofs[iu]) / 2;
					mmd->sb0->Pmx->connectRb(ou.rb, o.rb, 0, 0, jt)->setBreakThreshold(pow(gr / 6.f, 3.f));
					break;
				}
			}

			// Connect to the nearest non-zero timer object to the left
			for (int dx = 1; x - dx >= 0; dx++) {
				int il = y * ivi.vfg.xc + (x - dx);
				if (ivi.po[il]->timer != 0) {
					auto& ol = *ivi.po[il];
					jt.translate = (ivi.ofs[i] + ivi.ofs[il]) / 2;
					mmd->sb0->Pmx->connectRb(ol.rb, o.rb, 0, 0, jt)->setBreakThreshold(pow(gr / 6.f, 3.f));
					break;
				}
			}
#endif
			bool connBase = true;
			if (ivi.gridCenterIdx > 0) {
				auto& o = *ivi.po[i];
				auto& oc = *ivi.po[ivi.gridCenterIdx];
				saba::PMXJoint jt{}; jt.setLocalPos = 0;
				jt.springT = glm::vec3(10000);
				jt.springR = glm::vec3(10000);
				jt.rotate = ivi.rtt;
				jt.translate = (ivi.ofs[i] + ivi.ofs[ivi.gridCenterIdx]) / 2;
				if (oc.rb != o.rb)
					mmd->sb0->Pmx->connectRb(oc.rb, o.rb, 0, 0, jt)->setBreakThreshold(pow(gr / 6.f, 3.f));
				saba::MMDRigidBody* rb = ivi.vfg.baseNode ? ivi.vfg.baseNode->rb0 : nullptr;
				connBase = false;
				//if (i == ivi.gridCenterIdx && rb) connBase = true;

			}
			if (connBase)
			{
				saba::MMDRigidBody* rb = ivi.vfg.baseNode ? ivi.vfg.baseNode->rb0 : nullptr;
				if (rb && ivi.vfg.baseNode) {
					saba::PMXJoint jt{}; jt.setLocalPos = 1;
					jt.springT = glm::vec3(1000); jt.dampingT = glm::vec3(100);
					jt.springR = glm::vec3(1000); jt.dampingR = glm::vec3(100);
					jt.translate = ivi.ofs[i]; jt.rotate = ivi.rtt;
					jt.limitMinT = glm::vec3(-1);						jt.limitMaxT = glm::vec3(1);
					//o.rb->setCollideFilterMask(1,0);
					auto j = mmd->sb0->Pmx->connectRb(rb, o.rb, 0, 0, jt);
				}
			}
		}
		break;
	case 2://  each other ALL
		for (int y = 0; y < ivi.vfg.yc; y++) for (int x = 0; x < ivi.vfg.xc; x++) {
			int i = y * ivi.vfg.xc + x; if (i >= MaxVtxGrid) { break; }
			int iu = (y - 1) * ivi.vfg.xc + x;
			int il = y * ivi.vfg.xc + x - 1;
			auto& o = *ivi.po[i]; auto& ou = *ivi.po[iu]; auto& ol = *ivi.po[il];
			saba::PMXJoint jt{}; jt.setLocalPos = 0;
			jt.springT = glm::vec3(100000000.f);
			jt.springR = glm::vec3(10000);
			jt.dampingT = glm::vec3(10.f);
			jt.dampingR = glm::vec3(10.f);
			jt.translate = (ivi.ofs[i] + ivi.ofs[iu]) / 2;
			jt.rotate = ivi.rtt;
			//jt.limitMinT = glm::vec3(-1);						jt.limitMaxT = glm::vec3(1);

			if (y > 0) mmd->sb0->Pmx->connectRb(ou.rb, o.rb, 0, 0, jt);// ->setBreakThreshold(pow(gr / 6.f, 3.f));;
			jt.translate = (ivi.ofs[i] + ivi.ofs[il]) / 2;
			if (x > 0) mmd->sb0->Pmx->connectRb(ol.rb, o.rb, 0, 0, jt);// ->setBreakThreshold(pow(gr / 6.f, 3.f));;

			bool connBase = true;
			if (ivi.gridCenterIdx > 0) {
				auto& o = *ivi.po[i];
				auto& oc = *ivi.po[ivi.gridCenterIdx];
				saba::PMXJoint jt{}; jt.setLocalPos = 0;
				jt.springT = glm::vec3(100000000.f);
				jt.springR = glm::vec3(10000);
				jt.rotate = ivi.rtt;
				jt.translate = (ivi.ofs[i] + ivi.ofs[ivi.gridCenterIdx]) / 2;
				if (oc.rb != o.rb)
					mmd->sb0->Pmx->connectRb(oc.rb, o.rb, 1, 0, jt);// ->setBreakThreshold(pow(gr / 6.f, 3.f));
				saba::MMDRigidBody* rb = ivi.vfg.baseNode ? ivi.vfg.baseNode->rb0 : nullptr;
				connBase = false;
				//if (i == ivi.gridCenterIdx && rb) connBase = true;

			}
			if (connBase)
			{
				saba::MMDRigidBody* rb = ivi.vfg.baseNode ? ivi.vfg.baseNode->rb0 : nullptr;
				if (rb && ivi.vfg.baseNode) {
					saba::PMXJoint jt{}; jt.setLocalPos = 1;
					jt.springT = glm::vec3(1000); jt.dampingT = glm::vec3(100);
					jt.springR = glm::vec3(1000); jt.dampingR = glm::vec3(100);
					jt.translate = ivi.ofs[i]; jt.rotate = ivi.rtt;
					jt.limitMinT = glm::vec3(-1);						jt.limitMaxT = glm::vec3(1);
					//o.rb->setCollideFilterMask(1,0);
					auto j = mmd->sb0->Pmx->connectRb(rb, o.rb, 0, 0, jt);
				}
			}
		}
		break;
	case 3://ground
	{
		auto baseRb = mmd->sb0->Pom->groundRb;
		auto baseRbPos = baseRb->getPosition();
		for (int y = 0; y < ivi.vfg.yc; y++) for (int x = 0; x < ivi.vfg.xc; x++) {
			int i = y * ivi.vfg.xc + x; if (i >= MaxVtxGrid) { break; }
			int iu = (y - 1) * ivi.vfg.xc + x;
			int il = y * ivi.vfg.xc + x - 1;
			auto& o = *ivi.po[i]; auto& ou = *ivi.po[iu]; auto& ol = *ivi.po[il];
			auto rb = o.rb;
			saba::PMXJoint jt{}; jt.setLocalPos = true;
			jt.springT = glm::vec3(10000);
			jt.springR = glm::vec3(10000);
			jt.translate = ivi.ofs[i] - baseRbPos;
			jt.limitMaxT = glm::vec3(0, 1, 0);
			//o.rb->setCollideFilterMask(1,0);
			auto j = mmd->sb0->Pmx->connectRb(baseRb, o.rb, 0, 0, jt);
			j->setBreakThreshold(pow(gr / 60.f, 3.f));
			FrameWaiter waiter;
			waiter.waitNframeAndRun(ivi.vfg.poTimer * 60 * 0.2f, [=](FWTask& t) {
				j->destroy();
				//rb->addLinearVel({ 0,10,0 });
				});
			Ctx->setSharedRbCb(o.rb);
		}
	}
	break;
	}
}
vector3di putDotInGrid(float x, float y, int m, int n, float a) {
	// Find the offset from the center
	float xOffset = m * a / 2;
	float yOffset = n * a / 2;

	// Translate point (x, y) to grid coordinates
	int gridX = static_cast<int>(std::floor((x + xOffset) / a));
	int gridY = static_cast<int>(std::floor((y + yOffset) / a));

	return { std::clamp(gridX,0,m-1), std::clamp(gridY,0,n-1),0 };
}

 

void EQV::PtrImgLoop(EQV* eqv, const size_t& startId, const size_t& count, GenFirework* gfs , 
	Firework& fwDat, float _pmz, uint32_t bandId, irr::core::vector3df& pos, irr::core::matrix4& mrTransSrc, irr::core::matrix4& mrTransTgt, PixelData* pd  )
{
	
	auto& ppm = eqv->ppm;
	auto& mTfpm = eqv->mTfpm;
	auto& pfp = eqv->pfp;
	//DP(("tcl %3.1f", gf.tcl.x));



	for (size_t i=0;i<count;i++)
	{
		size_t id = ppm.id+startId +i;
		GenFirework& gf = *(gfs + i);
		PixelData& pd1 = pd[id];

		int tx = pd1.x;
		int ty = pd1.y;
		
		if (tx > ppm.imgW || ty > ppm.imgH) 
			return;
		int iPtRand = rand();// RAND_MAX;
		gf.bandId = bandId;
		core::vector3df vec;
#if TXT_FROM_MMD
		vec = eqv->sabaVel;
#else
		if (mTfpm.srcVelType & 0x01) vec += Float3ToV3d(mTfpm.srcVel);
		if (mTfpm.srcVelType & 0x02) vec += vector3df(UaRandm1to1(), UaRandm1to1(), UaRandm1to1());
		if (mTfpm.srcVelType & 0x08) vec += FibonacciSphere(ppm.ptCount, id);
		else if (mTfpm.srcVelType & 0x10) 
			vec += EqvFun_DxDy(pd1.dx / 255.f, pd1.dy / 255.f);
		vec.normalize();
		if (mTfpm.srcVelType & 0x100) vec += HeartCurve2D(ppm.ptCount, id);
		else if (mTfpm.srcVelType & 0x1000) vec += HelixUp(ppm.ptCount, id);

		vec *= eqv->pifVelLen;
#endif
		//DP((" i=%d %d  %d,%d", i,id , tx, ty));

		u32 c = pd1.color;
		u32 a = c >> 24;
#if HAS_HEIGHT_MAP
		if (pd1.hmVal > 0)
#endif
		if (a < 1)				continue;

		SColorf colOut(c);
		if (pfp.hueOfs > 0.001f)
		{
			SColorHSL hsl(colOut);
			hsl.Hue += pfp.hueOfs;
			//hsl.Hue += UaRand(60) 
			hsl.toRGB(colOut);
		}
		float fa = colOut.a;
		gf.embId = fwDat.startId;
		gf.vec = V3dToFloat3(vec);
		vector3df tgtPos;
		gf.pmz = _pmz;
		gf.col = float4(colOut.r, colOut.g, colOut.b, colOut.a);
		//gf.tcl = float3(ct.x - imgW * pth / 2 + tx * pth, ct.y + height / 2 - ty * pth, ct.z);

		vector3df uv(float(tx) / ppm.imgW, float(ty) / ppm.imgH, 0);
				

		//vector3df scrPos(scrPosBase + uv * mTfpm.srcScale * pfp.wordSize);
		//vector3df scenePos = vector3df(Ctx->gd.screenX + scrPos.X, Ctx->gd.screenY - scrPos.Y, scrPos.Z) * ppm.ptScale;
		vector3df scenePos = pfp.corner1s + (pfp.corner2s - pfp.corner1s) * uv;
#if ROTATE_TEXT_90
		std::swap(scenePos.Y, scenePos.Z);
#endif

		//vector3df scrPosT(scrPosBaseT + uv * mTfpm.tgtScale /** pfp.power*/ * pfp.wordSize);
		vector3df scenePosT = pfp.corner1 + (pfp.corner2 - pfp.corner1) * uv;
#if ROTATE_TEXT_90
		std::swap(scenePosT.Y, scenePosT.Z);
#endif
			
#if USE_SBT_ANIM3D
		matrix4 mt; 

		mt = eqv->snSbtCur->getAbsoluteTransformation();

		matrix4 mts = mt *mrTransSrc;
		mts.transformVect(scenePos);
		matrix4 mtt = mt *mrTransTgt;
		mtt.transformVect(scenePosT);

#else
		mrTransSrc.transformVect(scenePos);
#if !TXT_FROM_MMD
		if (mTfpm.plane & 1) {			
			mrTransSrc.rotateVect(vec);
			gf.vec = vec;
		}
#endif
		mrTransTgt.transformVect(scenePosT);
#endif
			
		tgtPos = scenePosT;//pfp.scrPosOfs + tgtOfs;
#if PHYSICS_MMD_FW
		if (mTfpm.srcType == 1) {
			gf.pos = eqv->ft.mmdSrcPos;
			gf.vec = eqv->ft.mmdSrcVel;
		}
		else
#endif
			gf.pos = ualib::V3dToFloat3(scenePos)
			//+float3(0,  cos(a * uv.X) * rad,  sin(a * uv.X) * rad)
			;
		

#if 1

		//mrTransTgt.transformVect(tgtPos);
#endif			

#if HAS_HEIGHT_MAP
		gf.tcl = ualib::V3dToFloat3(tgtPos) + float3(0, 0, -int(pd1.hmVal & 0xFF) / 10.f);
#else
		gf.tcl = ualib::V3dToFloat3(tgtPos);
#endif
		//std::swap(gf.tcl, gf.pos);
#if   0
		vector3df pos1, vec1, posCamSpace(gf.pos.x - Ctx->gd.screenX, Ctx->gd.screenY - gf.pos.y, gf.pos.z + Ctx->getDriver()->dsd.stdDistance * 1.5);
		CamSpaceToSceneSpace(0, &invcammat, vector2df(Ctx->gd.screenX, Ctx->gd.screenY), posCamSpace, vec, pos1, vec1);
		gf.pos = ualib::V3dToFloat3(pos1);
		posCamSpace.set(gf.tcl.x - Ctx->gd.screenX, Ctx->gd.screenY - gf.tcl.y, gf.tcl.z + Ctx->getDriver()->dsd.stdDistance * 1.5);
		CamSpaceToSceneSpace(0, &invcammat, vector2df(Ctx->gd.screenX, Ctx->gd.screenY), posCamSpace, vec, pos1, vec1);
		gf.tcl = ualib::V3dToFloat3(pos1);
#elif CAM_MOVE_FORWARD
		//float addZ = eqv->Ctx->getSceneManager()->getActiveCamera()->getPosition().Z + eqv->Ctx->getDriver()->dsd.stdDistance * FW_TEXT_STD_DIS_MUL;
		//gf.tcl.z += addZ;		gf.pos.z += addZ;
#endif

		if (mTfpm.durType < 2)
		{
			float durTime = mTfpm.durType == 1 ? (pfp.delayS0dur * id / ppm.ptCount) : (0.f);
			gf.pv.x = std::max(0.f, pfp.delayS0b + durTime);
			gf.pv.y = std::max(0.f, pfp.delayS0dur - durTime);
		}
		else
		{
			gf.pv.x = std::max(0.f, pfp.delayS0b);
			gf.pv.y = std::max(0.f, pfp.delayS0dur);
		}
		gf.flag = PFLAG_NO_KLD | (pd1.flag) | (iPtRand % mTfpm.genOfsRatDen == 0 ? PFLAG_GenOfsRatio : 0);// | pd1.flag;;
																						   
		gf.mid = pfp.mid;																	
		 
		if (pfp.mid > 0 && pfp.vfg.r > 0) {
			auto& ivi = eqv->ivi;
			glm::ivec3 gr = glm::ivec3(uv.x*pfp.vfg.xc, (1.f-uv.y)*pfp.vfg.yc, 0);//
			//putDotInGrid(gf.pos.x , gf.pos.y  , pfp.vfg.xc, pfp.vfg.yc, pfp.vfg.r);
			float w = pfp.boxSize.x, h = pfp.boxSize.y;
			float a = pfp.gr;
			int offset_x = (w - pfp.vfg.xc * a) / 2;
			int offset_y = (h - pfp.vfg.yc * a) / 2;

			gr.x = std::floor((uv.x*w - offset_x) / static_cast<float>(a));
			gr.y = std::floor(((1-uv.y)*h - offset_y) / static_cast<float>(a));

			int vid = gr.y * pfp.vfg.xc + gr.x;
			if (!pfp.centerView){
				gf.pos -= ivi.irrOfs[vid];
				if (!SABA_RB_LOCAL_TRANSFORM) gf.pos = glm::quat(ivi.rtt) * gf.pos;
			//gf.pos.x += -a / 2;
			// gf.pos.y += -a / 2;
			}
			else 
			{
				gf.pos.x += (pfp.vfg.xc - 1) * pfp.vfg.r / 2 - gr.x * pfp.vfg.r;
				gf.pos.y += (pfp.vfg.yc - 1) * pfp.vfg.r / 2 - gr.y * pfp.vfg.r;
			}

			 //gr = putDotInGrid(gf.pos.x, gf.pos.y, pfp.vfg.xc, pfp.vfg.yc,pfp.vfg.r);
			//gf.col.r = 1.0f * gr.x / (pfp.vfg.xc - 1);// *(gr.x % 2);
			//gf.col.g = 1.0f * gr.y / (pfp.vfg.yc - 1);// *(gr.y % 2);	
			//gf.col.b = 0;

			gf.mid = ivi.ids[vid]; ivi.gridFwCount[vid]++;
		}
	}

}

void EQV::setSbtTarget(SbtAniParam sap)
{

#if CAM_TIMELINE_ANIMATION
	Sap = sap;
	Sap.timer = 1.f - Sap.durS;
	snSbtCur->setPosition(snSbtCur->getPosition() + sap.pos);
	snSbtCur->setRotation(snSbtCur->getRotation()+ sap.rtt);
	snSbtCur->updateAbsolutePosition();
	Sap.pos = snSbtCur->getPosition();
	Sap.rtt = snSbtCur->getRotation();
#endif
}

void EQV::resetSbtAni() {
#if CAM_MOVE_FORWARD
	snSbtCur->setPosition({ 0, Ctx->gd.scrHeight/4.f, 0 });
#else
	snSbtCur->setPosition({ 0, 0, 0 });
#endif

	snSbtCur->setRotation({ 0, 0, 0 });
	snSbtTgt->setPosition({ 0, 0, 0 });
	snSbtTgt->setRotation({ 0, 0, 0 });
	snSbtItv->setPosition({ 0, 0, 0 });
	snSbtItv->setRotation({ 0, 0, 0 });
#if USE_SVG
	if (svgMan) svgMan->reset();
#endif
}

bool EQV::loadStyleFile(std::string pm1)
{
#if IS_WIN
	DP(("Load style %s ", pm1.c_str()));
	std::string s, ani;
	apng::loadStyleFromImage(pm1.c_str(), s, ani);
	if (s.length() > 0)
	{
		DP(("Load style OK "));
		eqvDatStringToLoad = s;
		recreateOnUpdate();
	}
	else return false;
	ScdPtsTxt.isOn = false;
	stringSaveToFile(s, EQV_STYLE_PATH "/a aaaaa LOADED.eqvdef", false);
	if (ani.length() > 0)
		stringSaveToFile(ani, EQV_STYLE_PATH "/1camtl LOADED.eqvdef", false);
	ualib::SleepMs(500);
	ScdPtsTxt.isOn = true;
#endif
	return true;
}
void EQV::saveScreenShot()
{
#ifdef DBG_ON_PC


#if !KALEIDO_ART
	auto fn = getTempStrByTime("", "", false);
	saveStyleImg(fn);

#else //ART KALEIDO OUTPUT
	io::path str = getTempStrByTime("data/savedImg/FTIMG", ".tmp", true).c_str();
	//IImage* img = mDriver->createScreenShot();


	auto tex = VkDrv->TexRT;// mDriver->addTexture("t", img);
	if (!rtScr)
		rtScr = mDriver->addRenderTargetTexture(tex->getSize());

	drawTexArtEffect(tex, true);

	str.replace(".tmp", "1.png");
	//Driver->writeImageToFile(img, str);
#if 1
	//str.replace("1.png", "2.png");
	//Driver->saveTexture(rtScr, str);//	
	//str.replace("2.png", ".jpg");
	Driver->saveTexture(rtScr, str);//			Driver->writeImageToFile(img, str);
#endif
#endif
#endif
}
void EQV::saveStyleImg(std::string fname )
{
	DP(("saveStyleImg"));
	auto drv = Ctx->getDriver(0);
	auto img = drv->createScreenShot();

	MakeImageSetting mis;
	mis.formatDynamic = 2;
	mis.bgColor = 0;
	//mis.bgTransparent = 0;
	mis.dpFrameNum = 1;
	mis.dpFrameIntervalMs = 1000;
	mis.dpSkipFrame = 60;
	mis.unMultiplyAlpha = 0;
	styleImgEnc.pMis = &mis;
	auto fp = Ctx->getPictureFilePath(CTL.strFile.length() > 0 ? "STYLES/TFStyleA" : "STYLES/TFStyle") + fname.c_str();
	std::string s = fp.c_strA();
	styleImgEnc.startImage(mis.formatDynamic, s, false, false);


#if 1//USE_STYLEWATERMARK
	ITexture* texCvr = drv->getTexture("res/tfStyleCover.png");
#endif
	ITexture* texImg = drv->addTexture("texImg", img);

	img->drop();

	ITexture* rt = drv->addRenderTargetTexture(core::dimension2du(256, 256));
	drv->setRenderTarget(rt, true, true, 0x80000000);
	drv->draw2DImageAutoFit(texImg);
#if 1//USE_STYLEWATERMARK
	drv->draw2DImage(texCvr, 0, 0, true);
#endif
	drv->flush();

	ImageData imd = { 0 };
	imd.w = rt->getSize().Width;
	imd.h = rt->getSize().Height;
	{
		void* data = rt->lock(ETLM_READ_ONLY);
		int w = rt->getSize().Width;
		int h = rt->getSize().Height;
		int rowPitch = rt->getPitch();
		imd.pb = new uint8_t[rowPitch * h];
#if IS_SYSTEM_BGRA
		uu::swapRBinRGBA(data, imd.pb, rowPitch * h);
#else
		memcpy(imd.pb, data, rowPitch * h);
#endif
		rt->unlock();
		drv->saveTexture(rt, "data/texImgStyle.png");
	}

	styleImgEnc.pDrvLock = &drv->dsd.driverLock;;
	styleImgEnc.addImgData(imd);
	styleImgEnc.finishEnc(eqvDatString, CTL.strFile);

	drv->freeTexture(rt);
	drv->freeTexture(texImg);

}