﻿  ImCurveEdit.cpp
  ImGradient.cpp
  ImGuizmo.cpp
  ImSequencer.cpp
  imgui_impl_win32.cpp
  imgui.cpp
  imgui_draw.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  imgui_stdlib.cpp
  ISceneNodeAnimator.cpp
  C3DSMeshFileLoader.cpp
  CAnimatedMeshHalfLife.cpp
  CAnimatedMeshMD2.cpp
  CAnimatedMeshMD3.cpp
  CAnimatedMeshSceneNode.cpp
  CAttributes.cpp
  CB3DMeshFileLoader.cpp
  CBillboardSceneNode.cpp
  CBoneSceneNode.cpp
  CBSPMeshFileLoader.cpp
  CCameraSceneNode.cpp
  CColladaFileLoader.cpp
  CColladaMeshWriter.cpp
  CCSMLoader.cpp
  CCubeSceneNode.cpp
  CD3D9HardwareBuffer.cpp
  CDefaultGUIElementFactory.cpp
  CDefaultSceneNodeAnimatorFactory.cpp
  CDefaultSceneNodeFactory.cpp
  CDMFLoader.cpp
  CDummyTransformationSceneNode.cpp
  CEmptySceneNode.cpp
  CFileList.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file '../../include/ISceneNodeAnimator.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CAnimatedMeshMD2.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CFileList.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CDefaultSceneNodeFactory.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CFileSystem.cpp
  CGeometryCreator.cpp
  CGUIButton.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CDefaultSceneNodeAnimatorFactory.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CGUICheckbox.cpp
  CGUIColorSelectDialog.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CFileSystem.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\external\ImGuizmo\ImSequencer.cpp(221,38): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
D:\AProj\UaIrrlicht\external\ImGuizmo\ImSequencer.cpp(232,93): warning C4244: '-=': conversion from 'float' to 'int', possible loss of data
D:\AProj\UaIrrlicht\external\ImGuizmo\ImSequencer.cpp(236,93): warning C4244: '+=': conversion from 'float' to 'int', possible loss of data
  CGUIComboBox.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CGeometryCreator.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CGUIContextMenu.cpp
  CGUIEditBox.cpp
  CGUIEnvironment.cpp
  CGUIFileOpenDialog.cpp
  CGUIFont.cpp
  CGUIImage.cpp
  CGUIImageList.cpp
  CGUIInOutFader.cpp
  CGUIListBox.cpp
  CGUIMenu.cpp
  CGUIMeshViewer.cpp
  CGUIMessageBox.cpp
  CGUIModalScreen.cpp
  CGUIScrollBar.cpp
  CGUISkin.cpp
  CGUISpinBox.cpp
  CGUISpriteBank.cpp
  CGUIStaticText.cpp
  CGUITabControl.cpp
  CGUITable.cpp
  CGUIToolBar.cpp
  CGUITreeView.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CGUIImageList.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CGUIWindow.cpp
  CImageLoaderSTB.cpp
  CImageLoaderWIC.cpp
  CImageWriterSTB.cpp
  CIrrDeviceWin32.cpp
  CIrrMeshFileLoader.cpp
  CIrrMeshWriter.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CImageLoaderSTB.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CGUITreeView.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CLightSceneNode.cpp
  CLimitReadFile.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CImageLoaderWIC.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CIrrDeviceWin32.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CImageWriterSTB.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CLMTSMeshFileLoader.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CLimitReadFile.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CLWOMeshFileLoader.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CLWOMeshFileLoader.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CMD2MeshFileLoader.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CMD2MeshFileLoader.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CMD3MeshFileLoader.cpp
  CMemoryFile.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CMemoryFile.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CDummyTransformationSceneNode.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../../include/ISceneNodeAnimator.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CGeometryCreator.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CBillboardSceneNode.cpp')
  
D:\AProj\UaIrrlicht\include\ISceneNodeAnimator.cpp(44,21): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CCubeSceneNode.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CAnimatedMeshMD2.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CEmptySceneNode.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CDefaultSceneNodeFactory.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CGUITreeView.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CCameraSceneNode.cpp')
  
  CMeshCache.cpp
  CMeshManipulator.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CAnimatedMeshSceneNode.cpp')
  
  CMeshSceneNode.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CMeshCache.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CMetaTriangleSelector.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CMeshManipulator.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CLWOMeshFileLoader.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CDefaultSceneNodeAnimatorFactory.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CImageLoaderSTB.cpp(61,6): warning C4101: 'headerID': unreferenced local variable
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CGUIImageList.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CIrrDeviceWin32.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CMetaTriangleSelector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\source\Irrlicht\CImageWriterSTB.cpp(207,33): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  CMountPointReader.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CFileSystem.cpp(988,21): warning C4018: '<': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\CFileSystem.cpp(1000,17): warning C4018: '<': signed/unsigned mismatch
  CMS3DMeshFileLoader.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CLightSceneNode.cpp')
  
  CMY3DMeshFileLoader.cpp
  CNPKReader.cpp
  COBJMeshFileLoader.cpp
  COBJMeshWriter.cpp
  COCTLoader.cpp
  COctreeSceneNode.cpp
  COctreeTriangleSelector.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(81,10): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(337,51): warning C4244: 'argument': conversion from 'const T' to 'irr::f32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(337,51): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(337,51): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(337,51): warning C4244:             T=irr::u32
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(337,51): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(336,124): warning C4244: 'argument': conversion from 'const T' to 'irr::f32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(336,124): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(336,124): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(336,124): warning C4244:             T=irr::u32
D:\AProj\UaIrrlicht\source\Irrlicht\CCameraSceneNode.cpp(336,124): warning C4244:         ]
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CMD2MeshFileLoader.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'COBJMeshFileLoader.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'COctreeTriangleSelector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'COBJMeshWriter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  COgreMeshFileLoader.cpp
  COpenGLCgMaterialRenderer.cpp
  COpenGLDriver.cpp
  COpenGLExtensionHandler.cpp
  COpenGLNormalMapRenderer.cpp
  COpenGLParallaxMapRenderer.cpp
  COpenGLShaderMaterialRenderer.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CIrrDeviceWin32.cpp(837,86): warning C4244: 'argument': conversion from 'irr::f32' to 'irr::s32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CIrrDeviceWin32.cpp(837,66): warning C4244: 'argument': conversion from 'irr::f32' to 'irr::s32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CIrrDeviceWin32.cpp(906,37): warning C4244: 'argument': conversion from 'WPARAM' to 'UINT', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CIrrDeviceWin32.cpp(1881,32): warning C4244: 'argument': conversion from 'LONG_PTR' to 'DWORD', possible loss of data
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'COpenGLDriver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  COpenGLSLMaterialRenderer.cpp
  COpenGLTexture.cpp
  CPakReader.cpp
  CParticleAnimatedMeshSceneNodeEmitter.cpp
  CParticleAttractionAffector.cpp
  CParticleBoxEmitter.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleAnimatedMeshSceneNodeEmitter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CParticleCylinderEmitter.cpp
  CParticleFadeOutAffector.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleAttractionAffector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CParticleGravityAffector.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleBoxEmitter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleFadeOutAffector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleCylinderEmitter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleGravityAffector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CParticleMeshEmitter.cpp
  CParticlePointEmitter.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleMeshEmitter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticlePointEmitter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CParticleRingEmitter.cpp
  CParticleRotationAffector.cpp
  CParticleScaleAffector.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleRingEmitter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CParticleSphereEmitter.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleScaleAffector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CMeshManipulator.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleRotationAffector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CParticleSystemSceneNode.cpp
  CPLYMeshFileLoader.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CParticleSphereEmitter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CPLYMeshWriter.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CMetaTriangleSelector.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CPLYMeshFileLoader.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CMeshSceneNode.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CPLYMeshWriter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CMeshCache.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'COBJMeshFileLoader.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'COctreeTriangleSelector.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'COctreeSceneNode.cpp')
  
  CQ3LevelMesh.cpp
  CQuake3ShaderSceneNode.cpp
  CReadFile.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'COBJMeshWriter.cpp')
  
  CSceneCollisionManager.cpp
  CSceneLoaderIrr.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CReadFile.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleAttractionAffector.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CSceneLoaderIrr.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'COpenGLDriver.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CSceneCollisionManager.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleAnimatedMeshSceneNodeEmitter.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleBoxEmitter.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CParticleBoxEmitter.cpp(33,13): warning C4244: '=': conversion from 'float' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleFadeOutAffector.cpp')
  
  CSceneManager.cpp
  CSceneNodeAnimatorCameraFPS.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleMeshEmitter.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleSphereEmitter.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleGravityAffector.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CSceneManager.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\source\Irrlicht\CParticleSphereEmitter.cpp(35,13): warning C4244: '=': conversion from 'float' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleRingEmitter.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleScaleAffector.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CSceneNodeAnimatorCameraFPS.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CSceneNodeAnimatorCameraTouchControl.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CPLYMeshFileLoader.cpp')
  
  CSceneNodeAnimatorCollisionResponse.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleCylinderEmitter.cpp')
  
  CSceneNodeAnimatorDelete.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CPLYMeshWriter.cpp')
  
  CSceneNodeAnimatorFlyCircle.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticlePointEmitter.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CPLYMeshWriter.cpp(131,31): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CPLYMeshWriter.cpp(166,31): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  CSceneNodeAnimatorFlyStraight.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CSceneNodeAnimatorCameraTouchControl.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\source\Irrlicht\CParticlePointEmitter.cpp(33,13): warning C4244: '=': conversion from 'float' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleSystemSceneNode.cpp')
  
  CSceneNodeAnimatorFollowSpline.cpp
  CSceneNodeAnimatorFollowSplineRotation.cpp
  CSceneNodeAnimatorRotation.cpp
  CSceneNodeAnimatorTexture.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CParticleRotationAffector.cpp')
  
  CShadowVolumeSceneNode.cpp
  CSkinnedMesh.cpp
  CSkyBoxSceneNode.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneCollisionManager.cpp')
  
  CSkyDomeSceneNode.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CSceneNodeAnimatorTexture.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CSMFMeshFileLoader.cpp
  CSphereSceneNode.cpp
  CSTLMeshFileLoader.cpp
  CSTLMeshWriter.cpp
  CTarReader.cpp
  CTerrainSceneNode.cpp
  CTerrainTriangleSelector.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CParticleSystemSceneNode.cpp(428,63): warning C4018: '<': signed/unsigned mismatch
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CSTLMeshFileLoader.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CTextSceneNode.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CTerrainTriangleSelector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CTriangleBBSelector.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneLoaderIrr.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CTriangleBBSelector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CTriangleSelector.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CTriangleSelector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneManager.cpp')
  
  CVideoModeList.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CVideoModeList.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorCameraTouchControl.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorFlyStraight.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorCollisionResponse.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorCameraFPS.cpp')
  
  CColorConverter.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorFlyCircle.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CShadowVolumeSceneNode.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSphereSceneNode.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CColorConverter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSkyBoxSceneNode.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorDelete.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorFollowSplineRotation.cpp')
  
  CFPSCounter.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CTextSceneNode.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorRotation.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CTerrainSceneNode.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CTriangleBBSelector.cpp')
  
  CImage.cpp
  CNullDriver.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CTriangleSelector.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CFPSCounter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(164,12): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(164,34): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(164,56): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(164,78): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(170,12): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(170,34): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(170,56): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(170,78): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(171,21): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(172,21): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(173,21): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(240,11): warning C4244: 'return': conversion from 'irr::f64' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(267,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(275,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,52): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,52): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,52): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,52): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,52): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,95): warning C4244: 'argument': conversion from 'int' to 'const T', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,95): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,95): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,95): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(446,95): warning C4244:         ]
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorFollowSpline.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(454,34): warning C4244: '=': conversion from 'T' to 'LONG', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(454,34): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(454,34): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(454,34): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(454,34): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(455,34): warning C4244: '=': conversion from 'T' to 'LONG', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(455,34): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(455,34): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(455,34): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(455,34): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(538,32): warning C4244: '=': conversion from 'T' to 'LONG', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(538,32): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(538,32): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(538,32): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(538,32): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(539,32): warning C4244: '=': conversion from 'T' to 'LONG', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(539,32): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(539,32): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(539,32): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(539,32): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(585,36): warning C4244: '+=': conversion from 'double' to 'irr::f32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(589,36): warning C4244: '-=': conversion from 'double' to 'irr::f32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(805,12): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(808,26): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CSceneNodeAnimatorCameraTouchControl.cpp(811,26): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CImage.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CImageWriterBMP.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CTerrainTriangleSelector.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CNullDriver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSkyDomeSceneNode.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSceneNodeAnimatorTexture.cpp')
  
  CImageWriterJPG.cpp
  CImageWriterPCX.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CImageWriterBMP.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CImageWriterPNG.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CSTLMeshFileLoader.cpp')
  
  CImageWriterPPM.cpp
  CImageWriterPSD.cpp
  CImageWriterTGA.cpp
  CImageLoaderBMP.cpp
  CImageLoaderDDS.cpp
  CImageLoaderJPG.cpp
  CImageLoaderPCX.cpp
  CImageLoaderPNG.cpp
  CImageLoaderPPM.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CImageLoaderPCX.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CImageLoaderPSD.cpp
  CImageLoaderRGB.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CImageLoaderRGB.cpp(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  CImageLoaderTGA.cpp
  CImageLoaderWAL.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CImageLoaderBMP.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CD3D9Driver.cpp
  CD3D9HLSLMaterialRenderer.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CImageLoaderTGA.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CD3D9NormalMapRenderer.cpp
  CD3D9ParallaxMapRenderer.cpp
  CD3D9ShaderMaterialRenderer.cpp
  CD3D9Texture.cpp
  CLogger.cpp
  COSOperator.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CImageLoaderWAL.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CVolumeLightSceneNode.cpp
  CWADReader.cpp
  CWaterSurfaceSceneNode.cpp
  CWriteFile.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'COSOperator.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CWriteFile.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CXMeshFileLoader.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CLogger.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  CXMLReader.cpp
  CXMLWriter.cpp
  CZipReader.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CXMLReader.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  Irrlicht.cpp
  irrXML.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CXMLWriter.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  ISceneNode.cpp
  leakHunter.cpp
  os.cpp
  CIrrDeviceStub.cpp
  VulkanBuffer.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CZipReader.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  VulkanDebug.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'os.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'ISceneNode.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'CIrrDeviceStub.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  VulkanTools.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CColorConverter.cpp(168,2): warning C5033: 'register' is no longer a supported storage class
  VulkanUIOverlay.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CImage.cpp')
  
  VkDriver.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CImage.cpp(263,46): warning C4244: 'argument': conversion from 'float' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CImage.cpp(263,29): warning C4244: 'argument': conversion from 'float' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CNullDriver.cpp')
  
  VkDriverBase.cpp
  VkFixedFunctionMaterialRenderer.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CWaterSurfaceSceneNode.cpp')
  
  VkHardwareBuffer.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\os.cpp(121,21): warning C4244: 'return': conversion from 'double' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'ISceneNode.cpp')
  
  VkMaterialRenderer.cpp
  VkMr2D.cpp
  VkMrFF_MMD.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.cpp(909,39): warning C4114: same type qualifier used more than once
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.cpp(1532,27): warning C4244: '=': conversion from 'double' to 'unsigned char', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.cpp(1533,31): warning C4244: '=': conversion from 'double' to 'unsigned char', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.cpp(1534,29): warning C4244: '=': conversion from 'double' to 'unsigned char', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.cpp(1843,23): warning C4018: '<': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.cpp(1850,27): warning C4018: '<': signed/unsigned mismatch
  VkMrFF_SSAO.cpp
  VkFxBase.cpp
  VkFxDescriptorSetManager.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CImageLoaderWAL.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CVolumeLightSceneNode.cpp')
  
  VkFxShaderRunner.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CIrrDeviceStub.cpp')
  
  VkTexture.cpp
  VkVertexDeclaration.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h(307,17): warning C4005: '_malloca': macro redefinition
  (compiling source file 'VulkanRenderer/VkTexture.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h(120,17):
      see previous definition of '_malloca'
  
  vulkanRenderPass.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Irrlicht.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(73,28): warning C4244: '=': conversion from 'const irr::f32' to 'irr::s32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(74,28): warning C4244: '=': conversion from 'const irr::f32' to 'irr::s32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,17): warning C4244: '=': conversion from 'irr::s32' to 'T', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,17): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,17): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,17): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,17): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,34): warning C4244: '=': conversion from 'irr::s32' to 'T', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,34): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,34): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,34): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(88,34): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(202,16): warning C4244: '=': conversion from 'int' to 'T', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(202,16): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(202,16): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(202,16): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(202,16): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(203,16): warning C4244: '=': conversion from 'int' to 'T', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(203,16): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(203,16): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(203,16): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(203,16): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(204,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(209,19): warning C4244: '=': conversion from 'double' to 'irr::f32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(219,16): warning C4244: '=': conversion from 'int' to 'T', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(219,16): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(219,16): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(219,16): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(219,16): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(220,16): warning C4244: '=': conversion from 'int' to 'T', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(220,16): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(220,16): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(220,16): warning C4244:             T=irr::f32
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(220,16): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(221,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\ISceneNode.cpp(226,19): warning C4244: '=': conversion from 'double' to 'irr::f32', possible loss of data
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkDriver.cpp')
  
D:\AProj\UaIrrlicht\include\IVideoDriver.h(422,1): warning C4172: returning address of local variable or temporary 
D:\AProj\UaIrrlicht\source\Irrlicht\CIrrDeviceStub.cpp(232,45): warning C4244: '=': conversion from 'irr::f64' to 'irr::f32', possible loss of data
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkDriverBase.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file 'VulkanRenderer/VkDriver.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(192,77): warning C4267: 'argument': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(1135,51): warning C4838: conversion from 'int' to 'float' requires a narrowing conversion
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(1135,51): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(1236,11): warning C4553: '==': result of expression not used; did you intend '='?
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\base\VulkanUIOverlay.h(110,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/base/VulkanUIOverlay.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkShaderMan\VkFxBase.cpp(214,13): warning C4018: '>=': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkShaderMan\VkFxBase.cpp(227,13): warning C4018: '>=': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkShaderMan\VkFxBase.cpp(240,13): warning C4018: '>=': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(3705,20): warning C4018: '<': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(4115,3): warning C4018: '>': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(4482,84): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(4482,56): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriver.cpp(4694,17): warning C4101: 'dstImageMemory': unreferenced local variable
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkMaterialRenderer.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\base\VulkanUIOverlay.h(110,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkDriverBase.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkFixedFunctionMaterialRenderer.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkDriverBase.cpp(849,12): warning C4018: '<': signed/unsigned mismatch
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkMrFF_MMD.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkMrFF_SSAO.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkMr2D.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkTexture.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file 'VulkanRenderer/VkFixedFunctionMaterialRenderer.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file 'VulkanRenderer/VkMrFF_MMD.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkHardwareBuffer.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244:             T=irr::u32
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244:             T=irr::u32
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(935,44): warning C4267: 'return': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file 'VulkanRenderer/VkMrFF_SSAO.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/vulkanRenderPass.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkHardwareBuffer.cpp(169,26): warning C4101: 'bvc': unreferenced local variable
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkHardwareBuffer.cpp(225,23): warning C4101: 'memReqs': unreferenced local variable
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(355,44): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(377,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(388,22): warning C4244: '+=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(779,44): warning C4267: 'return': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\include\IVideoDriver.h(422,1): warning C4172: returning address of local variable or temporary 
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkVertexDeclaration.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkTexture.cpp(142,25): warning C4267: 'initializing': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkTexture.cpp(170,25): warning C4018: '<': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkTexture.cpp(525,27): warning C4018: '<': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkTexture.cpp(708,86): warning C4244: '=': conversion from 'double' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkTexture.cpp(1162,25): warning C4018: '<': signed/unsigned mismatch
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\vulkanRenderPass.cpp(451,61): warning C4267: 'argument': conversion from 'size_t' to 'uint32_t', possible loss of data
  adler32.c
  compress.c
  crc32.c
  deflate.c
  gzclose.c
  gzlib.c
  gzread.c
  gzwrite.c
  infback.c
  inffast.c
  inflate.c
  inftrees.c
  trees.c
  uncompr.c
  zutil.c
D:\AProj\UaIrrlicht\source\Irrlicht\zlib\deflate.c(1915,13): warning C4244: '=': conversion from 'ush' to 'uchf', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\zlib\deflate.c(2040,13): warning C4244: '=': conversion from 'ush' to 'uchf', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\zlib\deflate.c(2150,13): warning C4244: '=': conversion from 'ush' to 'uchf', possible loss of data
  IrrlichtUP.vcxproj -> D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib
