﻿#pragma once
#include <irrlicht.h>
#include <unordered_set>

#include <vector>
#include <variant>
#include <algorithm>
#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtx/quaternion.hpp>
 
#include <stlUtils.h>
#include <Saba/Model/MMD/MMDNode.h>
#include <Saba/Model/MMD/MMDModel.h>
#include <Saba/Model/MMD/MMDPhysics.h>

/*
If you can not see the full class definition, please refer to the following code:
saba::MMDNode node;
auto rb=node->rb0; //rb is MMDRigidBody*
class MMDRigidBody{ ...
public:
	//main control functions 
    glm::vec3 addLinearVelToPos(const glm::vec3& pos, float mul)  // add linear velocity to rb to move to position
    {
        glm::vec3 v = pos - getPosition();
        addLinearVel(v * mul); // add linear velocity to rb
        return v;
    };       
    void addRotationToMatOnNode(glm::mat3 rttMat, float angleMul = 1000.f, float chgVel=false); // add torque to rb to rotate to matrix rotation

	//other functions
    virtual void addLinearVel(const glm::vec3& vel) = 0;
    virtual void addTorque(const glm::vec3& torque, bool isVelChange = true) = 0;
    void addTorqueLocal(const glm::vec3& torque, bool onMass = true);
    void addVelToRotateOnAxis(const glm::vec3& pos, const glm::vec3& axis, float velMul, float leaveVelMul);
    virtual void setAngularVel(const glm::vec3& vel) = 0;
    virtual void setCollideFilterMask(int setMode, uint32_t ft) = 0;//setmode 1:set 0:restore
    void setRotateTo(glm::mat4 rttMat);
 
   ...
}

*/
namespace irr::scene {
    class IrrSaba;
}
namespace saba {
    using namespace irr::scene; using namespace irr::video;
    using namespace glm;

    class MmdPhysicsController {
    public:
        enum class NodeType {
            Foot,
            Hand
        };

        // 状态数据结构
        struct PhysicsState {
            float bodyHeight;         // 身体高度
            glm::vec3 bodyVel;   // 身体速度
            glm::vec3 bodyPosition;   // 身体位置
            float groundAngle;        // 地面接触角度
            bool isHighFall;          // 是否高速下落
            glm::vec3 footPosition;   // 足部位置
            float scaleFactor;        // 缩放系数
 
        };

        // 配置参数
        struct Config {
            float baseRttMul = 900.0f;        // 基础旋转系数
            float highFallRttMul = 3000.0f;   // 高速下落旋转系数
            
            float maxAdjustSpeed = 10.0f;     // 最大调整速度
            float heightThreshold = 0.3f;     // 高度阈值系数
            float farDistance = 2.5f;        // 最大允许距离
        };

		struct NodeData {
            saba::MMDNode* node;
			PhysicsState lastState,curState;
            float vMulInc=0.f;   
			float highTime = 0.f;
			float farCD = 0.f,farMul=1.f, farPostCD=0.f;
            float lastFarTime = 0.f;
            bool movR = false;
            glm::vec3 tgtPos;
		} ndt[4]; //Left foot, right foot, left hand, right hand

        // 行为策略接口
        class BehaviorStrategy {
        public:
            virtual ~BehaviorStrategy() = default;
            virtual void apply(MmdPhysicsController& controller, float deltaTime) = 0;
        };

        MmdPhysicsController(
            const Config& config,
            irr::scene::IrrSaba* sb,
            saba::MMDNode* bodyNode,
            saba::MMDNode* leftFoot,
            saba::MMDNode* rightFoot,
            saba::MMDNode* leftHand,  // Add left hand node
            saba::MMDNode* rightHand, // Add right hand node
            const glm::vec3& bodyBasePos
        ) : m_config(config), Sb(sb),
            body(bodyNode),
            footL(leftFoot),
            footR(rightFoot),
            handL(leftHand),  // Initialize left hand node
            handR(rightHand), // Initialize right hand node
            baseBodyPos(bodyBasePos)
        {
			ndt[0].node = leftFoot;
			ndt[1].node = rightFoot;
			ndt[2].node = leftHand;
			ndt[3].node = rightHand;
 
        }

        void addBehavior(std::unique_ptr<BehaviorStrategy> behavior) {
            m_behaviors.emplace_back(std::move(behavior));
        }

        void update(int id, float deltaTime ) {
            curId = id;
             
#ifdef _DEBUG
            if (!validateNodes(ndt[curId].node)) return;            
#endif
            // Update state history
            ndt[curId].lastState = ndt[curId].curState;
            m_currentState = detectFootState(ndt[curId].node,deltaTime);
            ndt[curId].curState = m_currentState;

            // Only apply appropriate behaviors based on node type
            for (auto& behavior : m_behaviors) {
                if ((id==0 || id== 1) && dynamic_cast<FootAdjustStrategy*>(behavior.get())) { //FOOT
                    
                        behavior->apply(*this, deltaTime);
                    
                }
                //else if ((id == 2 || id == 3) && dynamic_cast<HandAdjustStrategy*>(behavior.get())) { // HAND
                //   
                //        behavior->apply(*this, deltaTime);
                //    
                //}
            }
        }
 

        //void updateHands(float deltaTime) {
        //    // Update both hands if they exist
        //    if (handL && handR) {
        //        update(2, deltaTime );
        //        update(3, deltaTime );
        //    }
        //}

    private:
        // 状态检测
        PhysicsState detectFootState(saba::MMDNode* footNode,float stepTime) {
            PhysicsState state;
            const float scale = body->absScale.y;
            auto& curNdt = ndt[curId];
            // 基础状态
            state.bodyHeight = body->rb0->pos.y;
            state.bodyVel = body->rb0->vel;
            state.bodyPosition = body->rb0->pos;
            state.scaleFactor = scale;

            // 地面角度计算
            auto parentNode = footNode->GetParent();
            if (parentNode && parentNode->rb0) {
                const glm::mat4 parentMat = parentNode->rb0->getNodeTransform();
                const glm::quat parentRot(parentMat);
                const glm::vec3 downDir = parentRot * glm::vec3(0, -1, 0);
                state.groundAngle = glm::degrees(glm::acos(glm::dot(glm::vec3(0, -1, 0), downDir)));
            }

            // 高速下落判断
            baseFootY =  baseBodyPos.y * scale * 0.1f;
            state.isHighFall = state.groundAngle < 120.0f &&
                body->rb0->vel.y < -scale * 3.0f &&
                body->rb0->pos.y > baseBodyPos.y * 2.0f
				&& body->rb0->pos.y < baseBodyPos.y * 5.0f;
                ;
                
            if (state.isHighFall) if (!curNdt.lastState.isHighFall)
                curNdt.vMulInc = 0.f, curNdt.highTime=0.f;
			else curNdt.vMulInc = glm::min(curNdt.vMulInc+stepTime*2, 1.f), curNdt.highTime+= stepTime;

 
            return state;
        }



        // 节点验证
        bool validateNodes(saba::MMDNode* footNode) const {
            return footNode && footNode->rb0 &&
                body && body->rb0 &&
                footL && footL->rb0 &&
                footR && footR->rb0;
        }

 
        Config m_config;
        PhysicsState m_currentState;
        irr::scene::IrrSaba* Sb;
        saba::MMDNode* body;
        saba::MMDNode* footL;
        saba::MMDNode* footR;
        saba::MMDNode* handL;  // Add left hand node
        saba::MMDNode* handR; // Add right hand node
        glm::vec3 baseBodyPos;
        float baseFootY = 1.f; 
        int curId=0;
        std::vector<std::unique_ptr<BehaviorStrategy>> m_behaviors;

        public:
        // 默认足部调整策略
        class FootAdjustStrategy : public BehaviorStrategy {
        public:       

            void apply(MmdPhysicsController& con, float deltaTime) override;
            int moveStage = 0;
        };

         // 默认手部调整策略
         // 修改后的手部策略类
         class HandAdjustStrategy : public BehaviorStrategy {
         public:
  
             void apply(MmdPhysicsController& controller, float deltaTime) override {
                 auto* handNode = controller.ndt[controller.curId].node;
                 // ...rest of the existing HandAdjustStrategy code...
             }        
         };
    };

 
 
 }

