﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//
#include "mmdPCH.h"

#define PEI_ZHONG		0
#define BOX_FOOT  1
#define OTHER_RB_MASS_CHANGE 1
#define CVT_ALIGN_TO_DYNAMIC_JOINT 1

#define MAX_Thread 8 //orig: 16
#define MAX_EXT_NODE 32

#define SMALL_DYNAMIC_RB_DIMMER 10.f


#include "PMXFile.h"
#include "helpers/glmUtils.h"
#include <Saba/Base/Path.h>
#include <Saba/Base/File.h>
#include <Saba/Base/Log.h>
#include <Saba/Base/Singleton.h>

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtx/matrix_decompose.hpp>
#include <glm/gtx/euler_angles.hpp>
#include <glm/gtx/quaternion.hpp>
#include <glm/gtx/dual_quaternion.hpp>
#include <map>
#include <limits>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <thread>
#include <mutex>
#include <condition_variable>
#include "Helpers/ThreadPool/ThreadPool.h"
#include <glm/gtx/matrix_decompose.hpp>
#include <jsoncpp/json5cpp.h>
#include "../../../AppMainLib/src/UaJsonSetting.h"

#define NO_TR2B 1 // standard PMX does not support 
using namespace glm;
namespace
{
	const float PI = glm::pi<float>();
	const float PIx2 = glm::pi<float>() * 2.f;
}
using namespace saba;

float limitJointAngleLimitToPi(float angle) {
	// Normalize angle to (-π, π] range
	angle = fmod(angle, PIx2);
	if (angle > PI)
		angle -= PIx2;
	else if (angle <= -PI)
		angle += PIx2;
	return angle;
}

// Replace the existing implementation with this enhanced version

void PMXFile::DuplicateCombine()
{
	// If no copies requested, do nothing
	if (fcp.copyCount <= 0) {
		return;
	}

	// Auto-generate rotation angles if they're not provided
	bool autoGeneratedAngles = false;
	if (fcp.copyRttArray.size() == 0) {
		// Generate rotation angles based on the increment values
		fcp.copyRttArray.reserve(fcp.copyCount);
		for (int i = 0; i < fcp.copyCount; i++) {
			glm::vec3 angle = fcp.copyRttInc * static_cast<float>(i + 1);
			//vec3(0, std::rand() % 360, 0);
			fcp.copyRttArray.push_back(angle);
		}
		autoGeneratedAngles = true;
	}



	// Auto-generate scale values if they're not provided
	bool autoGeneratedScales = false;
	if (fcp.copyScaleArray.size() == 0) {
		// Generate scale values based on the increment values
		fcp.copyScaleArray.reserve(fcp.copyCount);
		glm::vec3 scale = glm::vec3(1.0f, 1.0f, 1.0f);
		for (int i = 0; i < fcp.copyCount; i++) {
			// If no scale increment provided, default to scale of 1.0 (no scaling)

			if (fcp.copyScaleInc.x != 0.0f || fcp.copyScaleInc.y != 0.0f || fcp.copyScaleInc.z != 0.0f) {
				scale *= glm::vec3(1.0f, 1.0f, 1.0f) + fcp.copyScaleInc;
			}
			fcp.copyScaleArray.push_back(scale);
		}
		autoGeneratedScales = true;
	}
	// Auto-generate translation offsets if they're not provided
	bool autoGeneratedOffsets = false;
	if (fcp.copyTrsArray.size() == 0 && fcp.copyCount == fcp.copyScaleArray.size()) {
		// Generate translation offsets based on the increment values
		fcp.copyTrsArray.reserve(fcp.copyCount);
		glm::vec3 trs(fcp.copyTrsInc * fcp.copyScaleArray[0]);
		for (int i = 0; i < fcp.copyCount; i++) {
			fcp.copyTrsArray.push_back(trs);
			trs += fcp.copyTrsInc * fcp.copyScaleArray[i];
		}
		autoGeneratedOffsets = true;
	}
	// Determine if we're using predefined values
	bool usePredefinedAngles = fcp.copyRttArray.size() > 0;
	bool usePredefinedOffsets = fcp.copyTrsArray.size() > 0;
	bool usePredefinedScales = fcp.copyScaleArray.size() > 0;

	// Validate predefined arrays if specified
	if (usePredefinedAngles && fcp.copyRttArray.size() < fcp.copyCount) {
		SABA_WARN("Not enough predefined rotation angles. Expected {}, got {}. Using incremental rotation instead.",
			fcp.copyCount, fcp.copyRttArray.size());
		usePredefinedAngles = false;
	}

	if (usePredefinedOffsets && fcp.copyTrsArray.size() < fcp.copyCount) {
		SABA_WARN("Not enough predefined offsets. Expected {}, got {}. Using zero offsets.",
			fcp.copyCount, fcp.copyTrsArray.size());
		usePredefinedOffsets = false;
	}

	if (usePredefinedScales && fcp.copyScaleArray.size() < fcp.copyCount) {
		SABA_WARN("Not enough predefined scales. Expected {}, got {}. Using default scale.",
			fcp.copyCount, fcp.copyScaleArray.size());
		usePredefinedScales = false;
	}

	// Check if all values are zero/default
	if (!usePredefinedAngles && !usePredefinedOffsets &&
		fcp.copyRttInc.x == 0.0f && fcp.copyRttInc.y == 0.0f && fcp.copyRttInc.z == 0.0f &&
		fcp.copyScaleInc.x == 0.0f && fcp.copyScaleInc.y == 0.0f && fcp.copyScaleInc.z == 0.0f) {
		SABA_WARN("No rotation angles, offsets, or scaling defined. Copies will be identical and overlap.");
	}

	// Store original sizes to correctly handle indices
	size_t originalVertexCount = m_vertices.size();
	size_t originalFaceCount = m_faces.size();
	size_t originalBoneCount = m_bones.size();
	size_t originalMaterialCount = m_materials.size();
	size_t originalMorphCount = m_morphs.size();
	size_t originalRigidbodyCount = m_rigidbodies.size();
	size_t originalJointCount = m_joints.size();


	// Calculate final vertex count to determine if we need to upgrade the index size
	size_t finalVertexCount = originalVertexCount * (fcp.copyCount + 1);
	bool indexSizeUpgraded = false;
	uint8_t originalIndexSize = m_header.m_vertexIndexSize;

	if (finalVertexCount > 65535) {
		m_header.m_vertexIndexSize = 4;
	}
	else if (finalVertexCount > 255) {
		m_header.m_vertexIndexSize = 2;
	}    // Determine how many bones we'll copy
	vec3 center(0.0f, 0.0f, 0.0f);
	if (fcp.skipFirstNbones == -1)
	{
		fcp.skipFirstNbones = std::min(1, (int)m_bones.size() - fcp.addRootBone);
		// find last node named "copySkipLast"
		for (size_t i = 0; i < m_bones.size(); ++i) {
			if (m_bones[i].m_name == "copySkipLast") {
				fcp.skipFirstNbones = i;
				center = m_bones[i].m_position;
				break;
			}
		}
	}


	size_t boneStartIndex = fcp.addRootBone + fcp.skipFirstNbones;
	size_t bonesToCopy = originalBoneCount - boneStartIndex;
	size_t skippedBoneCount = fcp.skipFirstNbones;

	// Create a set of rigidbodies to skip (those attached to the first N bones)
	std::vector<bool> skipRigidbody(originalRigidbodyCount, false);
	size_t rbsToSkipCount = 0;

	// Identify rigidbodies associated with the first N bones
	if (fcp.skipFirstNbones > 0) {
		for (size_t i = 0; i < originalRigidbodyCount; ++i) {
			if (m_rigidbodies[i].m_boneIndex >= 0 &&
				m_rigidbodies[i].m_boneIndex < fcp.skipFirstNbones) {
				skipRigidbody[i] = true;
				rbsToSkipCount++;
				SABA_INFO("Skipping rigidbody {} attached to bone {}",
					i, m_rigidbodies[i].m_boneIndex);
			}
		}
	}
	// Create mapping from original rb indices to new indices (after skipping)
	std::vector<int32_t> rbIndexMap(originalRigidbodyCount, -1);
	size_t newRbIndex = 0;
	for (size_t i = 0; i < originalRigidbodyCount; ++i) {
		if (!skipRigidbody[i]) {
			rbIndexMap[i] = newRbIndex++;
		}
	}
	size_t rbsToCopy = originalRigidbodyCount - rbsToSkipCount;

	// Track how many joints will be skipped in total across all copies
	size_t totalJointsSkipped = 0;

	// Log the summary of what we're skipping
	if (fcp.skipFirstNbones > 0) {
		SABA_INFO("PMX file duplication - Skipping first {} bones and {} associated rigidbodies",
			fcp.skipFirstNbones, rbsToSkipCount);
	}

	int skipSubMeshs = 0; int skipSubMeshVertices = 0;
	for (size_t i = 0; i < originalMaterialCount; i++)
	{
		PMXMaterial newMaterial = m_materials[i];
		skipSubMeshVertices += m_materials[i].m_numFaceVertices;
		if (m_materials[i].m_englishName == "copySkipLast")
		{
			skipSubMeshs = i + 1; break;
		}
	}


	// Reserve capacity to avoid reallocations
	m_vertices.reserve(originalVertexCount * (fcp.copyCount + 1));
	m_faces.reserve(originalFaceCount * (fcp.copyCount + 1));
	m_bones.reserve(originalBoneCount + bonesToCopy * fcp.copyCount);
	if (!fcp.skipMaterialCopy) {
		m_materials.reserve(originalMaterialCount * (fcp.copyCount + 1));
	}
	m_morphs.reserve(originalMorphCount * (fcp.copyCount + 1));
	m_rigidbodies.reserve(originalRigidbodyCount + rbsToCopy * fcp.copyCount);
	m_joints.reserve(originalJointCount * (fcp.copyCount + 1));

	// Create copies with specified rotation, translation, and scaling
	for (int copyIndex = 0; copyIndex < fcp.copyCount; copyIndex++)
	{
		// Get rotation angles, offsets, and scales for this copy
		glm::vec3 copyRotationAngles;
		glm::vec3 copyOffset(0.0f, 0.0f, 0.0f);
		glm::vec3 copyScale(1.0f, 1.0f, 1.0f);

		// Get rotation angles
		if (usePredefinedAngles) {
			copyRotationAngles = glm::radians(fcp.copyRttArray[copyIndex]);
		}
		else {
			copyRotationAngles = glm::radians(fcp.copyRttInc * static_cast<float>(copyIndex + 1));
		}

		// Get translation offset
		if (usePredefinedOffsets) {
			copyOffset = fcp.copyTrsArray[copyIndex];
		}

		// Get scale
		if (usePredefinedScales) {
			copyScale = fcp.copyScaleArray[copyIndex];
		}

		// Combine rotations (order: Y * X * Z)
		glm::mat3 rotMat = glm::mat3(glm::quat(copyRotationAngles));

		// Create a suffix for this copy with scale information
		std::string suffix = std::string(".") + std::to_string(copyIndex + 1), suffixE = " (Copy " + std::to_string(copyIndex + 1);

		// Add rotation info
		if (usePredefinedAngles) {
			suffixE += ", rot: X:" + std::to_string(glm::degrees(copyRotationAngles.x));
			suffixE += " Y:" + std::to_string(glm::degrees(copyRotationAngles.y));
			suffixE += " Z:" + std::to_string(glm::degrees(copyRotationAngles.z));
		}
		else {
			if (fcp.copyRttInc.x != 0.0f)
				suffixE += " X:" + std::to_string(fcp.copyRttInc.x * (copyIndex + 1));
			if (fcp.copyRttInc.y != 0.0f)
				suffixE += " Y:" + std::to_string(fcp.copyRttInc.y * (copyIndex + 1));
			if (fcp.copyRttInc.z != 0.0f)
				suffixE += " Z:" + std::to_string(fcp.copyRttInc.z * (copyIndex + 1));
		}

		// Add offset info
		if (usePredefinedOffsets && (copyOffset.x != 0.0f || copyOffset.y != 0.0f || copyOffset.z != 0.0f)) {
			suffixE += ", offset: " + std::to_string(copyOffset.x) + "," +
				std::to_string(copyOffset.y) + "," + std::to_string(copyOffset.z);
		}

		// Add scale info
		if ((copyScale.x != 1.0f || copyScale.y != 1.0f || copyScale.z != 1.0f)) {
			suffixE += ", scale: " + std::to_string(copyScale.x) + "," +
				std::to_string(copyScale.y) + "," + std::to_string(copyScale.z);
		}
		// Scale mass based on volume scaling (mass typically scales with volume)
		float volumeScale = copyScale.x * copyScale.y * copyScale.z;
		suffixE += ")";

		// Offset indices for this copy - adjusted for skipped elements
		size_t vertexOffset = originalVertexCount * (copyIndex + 1);
		size_t faceOffset = originalFaceCount * (copyIndex + 1);
		size_t boneOffset = originalBoneCount + bonesToCopy * copyIndex;
		size_t materialOffset = originalMaterialCount * (copyIndex + 1);
		size_t morphOffset = originalMorphCount * (copyIndex + 1);
		size_t rigidbodyOffset = originalRigidbodyCount + rbsToCopy * copyIndex;
		size_t jointOffset = originalJointCount * (copyIndex + 1);

		// Duplicate bones with scaling, rotation, and translation
		for (size_t i = boneStartIndex; i < originalBoneCount; i++)
		{
			PMXBone newBone = m_bones[i];
			// Add copy suffix to bone names
			newBone.m_name += suffix;
			newBone.m_nameU = ToWString(newBone.m_name);
			newBone.m_englishName += suffixE;
			// Translate to origin (subtract center)
			newBone.m_position -= center;
			
			// Apply scaling to position
			newBone.m_position.x *= copyScale.x;
			newBone.m_position.y *= copyScale.y;
			newBone.m_position.z *= copyScale.z;

			// Apply rotation matrix to position
			newBone.m_position = rotMat * newBone.m_position;
			
			// Translate back to center position
			newBone.m_position += center;

			// Apply translation offset
			newBone.m_position += copyOffset;

			// Apply scaling to position offset
			newBone.m_positionOffset.x *= copyScale.x;
			newBone.m_positionOffset.y *= copyScale.y;
			newBone.m_positionOffset.z *= copyScale.z;

			// Apply rotation matrix to position offset
			newBone.m_positionOffset = rotMat * newBone.m_positionOffset;            // Handle bone references...
			// [The rest of the bone reference handling code remains the same]
			if (newBone.m_parentBoneIndex >= 0) {
				if (fcp.skipFirstNbones > 0 && newBone.m_parentBoneIndex < fcp.skipFirstNbones) {
					// Keep pointing to original bone (don't adjust)
				}
				else {
					// Adjust index to point to the copied bones
					int32_t adjustedIndex = newBone.m_parentBoneIndex;
					if (fcp.skipFirstNbones > 0 && adjustedIndex >= fcp.skipFirstNbones) {
						adjustedIndex -= skippedBoneCount; // Account for skipped bones
					}
					newBone.m_parentBoneIndex = adjustedIndex + static_cast<uint32_t>(boneOffset) - fcp.addRootBone;
				}
			}            // Process other bone references similarly...
			if (((uint16_t)newBone.m_boneFlag & (uint16_t)PMXBoneFlags::TargetShowMode) != 0
				&& newBone.m_linkBoneIndex >= 0) {
				if (fcp.skipFirstNbones > 0 && newBone.m_linkBoneIndex < fcp.skipFirstNbones) {
					// Keep pointing to original bone (don't adjust)
				}
				else {
					int32_t adjustedIndex = newBone.m_linkBoneIndex;
					if (fcp.skipFirstNbones > 0 && adjustedIndex >= fcp.skipFirstNbones) {
						adjustedIndex -= skippedBoneCount;
					}
					newBone.m_linkBoneIndex = adjustedIndex + static_cast<int32_t>(boneOffset) - fcp.addRootBone;
				}
			}            // Append bone index handling
			if ((((uint16_t)newBone.m_boneFlag & (uint16_t)PMXBoneFlags::AppendRotate) ||
				((uint16_t)newBone.m_boneFlag & (uint16_t)PMXBoneFlags::AppendTranslate)) &&
				newBone.m_appendBoneIndex >= 0) {
				// [Same handling as above]
				if (fcp.skipFirstNbones > 0 && newBone.m_appendBoneIndex < fcp.skipFirstNbones) {
					// Keep pointing to original bone (don't adjust)
				}
				else {
					int32_t adjustedIndex = newBone.m_appendBoneIndex;
					if (fcp.skipFirstNbones > 0 && adjustedIndex >= fcp.skipFirstNbones) {
						adjustedIndex -= skippedBoneCount;
					}
					newBone.m_appendBoneIndex = adjustedIndex + static_cast<int32_t>(boneOffset) - fcp.addRootBone;
				}
			}

			// Rotate fixed axis if present
			if ((uint16_t)newBone.m_boneFlag & (uint16_t)PMXBoneFlags::FixedAxis) {
				// Don't scale direction vectors, just rotate them
				newBone.m_fixedAxis = rotMat * newBone.m_fixedAxis;
				newBone.m_fixedAxis = glm::normalize(newBone.m_fixedAxis);
			}

			// Rotate local axes if present
			if ((uint16_t)newBone.m_boneFlag & (uint16_t)PMXBoneFlags::LocalAxis) {
				// Don't scale direction vectors, just rotate them
				newBone.m_localXAxis = rotMat * newBone.m_localXAxis;
				newBone.m_localZAxis = rotMat * newBone.m_localZAxis;
				newBone.m_localXAxis = glm::normalize(newBone.m_localXAxis);
				newBone.m_localZAxis = glm::normalize(newBone.m_localZAxis);
			}            // Handle IK links
			if ((uint16_t)newBone.m_boneFlag & (uint16_t)PMXBoneFlags::IK) {
				// IK target bone handling
				if (newBone.m_ikTargetBoneIndex >= 0) {
					if (fcp.skipFirstNbones > 0 && newBone.m_ikTargetBoneIndex < fcp.skipFirstNbones) {
						// Keep pointing to original bone (don't adjust)
					}
					else {
						int32_t adjustedIndex = newBone.m_ikTargetBoneIndex;
						if (fcp.skipFirstNbones > 0 && adjustedIndex >= fcp.skipFirstNbones) {
							adjustedIndex -= skippedBoneCount;
						}
						newBone.m_ikTargetBoneIndex = adjustedIndex + static_cast<int32_t>(boneOffset) - fcp.addRootBone;
					}
				}

				// IK link bone indices
				for (auto& ikLink : newBone.m_ikLinks) {
					if (ikLink.m_ikBoneIndex >= 0) {
						if (fcp.skipFirstNbones > 0 && ikLink.m_ikBoneIndex < fcp.skipFirstNbones) {
							// Keep pointing to original bone (don't adjust)
						}
						else {
							int32_t adjustedIndex = ikLink.m_ikBoneIndex;
							if (fcp.skipFirstNbones > 0 && adjustedIndex >= fcp.skipFirstNbones) {
								adjustedIndex -= skippedBoneCount;
							}
							ikLink.m_ikBoneIndex = adjustedIndex + static_cast<int32_t>(boneOffset) - fcp.addRootBone;
						}
					}
				}
			}



			m_bones.push_back(newBone);
		}

		
		float vOffset = float(copyIndex + 1) / float(fcp.copyCount + 1);
		

		// Duplicate vertices with rotation, translation, and scaling
		for (size_t i = 0; i < originalVertexCount; i++)
		{
			PMXVertex newVertex = m_vertices[i];			// Translate to origin (subtract center)
			newVertex.m_position -= center;
			
			// Apply scaling 
			newVertex.m_position.x *= copyScale.x;
			newVertex.m_position.y *= copyScale.y;
			newVertex.m_position.z *= copyScale.z;

			// Apply rotation matrix 
			newVertex.m_position = rotMat * newVertex.m_position;
			
			// Translate back to center position
			newVertex.m_position += center;

			// Apply translation offset
			newVertex.m_position += copyOffset;

			// Apply rotation matrix to normal (don't scale normals, just normalize after rotation)
			newVertex.m_normal = rotMat * newVertex.m_normal;
			newVertex.m_normal = glm::normalize(newVertex.m_normal);

			if (fcp.copyUvOfs)
				newVertex.m_uv.y = vOffset;


			// For SDEF vertices, also apply scaling, rotation, and translation
			if (newVertex.m_weightType == PMXVertexWeight::SDEF)
			{
				// Apply scaling to SDEF center
				newVertex.m_sdefC.x *= copyScale.x;
				newVertex.m_sdefC.y *= copyScale.y;
				newVertex.m_sdefC.z *= copyScale.z;

				// Apply rotation
				newVertex.m_sdefC = rotMat * newVertex.m_sdefC;

				// Apply translation
				newVertex.m_sdefC += copyOffset;

				// Apply scaling to SDEF radii
				newVertex.m_sdefR0.x *= copyScale.x;
				newVertex.m_sdefR0.y *= copyScale.y;
				newVertex.m_sdefR0.z *= copyScale.z;

				newVertex.m_sdefR1.x *= copyScale.x;
				newVertex.m_sdefR1.y *= copyScale.y;
				newVertex.m_sdefR1.z *= copyScale.z;

				// Apply rotation to SDEF radii
				newVertex.m_sdefR0 = rotMat * newVertex.m_sdefR0;
				newVertex.m_sdefR1 = rotMat * newVertex.m_sdefR1;
			}            // Adjust bone indices
			for (int j = 0; j < 4; j++) {
				if (newVertex.m_boneIndices[j] >= 0) {
					// Special handling for bone indices when skipping bones
					if (fcp.skipFirstNbones > 0) {
						if (newVertex.m_boneIndices[j] < fcp.skipFirstNbones) {
							// Keep pointing to original bone (don't adjust)
						}
						else {
							// For other bones, adjust the index
							int32_t adjustedIndex = newVertex.m_boneIndices[j];
							if (adjustedIndex >= fcp.skipFirstNbones) {
								adjustedIndex -= skippedBoneCount; // Account for skipped bones
							}
							newVertex.m_boneIndices[j] = adjustedIndex + static_cast<int32_t>(boneOffset) - fcp.addRootBone;
						}
					}
					else {
						// Standard behavior when not skipping bones
						newVertex.m_boneIndices[j] += static_cast<int32_t>(boneOffset) - fcp.addRootBone;
					}
				}
				else
				{
					assert(0);
				}
			}

			if (fcp.combineMode && fcp.modVertexWeight)
			{
				// Modify vertex weight based on vertex pos.y
			// Calculate normalized y position (0 to 1) based on bone positions
			// Find the relevant bones
				int32_t lastBoneIdx = -1, curBoneIdx = -1, nextBoneIdx = -1;
				float scale = 1 / fcp.copyTrsInc.y;
				// Determine which bones to use based on vertex weights
				float maxWeight = 0.0f;
				int32_t primaryBoneIdx = newVertex.m_boneIndices[0];


				if (primaryBoneIdx == boneOffset)  // only mod first copied bone
				{
					newVertex.m_weightType = PMXVertexWeight::BDEF2;
					// Find associated bones (last, current, next)
					curBoneIdx = primaryBoneIdx;

					lastBoneIdx = curBoneIdx - bonesToCopy;
					nextBoneIdx = curBoneIdx + bonesToCopy;
					auto& ovtx = m_vertices[i];

					// Special handling for the first copy
					if (ovtx.m_position.y < 0.5) {
						newVertex.m_boneIndices[0] = lastBoneIdx;
						newVertex.m_boneIndices[1] = curBoneIdx;
						newVertex.m_boneWeights[0] = 0.5f - ovtx.m_position.y * scale;
						newVertex.m_boneWeights[1] = 1.f - newVertex.m_boneWeights[0];
					}
					else {
						if (copyIndex == fcp.copyCount - 1)
						{
							newVertex.m_boneIndices[0] = curBoneIdx;
							newVertex.m_boneIndices[1] = curBoneIdx;
							newVertex.m_boneWeights[0] = 1.0f;
							newVertex.m_boneWeights[1] = 0.0f; // Full weight to the last bone
						}
						else {
							newVertex.m_boneIndices[0] = curBoneIdx;
							newVertex.m_boneIndices[1] = nextBoneIdx;
							newVertex.m_boneWeights[0] = 0.5f + 0.5f * (1.0f - (ovtx.m_position.y * scale - 0.5f) * 2.0f);
							newVertex.m_boneWeights[1] = 1.f - newVertex.m_boneWeights[0];
						}
					}
				}
			}

			m_vertices.push_back(newVertex);
		}




		
		// Duplicate materials - skip if requested
		if (!fcp.skipMaterialCopy) {
			for (size_t i = 0; i < originalMaterialCount; i++)
			{
				PMXMaterial newMaterial = m_materials[i];
				newMaterial.m_name += suffix;


				m_materials.push_back(newMaterial);
			}

		}
 
		// Duplicate rigidbodies with scaling, rotation, and translation
		for (size_t i = 0; i < originalRigidbodyCount; i++)
		{
			if (skipRigidbody[i]) {
				continue;
			}
			PMXRigidbody newRigidbody = m_rigidbodies[i];
			newRigidbody.m_name += suffix;            // Adjust bone index with special handling for skipping bones
			if (newRigidbody.m_boneIndex >= 0) {
				if (fcp.skipFirstNbones > 0 && newRigidbody.m_boneIndex < fcp.skipFirstNbones) {
					// Keep pointing to original bone (don't adjust)
				}
				else {
					// Adjust the bone index by accounting for skipped bones
					int32_t adjustedIndex = newRigidbody.m_boneIndex;

					// Subtract the number of skipped bones to get the proper mapping
					if (fcp.skipFirstNbones > 0 && adjustedIndex >= fcp.skipFirstNbones) {
						adjustedIndex -= skippedBoneCount;
					}

					// Add the bone offset and adjust for any additional root bones
					newRigidbody.m_boneIndex = adjustedIndex + static_cast<int32_t>(boneOffset) - fcp.addRootBone;
				}
			}			// Translate to origin (subtract center)
			newRigidbody.m_translate -= center;
			
			// Apply scaling to position
			newRigidbody.m_translate.x *= copyScale.x;
			newRigidbody.m_translate.y *= copyScale.y;
			newRigidbody.m_translate.z *= copyScale.z;

			// Apply rotation matrix to position
			newRigidbody.m_translate = rotMat * newRigidbody.m_translate;
			
			// Translate back to center position
			newRigidbody.m_translate += center;

			// Apply translation offset
			newRigidbody.m_translate += copyOffset;

			// Apply scaling to shape size
			newRigidbody.m_shapeSize.x *= copyScale.x;
			newRigidbody.m_shapeSize.y *= copyScale.y;
			newRigidbody.m_shapeSize.z *= copyScale.z;            // Handle rotation using quaternions for accuracy
			glm::vec3 eulerRad = newRigidbody.m_rotate;
			glm::quat rbOrigQuat = glm::quat(eulerRad);
			glm::quat rotQuat = copyRotationAngles;
			glm::quat rbNewQuat = rotQuat * rbOrigQuat;			
			glm::vec3 newEulerRad = glh::quatToEulerYXZ(rbNewQuat);
			// Instead of unsupported glm::extractEulerAngleYXZ  
			// glm::extractEulerAngleYXZ(glm::mat4(rbNewQuat), newEulerRad.y, newEulerRad.x, newEulerRad.z);
			newRigidbody.m_rotate = newEulerRad;

			// Apply scaling and rotation to mass center if it exists
			if (glm::length(newRigidbody.massCtr) > 0.0001f) {
				// Apply scaling to mass center
				newRigidbody.massCtr.x *= copyScale.x;
				newRigidbody.massCtr.y *= copyScale.y;
				newRigidbody.massCtr.z *= copyScale.z;

				// Apply rotation matrix to mass center
				newRigidbody.massCtr = rotMat * newRigidbody.massCtr;
			}


			if (volumeScale > 0.0001f) {
				newRigidbody.m_mass *= volumeScale;
			}

			// Rotate frame velocities if they exist
			if (newRigidbody.frameChgMask & 0x01) {
				// Apply rotation to linear velocity (no scaling for velocity)
				newRigidbody.frameAddLinearVel = rotMat * newRigidbody.frameAddLinearVel;
			}

			if (newRigidbody.frameChgMask & 0x02) {
				// Apply rotation to torque (no scaling for torque)
				newRigidbody.frameAddTorque = rotMat * newRigidbody.frameAddTorque;
			}

			m_rigidbodies.push_back(newRigidbody);
		}        // Duplicate joints with scaling, rotation, and translation
		for (size_t i = 0; i < originalJointCount; i++)
		{
			// Check if this joint should be skipped because it references rigidbodies that aren't being copied
			int skipJoint = 0;
			bool specialConnectingJoint = false;

			// Check rigidbodyA
			if (m_joints[i].m_rigidbodyAIndex >= 0) {
				int32_t rbAIndex = m_joints[i].m_rigidbodyAIndex;

				// Skip if this rigidbody won't be in this copy
				if (rbAIndex >= 0 && rbAIndex < originalRigidbodyCount && (skipRigidbody[rbAIndex] || m_joints[i].m_rigidbodyAIndex == m_joints[i].m_rigidbodyBIndex)) {
					// Special case for connecting joints in combine modes
					if (fcp.combineMode > 0 && i == 0) {
						specialConnectingJoint = true;
					}
					else {
						skipJoint++;
					}
				}
			}

			// Check rigidbodyB only if we haven't already decided to skip
			if ( m_joints[i].m_rigidbodyBIndex >= 0) {
				int32_t rbBIndex = m_joints[i].m_rigidbodyBIndex;

				// Skip if this rigidbody won't be in this copy
				if (rbBIndex >= 0 && rbBIndex < originalRigidbodyCount && skipRigidbody[rbBIndex]) {
					skipJoint++;
				}
			}
			// Skip this joint if both rigidbodies aren't available in this copy
			if (skipJoint == 2) {
				SABA_INFO("Skipping joint {} because one or both of its rigidbodies were skipped", i);
				totalJointsSkipped++;
				continue;
			}

			PMXJoint newJoint = m_joints[i];
			newJoint.m_name += suffix;

			// Adjust rigidbody indices
			if (newJoint.m_rigidbodyAIndex >= 0) {
				if (specialConnectingJoint) {
					// Special case for the first joint in combine mode
					switch (fcp.combineMode) {
					case 1: // new dup connect to last rb head
						// Get the previous copy's last rigidbody index
						newJoint.m_rigidbodyAIndex = static_cast<int32_t>(rigidbodyOffset - rbsToCopy);
						break;
					case 2: // new dup connect to last rb tail
						newJoint.m_rigidbodyAIndex = static_cast<int32_t>(rigidbodyOffset - 1);
						break;
					default:
						break;
					}
				}
				else {
					// Find the new index for this rigidbody using our mapping
					int32_t adjustedIndex = newJoint.m_rigidbodyAIndex;

					// If this is a rigidbody that wasn't skipped, map its index
					if (adjustedIndex >= 0 && adjustedIndex < originalRigidbodyCount &&
						!skipRigidbody[adjustedIndex]) {
						// Use rbIndexMap to get the new index
						adjustedIndex = rbIndexMap[adjustedIndex];
						newJoint.m_rigidbodyAIndex = adjustedIndex + static_cast<int32_t>(rigidbodyOffset);
					}
				}

				newJoint.springR *= copyScale;
				newJoint.springT *= volumeScale; // acceleration drive, to check for T
			}

			if (newJoint.m_rigidbodyBIndex >= 0) {
				{
					// Find the new index for this rigidbody using our mapping
					int32_t adjustedIndex = newJoint.m_rigidbodyBIndex;

					// If this is a rigidbody that wasn't skipped, map its index
					if (adjustedIndex >= 0 && adjustedIndex < originalRigidbodyCount &&
						!skipRigidbody[adjustedIndex]) {
						// Use rbIndexMap to get the new index
						adjustedIndex = rbIndexMap[adjustedIndex];
						newJoint.m_rigidbodyBIndex = adjustedIndex + static_cast<int32_t>(rigidbodyOffset);
					}
				}
			}			// Translate to origin (subtract center)
			newJoint.translate -= center;
			
			// Apply scaling to position
			newJoint.translate.x *= copyScale.x;
			newJoint.translate.y *= copyScale.y;
			newJoint.translate.z *= copyScale.z;

			// Apply rotation matrix to position
			newJoint.translate = rotMat * newJoint.translate;
			
			// Translate back to center position
			newJoint.translate += center;

			// Apply translation offset
			newJoint.translate += copyOffset;

#if !NO_TR2B
			// Apply scaling to t2B
			newJoint.t2B.x *= copyScale.x;
			newJoint.t2B.y *= copyScale.y;
			newJoint.t2B.z *= copyScale.z;

			// Apply rotation to t2B
			newJoint.t2B = rotMat * newJoint.t2B;
#endif            // Handle rotation using quaternions
			glm::vec3 eulerRad = newJoint.rotate;
			glm::quat rbOrigQuat = glm::quat(eulerRad);
			glm::quat rotQuat = glm::quat(copyRotationAngles);
			glm::quat rbNewQuat = rotQuat * rbOrigQuat;
			glm::vec3 newEulerRad = glm::eulerAngles(rbNewQuat);

			// Normalize and update rotation
			newJoint.rotate = newEulerRad;

#if !NO_TR2B
			// Same for r2B
			eulerRad = glm::radians(newJoint.r2B);
			jointOrigQuat = glm::quat(eulerRad);
			jointNewQuat = rotQuat * jointOrigQuat;
			newEulerRad = glm::eulerAngles(jointNewQuat);

			newJoint.r2B.x = (newEulerRad.x);
			newJoint.r2B.y = (newEulerRad.y);
			newJoint.r2B.z = (newEulerRad.z);
#endif

			// Apply scaling to translation limits
			newJoint.limitMinT.x *= copyScale.x;
			newJoint.limitMinT.y *= copyScale.y;
			newJoint.limitMinT.z *= copyScale.z;

			newJoint.limitMaxT.x *= copyScale.x;
			newJoint.limitMaxT.y *= copyScale.y;
			newJoint.limitMaxT.z *= copyScale.z;

			// Apply rotation to translation limits
		   // newJoint.limitMinT = rotMat * newJoint.limitMinT;
		   // newJoint.limitMaxT = rotMat * newJoint.limitMaxT;

#if 0
			// Handle rotation limits using quaternions
			eulerRad = glm::radians(newJoint.limitMinR);
			jointOrigQuat = glm::quat(eulerRad);
			jointNewQuat = rotQuat * jointOrigQuat;
			newEulerRad = glm::eulerAngles(jointNewQuat);

			newJoint.limitMinR.x = limitJointAngleLimitToPi(newEulerRad.x);
			newJoint.limitMinR.y = limitJointAngleLimitToPi(newEulerRad.y);
			newJoint.limitMinR.z = limitJointAngleLimitToPi(newEulerRad.z);

			eulerRad = glm::radians(newJoint.limitMaxR);
			jointOrigQuat = glm::quat(eulerRad);
			jointNewQuat = rotQuat * jointOrigQuat;
			newEulerRad = glm::eulerAngles(jointNewQuat);

			newJoint.limitMaxR.x = limitJointAngleLimitToPi(newEulerRad.x);
			newJoint.limitMaxR.y = limitJointAngleLimitToPi(newEulerRad.y);
			newJoint.limitMaxR.z = limitJointAngleLimitToPi(newEulerRad.z);
#endif

#if NO_TR2B 
			newJoint.t2B = glm::vec3(0);
			newJoint.r2B = glm::vec3(0);
#endif

			m_joints.push_back(newJoint);
		}
	}
	for (size_t i = boneStartIndex; i < originalBoneCount; i++)
	{
		m_bones[i].m_name += ".0";
		m_bones[i].m_nameU = ToWString(m_bones[i].m_name);
	}
	// Adjust the material face indices if we didn't copy materials
	if (fcp.skipMaterialCopy && fcp.copyCount > 0) {


		// Handle face organization when not duplicating materials

			// When not duplicating materials, we need to duplicate faces material by material
		std::vector<PMXFace> newFaces;
		int skipFaces = skipSubMeshVertices / 3;
		newFaces.reserve(originalFaceCount +  (originalFaceCount- skipFaces) * fcp.copyCount );

		// Track face offset within the faces array for each material
		size_t faceOffset = 0;

		// For each material
		for (size_t materialIdx = 0; materialIdx < originalMaterialCount; materialIdx++) {
			// Get face count for this material (convert from vertex count to face count)
			size_t materialFaceCount = m_materials[materialIdx].m_numFaceVertices / 3;

			int cc = fcp.copyCount;
			if (materialIdx < skipSubMeshs) {
				cc = 0;
			}
			 
			// Verify that faceOffset + materialFaceCount doesn't exceed the array bounds
			if (faceOffset + materialFaceCount > originalFaceCount) {
				SABA_WARN("Material {} face count is inconsistent. Expected max {}, got offset {} + count {}.",
					materialIdx, originalFaceCount, faceOffset, materialFaceCount);
				// Adjust to prevent overflow
				materialFaceCount = (faceOffset < originalFaceCount) ?
					(originalFaceCount - faceOffset) : 0;
			}

			// Duplicate faces for this material for each copy
			int vc = 0;
			for (int copyIdx = 0; copyIdx <= cc; copyIdx++) {
				// Duplicate all faces for this material
				for (size_t faceIdx = 0; faceIdx < materialFaceCount; faceIdx++) {
					size_t originalFaceIdx = faceOffset + faceIdx;

					// Safety check
					if (originalFaceIdx >= originalFaceCount) {
						SABA_WARN("Face index out of range: {} >= {}", originalFaceIdx, originalFaceCount);
						continue;
					}

					PMXFace newFace = m_faces[originalFaceIdx];

					// Adjust vertex indices to point to duplicated vertices
					size_t copyVertexOffset = (originalVertexCount) * (copyIdx);
					newFace.vtxId[0] = m_faces[originalFaceIdx].vtxId[0] + static_cast<uint32_t>(copyVertexOffset);
					newFace.vtxId[1] = m_faces[originalFaceIdx].vtxId[1] + static_cast<uint32_t>(copyVertexOffset);
					newFace.vtxId[2] = m_faces[originalFaceIdx].vtxId[2] + static_cast<uint32_t>(copyVertexOffset);

					newFaces.push_back(newFace);
					vc += 3;
				}
			}

			// Update face offset for next material
			faceOffset += materialFaceCount;

			// Update material face count (includes original + copies)
			if (cc>0)
			m_materials[materialIdx].m_numFaceVertices *= (fcp.copyCount + 1);
			assert(vc == m_materials[materialIdx].m_numFaceVertices);
		}

		// Replace the old faces with the properly organized new faces
		m_faces = std::move(newFaces);
	}


	// Clean up auto-generated arrays if we created them
	if (autoGeneratedAngles) {
		fcp.copyRttArray.clear();
	}

	if (autoGeneratedOffsets) {
		fcp.copyTrsArray.clear();
	}

	if (autoGeneratedScales) {
		fcp.copyScaleArray.clear();
	}



	std::string infoMessage = "Model duplicated with " + std::to_string(fcp.copyCount) + " copies";

	if (usePredefinedAngles) {
		infoMessage += " using predefined rotation angles";
	}
	else {
		infoMessage += " with rotation increments X:" + std::to_string(fcp.copyRttInc.x) +
			" Y:" + std::to_string(fcp.copyRttInc.y) +
			" Z:" + std::to_string(fcp.copyRttInc.z);
	}

	if (usePredefinedOffsets) {
		infoMessage += " and predefined offsets";
	}

	if (usePredefinedScales) {
		infoMessage += " and predefined scales";
	}
	else if (fcp.copyScaleInc.x != 0.0f || fcp.copyScaleInc.y != 0.0f || fcp.copyScaleInc.z != 0.0f) {
		infoMessage += " with scale increments X:" + std::to_string(fcp.copyScaleInc.x) +
			" Y:" + std::to_string(fcp.copyScaleInc.y) +
			" Z:" + std::to_string(fcp.copyScaleInc.z);
	}    if (fcp.skipFirstNbones > 0) {
		infoMessage += ". First " + std::to_string(fcp.skipFirstNbones) + " bones and " +
			std::to_string(rbsToSkipCount) + " associated rigidbodies skipped";

		if (totalJointsSkipped > 0) {
			infoMessage += ", " + std::to_string(totalJointsSkipped) + " joints skipped";
		}
	}

	if (fcp.skipMaterialCopy) {
		infoMessage += ". Materials not duplicated";
	}

	// m_materials[0].m_diffuse = glm::vec4(.83f, 0.64f, .12f, 1.0f);
	// m_materials[0].m_diffuse = glm::vec4(0.5f, 1, 0, 1.0f);
	 //m_materials[0].m_ambient = glm::vec4(.83f, 0.64f, .12f, 1.0f) * 0.3f;;
	// m_joints[0].breakMassMul = 0.f;    SABA_INFO(infoMessage.c_str());

	 // Report the final counts of elements in the model
	SABA_INFO("Final model contains {} vertices, {} faces, {} bones, {} rigidbodies, {} joints",
		m_vertices.size(), m_faces.size(), m_bones.size(), m_rigidbodies.size(), m_joints.size());

}
 