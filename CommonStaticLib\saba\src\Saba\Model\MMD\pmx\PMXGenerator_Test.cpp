//
// PMXGenerator_Test.cpp - Test program for PMXFile_Generator
//

#include <Saba/Model/MMD/PMXFile_Generator.h>
#include <iostream>
#include <vector>
#include <string>
#include <glm/glm.hpp>
#include <glm/gtc/constants.hpp>

using namespace saba;
using namespace glm;

// Create a simple box model
void createBoxModel()
{
    std::cout << "Creating box model..." << std::endl;
    
    PMXFile_Generator generator;
    
    // Define a box
    Gpm_BoxShape box;
    box.size = vec3(2.0f, 1.0f, 3.0f);
    box.position = vec3(0.0f, 1.0f, 0.0f);
    box.rotation = vec3(0.0f, 0.0f, 0.0f);
    
    // Create the box model
    generator.createBox(box);
    
    // Save to file
    if (generator.saveToFile("box_model.pmx")) {
        std::cout << "Box model saved to box_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save box model" << std::endl;
    }
}

// Create a chain model
void createChainModel()
{
    std::cout << "Creating chain model..." << std::endl;
    
    PMXFile_Generator generator;
    
    // Define chain links
    std::vector<Gpm_ChainLink> links;
    
    // Create 5 links in a vertical chain
    for (int i = 0; i < 1000; i++) {
        Gpm_ChainLink link;
        link.radius = 1.f;
        link.length = 2.f;// 1.275f;
        link.position = gPmxGenPm.position+ vec3(0.0f,    i * link.length*0.8f, 0.0f);
        link.rotation = vec3(0, i%2?0:glm::half_pi<float>(), 0);
        links.push_back(link);
    }
    
    // Create the chain model
    generator.createChain(links,true);
    
    // Save to file
    if (generator.saveToFile("chain_model.pmx")) {
        std::cout << "Chain model saved to chain_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save chain model" << std::endl;
    }
}

// Create a chain model
void createRopeModel(int connect)
{
    std::cout << "Creating chain model..." << std::endl;
    
    PMXFile_Generator generator;

    // Define rope segments
    std::vector<Gpm_ChainLink> links;

    // Create 5 segments in a vertical rope
    for (int i = 0; i < gPmxGenPm.chainCount; i++) {
        Gpm_ChainLink link;
        link.radius = 0.3f;
        link.length = 1.5f;
        link.position = gPmxGenPm.position +    vec3(0.0f,   i * link.length, 0.0f);
        link.rotation = vec3(0.0f, 0.0f, 0.0f);
        links.push_back(link);
    }

    // Create the rope model
    if (abs(gPmxGenPm.chainScale-1)>0.000001f)
    generator.createRope(links,0,3, 1);
    else generator.createRope(links, 0, 36, 0);

    // Save to file
    generator.saveToFile(connect? "ropeCnt_model.pmx" : "rope_model.pmx");
}// Create a cube model with 6 box faces

void createCubeModel()
{
    std::cout << "Creating cube model..." << std::endl;
    
    PMXFile_Generator generator;
    
    // Define boxes for each face of the cube
    std::vector<Gpm_BoxShape> boxes;
    float size = 5.0f;
    float thickness = 0.2f;
    
    // Front face
    Gpm_BoxShape front;
    front.size = vec3(size, size, thickness);
    front.position = vec3(0.0f, 0.0f, size/2);
    boxes.push_back(front);
    
    // Back face
    Gpm_BoxShape back;
    back.size = vec3(size, size, thickness);
    back.position = vec3(0.0f, 0.0f, -size/2);
    boxes.push_back(back);
    
    // Left face
    Gpm_BoxShape left;
    left.size = vec3(thickness, size, size);
    left.position = vec3(-size/2, 0.0f, 0.0f);
    boxes.push_back(left);
    
    // Right face
    Gpm_BoxShape right;
    right.size = vec3(thickness, size, size);
    right.position = vec3(size/2, 0.0f, 0.0f);
    boxes.push_back(right);
    
    // Top face
    Gpm_BoxShape top;
    top.size = vec3(size, thickness, size);
    top.position = vec3(0.0f, size/2, 0.0f);
    boxes.push_back(top);
    
    // Bottom face
    Gpm_BoxShape bottom;
    bottom.size = vec3(size, thickness, size);
    bottom.position = vec3(0.0f, -size/2, 0.0f);
    boxes.push_back(bottom);
    
    // Create the multi-box model
    generator.createMultiBox(boxes);
    
    // Save to file
    if (generator.saveToFile("cube_model.pmx")) {
        std::cout << "Cube model saved to cube_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save cube model" << std::endl;
    }
}

// Create a brick wall model
void createBrickWall()
{
	std::cout << "Creating brick wall model..." << std::endl;

	PMXFile_Generator generator;

	// Define parameters for the brick wall
	int rows = 10;
	int columns = 10;
	float brickWidth = 1.0f;
	float brickHeight = 0.5f;
	float brickDepth = 0.2f;
	float mortar = 0.05f;

	// Create the brick wall model
	generator.createBrickWall(rows, columns, brickWidth, brickHeight, brickDepth, mortar);

	// Save to file
	if (generator.saveToFile("brick_wall_model.pmx")) {
		std::cout << "Brick wall model saved to brick_wall_model.pmx" << std::endl;
	}
	else {
		std::cout << "Failed to save brick wall model" << std::endl;
	}
}

void createBalanceModel()
{
    std::cout << "Creating balance model..." << std::endl;
    
    PMXFile_Generator generator;
    generator.initialize("BalanceModel", "A balance scale model with physics");
    
    // Parameters for the balance
    const float baseWidth = 5.0f;
    const float baseHeight = 1.0f;
    const float baseDepth = 5.0f;
    
    const float poleHeight = 12.0f;
    const float poleRadius = 0.3f;
    
    const float beamLength = 10.0f;
    const float beamHeight = 0.4f;
    const float beamWidth = 0.4f;
    
    const float panRadius = 2.0f;
    const float panDepth = 0.5f;
    const float chainLength = 3.0f;
    
    // Step 1: Create the base (static)
    {
        Gpm_BoxShape base;
        base.size = vec3(baseWidth, baseHeight, baseDepth);
        base.position = vec3(0.0f, baseHeight/2.0f, 0.0f);
        base.rotation = vec3(0.0f, 0.0f, 0.0f);
        
        // Create a bone for the base
        int32_t baseBoneIndex = generator.addBone("Base", base.position, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the base
        RigidBodyDef baseRbDef;
        baseRbDef.name = "BaseBody";
        baseRbDef.engName = "BaseBody";
        baseRbDef.shape = PMXRigidbody::Shape::Box;
        baseRbDef.size = base.size;
        baseRbDef.position = base.position;
        baseRbDef.boneIndex = baseBoneIndex;
        baseRbDef.mass = 0.0f; // Static (immovable)
        baseRbDef.operation = PMXRigidbody::Operation::Static;
        int32_t baseRbIndex = generator.addRigidBody(baseRbDef);
        
        // Create the mesh for the base
        generator.createBoxMesh(base.size, base.position, base.rotation);
    }
    
    // Step 2: Create the vertical pole
    vec3 polePosition = vec3(0.0f, baseHeight + poleHeight/2.0f, 0.0f);
    int32_t poleRbIndex;
    {
        // Create a bone for the pole
        int32_t poleBoneIndex = generator.addBone("Pole", polePosition, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the pole
        RigidBodyDef poleRbDef;
        poleRbDef.name = "PoleBody";
        poleRbDef.engName = "PoleBody";
        poleRbDef.shape = PMXRigidbody::Shape::Capsule;
        poleRbDef.size = vec3(poleRadius, poleHeight/2.0f, 0.0f);
        poleRbDef.position = polePosition;
        poleRbDef.boneIndex = poleBoneIndex;
        poleRbDef.mass = 0.0f; // Static
        poleRbDef.operation = PMXRigidbody::Operation::Static;
        poleRbIndex = generator.addRigidBody(poleRbDef);
        
        // Create the mesh for the pole
        // We'll use a chain link as a cylinder for the pole
        Gpm_ChainLink pole;
        pole.radius = poleRadius;
        pole.length = poleHeight;
        pole.position = polePosition;
        pole.rotation = vec3(0.0f, 0.0f, 0.0f);
        
        std::vector<Gpm_ChainLink> poleLinks = {pole};
        for (auto& link : poleLinks) {
            generator.createChainLinkMesh(link.radius, link.length, link.position, link.rotation, poleBoneIndex);
        }
    }
    
    // Step 3: Create the balance beam (dynamic, pivots at the top of the pole)
    vec3 beamPosition = vec3(0.0f, baseHeight + poleHeight, 0.0f);
    int32_t beamBoneIndex, beamRbIndex;
    {
        Gpm_BoxShape beam;
        beam.size = vec3(beamLength, beamHeight, beamWidth);
        beam.position = beamPosition;
        beam.rotation = vec3(0.0f, 0.0f, 0.0f);
        
        // Create a bone for the beam
        beamBoneIndex = generator.addBone("Beam", beamPosition, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the beam
        RigidBodyDef beamRbDef;
        beamRbDef.name = "BeamBody";
        beamRbDef.engName = "BeamBody";
        beamRbDef.shape = PMXRigidbody::Shape::Box;
        beamRbDef.size = beam.size;
        beamRbDef.position = beam.position;
        beamRbDef.boneIndex = beamBoneIndex;
        beamRbDef.mass = 2.0f; // Light but has mass
        beamRbDef.linearDamping = 0.1f; // Low damping to allow swinging
        beamRbDef.angularDamping = 0.1f;
        beamRbDef.operation = PMXRigidbody::Operation::Dynamic;
        beamRbIndex = generator.addRigidBody(beamRbDef);
        
        // Create the mesh for the beam
        generator.createBoxMesh(beam.size, beam.position, beam.rotation);
    }
    
    // Create a hinge joint at the top of the pole to allow the beam to pivot
    {
        JointDef beamJointDef;
        beamJointDef.name = "BeamPivot";
        beamJointDef.type = PMXJoint::JointType::Hinge;
        beamJointDef.rigidBodyAIndex = beamRbIndex; // Beam
        beamJointDef.rigidBodyBIndex = poleRbIndex; // Pole rigid body
        beamJointDef.position = beamPosition;
        beamJointDef.rotation = vec3(0.0f, 0.0f, glm::half_pi<float>()); // Rotate to make pivot around X axis
        
        // Set rotation limits and spring properties
        beamJointDef.angularLimitMin = vec3(-glm::quarter_pi<float>(), 0.0f, 0.0f); // Limit tilt
        beamJointDef.angularLimitMax = vec3(glm::quarter_pi<float>(), 0.0f, 0.0f);
        beamJointDef.linearLimitMin = vec3(0.0f);
        beamJointDef.linearLimitMax = vec3(0.0f);
        beamJointDef.springAngular = vec3(5.0f, 0.0f, 0.0f); // Small restoring force
        
        generator.addJoint(beamJointDef);
    }
    
    // Step 4: Create the pans on either side
    // Left pan
    vec3 leftPanPosition = vec3(-beamLength/2.0f, baseHeight + poleHeight - chainLength - panDepth/2.0f, 0.0f);
    int32_t leftPanBoneIndex, leftPanRbIndex;
    {
        // Create a bone for the left pan
        leftPanBoneIndex = generator.addBone("LeftPan", leftPanPosition, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the left pan
        RigidBodyDef leftPanRbDef;
        leftPanRbDef.name = "LeftPanBody";
        leftPanRbDef.engName = "LeftPanBody";
        leftPanRbDef.shape = PMXRigidbody::Shape::Box; // Using box instead of cylinder for simplicity
        leftPanRbDef.size = vec3(panRadius, panDepth, panRadius);
        leftPanRbDef.position = leftPanPosition;
        leftPanRbDef.boneIndex = leftPanBoneIndex;
        leftPanRbDef.mass = 1.0f;
        leftPanRbDef.linearDamping = 0.3f;
        leftPanRbDef.angularDamping = 0.3f;
        leftPanRbDef.operation = PMXRigidbody::Operation::Dynamic;
        leftPanRbIndex = generator.addRigidBody(leftPanRbDef);
        
        // Create a box for the left pan
        Gpm_BoxShape leftPan;
        leftPan.size = vec3(panRadius*2.0f, panDepth, panRadius*2.0f);
        leftPan.position = leftPanPosition;
        leftPan.rotation = vec3(0.0f, 0.0f, 0.0f);
        generator.createBoxMesh(leftPan.size, leftPan.position, leftPan.rotation);
    }
    
    // Right pan
    vec3 rightPanPosition = vec3(beamLength/2.0f, baseHeight + poleHeight - chainLength - panDepth/2.0f, 0.0f);
    int32_t rightPanBoneIndex, rightPanRbIndex;
    {
        // Create a bone for the right pan
        rightPanBoneIndex = generator.addBone("RightPan", rightPanPosition, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible | PMXBoneFlags::AllowControl));
        
        // Create rigid body for the right pan
        RigidBodyDef rightPanRbDef;
        rightPanRbDef.name = "RightPanBody";
        rightPanRbDef.engName = "RightPanBody";
        rightPanRbDef.shape = PMXRigidbody::Shape::Box;
        rightPanRbDef.size = vec3(panRadius, panDepth, panRadius);
        rightPanRbDef.position = rightPanPosition;
        rightPanRbDef.boneIndex = rightPanBoneIndex;
        rightPanRbDef.mass = 1.0f;
        rightPanRbDef.linearDamping = 0.3f;
        rightPanRbDef.angularDamping = 0.3f;
        rightPanRbDef.operation = PMXRigidbody::Operation::Dynamic;
        rightPanRbIndex = generator.addRigidBody(rightPanRbDef);
        
        // Create a box for the right pan
        Gpm_BoxShape rightPan;
        rightPan.size = vec3(panRadius*2.0f, panDepth, panRadius*2.0f);
        rightPan.position = rightPanPosition;
        rightPan.rotation = vec3(0.0f, 0.0f, 0.0f);
        generator.createBoxMesh(rightPan.size, rightPan.position, rightPan.rotation);
    }
    
    // Step 5: Create chains that connect the pans to the beam
    // Left chain
    {
        vec3 leftChainTop = vec3(-beamLength/2.0f, baseHeight + poleHeight, 0.0f);
        
        // Create a joint to connect the left pan to the beam
        JointDef leftChainJointDef;
        leftChainJointDef.name = "LeftChainJoint";
        leftChainJointDef.type = PMXJoint::JointType::SpringDOF6;
        leftChainJointDef.rigidBodyAIndex = leftPanRbIndex;
        leftChainJointDef.rigidBodyBIndex = beamRbIndex;
        leftChainJointDef.position = leftPanPosition; // Attach at pan center
        
        // Allow limited movement in all directions
        leftChainJointDef.linearLimitMin = vec3(-0.5f, -0.1f, -0.5f);
        leftChainJointDef.linearLimitMax = vec3(0.5f, 0.1f, 0.5f);
        leftChainJointDef.angularLimitMin = vec3(-glm::quarter_pi<float>());
        leftChainJointDef.angularLimitMax = vec3(glm::quarter_pi<float>());
        
        // Spring forces to simulate chain tension
        leftChainJointDef.springLinear = vec3(50.0f, 100.0f, 50.0f);
        leftChainJointDef.springAngular = vec3(10.0f);
        
        generator.addJoint(leftChainJointDef);
        
        // Visualize the chain with links
        int chainBoneIndex = generator.addBone("LeftChain", leftChainTop, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible));
        
        const int numLinks = 5;
        float linkLength = chainLength / numLinks;
        
        for (int i = 0; i < numLinks; i++) {
            Gpm_ChainLink link;
            link.radius = 0.1f;
            link.length = linkLength;
            float t = (float)(i + 0.5) / numLinks; // Position along chain (0-1)
            link.position = vec3(
                leftChainTop.x, 
                leftChainTop.y - t * chainLength,
                leftChainTop.z
            );
            link.rotation = vec3(glm::half_pi<float>(), 0.0f, 0.0f); // Orient vertically
            
            generator.createChainLinkMesh(link.radius, link.length, link.position, link.rotation, chainBoneIndex);
        }
    }
    
    // Right chain
    {
        vec3 rightChainTop = vec3(beamLength/2.0f, baseHeight + poleHeight, 0.0f);
        
        // Create a joint to connect the right pan to the beam
        JointDef rightChainJointDef;
        rightChainJointDef.name = "RightChainJoint";
        rightChainJointDef.type = PMXJoint::JointType::SpringDOF6;
        rightChainJointDef.rigidBodyAIndex = rightPanRbIndex;
        rightChainJointDef.rigidBodyBIndex = beamRbIndex;
        rightChainJointDef.position = rightPanPosition; // Attach at pan center
        
        // Allow limited movement in all directions
        rightChainJointDef.linearLimitMin = vec3(-0.5f, -0.1f, -0.5f);
        rightChainJointDef.linearLimitMax = vec3(0.5f, 0.1f, 0.5f);
        rightChainJointDef.angularLimitMin = vec3(-glm::quarter_pi<float>());
        rightChainJointDef.angularLimitMax = vec3(glm::quarter_pi<float>());
        
        // Spring forces to simulate chain tension
        rightChainJointDef.springLinear = vec3(50.0f, 100.0f, 50.0f);
        rightChainJointDef.springAngular = vec3(10.0f);
        
        generator.addJoint(rightChainJointDef);
        
        // Visualize the chain with links
        int chainBoneIndex = generator.addBone("RightChain", rightChainTop, -1,
            (uint16_t)(PMXBoneFlags::AllowRotate | PMXBoneFlags::AllowTranslate | 
                      PMXBoneFlags::Visible));
        
        const int numLinks = 5;
        float linkLength = chainLength / numLinks;
        
        for (int i = 0; i < numLinks; i++) {
            Gpm_ChainLink link;
            link.radius = 0.1f;
            link.length = linkLength;
            float t = (float)(i + 0.5) / numLinks; // Position along chain (0-1)
            link.position = vec3(
                rightChainTop.x, 
                rightChainTop.y - t * chainLength,
                rightChainTop.z
            );
            link.rotation = vec3(glm::half_pi<float>(), 0.0f, 0.0f); // Orient vertically
            
            generator.createChainLinkMesh(link.radius, link.length, link.position, link.rotation, chainBoneIndex);
        }
    }
    
    // Create default materials for all the meshes we've created
    generator.createDefaultMaterial(generator.m_currentMaterialFaceCount);
    
    // Save to file
    if (generator.saveToFile("balance_model.pmx")) {
        std::cout << "Balance model saved to balance_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save balance model" << std::endl;
    }
}

// Create a cylinder model
template<typename T=void>
void createCylinderModel()
{
    std::cout << "Creating cylinder model..." << std::endl;
    PMXFile_Generator generator;
    float radius = 1.0f;
    float height = 3.0f;
    glm::vec3 position(0.0f, 2.0f, 0.0f);
    glm::vec3 rotation(0.0f, 0.0f, 0.0f);
   // generator.createCylinder(radius, height, position, rotation);
    if (generator.saveToFile("cylinder_model.pmx")) {
        std::cout << "Cylinder model saved to cylinder_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save cylinder model" << std::endl;
    }
}

// Create a cone model
template<typename T=void>
void createConeModel()
{
    std::cout << "Creating cone model..." << std::endl;
    PMXFile_Generator generator;
    float radius = 1.0f;
    float height = 3.0f;
    glm::vec3 position(0.0f, 2.0f, 0.0f);
    glm::vec3 rotation(0.0f, 0.0f, 0.0f);
    generator.createCone(radius, height, position, rotation);
    if (generator.saveToFile("cone_model.pmx")) {
        std::cout << "Cone model saved to cone_model.pmx" << std::endl;
    } else {
        std::cout << "Failed to save cone model" << std::endl;
    }
}

int testPmxGen()
{
    try {
        // Create example models        
       // createChainModel();
        //createCubeModel(); 
       // createRopeModel(0);
       // createBoxModel();
       // createBrickWall();
       // createCylinderModel();
      //   createConeModel();
        createBalanceModel();
        std::cout << "All models created successfully!" << std::endl;
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
