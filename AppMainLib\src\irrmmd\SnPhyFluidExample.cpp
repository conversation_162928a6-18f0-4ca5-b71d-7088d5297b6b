// Example usage of SnPhyFluid class
// This file demonstrates how to create and use a fluid simulation

#include "SnPhyFluid.h"
#include "ISceneManager.h"

namespace irr {
namespace scene {

// Example function to create a fluid simulation
SnPhyFluid* createFluidSimulation(ISceneManager* sceneManager,
                                  const core::vector3df& position = core::vector3df(0, 10, 0),
                                  const core::vector3df& size = core::vector3df(5, 6, 3))
{
    // Create the fluid scene node
    SnPhyFluid* fluidNode = new SnPhyFluid(
        1.0f,                           // size parameter
        sceneManager->getRootSceneNode(), // parent node
        sceneManager,                   // scene manager
        -1,                            // id
        position                       // position
    );

    // Configure fluid parameters
    fluidNode->numPointsX = static_cast<u32>(size.X * 10);  // 50 particles in X
    fluidNode->numPointsY = static_cast<u32>(size.Y * 10);  // 60 particles in Y
    fluidNode->numPointsZ = static_cast<u32>(size.Z * 10);  // 30 particles in Z
    fluidNode->particleSpacing = 0.1f;                      // Distance between particles
    fluidNode->maxDiffuseParticles = 100000;                // Max foam/bubble particles

    // Configure rendering
    fluidNode->showDiffuseParticles = true;                 // Show foam/bubbles
    fluidNode->particleSize = 0.5f;                         // Size of billboard quads

    return fluidNode;
}

// Example function to create visual representation of the container
void createVisualContainer(ISceneManager* sceneManager)
{
    // Create visual representation of the container walls
    // (The actual physics collision is handled automatically by SnPhyFluid::createFluidContainer())

    // Create a simple floor plane for visual reference
    auto floorMesh = sceneManager->getGeometryCreator()->createPlaneMesh(
        core::dimension2d<f32>(30.0f, 30.0f),  // size
        core::dimension2d<u32>(1, 1)           // tessellation
    );

    auto floorNode = sceneManager->addMeshSceneNode(floorMesh);
    floorNode->setPosition(core::vector3df(0, 0, 0));
    floorNode->setMaterialTexture(0, sceneManager->getVideoDriver()->getTexture("data/img/floor.jpg"));
    floorMesh->drop();

    // Create visual walls (optional - for reference)
    auto wallMesh = sceneManager->getGeometryCreator()->createCubeMesh(core::vector3df(0.2f, 10.0f, 15.0f));

    // Left wall
    auto leftWall = sceneManager->addMeshSceneNode(wallMesh);
    leftWall->setPosition(core::vector3df(-7.5f, 5.0f, 0));
    leftWall->getMaterial(0).DiffuseColor.set(100, 100, 100, 100); // Semi-transparent

    // Right wall
    auto rightWall = sceneManager->addMeshSceneNode(wallMesh);
    rightWall->setPosition(core::vector3df(7.5f, 5.0f, 0));
    rightWall->getMaterial(0).DiffuseColor.set(100, 100, 100, 100);

    wallMesh->drop();
}

// Example usage in your main application
void exampleUsage(ISceneManager* sceneManager)
{
    // Create visual container (optional - for reference)
    createVisualContainer(sceneManager);

    // Create fluid simulation
    // Note: The SnPhyFluid constructor automatically creates physics collision planes
    SnPhyFluid* fluid = createFluidSimulation(sceneManager);

    // The fluid will automatically update and render through the scene manager
    // No additional code needed - the OnAnimate() method handles physics updates
    // and the render() method handles visualization

    // Optional: You can modify fluid properties at runtime
    // fluid->particleSize = 0.8f;  // Make billboard quads larger
    // fluid->showDiffuseParticles = false; // Disable foam/bubble effects

    // Optional: Create custom container with different size
    // fluid->createFluidContainer(float3(20.0f, 15.0f, 20.0f), float3(0, 0, 0));
}

} // end namespace scene
} // end namespace irr
