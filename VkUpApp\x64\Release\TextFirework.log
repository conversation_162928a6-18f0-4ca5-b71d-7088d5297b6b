﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppBuild.targets(524,5): warning MSB8004: Output Directory does not end with a trailing slash.  This build instance will add the slash as it is required to allow proper evaluation of the Output Directory.
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
  Finished searching libraries
     Creating library D:\AProj\VkUpApp\Publish\TextFirework.lib and object D:\AProj\VkUpApp\Publish\TextFirework.exp
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
  Finished searching libraries
  Generating code
  0 of 44174 functions ( 0.0%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    0 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Release Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletCollision.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletDynamics.lib:
      Searching D:\SDK\bullet3Build\lib\Release\LinearMath.lib:
      Searching D:\SDK\bullet3Build\lib\Release\BulletSoftBody.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\release\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Release\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Release\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmt.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
  Finished searching libraries
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in '* CIL library *(* CIL module *)' is imported by 'AppMainLib.lib(SnCsParticle.obj)'
  TextFirework.vcxproj -> D:\AProj\VkUpApp\Publish\TextFirework.exe
