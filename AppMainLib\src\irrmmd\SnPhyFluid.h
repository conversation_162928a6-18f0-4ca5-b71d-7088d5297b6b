// Copyright (C) 2002-2012 <PERSON><PERSON>
// This file is part of the "Irrlicht Engine".
// For conditions of distribution and use, see copyright notice in irrlicht.h

#pragma once

#include "IMeshSceneNode.h"
#include "SMesh.h"
#include "Saba/Model/MMD/MMDPhysics.h"

namespace irr
{
namespace scene
{ 
	class SnPhyFluid : public IMeshSceneNode
	{
	public:

		//! constructor
		SnPhyFluid(f32 size, ISceneNode* parent, ISceneManager* mgr, s32 id,
			const core::vector3df& position = core::vector3df(0,0,0),
			const core::vector3df& rotation = core::vector3df(0,0,0),
			const core::vector3df& scale = core::vector3df(1.0f, 1.0f, 1.0f));

		virtual ~SnPhyFluid();

		virtual void OnRegisterSceneNode() _IRR_OVERRIDE_;
		virtual void OnAnimate(u32 timeMs) override;
		//! renders the node.
		virtual void render() _IRR_OVERRIDE_;

		void updateMesh();

		//! returns the axis aligned bounding box of this node
		virtual const core::aabbox3d<f32>& getBoundingBox() const _IRR_OVERRIDE_;

		//! returns the material based on the zero based index i. To get the amount
		//! of materials used by this scene node, use getMaterialCount().
		//! This function is needed for inserting the node into the scene hirachy on a
		//! optimal position for minimizing renderstate changes, but can also be used
		//! to directly modify the material of a scene node.
		virtual video::SMaterial& getMaterial(u32 i) _IRR_OVERRIDE_;

		//! returns amount of materials used by this scene node.
		virtual u32 getMaterialCount() const _IRR_OVERRIDE_;

		//! Returns type of the scene node
		virtual ESCENE_NODE_TYPE getType() const _IRR_OVERRIDE_ { return ESNT_CUBE; }

		//! Creates shadow volume scene node as child of this node
		//! and returns a pointer to it.
		virtual IShadowVolumeSceneNode* addShadowVolumeSceneNode(const IMesh* shadowMesh,
			s32 id, bool zfailmethod=true, f32 infinity=10000.0f) _IRR_OVERRIDE_;

		//! Creates a clone of this scene node and its children.
		virtual ISceneNode* clone(ISceneNode* newParent=0, ISceneManager* newManager=0) _IRR_OVERRIDE_;

		//! Sets a new mesh to display
		virtual void setMesh(IMesh* mesh) _IRR_OVERRIDE_ {}

		//! Returns the current mesh
		virtual IMesh* getMesh(void) _IRR_OVERRIDE_ { return Mesh; }

		//! Sets if the scene node should not copy the materials of the mesh but use them in a read only style.
		/* In this way it is possible to change the materials a mesh causing all mesh scene nodes
		referencing this mesh to change too. */
		virtual void setReadOnlyMaterials(bool readonly) _IRR_OVERRIDE_ {}

		//! Returns if the scene node should not copy the materials of the mesh but use them in a read only style
		virtual bool isReadOnlyMaterials() const _IRR_OVERRIDE_ { return false; }

		//! Removes a child from this scene node.
		//! Implemented here, to be able to remove the shadow properly, if there is one,
		//! or to remove attached childs.
		virtual bool removeChild(ISceneNode* child) _IRR_OVERRIDE_;
		
		// Fluid-specific initialization
		void initFluid(const u32 numX, const u32 numY, const u32 numZ, const float3& position = float3(0, 0, 0));
		
		// Fluid parameters
		const float fluidDensity = 1000.0f;
		u32 numPointsX = 50;
		u32 numPointsY = 60;
		u32 numPointsZ = 30;
		float particleSpacing = 0.1f;
		u32 maxDiffuseParticles = 100000;
		
		// Rendering options
		bool renderAsPoints = true;
		bool showDiffuseParticles = true;
		float particleSize = 0.05f;

	private:
		void setSize();

		SMesh* Mesh;
		IMeshBuffer* Mb{};
		IShadowVolumeSceneNode* Shadow;
		f32 Size;
 
		float4* ptcBuf{};
		float4* diffusePtcBuf{};
		core::aabbox3d<f32> Box;
		const std::unique_ptr<vks::Thread>  *thread;
	};

} // end namespace scene
} // end namespace irr
