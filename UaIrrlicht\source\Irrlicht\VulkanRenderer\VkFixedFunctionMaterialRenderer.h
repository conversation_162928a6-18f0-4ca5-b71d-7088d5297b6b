﻿
#ifndef __C_DG_FIXED_FUNCTION_MATERIAL_RENDERER_H_INCLUDED__
#define __C_DG_FIXED_FUNCTION_MATERIAL_RENDERER_H_INCLUDED__

#include "IrrCompileConfig.h"

#ifdef _IRR_WINDOWS_
#define WIN32_LEAN_AND_MEAN
#define VKDRV_MAX_FFMR_ITEM_COUNT 16384
#else
#define VKDRV_MAX_FFMR_ITEM_COUNT 512
#endif
#define MAP_CBUFF 0
#ifdef _IRR_COMPILE_WITH_VULKAN_
#define HAS_TEXMATRIX 0
#define FF_HAS_OIT 1
#define FF_CLIP_Y  0.f

#include "VkHeader.h"
#include "os.h"
#include "irrString.h"

#include "VkDriver.h"
#include "VkMaterialRenderer.h"
#include "VkShaderMan/VkFxBase.h"
//#include "DGShaderMan/DGFxConstantBuffer.h"
#include "VkShaderMan/VkFxDescriptorSetManager.h"
namespace irr
{
namespace video
{
	//using namespace Diligent;
 #pragma pack(push)
 #pragma pack(16)

struct SCENE_MATERIAL
{
	float4 Ambient;
	float4 Diffuse;
	float4 Specular;
	float4 Emissive;
	float		Shininess;
	int			Type;	// video::E_MATERIAL_TYPE
	int			id, shadowType;
};

struct SCENE_LIGHT
{
	float4 Position;
	float4 Diffuse;
	float4 Specular;
	float4 Ambient;
	float4 Atten;
};

struct cbPerFrame
{
	float4x4 g_mView;
	float4x4 g_mProj;
	float4x4 g_mLightVP;
	float3 g_eyePos;
	float  pad_000000000000000;
};


struct  MmdDynamicParam {
	float   shadowRat, whiteRat, NdotLRat, NdotEyeRat;
	int		shadowBlurCount, toonMulCount, colorMode, pad3;
};
union cbPerDraw
{
	 
		struct FF{
			float4x4 g_mWorld;
			float4x4 g_mInvWV;
			SCENE_MATERIAL	g_material;
			SCENE_LIGHT		g_lights[_IRR_MAX_LIGHTS_];
			int     g_bEnableLighting;
			int			g_iLightCount; // Number of lights enabled
			float gTime;
			int		passEnum;
			uint32_t pickId, drawFlag;
			float  clipY; int isOIT;
			//custom param
			FFDrawCP cp;
		}ff;

		struct MMD{
			float4x4 g_mWorld;
			float4x4 g_mLightVP;
			float4	Diffuse;
			float4	Ambient;
			float4	Specular;
			float4	lnv;
			int4	TextureModes;
			float   clipY, padf1, padf2, padf3;
			float3	LightColor;
			int		outNormalMap;

			float3	LightDir;
			uint32_t	pickId;

			float4	TexMulFactor;
			float4	TexAddFactor;

			float4	ToonTexMulFactor;
			float4	ToonTexAddFactor;

			float4	SphereTexMulFactor;
			float4	SphereTexAddFactor;


			uint32_t	Flag,passType,oit, rainbow;

			float2  res,padf20;
			float4  pad1, pad2, pad3;

			MmdDynamicParam  dp;
		}mmd;

		struct {
			float4x4 g_mWorld;
			ShaderToyCb sdt;
		};
		
	
// #if USE_CLIPPLANES
// 	float4		g_clipplanes[3];
// #endif

};

struct MMDCbCp {
	cbPerDraw::MMD cb;
	int useCsTransVtx, useOIT;
	VkHardwareBuffer* hbVtx;
	VkDescriptorSet ds;
	uint32_t dsOfs;
	vks::Buffer* cbDrawBuf;
	bool twoSide;
};

#pragma pack(pop)
class VkFixedFunctionMaterialRenderer;

class VKFFMRSharedData {
public:
	VKFFMRSharedData(VkDriver* driver);
	~VKFFMRSharedData();
	int dsId = 0;
	VkFixedFunctionMaterialRenderer *pOwner;
	VkDriver* Driver;
	//VkDescriptorSet mDescSet;

	VkDescriptorImageInfo mTexDescriptor[8]{};


	size_t dynamicAlignment=0;
	bool isFirstDraw = true;
	bool isFirstEndScene = true;
	void* cbByteBuf = nullptr;

	std::vector<cbPerDraw> mDubDraws;  //dynamic Uniform Buffer
	vks::Buffer mFxCbFrame, mFxCbDraw;
	cbPerFrame cbFrame;
	VkFxUtil::VkFxDescriptorSetManager *DSMan=nullptr;
	
	void PrepareCbOnceBuffer();
	bool UpdateDynamicUniformBuffer(bool onEnd);

	void UploadBufferData(size_t upsize);
	UP_LOCK_DECLVAR_TYPE lock;
};

class VkFixedFunctionMaterialRenderer : public VkMaterialRenderer
{
public:
	friend class VkMaterialRenderer_SSAO;
	//! Constructor for fixed function effect
	VkFixedFunctionMaterialRenderer(IVideoDriver* driver, io::IFileSystem* fileSystem);


	virtual ~VkFixedFunctionMaterialRenderer();


	virtual bool OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId = -1) override;

	/** Called during the IVideoDriver::setMaterial() call before the new
	material will get the OnSetMaterial() call. */
	virtual void OnUnsetMaterial() { }

	/** Called by other renderers to set some variables*/
	virtual bool setVariable(const c8* name, const f32* floats, int count)
	{
		return false;
	}


	virtual const void* getShaderByteCode() const;
	virtual u32 getShaderByteCodeSize() const;

	virtual bool isTransparent() const { 
		return false; 
	}

	virtual void cleanFrameCache() override;


	virtual void preSubmit() override;
	virtual void ReloadShaders() override;

	E_MATERIAL_TYPE mtCheck = EMT_LAST;
	bool delayInit = false;
	static void RefreshMr(IVideoDriver* driver);

	VkDescriptorSet newDS() { return mSD->DSMan->newDS(); }
	void delDS(VkDescriptorSet ds) { mSD->DSMan->delDS(ds); }
	uint32_t dynBufAligment() { return mSD->dynamicAlignment; }
protected:
	virtual void InitMaterialRenderer();
	virtual void recreatePipeline() override;
	void CreateSamplers();


	virtual void setupDescriptorSetLayout();


	std::shared_ptr<VkFxUtil::VkFxBase> mCurFx,mFxStd,mFx2T,mFxTang, mFxStdNL, mFxStdSpec, mFx2TNL ;

	cbPerDraw cbDraw;
	VKFFMRSharedData  *mSD = nullptr;
	bool mOwnSD = false;
	bool mSamePipline = false;
	u32 mCbSize;

	VkFixedFunctionMaterialRenderer* pSolid{};
	VkPipeline mPlDraw= VK_NULL_HANDLE, mPlDrNL= VK_NULL_HANDLE, mPlDrSpec= VK_NULL_HANDLE;
	VkPipeline mPlCsVtxCullNone = VK_NULL_HANDLE, mPlCsVtxCullBack = VK_NULL_HANDLE;

	VkSampler mSampler{}, mSamplerClamp{}, mSamplerClampBdr{};
	


	//Diligent::RefCntAutoPtr<Diligent::IShaderResourceBinding>	mSrbLt, mSrbNL;

};

class VkMaterialRenderer_SOLID : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_SOLID(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override
	{
		blendAttachmentState.blendEnable = 0;
		VkFixedFunctionMaterialRenderer::InitMaterialRenderer();
	}
	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		// Store current material type
		CurrentMaterial = material;

		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );


	}
	
};

class VkMaterialRenderer_PickPoint : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_PickPoint(IVideoDriver* driver, io::IFileSystem* fSystem,bool isCloudList)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {
		mtCheck = isCloudList?EMT_POINT_LIST_PICK_POINT:EMT_PICK_POINT;
	}
	virtual void InitMaterialRenderer() override
	{
		blendAttachmentState.blendEnable = 0;
		VkFixedFunctionMaterialRenderer::InitMaterialRenderer();
	}
	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		// Store current material type
		CurrentMaterial = material;

		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
	}	
};


class VkMaterialRenderer_SOLID_2_LAYER : public VkFixedFunctionMaterialRenderer
{

public:
	VkMaterialRenderer_SOLID_2_LAYER(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {
	}
	virtual void InitMaterialRenderer() override
	{
		blendAttachmentState.blendEnable = 0;
		VkFixedFunctionMaterialRenderer::InitMaterialRenderer();
	}
	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );

	}
};

class VkMaterialRenderer_LIGHTMAP : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_LIGHTMAP(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override
	{
		blendAttachmentState.blendEnable = 0;
		VkFixedFunctionMaterialRenderer::InitMaterialRenderer();
	}
	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		// Store current material type
		CurrentMaterial = material;

		// set basic states
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
			
	}
	
};
	
class VkMaterialRenderer_DETAIL_MAP : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_DETAIL_MAP(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override
	{
		blendAttachmentState.blendEnable = 0;
		VkFixedFunctionMaterialRenderer::InitMaterialRenderer();
	}
	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		// Store current material type
		CurrentMaterial = material;

		// set basic states
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
			
	}
	
};

class VkMaterialRenderer_SPHERE_MAP : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_SPHERE_MAP(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override
	{
		blendAttachmentState.blendEnable = 0;
		VkFixedFunctionMaterialRenderer::InitMaterialRenderer();
	}
	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		// Store current material type
		CurrentMaterial = material;

		// set basic states
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
			

		// set texture matrix
		//DebugBreak(); //优化 cbuffer
#if HAS_TEXMATRIX  // ? not implemented
		BaseRenderer->setVariable("TextureMatrix0", reinterpret_cast<f32*>(&SphereMapMatrixD3D11), sizeof(SphereMapMatrixD3D11));
#endif
	}

	virtual void OnUnsetMaterial()
	{
#if HAS_TEXMATRIX
		BaseRenderer->setVariable("TextureMatrix0", reinterpret_cast<f32*>(&UnitMatrixD3D11), sizeof(UnitMatrixD3D11));
#endif
	}	
};

class VkMaterialRenderer_REFLECTION_2_LAYER : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_REFLECTION_2_LAYER(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override
	{
		blendAttachmentState.blendEnable = 0;
		VkFixedFunctionMaterialRenderer::InitMaterialRenderer();
	}
	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		// Store current material type
		CurrentMaterial = material;

		// set basic states
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
			

#if HAS_TEXMATRIX  
		// set texture matrix
		BaseRenderer->setVariable("TextureMatrix0", reinterpret_cast<f32*>(&SphereMapMatrixD3D11), sizeof(SphereMapMatrixD3D11));
#endif
	}

	virtual void OnUnsetMaterial()
	{
#if HAS_TEXMATRIX  
		BaseRenderer->setVariable("TextureMatrix0", reinterpret_cast<f32*>(&UnitMatrixD3D11), sizeof(UnitMatrixD3D11));
#endif
	}
};

class VkMaterialRenderer_TRANSPARENT_ADD_COLOR : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_TRANSPARENT_ADD_COLOR(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override;
	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
	}

	virtual bool isTransparent() const { return true; }	
};

class VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override;
	virtual bool isTransparent() const { return true; }

	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
	}
#if 1
	virtual bool OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId = -1)  override
	{
		return VkFixedFunctionMaterialRenderer::OnRender(service, vtxtype);
	}
#endif
};
class VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL_NoDepthWrite : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL_NoDepthWrite(IVideoDriver* driver, io::IFileSystem* fSystem)
		: VkFixedFunctionMaterialRenderer(driver, fSystem) {
		mtCheck = EMT_TRANSPARENT_ALPHA_CHANNEL_NoDepthWrite;
	}
	virtual void InitMaterialRenderer() override;
	virtual bool isTransparent() const { return true; }

	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
		bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
	}
#if 1
	virtual bool OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId = -1)  override
	{
		return VkFixedFunctionMaterialRenderer::OnRender(service, vtxtype);
	}
#endif
};
class VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL_REF : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL_REF(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {

	}
	virtual void InitMaterialRenderer() override;
	virtual bool isTransparent() const { return true; }

	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
	}
	
};

class VkMaterialRenderer_TRANSPARENT_VERTEX_ALPHA : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_TRANSPARENT_VERTEX_ALPHA(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override;
	virtual bool isTransparent() const { return true; }

	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
	}
	
};

class VkMaterialRenderer_TRANSPARENT_REFLECTION_2_LAYER : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_TRANSPARENT_REFLECTION_2_LAYER(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override;
	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
	}
	
};

class VkMaterialRenderer_ONETEXTURE_BLEND : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_ONETEXTURE_BLEND(IVideoDriver* driver, io::IFileSystem* fSystem)
	: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override;
	virtual bool isTransparent() const { return true; }

	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
					bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates( material, lastMaterial, resetAllRenderstates );
	}
	
};
class VkMaterialRenderer_PointCloud : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_PointCloud(IVideoDriver* driver, io::IFileSystem* fSystem)
		: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override;
	virtual bool isTransparent() const { return false; }

	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
		bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
	}

};

class VkMaterialRenderer_WaterSurface : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_WaterSurface(IVideoDriver* driver, io::IFileSystem* fSystem)
		: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override;
	virtual bool isTransparent() const { return false; }

	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
		bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
	}

};
class VkMaterialRenderer_3D_UI : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_3D_UI(IVideoDriver* driver, io::IFileSystem* fSystem)
		: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer() override;
	virtual bool isTransparent() const {
		return true;
	}

	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
		bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
	}

};
class VkMaterialRenderer_ShaderToy : public VkFixedFunctionMaterialRenderer
{
public:
	VkMaterialRenderer_ShaderToy(IVideoDriver* driver, io::IFileSystem* fSystem)
		: VkFixedFunctionMaterialRenderer(driver, fSystem) {}
	virtual void InitMaterialRenderer()
	{
		blendAttachmentState.blendEnable = VK_TRUE;
		blendAttachmentState.srcColorBlendFactor = VK_BLEND_FACTOR_SRC_ALPHA;
		blendAttachmentState.dstColorBlendFactor = VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
		blendAttachmentState.colorBlendOp = VK_BLEND_OP_ADD;
		blendAttachmentState.srcAlphaBlendFactor = VK_BLEND_FACTOR_ONE;
		blendAttachmentState.dstAlphaBlendFactor = VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
		blendAttachmentState.alphaBlendOp = VK_BLEND_OP_ADD;
		VkFixedFunctionMaterialRenderer::InitMaterialRenderer();
	}
	virtual bool isTransparent() const { return false; }

	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
		bool resetAllRenderstates, IMaterialRendererServices* services)
	{
		CurrentMaterial = material;
		services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
	}
	virtual bool OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype);
};
}
}

#endif
#endif
