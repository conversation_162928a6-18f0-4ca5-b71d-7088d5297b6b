﻿//
// Copyright(c) 2016-2017 benikabocha.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)
//

#ifndef SABA_MODEL_MMD_PMXMODEL_H_
#define SABA_MODEL_MMD_PMXMODEL_H_

#include "MMDMaterial.h"
#include "MMDModel.h"
#include "MMDIkSolver.h"
#include "PMXFile.h"

#include <glm/vec2.hpp>
#include <glm/vec3.hpp>
#include <glm/gtc/quaternion.hpp>
#include <vector>
#include <string>
#include <map>
#include <algorithm>
#include <future>
#include "Helpers/UpStrIdxRes.h"
#include "PMXWriter.h"
#define PMX_CACHE_DATA	1
#define FRAME_NODE_FW 0

#define PRECREATERB			1
namespace physx {
	class PxMaterial;
}
namespace saba
{
	struct ModelCreateParam
	{

	};

	class PMXNode : public MMDNode
	{
	public:
		PMXNode();

		void SetDeformDepth(int32_t depth) { m_deformDepth = depth; }
		int32_t GetDeformdepth() const { return m_deformDepth; }



		void SetAppendNode(PMXNode* node) { m_appendNode = node; }
		PMXNode* GetAppendNode() const { return m_appendNode; }

		void EnableAppendRotate(bool enable) { m_isAppendRotate = enable; }
		void EnableAppendTranslate(bool enable) { m_isAppendTranslate = enable; }
		void EnableAppendLocal(bool enable) { m_isAppendLocal = enable; }
		void SetAppendWeight(float weight) { m_appendWeight = weight; }
		float GetAppendWeight() const { return m_appendWeight; }

		const glm::vec3& GetAppendTranslate() const { return m_appendTranslate; }
		const glm::quat& GetAppendRotate() const { return m_appendRotate; }

		void SetIKSolver(MMDIkSolver* ik) { m_ikSolver = ik;  EnableIK(true); }
		MMDIkSolver* GetIKSolver() const { return m_enableIK? m_ikSolver:nullptr; }

		void UpdateAppendTransform();
		virtual bool IsIK() const { return m_ikSolver!=nullptr; }
		bool		m_isAppendRotate;
		bool		m_isAppendTranslate;
	protected:
		void OnBeginUpdateTransform() override;
		void OnEndUpdateTransfrom() override;
		void OnUpdateLocalTransform() override;

	private:
		int32_t		m_deformDepth;


		PMXNode*	m_appendNode;

		bool		m_isAppendLocal;
		float		m_appendWeight;

		glm::vec3	m_appendTranslate;
		glm::quat	m_appendRotate;

		MMDIkSolver*	m_ikSolver;

	};

	class PMXModel : public MMDModel
	{
	public:
		enum class SkinningType
		{
			Weight1,
			Weight2,
			Weight4,
			SDEF,
			DualQuaternion,
		};
		struct VertexBoneInfo
		{
			SkinningType	m_skinningType;
			int p1, p2, p3;
			union
			{
				struct
				{
					int32_t	m_boneIndex[4];
					float	m_boneWeight[4];
					float	pad[4];
					float	pad1[4];
				};
				struct
				{
					int32_t	m_boneIndex[2];
					float	m_boneWeight,p0;

					glm::vec3	m_sdefC;	float p1;
					glm::vec3	m_sdefR0;	float p2;
					glm::vec3	m_sdefR1;	float p3;

				} m_sdef;
			};
		};
	public:
		PMXModel();
		~PMXModel();

		MMDNodeManager* GetNodeManager() override { return &m_nodeMan; }
		MMDIKManager* GetIKManager() override { return &m_ikSolverMan; }
		MMDMorphManager* GetMorphManager() override { return &Sd->morphMan; };
		MMDPhysicsManager* GetPhysicsManager() override { return &m_physicsMan; }

		size_t GetVertexCount() const override { assert(Sd->positions_size); return Sd->positions_size; }
		const glm::vec3* GetPositions() const override { return Sd->positions.data(); }
		const glm::vec3* GetNormals() const override { return Sd->normals.data(); }
		const glm::vec2* GetUVs() const override { return Sd->uvs.data(); }
		const glm::vec3* GetUpdatePositions() const override { return m_updatePositions.data(); }
		const glm::vec3* GetUpdateNormals() const override { return m_updateNormals.data(); }
		const glm::vec2* GetUpdateUVs() const override { return m_updateUVs.data(); }
		const VertexBoneInfo* GetBoneInfos() const { return Sd->vertexBoneInfos.data(); }//ckadd
		bool hasBoneInfo() { return Sd->vertexBoneInfos.size() > 0; }
		const std::vector<glm::mat4>& get_transforms() { return m_transforms; };
		const auto& get_morphPositions() { return m_morphPositions; };
		void clearPNUV(bool freeAll);
		size_t GetIndexElementSize() const override { return m_indexElementSize; }
		size_t GetIndexCount() const override { return m_indexCount; }
		const void* GetIndices() const override { return &Sd->indices[0]; }

		size_t GetMaterialCount() const override { return Materials.size(); }
		const MMDMaterial* GetMaterials() const override { return &Materials[0]; }

		size_t GetSubMeshCount() const override { return Sd->subMeshes.size(); }
		const MMDSubMesh* GetSubMeshes() const override { return &Sd->subMeshes[0]; }

		MMDPhysics* GetMMDPhysics() override { return m_physicsMan.GetMMDPhysics(); }

		void InitializeAnimation() override;
		void InitializeAnimationUpdate();
		// アニメーションの前後で呼ぶ (VMDアニメーションの前後)
		void BeginAnimation() override;
		void EndAnimation() override;
		// Morphoot
		void UpdateMorphAnimation() override;
		// ノードを更新する
		int UpdateNodeAnimation(bool afterPhysicsAnim, bool realMove=true) override;
		// Physicsを更新する
		void ResetPhysics(bool dynActive=false) override;
		void setCollision(bool isOn, bool preDelete=false);
		void UpdatePhysicsAnimation(float elapsed) override;
		void onPhysicsStep(int step, int stepCount, float stepTime);
		void UpdatePhysicsBodiesToNodes();
		void moveAllOffset(glm::vec3 ofs,bool moveRootNode=false, bool resetRb = true);
		void moveAllRbTo(glm::vec3 pos, bool moveRootNode = false, bool resetRb = true);
		void moveAllRbToAnim();
		void UpdatePhysicsAnimation2Pass(float elapsed,uint32_t flag) override;
		// 頂点データーを更新する
		void Update(bool onlyTrans) override;
		void SetParallelUpdateHint(uint32_t parallelCount) override;

		bool Load(const std::string& filepath, const std::string& mmdDataDir,bool &simple,bool onlyGetInfo=false);
		void newRb(size_t i);
		void createNode(size_t i);
		void Destroy();

		const glm::vec3& GetBBoxMin() const { return m_bboxMin; }
		const glm::vec3& GetBBoxMax() const { return m_bboxMax; }

		
		//ckadd
		std::string modelIdStr;
		PMXFileCreateParam fcp;
		PMXJoint defaultDynRbJointParam{ .springT = glm::vec3{0}, .springR = glm::vec3(100.f), .dampingT=glm::vec3(10.f), .dampingR=glm::vec3(10.f)};
		bool allowAllPhysicsNode = true;
		int createIdx = 0;
		virtual irr::io::IFileArchive* getFileArchive() { 
			return pmx.fileArchive; 
		}
		void setTexInvV(bool v) { texInvV = v; }
		virtual void DoCallback(NodeCbStage reason) {
			if (cbStage) cbStage(reason);
			if (!cbNode) return;
			for (auto& node : (*m_nodeMan.GetNodes()))
			{
				if ((node->exd.flag & MNFlag::mnfCb)) cbNode(reason, *node.get());
			}
		};

		void resetRigidBodies(int flag=0);
		void resetDynBodies();
		std::vector<MMDRigidBody*> getRigidBodys(int dyn);
		void addChildModel(std::shared_ptr<MMDModel> model, MMDNode* node);
		void clearVtxPickNodes();
		void addBodyVel(const glm::vec3& f, bool dynOnly=false);
		void setBodyVel(const glm::vec3& f,bool dynOnly = false);
		void scaleBodyVel(float sc, int what);
		void scaleBodyVel(const glm::vec3& sc, int what);
		void scaleDynBodyVel(float sc, int what);
		void scaleDynBodyVel(const glm::vec3& sc, int what);
		//bool bAddWingBones = false;

		int morphVtxMin = 0, morphVtxMax = 0, mVtxCount = 0;
		MMDRigidBody* rootRb{}, * touchRB{}, * lastTouchRB{}, * writeFingerPinRB{}, * leftHandPinRB{};
		MMDRigidBody* getRbByVtxIdOrNode(int vid,glm::vec3 *hitPos, MMDNode* nd, MMDNode* ndTouchRb=nullptr, bool lockRtt=false, uint32_t flag=1);
		MMDJoint* touchJT{};
		glm::vec3 pickRbPos{}, pickRbOfs{};


		struct VtxPickData {
			MMDRigidBody* rb;
			MMDJoint* jt;
		};
		std::vector<VtxPickData> pickJTs;
		PMXInfo info;
		MMDRigidBody* getRb(int id) { return id<m_physicsMan.GetRigidBodys()->size()? m_physicsMan.GetRigidBodys()->at(id):nullptr; }

		MMDJoint* connectRb(MMDRigidBody* rbA, MMDRigidBody* rbB, bool lockPos = true, glm::vec3 ofs = {0,0,0}, 
			bool lockRtt = false, glm::vec3 rtt = { 0,0,0 });

		MMDJoint* connectRb(MMDRigidBody* rbA, MMDRigidBody* rbB, saba::PMXJoint jt, bool movB = false) {
			return connectRb(rbA, rbB, jt.lockPos, jt.lockRtt, jt, movB);
		}

		MMDJoint* connectRb(MMDRigidBody* rbA, MMDRigidBody* rbB, bool lockPos, bool lockRtt,  saba::PMXJoint jt,bool movB = false);

		MMDJoint* connectRbM(MMDRigidBody* rbA, MMDRigidBody* rbB, int lockPos, int lockRtt, saba::PMXJoint jt, bool movB = false);

		MMDJoint* connectToRootNodeRatio(MMDNode* ndRoot, MMDNode* node, MMDRigidBody* rbB, float ratio, bool lockPos, glm::vec3 ofs, bool lockRtt);

		MMDJoint* connectRbMoveB(saba::MMDRigidBody* rbA, glm::vec3 jtpos, saba::MMDRigidBody* rbB,   bool lockPos, const glm::vec3& ofs, bool lockRtt);

		void rebuildTmpIK(MMDNode* tgtNode, MMDNode* rootNode,MMDNode* rootNodeBack);
		void finishTmpIK();
		MMDNode* getTmpIkNode() { return tmpIKNode; }
		int getMeshIdByVtxId(uint32_t vid);
		std::vector<MMDMaterial>	Materials;
		void smaSetAll(float v) { for (auto &m:Materials)	m.subMeshAlphaMul=v; }
		void smaSetAllDec(float v) { for (auto& m : Materials) m.smaSetDec(v); }
		glm::vec3 ctrBallSize,yaoPos;
		float armLength = 6.f, legLength = 10.f;
		float rbRootY=10.f;
		size_t rootRbBoneIdx = -1;

		int resetPhyCD = 0;
		int headTgtRbId = 0;
		float initMassMulByVolume=1.f;
		float groundAngleDeg = 0.f;
		glm::mat4 mtRootParent{1};
		PMXFile pmx;
		MMDNode* nodeRoot{}; int rootIdx = 0;	bool foundRoot = false;

		//* ndLegL{}, * ndLegR{}, * ndLeg1L{}, * ndLeg1R{}, * ndFootL{}, * ndFootR{},
		;
		MMDNode* addExtNode(PMXBone bone,bool hasRb0);
		std::vector<MMDNode*> extNodes;

		std::vector<MMDRigidBody*> anchorRBs;
		std::vector<MMDNode*> ndfwNodes;
		MMDNode* ndFingers[2][5][5] = {};
		MMDNode* ndToeL{}, * ndToeR{};
		int fingerNdCount = 0;

		MMDNode* attachParentNode{}; 
		float flapT = 0.f;
		float wingFlapTMul = 1.0f; bool attachedWing = false;

		MMDNode* tgtNode{};  float tgtNodeChgTime = 0.f;
		//std::vector<MMDNode*> rbTreeNodes;
		//void sortedDynRbs(MMDNode * node) {
		//	float sum = 0.f;
		//	while (node) {
		//		if (node->rb0 && node->rb0->dynRbType==1) {
		//			rbTreeNodes.push_back(node);
		//		}
		//		sortedDynRbs(node->GetChild());
		//		node = node->GetNext();
		//	} 

		//}
		void A_CopyBoneWeightFromBtoC(const char* fileName,const char* from, const char* outfile, float maxDis=10.f);
		int writeFile(const char* addFileName);
		
		void saveState()
		{
			savedState = true;
			for (auto& rb : *m_physicsMan.GetRigidBodys()) {
				rb->saveState();
			}
		}
		void restoreState(int ofs, uint32_t flag = 0) //1:only dyn
		{
			if (!savedState) { saveState(); return; }
			for (auto& rb : *m_physicsMan.GetRigidBodys()) {
				if (!(flag&1) || rb->dynRbType)
					rb->restoreState(ofs,flag);
			}
		}

		std::map<std::wstring,std::shared_ptr<saba::VMDAnimation>> vmdMap;
	private:
		//std::vector< std::shared_ptr<MMDModel>> childModels;
 
		//void calcSubRbWeight();
		std::vector<int> rbids;

		bool savedState = false;
	public:
		struct PositionMorph
		{
			uint32_t	m_index;
			glm::vec3	m_position;
		};

		struct PositionMorphData
		{
			std::vector<PositionMorph>	m_morphVertices;
			int vtxMin, vtxMax;
		};

		struct UVMorph
		{
			uint32_t	m_index;
			glm::vec4	m_uv;
		};

		struct UVMorphData
		{
			std::vector<UVMorph>	m_morphUVs;
		};

		struct MaterialFactor
		{
			MaterialFactor() = default;
			MaterialFactor(const saba::PMXMorph::MaterialMorph& pmxMat);

			void Mul(const MaterialFactor& val, float weight);
			void Add(const MaterialFactor& val, float weight);

			glm::vec3	m_diffuse;
			float		m_alpha;
			glm::vec3	m_specular;
			float		m_specularPower;
			glm::vec3	m_ambient;
			glm::vec4	m_edgeColor;
			float		m_edgeSize;
			glm::vec4	m_textureFactor;
			glm::vec4	m_spTextureFactor;
			glm::vec4	m_toonTextureFactor;
		};

		struct MaterialMorphData
		{
			std::vector<saba::PMXMorph::MaterialMorph>	m_materialMorphs;
		};

		struct BoneMorphElement
		{
			int			nodeId;
			glm::vec3	m_position;
			glm::quat	m_rotate;
		};

		struct BoneMorphData
		{
			std::vector<BoneMorphElement>	m_boneMorphs;
		};

		struct GroupMorphData
		{
			std::vector<saba::PMXMorph::GroupMorph>		m_groupMorphs;
		};

		enum class MorphType
		{
			None,
			Position,
			UV,
			Material,
			Bone,
			Group,
		};

		class PMXMorph : public MMDMorph
		{
		public:
			MorphType	m_morphType;
			size_t		m_dataIndex;
		};

		struct UpdateRange
		{
			size_t	m_vertexOffset;
			size_t	m_vertexCount;
		};

		struct PmxModelShareData {
			PMXFile pmxFile;

			std::vector<glm::vec3>	positions;	size_t positions_size;
			std::vector<glm::vec3>	normals;	size_t normals_size;
			std::vector<glm::vec2>	uvs;		size_t uvs_size;
			std::vector<VertexBoneInfo>	vertexBoneInfos;	size_t vertexBoneInfos_size;

			std::vector<char>	indices;
			std::vector<PositionMorphData>	positionMorphDatas;
			std::vector<UVMorphData>		uvMorphDatas;
			std::vector<MaterialMorphData>	materialMorphDatas;
			std::vector<BoneMorphData>		boneMorphDatas;
			std::vector<GroupMorphData>		groupMorphDatas;

			std::vector<MMDMaterial>	initMaterials;
			std::vector<MMDSubMesh>		subMeshes;

			MMDMorphManagerT<PMXMorph>	morphMan;

			size_t			indexCount;
			glm::vec3		bboxMin;
			glm::vec3		bboxMax;
			physx::PxMaterial* pxMtrPtr[16]{};
			
		};
#if PMX_CACHE_DATA
		inline static uu::UpStrIdxRes<std::shared_ptr<PmxModelShareData>> Sds;//Same pmx VERTEX DATA CACHE 
		std::shared_ptr<PmxModelShareData> Sd;
		bool newSd = false;
#else
		PmxModelShareData SdObj, * Sd{};
#endif

	private:
		
		void SetupParallelUpdate();

		void UpdateRg(const UpdateRange& range);

		void Morph(PMXMorph* morph, float weight);

		void MorphPosition(const PositionMorphData& morphData, float weight);

		void MorphUV(const UVMorphData& morphData, float weight);

		void BeginMorphMaterial();
		void EndMorphMaterial();
		void MorphMaterial(const MaterialMorphData& morphData, float weight);

		void MorphBone(const BoneMorphData& morphData, float weight);


		size_t lastRbIdx = -1, weightRbIdx = -1;
		bool hasFoot = false, hasFootFront = false;
		int handRbId = 0, legLRbIdx = 0, legRRbIdx = 0, footLRbIdx = 0, footRRbIdx = 0;
		bool createRbs();
		bool createRb(size_t pi, int& retFlag);
		void createInflate();

	private:

		//std::vector<glm::vec3>	Sd->positions;
		//std::vector<glm::vec3>	Sd->normals;
		//std::vector<glm::vec2>	Sd->uvs;
		//std::vector<VertexBoneInfo>	Sd->vertexBoneInfos;
		std::vector<glm::vec3>	m_updatePositions;
		std::vector<glm::vec3>	m_updateNormals;
		std::vector<glm::vec2>	m_updateUVs;
		std::vector<glm::mat4>	m_transforms;

		//std::vector<char>	Sd->indices;
		size_t				m_indexCount;
		size_t				m_indexElementSize;

		//std::vector<PositionMorphData>	Sd->positionMorphDatas;
		//std::vector<UVMorphData>		Sd->uvMorphDatas;
		//std::vector<MaterialMorphData>	Sd->materialMorphDatas;
		//std::vector<BoneMorphData>		Sd->boneMorphDatas;
		//std::vector<GroupMorphData>		Sd->groupMorphDatas;

		// PositionMorph用
		std::vector<glm::vec4>	m_morphPositions;
		std::vector<glm::vec4>	m_morphUVs;

		// MaterialMorph用
		//std::vector<MMDMaterial>	Sd->initMaterials;
		std::vector<MaterialFactor>	m_mulMaterialFactors;
		std::vector<MaterialFactor>	m_addMaterialFactors;

		glm::vec3		m_bboxMin;
		glm::vec3		m_bboxMax;

		
		//std::vector<MMDSubMesh>		Sd->subMeshes;
		std::vector<PMXNode*>		m_sortedNodes;

		MMDNodeManagerT<PMXNode>	m_nodeMan;
		MMDIKManagerT<MMDIkSolver>	m_ikSolverMan;
		//MMDMorphManagerT<PMXMorph>	Sd->morphMan;
		MMDPhysicsManager			m_physicsMan;

		uint32_t							m_parallelUpdateCount;
		std::vector<UpdateRange>			m_updateRanges;
		std::vector<std::future<void>>		m_parallelUpdateFutures;

		bool uvUploaded = false;
		bool texInvV = false;
		bool hasUpper3 = false;

		void addData();
		size_t oriBoneCount=0;
		MMDNode* tmpIKNode{};
		
		std::vector<MMDRigidBody*> rbs;

		std::vector< InflateBufBind> ibbArr;


		
	};
}

#endif // !SABA_MODEL_MMD_PMXMODEL_H_

#define PICK_NODE_DROP		-1
#define PICK_NODE_KEEP		-2
#define PICK_NODE_CONTINUE  -20