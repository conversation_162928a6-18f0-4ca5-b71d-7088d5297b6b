﻿// Copyright (C) 2002-2009 <PERSON><PERSON>
// This file is part of the "Irrlicht Engine".
// For conditions of distribution and use, see copyright notice in irrlicht.h

//#define _IRR_DONT_DO_MEMORY_DEBUGGING_HERE
#include "VkDriver.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_ 
#ifdef _DEBUG
#define USE_VULKAN_VALIDATION  1
#else
#define USE_VULKAN_VALIDATION  0
#endif
#define SWAPCHAIN_HDR_IDX	0// 0,1,2  

#define CPU_2D_POS 0
#define USE_GRPHIC_CARD_1  0

#define USE_QUEUE_FENCE   0 // may need more sync to use this, check all resources depends last frame
#define PRESET_SPLIT 1

#include "os.h"
#include "irrTypes.h"
#include "S3DVertex.h"
#include "SIrrCreationParameters.h"
#include "VkTexture.h"
#include "VkHardwareBuffer.h"
#include "CImage.h"
#include "VkMaterialRenderer.h"
#include "VkFixedFunctionMaterialRenderer.h"
#include "vulkanRenderPass.h"
//#include "DGShaderMaterialRenderer.h"
//#include "DGNormalMapRenderer.h"
#include "VkVertexDeclaration.h"
#include "IWriteFile.h"
#include "VkMr2D.h"
#include <thread>
//#include "Helpers/thread-pool-cpp17/thread_pool.hpp"
//#include "MrImageProcessing.h"
#if IRR_MTR_MMD
#include "VkMrFF_MMD.h"
#endif
#if IRR_MTR_SSAO
#include "VkMrFF_SSAO.h"
#endif

#define DVBSIZE 1024
#pragma warning(once:4244)

#define SAFE_RELEASE(x) \
if(x)				\
x->Release(); 	\
x = nullptr;

#define SAFE_DELETE(x) \
if(x)				\
delete x; 	\
x = nullptr;

using namespace irr::io;

namespace irr
{

static core::matrix4 IdentityMatrix11;

video::VkDriver* g_irrDrv11 = nullptr;
namespace video
{


static VK_EXTERNAL_DEVICE_INFO g_edi;
VK_EXTERNAL_DEVICE_INFO* g_pExtDevInfo = NULL;

//! constructor
VkDriver::VkDriver(const irr::SIrrlichtCreationParameters& params, io::IFileSystem* io)
	:VkDriverBase(params, io),
	//CurrentRenderMode(ERM_NONE),
	ResetRenderStates(true),
	StencilBuffer(params.Stencilbuffer), AntiAliasing(0),
	//Adapter(0), DXGIFactory(0), Output(0), D3DLibrary(0), CurrentInputLayout(0), 


	SceneSourceRect(0), LastVertexType((video::E_VERTEX_TYPE)-1), VendorID(0), ColorFormat(ECF_A8R8G8B8),
	CurrentRenderMode(ERM_3D), MaxActiveLights(8), Fullscreen(params.Fullscreen), AlphaToCoverageSupport(true),
	VSync(false), DepthStencilFormat(VK_FORMAT_UNDEFINED),
	Transformation3DChanged(false),UseOIT(params.bUseOIT), 
	MaxTextureUnits(MATERIAL_MAX_TEXTURES) // DirectX 11 can handle much more than this value, but keep compatibility
	, mPickingTex(nullptr), bLastIsSimpleImage(false)//mStaticBgTex(nullptr),
{
	DP(("VkDriver::VkDriver"));




	DP(("CNullDriver ScreenSize %d,%d", ScreenSize.Width, ScreenSize.Height));

	if (OffScreen) {
		baseDriver = (VkDriver*)CreatePms.WindowId;
		CreatePms.WindowId = 0;
	}
	else {
		g_irrDrv11 = this;
		
		threadPool.setThreadCount(core::min_(24u,std::thread::hardware_concurrency()));
		DP(("THREAD POOL COUNT %d",threadPool.getThreadCount()));

	}
#ifdef __ANDROID__
	//MultiPassMode = 0;
#endif
	//MultiPassMode = 1;
	NullTexture = nullptr;
	dsd.oitOn = UseOIT;
#ifdef _DEBUG
	setDebugName("VkDriver");
#endif
	//g.driver->setTextureCreationFlag(ETCF_NO_GAMMA_CORRECT,true);
	printVersion();

	for (u32 i = 0; i < MATERIAL_MAX_TEXTURES; ++i)
	{
		CurrentTexture[i] = 0;
	}
#if USE_UNWORK
	// init clip planes
	ClipPlanes.push_back(core::plane3df());
	ClipPlanes.push_back(core::plane3df());
	ClipPlanes.push_back(core::plane3df());
	ClipPlaneEnabled[0] = ClipPlaneEnabled[1] = ClipPlaneEnabled[2] = false;

	// create sphere map matrix
	SphereMapMatrixD3D11 = DirectX::XMMATRIX(0.5f, 0.0f,
		0.0f, 0.0f,
		0.0f, 0.5f,
		0.0f, 0.0f,
		0.0f, 0.0f,
		1.0f, 0.0f,
		0.5f, 0.5f,
		0.0f, 1.0f);

	core::matrix4 mat;
	UnitMatrixD3D11 = DirectX::XMMATRIX(mat.pointer());
#endif
	md = (TMatrixData*)_aligned_malloc(sizeof(TMatrixData), 16);

	//_guiTexture=NULL;


	MrPickPoint.MaterialType = EMT_PICK_POINT;
 
	MrPickPointDrawPt.MaterialType = EMT_POINT_LIST_PICK_POINT;
 
}

VkDriver::~VkDriver()
{
	if (ssDstImg != VK_NULL_HANDLE) {
		vkFreeMemory(Device, ssDstImgMem, nullptr);
		vkDestroyImage(Device, ssDstImg, nullptr);
	}
	g_irrDrv11 = nullptr;
	_aligned_free(md);


#ifdef _DEBUG
	//ExposedData.D3D11.debug->Release();

#endif
// Unbound all shader resources
//ID3D11ShaderResourceView* views[1] = { NULL };
//ImmediateContext->VSSetShaderResources(0, 1, views);
//ImmediateContext->HSSetShaderResources(0, 1, views);
//ImmediateContext->DSSetShaderResources(0, 1, views);
//ImmediateContext->GSSetShaderResources(0, 1, views);
//ImmediateContext->PSSetShaderResources(0, 1, views);
	if (mCbOitCount) {
		mCbOitCount->drop();
		mCbOitData->drop();
	}
#ifdef _WIN32

	if (!g_pExtDevInfo)
	{
		// Set windowed mode before release swap chain

			//SwapChain->SetWindowedMode();
	}
#endif
	// Delete renderers and textures
	for (auto& thread : threadData) {
		vkFreeCommandBuffers(Device, thread.commandPool, thread.commandBuffer.size(), thread.commandBuffer.data());
		vkDestroyCommandPool(Device, thread.commandPool, nullptr);
	}

	deleteMaterialRenders();
	deleteAllTextures();
	removeAllHardwareBuffers();
	removeTexture(NullTexture); removeTexture(DepthProxyTexture);


	destroyAttachment(&fbaHdr);
	// clear state
	//if (ImmediateContext)
	//{
	//	//ImmediateContext->ClearState();
	//	ImmediateContext->Flush();
	//}

	// release Direct3D objects

	// clear vertex declarations
	DeclarationIterator it = declarationMap.getIterator();
	while (!it.atEnd())
	{
		it->getValue()->drop();
		it++;
	}
	declarationMap.clear();

	// release blend states

	delete DynIndexBuffer;
	delete DynVertexBuffer;
	//SAFE_RELEASE(_vb2D);
	//SAFE_RELEASE(_ib2D);

	//SAFE_RELEASE(Adapter);
	//SAFE_RELEASE(DXGIFactory);
	//SAFE_RELEASE(m_samplerBiLinear);
	//SAFE_RELEASE(m_samplerTriLinear);
	//SAFE_RELEASE(m_samplerPoint);


	if (!g_pExtDevInfo)
	{
		onRelease();

	}

}

void VkDriver::UpdateExternalDeviceInfo()
{
	//DP(("THis %pUPDExt %p %p", this, Device, g_pExtDevInfo->device));

	if (CreatePms.bUEPlugin) {
		if (Device != g_pExtDevInfo->device)
		{
			if (Device)
				throw;
			Device = g_pExtDevInfo->device; //Device->AddRef();
			physicalDevice = g_pExtDevInfo->physicalDevice;
			mDevice = new vks::VulkanDevice(physicalDevice);
			mDevice->logicalDevice = Device;
			mDevice->createLogicalDevice(enabledFeatures, enabledDeviceExtensions
				, false, true, 0
			);

			createPipelineCache();
		}
		RenderPass1 = RenderPass = g_pExtDevInfo->renderPass;
		_queueCompute = _queueRender = _queueCopy = g_pExtDevInfo->queue;
		_queueRenderT1 = _queueRender;


		mDevice->_commandPoolTransfer = mDevice->commandPool = cmdPool = cmdPoolCompute = g_pExtDevInfo->cmdPool;



		if (drawCmdBuffers.size() < 1)
			drawCmdBuffers.resize(3);
		currentBuffer = 0;
		drawCmdBuffers[currentBuffer] = g_pExtDevInfo->cmdBuf;
		//DefaultBackBuffer = g_pExtDevInfo->DefaultBackBuffer;
		//DefaultDepthBuffer = g_pExtDevInfo->DefaultDepthBuffer;
	}
	else {
		Device = g_pExtDevInfo->device;
		drawCmdBuffers[0] = g_pExtDevInfo->cmdBuf;
	}
	// Adjust screen size accordingly
	ScreenSize.Width = g_pExtDevInfo->getView().viewportWidth;
	ScreenSize.Height = g_pExtDevInfo->getView().viewportHeight;
	//DP(("Update Device %p  uid %p", Device, g_pExtDevInfo->Uid));

}

//! initialises the Direct3D API
bool VkDriver::initDriver(const core::dimension2d<u32>& screenSize,
	HWND hwnd, u32 bits, bool fullScreen, bool pureSoftware,
	bool highPrecisionFPU, bool vsync, u8 antiAlias)
{
	DP(("VkDriver::initDriver"));
	Fullscreen = fullScreen;
	//CurrentDepthBufferSize = screenSize;
	VSync = vsync;
	AntiAliasing = antiAlias;
	SwapChain.HDR_IDX = OffScreen?0:CreatePms.initHDR;

	isExtDev = (g_pExtDevInfo && !OffScreen);
	if (CreatePms.bUEPlugin && isExtDev) {
		UpdateExternalDeviceInfo();
		vkGetPhysicalDeviceProperties(physicalDevice, &deviceProperties);
		vkGetPhysicalDeviceFeatures(physicalDevice, &deviceFeatures);
		vkGetPhysicalDeviceMemoryProperties(physicalDevice, &deviceMemoryProperties);
		// Find a suitable depth format
		VkBool32 validDepthFormat = vks::tools::getSupportedDepthFormat(physicalDevice, &depthFormat);
		assert(validDepthFormat);
	}
	else
	{
		VkResult err;

		if (OffScreen) {
			instance = baseDriver->instance;
			physicalDevice = baseDriver->physicalDevice;
			deviceProperties = baseDriver->deviceProperties;
			deviceFeatures = baseDriver->deviceFeatures;
			deviceMemoryProperties = baseDriver->deviceMemoryProperties;

		}
		else if (isExtDev)
		{
			instance = g_pExtDevInfo->instance;
			physicalDevice = g_pExtDevInfo->physicalDevice;
			Device = g_pExtDevInfo->device;
			
			if (0)
			{
				// The report flags determine what type of messages for the layers will be displayed
				// For validating (debugging) an appplication the error and warning bits should suffice
				VkDebugReportFlagsEXT debugReportFlags = VK_DEBUG_REPORT_ERROR_BIT_EXT | VK_DEBUG_REPORT_WARNING_BIT_EXT;
				// Additional flags include performance info, loader and layer debug messages, etc.
				vks::debug::setupDebugging(instance, debugReportFlags, VK_NULL_HANDLE);
			}
		}
		else
		{

			bool validation = false;
#if defined(_DEBUG)

#if defined(_WIN32)
			validation = USE_VULKAN_VALIDATION;
#elif defined(__ANDROID__)
			//VK_LAYER_KHRONOS_validation
			validation=true;

#endif
#endif


#ifdef ENGINE_NO_GLUE
	//validation = false;
#endif

// Vulkan instance
			err = createInstance(validation);
			if (err) {
				vks::tools::exitFatal("Could not create Vulkan instance : \n" + vks::tools::errorString(err), err);
				return false;
			}

#if defined(VK_USE_PLATFORM_ANDROID_KHR)
			vks::android::loadVulkanFunctions(instance);
#endif

			// If requested, we enable the default validation layers for debugging
			if (validation)
			{
				// The report flags determine what type of messages for the layers will be displayed
				// For validating (debugging) an appplication the error and warning bits should suffice
				VkDebugReportFlagsEXT debugReportFlags = VK_DEBUG_REPORT_ERROR_BIT_EXT | VK_DEBUG_REPORT_WARNING_BIT_EXT;
				// Additional flags include performance info, loader and layer debug messages, etc.
				vks::debug::setupDebugging(instance, debugReportFlags, VK_NULL_HANDLE);
			}

			// Physical device
			uint32_t gpuCount = 0;
			// Get number of available physical devices
			VK_CHECK_RESULT(vkEnumeratePhysicalDevices(instance, &gpuCount, nullptr));
			assert(gpuCount > 0);
			// Enumerate devices
			std::vector<VkPhysicalDevice> physicalDevices(gpuCount);
			err = vkEnumeratePhysicalDevices(instance, &gpuCount, physicalDevices.data());
			if (err) {
				vks::tools::exitFatal("Could not enumerate physical devices : \n" + vks::tools::errorString(err), err);
				return false;
			}

			// GPU selection

			// Select physical device to be used for the Vulkan example
			// Defaults to the first device unless specified by command line
			uint32_t selectedDevice = 0;
#if defined(_WIN32) && USE_GRPHIC_CARD_1
			selectedDevice = 1;
#endif
#if 0 && !defined(VK_USE_PLATFORM_ANDROID_KHR)	
			// GPU selection via command line argument
			for (size_t i = 0; i < args.size(); i++)
			{
				// Select GPU
				if ((args[i] == std::string("-g")) || (args[i] == std::string("-gpu")))
				{
					char* endptr;
					uint32_t index = strtol(args[i + 1], &endptr, 10);
					if (endptr != args[i + 1])
					{
						if (index > gpuCount - 1)
						{
							std::cerr << "Selected device index " << index << " is out of range, reverting to device 0 (use -listgpus to show available Vulkan devices)" << std::endl;
						}
						else
						{
							std::cout << "Selected Vulkan device " << index << std::endl;
							selectedDevice = index;
						}
					};
					break;
				}
				// List available GPUs
				if (args[i] == std::string("-listgpus"))
				{
					uint32_t gpuCount = 0;
					VK_CHECK_RESULT(vkEnumeratePhysicalDevices(instance, &gpuCount, nullptr));
					if (gpuCount == 0)
					{
						std::cerr << "No Vulkan devices found!" << std::endl;
					}
					else
					{
						// Enumerate devices
						std::cout << "Available Vulkan devices" << std::endl;
						std::vector<VkPhysicalDevice> devices(gpuCount);
						VK_CHECK_RESULT(vkEnumeratePhysicalDevices(instance, &gpuCount, devices.data()));
						for (uint32_t i = 0; i < gpuCount; i++) {
							VkPhysicalDeviceProperties deviceProperties;
							vkGetPhysicalDeviceProperties(devices[i], &deviceProperties);
							std::cout << "Device [" << i << "] : " << deviceProperties.deviceName << std::endl;
							std::cout << " Type: " << vks::tools::physicalDeviceTypeString(deviceProperties.deviceType) << std::endl;
							std::cout << " API: " << (deviceProperties.apiVersion >> 22) << "." << ((deviceProperties.apiVersion >> 12) & 0x3ff) << "." << (deviceProperties.apiVersion & 0xfff) << std::endl;
						}
					}
				}
			}
#endif

			physicalDevice = physicalDevices[selectedDevice];

			// Store properties (including limits), features and memory properties of the phyiscal device (so that examples can check against them)
			vkGetPhysicalDeviceProperties(physicalDevice, &deviceProperties);
			vkGetPhysicalDeviceFeatures(physicalDevice, &deviceFeatures);
			vkGetPhysicalDeviceMemoryProperties(physicalDevice, &deviceMemoryProperties);
			{
				auto& lmt = deviceProperties.limits;
				DP(("LIMITS====================================\n"
					"maxUniformBufferRange=%d\nmaxStorageBufferRange=%d\n",
					lmt.maxUniformBufferRange, lmt.maxStorageBufferRange));
			}
			// Derived examples can override this to set actual features (based on above readings) to enable for logical device creation
			//NEED CAll back ? getEnabledFeatures();
			if (deviceFeatures.geometryShader) {
				//enabledFeatures.geometryShader = VK_TRUE;
			}
#if VK_ENABLE_NON_SOLID_FILL_MODE
			enabledFeatures.fillModeNonSolid = deviceFeatures.fillModeNonSolid;
#endif
			//#ifdef _WIN32
			DP(("deviceFeatures.vertexPipelineStoresAndAtomics %d", deviceFeatures.vertexPipelineStoresAndAtomics));
			//if (deviceFeatures.vertexPipelineStoresAndAtomics) 		enabledFeatures.vertexPipelineStoresAndAtomics = 1;
			enabledFeatures.samplerAnisotropy = deviceFeatures.samplerAnisotropy;
			if (UseOIT)
			enabledFeatures.fragmentStoresAndAtomics = deviceFeatures.fragmentStoresAndAtomics;
			//enabledFeatures.fillModeNonSolid = deviceFeatures.fillModeNonSolid;

			enabledDeviceExtensions.push_back(VK_KHR_MAINTENANCE_4_EXTENSION_NAME);

#if HAS_ARCORE_SHARETEX
			to do check
			enabledDeviceExtensions.push_back(VK_KHR_DEDICATED_ALLOCATION_EXTENSION_NAME);
			enabledDeviceExtensions.push_back(VK_KHR_SAMPLER_YCBCR_CONVERSION_EXTENSION_NAME);
			enabledDeviceExtensions.push_back(VK_EXT_QUEUE_FAMILY_FOREIGN_EXTENSION_NAME);
			enabledDeviceExtensions.push_back(VK_ANDROID_EXTERNAL_MEMORY_ANDROID_HARDWARE_BUFFER_EXTENSION_NAME);

#endif
			// Vulkan device creation
			// This is handled by a separate class that gets a logical device representation
			// and encapsulates functions related to a device

		}

		mDevice = new vks::VulkanDevice(physicalDevice);
		if (isExtDev)
			mDevice->logicalDevice = Device;
		if (OffScreen)
			mDevice->logicalDevice = baseDriver->mDevice->logicalDevice;
		else
			mDevice->ownLD = true;
		bool needMultiQue = false;
#ifdef _WIN32
		needMultiQue = true;
#endif
		VkResult res = mDevice->createLogicalDevice(enabledFeatures, enabledDeviceExtensions, true,
			isExtDev ? true : false,  needMultiQue);

		if (res != VK_SUCCESS) {
			vks::tools::exitFatal("Could not create Vulkan device: \n" + vks::tools::errorString(res), res);
			return false;
		}


		Device = mDevice->logicalDevice;
#ifdef _DEBUG
		vks::debugmarker::setup(Device);
#endif
		// Get a graphics queue from the device

		int queIdxAdd = 0;
		if (mDevice->isMultiQueue) {
			if (OffScreen) queIdxAdd = 2;  // Since 2 drivers created on the same VkDevice
		}
		else {
			if (OffScreen && !g_pExtDevInfo) queIdxAdd = 1;
		}
		vkGetDeviceQueue(Device, mDevice->queueFamilyIndices.graphics, 0 + queIdxAdd, &_queueRender);
#if !VKDRIVER_USE_DEDICATED_COMPUTE_QUEUE
		assert(mDevice->queueFamilyIndices.graphics == mDevice->queueFamilyIndices.compute);
		_queueCompute = _queueRender;
		cmdPoolCompute = mDevice->commandPool;
#endif

		if (mDevice->isMultiQueue) {
			if (mDevice->queueFamilyProperties[0].queueCount > 1 && !(OffScreen)) {
				vkGetDeviceQueue(Device, mDevice->queueFamilyIndices.graphics, 1 + queIdxAdd, &_queueRenderT1);
			}
			else
				_queueRenderT1 = _queueRender;
		}
		else {
			_queueRenderT1 = _queueRender;
		}
		//vkGetDeviceQueue(Device, mDevice->queueFamilyIndices.transfer, 0, &QueueCopy);
		_queueCopy = _queueRender;

		// Find a suitable depth format
		VkBool32 validDepthFormat = vks::tools::getSupportedDepthFormat(physicalDevice, &depthFormat);
		assert(validDepthFormat);

		//TODO SampleCount = VK_SAMPLE_COUNT_2_BIT;// getMaxUsableSampleCount();//VK_SAMPLE_COUNT_1_BIT
		if (isExtDev)
		{

		}
		else if (OffScreen)
		{
			SwapChain.queueNodeIndex = baseDriver->SwapChain.queueNodeIndex;
			hwnd = 0;
			isSystemRGBA = baseDriver->isSystemRGBA;
			SwapChain.instance = baseDriver->SwapChain.instance;
			SwapChain.physicalDevice = baseDriver->SwapChain.physicalDevice;
			SwapChain.device = Device;
			SwapChain.colorFormat = baseDriver->SwapChain.colorFormat;
		}
		else
		{
			SwapChain.connect(instance, physicalDevice, Device);


#if defined(VK_USE_PLATFORM_ANDROID_KHR)
			// Get Android device name and manufacturer (to display along GPU name)
			androidProduct = "";
			char prop[PROP_VALUE_MAX + 1];
			int len = __system_property_get("ro.product.manufacturer", prop);
			if (len > 0) {
				androidProduct += std::string(prop) + " ";
			};
			len = __system_property_get("ro.product.model", prop);
			if (len > 0) {
				androidProduct += std::string(prop);
			};
			LOGD("androidProduct = %s", androidProduct.c_str());
#endif	



			initSwapchain(hwnd);
			if (SwapChain.colorFormat == VK_FORMAT_R8G8B8A8_UNORM)
				isSystemRGBA = true;
		}


		//void createCommandPool()
		{
			VkCommandPoolCreateInfo cmdPoolInfo = {};
			cmdPoolInfo.sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO;
			cmdPoolInfo.queueFamilyIndex = mDevice->getQueueFamilyIndex(VK_QUEUE_GRAPHICS_BIT);
			cmdPoolInfo.flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT;
			VK_CHECK_RESULT(vkCreateCommandPool(Device, &cmdPoolInfo, nullptr, &cmdPool));
			//TODO: If need and when to use vkResetCommandPool ?  https://devblogs.nvidia.com/vulkan-dos-donts/
		}

		//void VulkanExampleBase::setupSwapChain()
		if (isExtDev)
		{
			if (!CreatePms.bUEPlugin) {
				ScreenSize.Width = g_pExtDevInfo->getView().viewportWidth;
				ScreenSize.Height = g_pExtDevInfo->getView().viewportHeight;
				SwapChain.imageCount = 2;
				SwapChain.colorFormat = VK_FORMAT_B8G8R8A8_UNORM;

				SampleCount = VK_SAMPLE_COUNT_8_BIT;
				depthFormat = VK_FORMAT_D24_UNORM_S8_UINT;
				DP(("ScreenSize init  %d,%d ", ScreenSize.Width, ScreenSize.Height));
				drawCmdBuffers.resize(2);
				drawCmdBuffers[0] = drawCmdBuffers[1] = g_pExtDevInfo->cmdBuf;
				//createCmdBufs();
				setupRenderPass(isExtDev);
				createAttachment(VK_FORMAT_R16G16B16A16_SFLOAT, VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT, &fbaHdr);
			}

			createPipelineCache();
			//setupFrameBuffer();
			prepareUI();
		}
		else if (OffScreen)
		{
			setupRenderPass(false);
			createPipelineCache();
		}
		else
		{
			DP(("ScreenSize init  %d,%d ", ScreenSize.Width, ScreenSize.Height));
			auto oldSize = ScreenSize;
			ScreenSize.set(0, 0);//should been created first time
			SwapChain.create(&ScreenSize.Width, &ScreenSize.Height, VSync);
			ColorFormat = getColorFormatFromDeviceFormat(SwapChain.colorFormat);
			DP(("ScreenSize set   %d,%d format=%d", ScreenSize.Width, ScreenSize.Height, ColorFormat));
			assert(oldSize == ScreenSize || oldSize.Width == 0 && oldSize.Height == 0);



			// Create synchronization objects
			VkSemaphoreCreateInfo semaphoreCreateInfo = vks::initializers::semaphoreCreateInfo();
			// Create a semaphore used to synchronize image presentation
			// Ensures that the image is displayed before we start submitting new commands to the queu
			VK_CHECK_RESULT(vkCreateSemaphore(Device, &semaphoreCreateInfo, nullptr, &semaphores.presentComplete));
			// Create a semaphore used to synchronize command submission
			// Ensures that the image is not presented until all commands have been sumbitted and executed
			VK_CHECK_RESULT(vkCreateSemaphore(Device, &semaphoreCreateInfo, nullptr, &semaphores.renderComplete));

			// Set up submit info structure
			// Semaphores will stay the same during application lifetime
			// Command buffer submission info is set by each example
			submitInfo = vks::initializers::submitInfo();
			submitInfo.pWaitDstStageMask = &submitPipelineStages;
			submitInfo.waitSemaphoreCount = 1;
			submitInfo.pWaitSemaphores = &semaphores.presentComplete;
			submitInfo.signalSemaphoreCount = 1;
			submitInfo.pSignalSemaphores = &semaphores.renderComplete;



			createCmdBufs();


			setupDepthStencil();
			setupRenderPass(false);;

			createPipelineCache();

			setupFrameBuffer();

			if (CreatePms.bAllowMultithreadRendering) prepareParaRenderRes();

#if 1
			prepareUI();
#else
			std::thread t([=]() {prepareUI(); });		    t.detach();
			//t.join();
#endif
		
#if VKR_OVERLAY
			settings.overlay = settings.overlay && (!benchmark.active);
			if (settings.overlay) {
				UIOverlay.device = vulkanDevice;
				UIOverlay.queue = queue;
				UIOverlay.shaders = {
					loadShader(getAssetPath() + "shaders/base/uioverlay.vert.spv", VK_SHADER_STAGE_VERTEX_BIT),
					loadShader(getAssetPath() + "shaders/base/uioverlay.frag.spv", VK_SHADER_STAGE_FRAGMENT_BIT),
				};
				UIOverlay.prepareResources();
				UIOverlay.preparePipeline(PipelineCache, renderPass);
		}
#endif
			if (UseOIT )
				recreateOitScrBuf();
				
		}

							//void createSynchronizationPrimitives()
		{
			// Wait fences to sync command buffer access
			VkFenceCreateInfo fenceCreateInfo = vks::initializers::fenceCreateInfo(VK_FENCE_CREATE_SIGNALED_BIT);
			waitFences.resize(drawCmdBuffers.size());
			for (auto& fence : waitFences) {
				VK_CHECK_RESULT(vkCreateFence(Device, &fenceCreateInfo, nullptr, &fence));
			}
		}
	}// !External



// register vertex types
	core::array<SVertexElement> el;
	el.push_back(SVertexElement(EVES_POSITION, EVET_FLOAT3, 0));
	el.push_back(SVertexElement(EVES_NORMAL, EVET_FLOAT3, 0));
	el.push_back(SVertexElement(EVES_COLOR, EVET_COLOUR, 0));
	el.push_back(SVertexElement(EVES_TEXTURE_COORD, EVET_FLOAT2, 0));
	registerVertexType(el);// 第一个 E_VERTEX_TYPE::EVT_STANDARD=0

	el.push_back(SVertexElement(EVES_TEXTURE_COORD, EVET_FLOAT2, 1));
	registerVertexType(el);// EVT_2TCOORDS

	el[4] = SVertexElement(EVES_TEXTURE_COORD, EVET_FLOAT3, 1);
	el.push_back(SVertexElement(EVES_TEXTURE_COORD, EVET_FLOAT3, 2));
	registerVertexType(el);


	// Init dynamic buffers
	reallocateDynamicBuffers(DVBSIZE * sizeof(S3DVertexTangents), DVBSIZE * sizeof(int));


	// With all DX 11 objects created, init driver states

	// Only enable multisampling if needed
	disableFeature(EVDF_TEXTURE_MULTISAMPLING, true);

 
	{
		// Create a texture that will be used when material textures are NULL
		// 1x1 texture, opaque White.
		u32 data = 0xffffffff;
		IImage* image = this->createImageFromData(ECF_A8R8G8B8, core::dimension2du(1, 1), &data, false);
		NullTexture = static_cast<VkTexture*>(this->addTexture("NullTexture", image));	// DO NOT DROP THIS POINTER (only drop with getTexture)
		DepthProxyTexture = static_cast<VkTexture*>(this->addTexture("NullTexture", image));
		image->drop();
	}
	// Set render targets
	//if (DefaultBackBuffer)	setRenderTarget(0, true, true);

	// set fog mode
	//setFog(FogColor, FogType, FogStart, FogEnd, FogDensity, PixelFog, RangeFog);
	setFog(FogColor, (E_FOG_TYPE)0, FogStart, FogEnd, FogDensity, PixelFog, RangeFog);

	ResetRenderStates = true;

	// create materials

	//if (!g_pExtDevInfo || !g_pExtDevInfo->bNoRenderer)

	createMaterialRenderers();

	// clear textures
	setActiveTexture(0, 0);
	setActiveTexture(1, 0);
	setActiveTexture(2, 0);
	setActiveTexture(3, 0);


	prepared = true;
	return true;
}



void VkDriver::createMaterialRenderers()
{
	texDummyRT = (irr::video::VkTexture*)addRenderTargetTexture(core::dimension2du(16, 16), "dummyRT",ColorFormat);
	//texDummyF16RT = (irr::video::VkTexture*)addRenderTargetTexture(core::dimension2du(16, 16), "dummyRT", ECF_A16B16G16R16F);
	VkMaterialRenderer_SOLID* solidRenderer = new VkMaterialRenderer_SOLID(this, FileSystem);
	
	solidRenderer->InitMaterialRenderer();
	addMaterialRenderer(solidRenderer);					//0	
	solidRenderer->mtCheck = EMT_SOLID;
	addAndDropMaterialRenderer(solidRenderer);			//SOLID


	{
		auto r = new VkMaterialRenderer_SOLID(this, FileSystem);
		r->mtCheck = EMT_SOLID_LINE_TRIANGLE_LIST;
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r);
	}
	{
		VkMaterialRenderer_PickPoint* r = new VkMaterialRenderer_PickPoint(this, FileSystem,false);
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r);
	}
	{
		VkMaterialRenderer_PickPoint* r = new VkMaterialRenderer_PickPoint(this, FileSystem, true);
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r);
	}
#if USE_IRR_OLD_MRS
	VkMaterialRenderer_SOLID_2_LAYER* mrs2l = new VkMaterialRenderer_SOLID_2_LAYER(this, FileSystem);
	mrs2l->InitMaterialRenderer();
	addAndDropMaterialRenderer(mrs2l); // video::EMT_SOLID_2_LAYER;


	VkMaterialRenderer_LIGHTMAP* lmr = new VkMaterialRenderer_LIGHTMAP(this, FileSystem);
	lmr->InitMaterialRenderer();
	addMaterialRenderer(lmr); // video::EMT_LIGHTMAP
	addMaterialRenderer(lmr); // video::EMT_LIGHTMAP_ADD
	addMaterialRenderer(lmr); // video::EMT_LIGHTMAP_M2
	addMaterialRenderer(lmr); // video::EMT_LIGHTMAP_M4
	addMaterialRenderer(lmr); // video::EMT_LIGHTMAP_LIGHTING
	addMaterialRenderer(lmr); // video::EMT_LIGHTMAP_LIGHTING_M2
	addMaterialRenderer(lmr); // video::EMT_LIGHTMAP_LIGHTING_M4
	lmr->drop();

	{
		auto r = new VkMaterialRenderer_DETAIL_MAP(this, FileSystem);
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // video::EMT_DETAIL_MAP
	}
	{
		auto r = new VkMaterialRenderer_SPHERE_MAP(this, FileSystem);
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // video::EMT_SPHERE_MAP
	}
	{
		auto r = new VkMaterialRenderer_REFLECTION_2_LAYER(this, FileSystem);
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // video::EMT_REFLECTION_2_LAYER
}
#else
	addMaterialRenderer(solidRenderer);
#endif

#if USE_IRR_OLD_MRS
	xxx
#else
	auto rtc = new VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL(this, FileSystem);
	rtc->InitMaterialRenderer();
	addAndDropMaterialRenderer(rtc); // video::EMT_TRANSPARENT_ALPHA_CHANNEL

	addMaterialRenderer(rtc);
	if (OffScreen) { addMaterialRenderer(rtc); }
	else {
		auto r = new VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL_NoDepthWrite(this, FileSystem);
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); 
	}
	
	if (OffScreen) { addMaterialRenderer(rtc); }
	else {auto r = new VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL_REF(this, FileSystem);
	r->mtCheck = EMT_TRANSPARENT_ALPHA_CHANNEL_BACKFACE;
	r->InitMaterialRenderer();
	addAndDropMaterialRenderer(r);  
	}
	if (OffScreen) { addMaterialRenderer(rtc); }
	else {auto r = new VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL_REF(this, FileSystem);
	r->mtCheck = EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES;
	r->InitMaterialRenderer();
	addAndDropMaterialRenderer(r);  
	}
	if (OffScreen) { addMaterialRenderer(rtc); }
	else {auto r = new VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL_REF(this, FileSystem);
	r->mtCheck = EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES_INSTANCING;
	r->delayInit = true;//r->InitMaterialRenderer();
	addAndDropMaterialRenderer(r);  
	}
	if (OffScreen) { addMaterialRenderer(rtc); }
	else {auto r = new VkMaterialRenderer_TRANSPARENT_ALPHA_CHANNEL_REF(this, FileSystem);
	r->mtCheck = EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES_INSTANCING_OIT;
	r->delayInit = true;//r->InitMaterialRenderer();
	addAndDropMaterialRenderer(r);  
	}
	if (OffScreen) { addMaterialRenderer(rtc); }
	else {auto r = new VkMaterialRenderer_SOLID(this, FileSystem);
	r->mtCheck = EMT_SOLID_2SIDES;
	r->InitMaterialRenderer();
	addAndDropMaterialRenderer(r);  
	}
	addMaterialRenderer(rtc);
#endif
	{
		addMaterialRenderer(solidRenderer);

		//auto r = new VkMaterialRenderer_TRANSPARENT_REFLECTION_2_LAYER(this, FileSystem);
		//r->InitMaterialRenderer();
		//addAndDropMaterialRenderer(r); // video::EMT_TRANSPARENT_REFLECTION_2_LAYER
	}
	// add normal map renderers
	s32 tmp = 0;
	video::IMaterialRenderer* matRenderer = 0;

#if USE_IRR_OLD_MRS
	matRenderer = new DGNormalMapRenderer(Device, this, FileSystem, tmp,
		MaterialRenderers[EMT_SOLID].Renderer);
	matRenderer->
		matRenderer->drop();

	matRenderer = new DGNormalMapRenderer(Device, this, FileSystem, tmp,
		MaterialRenderers[EMT_TRANSPARENT_ADD_COLOR].Renderer);
	matRenderer->drop();

	matRenderer = new DGNormalMapRenderer(Device, this, FileSystem, tmp,
		MaterialRenderers[EMT_TRANSPARENT_VERTEX_ALPHA].Renderer);
	matRenderer->drop();
#endif
	// The following shall be changed to parallax in future
	addMaterialRenderer(solidRenderer);
	addMaterialRenderer(solidRenderer);
	addMaterialRenderer(solidRenderer);


	addMaterialRenderer(solidRenderer);// new VkMaterialRenderer_ONETEXTURE_BLEND(this, FileSystem) ); // video::EMT_TRANSPARENT_REFLECTION_2_LAYER

	if (OffScreen) { addMaterialRenderer(solidRenderer); }
	else {
		auto r = new VkMaterialRenderer_PointCloud(this, FileSystem);
		r->mtCheck = EMT_LINE;
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // 
	}
	if (OffScreen) { addMaterialRenderer(solidRenderer); }
	else {
		auto r = new VkMaterialRenderer_PointCloud(this, FileSystem);
		r->mtCheck = EMT_POINT_CLOUD;
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // 
	}
	if (OffScreen) { addMaterialRenderer(solidRenderer); }
	else {
		auto r = new VkMaterialRenderer_WaterSurface(this, FileSystem);
		r->mtCheck = EMT_WATER_SURFACE;
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // 
	}
	if (OffScreen) { addMaterialRenderer(solidRenderer); }
	else {
		auto r = new VkMaterialRenderer_3D_UI(this, FileSystem);
		r->mtCheck = EMT_3D_UI;
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // 
	}
#if IRR_MTR_SDTOY
	if (OffScreen) { addMaterialRenderer(solidRenderer); }
	else	{
		auto r = new VkMaterialRenderer_ShaderToy(this, FileSystem);
		r->mtCheck = EMT_SHADER_TOY;
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // 
	}
#endif
#if IRR_MTR_SSAO
	if (OffScreen) { addMaterialRenderer(solidRenderer); }
	else	{
		auto r = new VkMaterialRenderer_SSAO(this, FileSystem);
		r->mtCheck = EMT_SSAO_GBUFFER;
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // 
	}
#endif
#if IRR_MTR_CUBEMAP
	if (OffScreen) { addMaterialRenderer(solidRenderer); }
	else {
		auto r = new VkMaterialRenderer_SOLID(this, FileSystem);
		r->mtCheck = EMT_CUBE_MAP;
		r->InitMaterialRenderer();
		addAndDropMaterialRenderer(r); // 
	}
#endif
#if IRR_MTR_MMD
	if (OffScreen) { addMaterialRenderer(solidRenderer); addMaterialRenderer(solidRenderer);	addMaterialRenderer(solidRenderer); addMaterialRenderer(solidRenderer);
	}
	else {
		{
			auto r = new VkMaterialRenderer_MMD(this, FileSystem);
			r->mtCheck = EMT_MMD;
			r->InitMaterialRenderer();
			addAndDropMaterialRenderer(r); 
		}
		{
			auto r = new VkMaterialRenderer_MMD(this, FileSystem);
			r->mtCheck = EMT_MMD_OIT;
			r->InitMaterialRenderer();
			addAndDropMaterialRenderer(r);
		}

		{
			auto r = new VkMaterialRenderer_MMD_EDGE(this, FileSystem);
			r->mtCheck = EMT_MMD_EDGE;
			r->InitMaterialRenderer();
			addAndDropMaterialRenderer(r); 
		}

		{
			auto r = new VkMaterialRenderer_MMD(this, FileSystem);
			r->mtCheck = EMT_MMD_PICK;
			r->InitMaterialRenderer();
			addAndDropMaterialRenderer(r); 
		}	
	}
#endif

#if VK_USE_2D_FX_RENDERER
	//FxId { Fx2D_Alpha, Fx2D_VertexAlpha, FxVertexColor, Fx2DNormalMap, FxModHSL, FxMod_RGB2HSL, FxMod_HSL2RGB };
	mt2DVertexAlpha = add2DMaterialType(Fx2DIdEnum::Fx2D_VertexAlpha, "Tech2DVertexAlpha");
	mt2DVertexColor = add2DMaterialType(Fx2DIdEnum::Fx2D_VertexColor, "Tech2DVertexColor");
	mt2DVertexColor2a = add2DMaterialType(Fx2DIdEnum::Fx2D_VertexColor2a, "Tech2DVertexColor");
	// 2DAlpha
	//FxVertexColor
	// ...
#endif
	solidRenderer->drop();

}

E_MATERIAL_TYPE VkDriver::get2DNativeMaterialType(Fx2DIdEnum fid)
{
	switch (fid)
	{
	case irr::video::Fx2D_VertexAlpha:			return mt2DVertexAlpha;
	case irr::video::Fx2D_Alpha:
		break;
	case irr::video::Fx2D_VertexColor:		return mt2DVertexColor;
	case irr::video::Fx2D_VertexColor2a:		return mt2DVertexColor2a;
 
	case irr::video::Fx2D_Blur:case irr::video::Fx2D_BlurHM:
		break;
	case irr::video::Fx2D_BlurV:
		break;
	case irr::video::Fx2D_BlurH:
		break;
	case irr::video::Fx2D_BlurToShadow:
		break;
	case irr::video::Fx2D_NormalMap:
		break;
	case irr::video::FxModHSL:
		break;
	case irr::video::FxMod_RGB2HSL:
		break;
	case irr::video::FxMod_HSL2RGB:
		break;

	case irr::video::Fx2D_HDR:
	{
		if (mt2DToneMapping == EMT_FORCE_32BIT)
			mt2DToneMapping = add2DMaterialType(fid, "Tech2DHDR");
		return mt2DToneMapping;
	}
	case irr::video::Fx2D_ToneMapping:
	{
		if (mt2DToneMapping == EMT_FORCE_32BIT)
			mt2DToneMapping = add2DMaterialType(fid, "Tech2DToneMapping");
		return mt2DToneMapping;
	}
	break;
	default:
		break;
	}
	throw "to add";
	return mt2DVertexAlpha;
}



//! applications must call this method before performing any rendering. returns false if failed.
bool VkDriver::beginScene(bool backBuffer, bool zBuffer, SColor color,
	const SExposedVideoData& videoData, core::rect<s32>* sourceRect)
{
	//DP(("B"));
	resetFrameCache();

	if (useLastFrame)
		copyLastFrame();


	dsd.frameCount++;

	SColorf sf(color);//sf.a=1.0f;
	VkClearColorValue vc{ { sf.r,sf.g,sf.b,sf.a } };

	CNullDriver::beginScene(backBuffer, zBuffer, color, videoData, sourceRect);



	VkCommandBuffer cmdBuf;

	if (isExtDev)
	{
		cmdBuf = drawCmdBuffers[currentBuffer];
		if (!CreatePms.bUEPlugin) 
		beginRenderPass(&vc);

	}
	else
	{

		// Acquire the next image from the swap chain
		VkResult err = SwapChain.acquireNextImage(semaphores.presentComplete, &currentBuffer);
		cmdBuf = drawCmdBuffers[currentBuffer];
		// Recreate the swapchain if it's no longer compatible with the surface (OUT_OF_DATE) or no longer optimal for presentation (SUBOPTIMAL)
		if ((err == VK_ERROR_OUT_OF_DATE_KHR) || (err == VK_SUBOPTIMAL_KHR)) {
			resetSwapchain();
		}
		else if (err!=VK_TIMEOUT){
			VK_CHECK_RESULT(err);
		}

#if USE_QUEUE_FENCE
		VK_CHECK_RESULT(vkWaitForFences(Device, 1, &waitFences[currentBuffer], VK_TRUE, UINT64_MAX));
		VK_CHECK_RESULT(vkResetFences(Device, 1, &waitFences[currentBuffer]));
#endif

		VkCommandBufferBeginInfo cmdBufInfo = vks::initializers::commandBufferBeginInfo();
		VK_CHECK_RESULT(vkBeginCommandBuffer(cmdBuf, &cmdBufInfo));



		//for (int i = 1; i < MaterialRenderers.size();i++)	MaterialRenderers[i].Renderer->OnBeginScene();


		beginRenderPass(&vc);
		}



#ifdef _WIN32
	if (isExtDev && CreatePms.bUEPlugin) {
		VkViewport viewport = { g_pExtDevInfo->getView().vx, (float)ScreenSize.Height,(float)ScreenSize.Width,
	-(float)ScreenSize.Height, 0.0f, 1.0f };	//VkViewport viewport = { 0.f, 0,(float)ScreenSize.Width, (float)ScreenSize.Height, 0.0f, 1.0f };
		vkCmdSetViewport(cmdBuf, 0, 1, &viewport);

		VkRect2D scissor = vks::initializers::rect2D(ScreenSize.Width, ScreenSize.Height, g_pExtDevInfo->getView().vx, 0);
		vkCmdSetScissor(cmdBuf, 0, 1, &scissor);
 
	}
	else
#endif
	{

		VkViewport viewport = { 0.f, (float)ScreenSize.Height,(float)ScreenSize.Width ,
	-(float)ScreenSize.Height, 0.0f, 1.0f };	//VkViewport viewport = { 0.f, 0,(float)ScreenSize.Width, (float)ScreenSize.Height, 0.0f, 1.0f };
		vkCmdSetViewport(cmdBuf, 0, 1, &viewport);

		VkRect2D scissor = vks::initializers::rect2D(ScreenSize.Width, ScreenSize.Height, 0, 0);
		vkCmdSetScissor(cmdBuf, 0, 1, &scissor);
		VkClearAttachment ClearAttachment[2] = {};
		ClearAttachment[0].aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
		ClearAttachment[0].colorAttachment = 0;
		ClearAttachment[0].clearValue.color = vc;
		ClearAttachment[1].aspectMask = VK_IMAGE_ASPECT_DEPTH_BIT;
		ClearAttachment[1].colorAttachment = 0;
		ClearAttachment[1].clearValue.depthStencil = { 1.0f,0 };
		VkClearRect ClearRect;
		ClearRect.rect = { {0, 0}, {ScreenSize.Width,  ScreenSize.Height} };
		ClearRect.baseArrayLayer = 0;
		ClearRect.layerCount = 1;
		if (ClearOnBegin || ClearCC > 0)		vkCmdClearAttachments(cmdBuf, 2, ClearAttachment, 1, &ClearRect);
		if (ClearCC > 0) ClearCC--;
	}
	//BeginRenderPass();
	//EndRenderPass();


#if 0
	if (backBuffer)
	{
		if (color.color == 0)
		{
			const float c[4] = { 0.0f,0.0f,0.0f,0.0f };
			ImmediateContext->ClearRenderTarget(nullptr, c, RESOURCE_STATE_TRANSITION_MODE_TRANSITION);
		}
		else
		{
			SColorf fCol(color);
			const float c[4] = { fCol.r, fCol.g, fCol.b, fCol.a };		// don't swizzle clear color for default back buffer
			ImmediateContext->ClearRenderTarget(nullptr, c, RESOURCE_STATE_TRANSITION_MODE_TRANSITION);
		}
	}
	if (zBuffer && DefaultDepthBuffer)
	{
		ImmediateContext->ClearDepthStencil(nullptr, CLEAR_DEPTH_FLAG, 1.f, 0, RESOURCE_STATE_TRANSITION_MODE_TRANSITION);
	}
	setRenderTarget(NULL, false, false);
#endif



	//// For safety, unset all shader resource views
	//// If some is bound to shader stages, can cause error when bound to Output Merger too
	//ID3D11ShaderResourceView* views[1] = { NULL };
	//ImmediateContext->VSSetShaderResources( 0, 1, views );
	//ImmediateContext->GSSetShaderResources( 0, 1, views );
	//ImmediateContext->PSSetShaderResources( 0, 1, views );
	return true;
}


void VkDriver::resetFrameCache()
{
	mVICache.Reset();
	vks::Buffer::FreeLostBuffers();
	MaterialRenderers[0].Renderer->cleanFrameCache();

	MaterialRenderers[(int)mt2DVertexAlpha].Renderer->cleanFrameCache();
	
}


void VkDriver::beginRenderPass(VkClearColorValue* clearColor)
{
	if (inRenderPass)
	{
		assert(0);
		return;
	}
	inRenderPass = true;
			
	if (isExtDev)
	{
		if (CreatePms.bUEPlugin) {
			fbExt = g_pExtDevInfo->frBuf;
			RenderPass1 = g_pExtDevInfo->renderPass;
			return;
		}
		else {
			if (fbExt != VK_NULL_HANDLE)
			{
				vkDestroyFramebuffer(Device, fbExt, 0);
				fbExt == VK_NULL_HANDLE;
			}
			depthStencil.view = g_pExtDevInfo->depthView;

			VkImageView attachmentViews[3];

			// Depth/Stencil attachment is the same for all frame buffers
			attachmentViews[1] = depthStencil.view;

			VkFramebufferCreateInfo frameBufferCreateInfo = {};
			frameBufferCreateInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
			frameBufferCreateInfo.pNext = NULL;
			frameBufferCreateInfo.renderPass = MultiPassMode > 0 ? RenderPass : RenderPass1;
			frameBufferCreateInfo.attachmentCount = MultiPassMode > 0 ? 3 : 2;
			frameBufferCreateInfo.pAttachments = attachmentViews;
			frameBufferCreateInfo.width = ScreenSize.Width;
			frameBufferCreateInfo.height = ScreenSize.Height;
			frameBufferCreateInfo.layers = 1;


			attachmentViews[0] = g_pExtDevInfo->colorView;
			attachmentViews[2] = fbaHdr.view;
			DP(("CFB %p %p %p", attachmentViews[0], attachmentViews[1], attachmentViews[2]));
			VK_CHECK_RESULT(vkCreateFramebuffer(Device, &frameBufferCreateInfo, nullptr, &fbExt));
		}
				
	}

	VkClearValue clearValues[3];

	clearValues[0].color = clearColor ? *clearColor : VkClearColorValue{ { 0.0f, 0.0f, 0.0f, 0.0f } };
	clearValues[1].depthStencil = { 1.0f, 0 };
	clearValues[2].color = clearColor ? *clearColor : VkClearColorValue{ { 0.0f, 0.0f, 0.0f, 0.0f } };

	VkRenderPassBeginInfo rpbi{};
	rpbi.sType = VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO;
	rpbi.renderPass = MultiPassMode > 0 ? RenderPass : RenderPass1;
	rpbi.renderArea.offset.x = 0;
	rpbi.renderArea.offset.y = 0;
	rpbi.renderArea.extent.width = ScreenSize.Width;
	rpbi.renderArea.extent.height = ScreenSize.Height;
	rpbi.clearValueCount = MultiPassMode > 0 ? 3 : 2;
	rpbi.pClearValues = clearValues;
	rpbi.framebuffer = isExtDev?fbExt:frameBuffers[currentBuffer];


	vkCmdBeginRenderPass(drawCmdBuffers[currentBuffer], &rpbi, VK_SUBPASS_CONTENTS_INLINE);
	curSubPass = 0;

}
void VkDriver::endRenderPass()
{



	assert(_currentRT == nullptr);
	if (!inRenderPass)
	{
		//assert(0);
		return;
	}
	//DP(("RENDER_PASS++++++++++++++++++++++")); DP(("RENDER_PASS-------------------"));

	//SwapChain.queuePresent(Queue,->Present( VSync ? 1 : 0 );
	vkCmdEndRenderPass(drawCmdBuffers[currentBuffer]);

			
	inRenderPass = false;
}

void VkDriver::beginPass(const PassParam& ppm)
{
	if (_currentRT)
		_currentRT->getVRP()->beginNewRenderPass(ppm.rpForMultiThread);
	else
		throw;
}

//! applications must call this method after performing any rendering. returns false if failed.
bool VkDriver::endScene()
{

	//assert(_currentRT==0);//GUI to End
	if (!CreatePms.bUEPlugin && MultiPassMode > 0)
	{

		vkCmdNextSubpass(drawCmdBuffers[currentBuffer], VK_SUBPASS_CONTENTS_INLINE);
		curSubPass = 1;

		video::SMaterial mr;
		mr.MaterialType = get2DNativeMaterialType(Fx2D_ToneMapping);
		if (useLastFrame)
			mr.setTexture(0, texFrameCopy);
		video::VkMr2D* mr2d = (video::VkMr2D*)getMaterialRenderer(mr.MaterialType);
		mr2d->cbDraw.tone.exposure = dsd.hdrExposure;
		mr2d->cbDraw.tone.gamma = dsd.hdrGamma;
		//mr2d->cbDraw.tone.lastFrameRatio = (useLastFrame && texFrameCopy) ? 0.95f : 0.f;
		mr2d->hdrView = fbaHdr.view;
		assert(getRenderTarget() == nullptr);
		draw2DSubPassMr(mr);

		if (cbOnPass1) cbOnPass1();
	}


	if (renderUI)
	drawUI(drawCmdBuffers[currentBuffer]);

	if (isExtDev && CreatePms.bUEPlugin)
	{
		UploadSharedBuffers();
		CNullDriver::endScene();
		return true;
	}

	if (inRenderPass)
		endRenderPass();


	//for (int i = 1; i < MaterialRenderers.size(); i++)	MaterialRenderers[i].Renderer->OnEndScene();


	//void VkDriver::submitFrame()

	if (isExtDev) {
		UploadSharedBuffers();
	}
	else
	{
		VK_CHECK_RESULT(vkEndCommandBuffer(drawCmdBuffers[currentBuffer]));

		//upload dynamic vertex/index data
		UploadSharedBuffers();



		// Command buffer to be sumitted to the queue
		submitInfo.commandBufferCount = 1;
		submitInfo.pCommandBuffers = &drawCmdBuffers[currentBuffer];
		// Submit to queue
#if USE_QUEUE_FENCE
		VK_CHECK_RESULT(vkQueueSubmit(queueRender(), 1, &submitInfo, waitFences[currentBuffer]));
		lastFrameBufferId = (int)currentBuffer;
#else
		VkResult resultx = VK_NOT_READY;
		resultx = vkQueueSubmit(queueRender(), 1, &submitInfo, VK_NULL_HANDLE);
		if (resultx != VK_SUCCESS)
		{
			//LOGE("Fatal : VkResult is \" %s \" in %s at line %d", vks::tools::errorString(resultx).c_str(), __FILE__, __LINE__);
			assert(resultx == VK_SUCCESS); throw;
		}
#endif
#if !PRESET_SPLIT
		if (isExtDev) {

		}
		else
		{
			bool bReset = false;
			//CPU_COUNT_B(da);
			VkResult res = SwapChain.queuePresent(queueRender(), currentBuffer, semaphores.renderComplete);
			//CPU_COUNT_E(da);
			if (!((res == VK_SUCCESS) || (res == VK_SUBOPTIMAL_KHR))) {
				if (res == VK_ERROR_OUT_OF_DATE_KHR) {
					DP(("XXX:   Present VK_ERROR_OUT_OF_DATE_KHR"));
					// Swap chain is no longer compatible with the surface and needs to be recreated
					//resetSwapchain();
					bReset = true;
				}
				else {
					VK_CHECK_RESULT(res);
				}
			}
			if (!bReset)
			{
#if USE_QUEUE_FENCE
				//VK_CHECK_RESULT(vkWaitForFences(Device, 1, &waitFences[currentBuffer], VK_TRUE, UINT64_MAX));
#else
				VK_CHECK_RESULT(vkQueueWaitIdle(queueRender()));
#endif
			}
		}
#endif

	}
#if !PRESET_SPLIT
	SwapChain.imagesRendered[currentBuffer] = true;
	static int cc = 0; cc++;
	//if (cc%30==0)	DP(("FPS------ %d", getFPS()));
	CNullDriver::endScene();
	frameCC++;
#endif
	return true;

}
int video::VkDriver::present()
{
#if PRESET_SPLIT

	if (isExtDev) {

	}
	else
	{
		bool bReset = false;
		//CPU_COUNT_B(da);
		VkResult res = SwapChain.queuePresent(queueRender(), currentBuffer, semaphores.renderComplete);
		//CPU_COUNT_E(da);
		if (!((res == VK_SUCCESS) || (res == VK_SUBOPTIMAL_KHR))) {
			if (res == VK_ERROR_OUT_OF_DATE_KHR) {
				DP(("XXX:   Present VK_ERROR_OUT_OF_DATE_KHR"));
				// Swap chain is no longer compatible with the surface and needs to be recreated
				//resetSwapchain();
				bReset = true;
		}
			else {
				VK_CHECK_RESULT(res);
			}
	}
		if (!bReset)
		{
#if USE_QUEUE_FENCE
			//VK_CHECK_RESULT(vkWaitForFences(Device, 1, &waitFences[currentBuffer], VK_TRUE, UINT64_MAX));
#else
			VK_CHECK_RESULT(vkQueueWaitIdle(queueRender()));
#endif
		}
	}
	SwapChain.imagesRendered[currentBuffer] = true;
	static int cc = 0; cc++;
	//if (cc%30==0)	DP(("FPS------ %d", getFPS()));
	CNullDriver::endScene();
	frameCC++;
#endif
	return 0;
}
void VkDriver::waitDriverIdle(u32 what)
{
	//VK_CHECK_RESULT(vkQueueWaitIdle(QueueRender));


	if (lastFrameBufferId >= 0)
	{
#if 0
		if (what & 0x10)
		{
			static int cc = 0, cct = 0; cc++;
			VkResult res = vkWaitForFences(Device, 1, &waitFences[lastFrameBufferId], VK_TRUE, 0);
			if (res == VK_TIMEOUT)
			{
				cct++;
				DP(("Fence Time Out %d/%d   FPS %d", cct, cc, getFPS()));
			}
		}
#endif
#if USE_QUEUE_FENCE
		VkResult res = vkWaitForFences(Device, 1, &waitFences[lastFrameBufferId], VK_TRUE, UINT64_MAX);
		VK_CHECK_RESULT(res);

#endif
}

	if (what & 1)
		vkDeviceWaitIdle(Device);
	if (what & 2)
		vkQueueWaitIdle(queueRender());
}

void VkDriver::UploadSharedBuffers()
{
	MaterialRenderers[(int)mt2DVertexAlpha].Renderer->preSubmit();
	MaterialRenderers[0].Renderer->preSubmit();
	{
		if (mVICache.mbV.curDataSize())
		{
			void* pv = DynVertexBuffer->lock();
			memcpy(pv, mVICache.mbV.CurBufPtr, mVICache.mbV.curDataSize());
			DynVertexBuffer->unlock();
		}


		if (mVICache.mbI.curDataSize())
		{
			void* pv = DynIndexBuffer->lock();
			memcpy(pv, mVICache.mbI.CurBufPtr, mVICache.mbI.curDataSize());
			DynIndexBuffer->unlock();
		}
	}
	waitAddedFences();
}

void VkDriver::draw2D3DVertexPrimitiveList(const void* vertices,
	u32 vertexCount, const void* indexList, u32 primitiveCount,
	E_VERTEX_TYPE vType, scene::E_PRIMITIVE_TYPE pType,
	E_INDEX_TYPE iType, bool is3D)
{

	bool bDraw = true;
	bool bExtMr = Material.MaterialType >= EMT_LAST;

	if (is3D)
		bDraw = setRenderStates3DMode(-1,vType);
	u32 indexCount = getIndexAmount(pType, primitiveCount);
	if (bDraw)
	{




		// copy vertices to dynamic buffers, if needed
		if (vertices || indexList)
			uploadVertexData(vertices, vertexCount, indexList, indexCount, vType, iType);
		//setRenderStates3DMode(vType);
		// finally, draw

	}

	if (pType == scene::EPT_POINTS || pType == scene::EPT_POINT_SPRITES || pType == scene::EPT_LINES)
	{

		vkCmdDraw(currentCmdBuffer(), vertexCount, 1, 0, 0);
	}
	else if (vertexCount == -1)
	{
		throw;
		//	ImmediateContext->DrawAuto();
	}
	else
	{
		if (indexCount)
		{
			vkCmdDrawIndexed(currentCmdBuffer(), indexCount, 1, 0, 0, 0);
		}
		else
			vkCmdDraw(currentCmdBuffer(), vertexCount, 1, 0, 0);
	}
	//DP(("Draw"));

	//endRenderPass();	beginRenderPass();
}



bool VkDriver::queryFeature(E_VIDEO_DRIVER_FEATURE feature) const
{
	switch (feature)
	{
	case EVDF_MULTIPLE_RENDER_TARGETS:

		return true; // mDevCaps.mmul == D3D_DRIVER_TYPE_HARDWARE;

	case EVDF_MRT_BLEND:
	case EVDF_ALPHA_TO_COVERAGE:
	case EVDF_MRT_BLEND_FUNC:
	case EVDF_TEXTURE_NSQUARE:
	case EVDF_VERTEX_BUFFER_OBJECT:
	case EVDF_COLOR_MASK:
	case EVDF_RENDER_TO_TARGET:
	case EVDF_MULTITEXTURE:
	case EVDF_BILINEAR_FILTER:
	case EVDF_TEXTURE_NPOT:
	case EVDF_STENCIL_BUFFER:
	case EVDF_HLSL:
	case EVDF_MIP_MAP:
	case EVDF_MIP_MAP_AUTO_UPDATE:
	case EVDF_TEXTURE_MULTISAMPLING:
		return true;

	case EVDF_VERTEX_SHADER_4_0:
	case EVDF_PIXEL_SHADER_4_0:
	case EVDF_GEOMETRY_SHADER_4_0:
	case EVDF_VERTEX_SHADER_4_1:
	case EVDF_PIXEL_SHADER_4_1:
	case EVDF_GEOMETRY_SHADER_4_1:
	case EVDF_VERTEX_SHADER_5_0:
	case EVDF_PIXEL_SHADER_5_0:
	case EVDF_GEOMETRY_SHADER_5_0:
		return deviceFeatures.geometryShader;
	case EVDF_STREAM_OUTPUT_4_0:
	case EVDF_STREAM_OUTPUT_4_1:
	case EVDF_STREAM_OUTPUT_5_0:
		assert(0);
		return false;// deviceFeatures.feedback

	case EVDF_TESSELATION_SHADER_5_0:
		return deviceFeatures.tessellationShader;

	case EVDF_COMPUTING_SHADER_4_0:
	case EVDF_COMPUTING_SHADER_4_1:
	case EVDF_COMPUTING_SHADER_5_0:
		return true;
	}

	return false;
}

//! sets transformation
void VkDriver::setTransform(E_TRANSFORMATION_STATE state, const core::matrix4& mat)
{
	Transformation3DChanged = true;
	Matrices[state] = mat;

	assert(0 == ((uintptr_t)md & 0xF));


	if (state == ETS_VIEW)
	{

		md->xmV = *(float4x4*)mat.pointer();
		md->xmVt = DRV_TransposeMatrix(md->xmV);
		//md->xmVit = DRV_TransposeMatrix(glm::inverse(md->xmV));

// 		XMMATRIX m(mat.pointer());
// 		XMVECTOR outScale,outRotQuat,outTrans;
// 		this->setVertexShaderConstant(nullptr)
// 
// 			bool br=XMMatrixDecompose(&outScale,&outRotQuat,&outTrans,m);
// 		XMFLOAT3 f3(-mat.getTranslation().X,-mat.getTranslation().Y,-mat.getTranslation().Z);
// 
// 		XMVECTOR pos= XMLoadFloat3(&f3);
// 		m=XMMatrixTranspose(m);
// 		XMVECTOR pos2=XMVector2TransformNormal(pos,m);
// 		pos=pos2;
		_bUpdatePerFrame = true;
	}
	if (state == ETS_PROJECTION)
	{

		md->xmP = *(float4x4*)(mat.pointer());
		md->xmPt = DRV_TransposeMatrix(md->xmP);

		_bUpdatePerFrame = true;
	}

}

bool VkDriver::setActiveTexture(u32 stage, const video::ITexture* texture)
{
	if (texture && texture->getDriverType() != EDT_VK)
	{
		IRRLOG(("Fatal Error: Tried to set a texture not owned by this driver.", ELL_ERROR));
		return false;
	}

	// For first stage, set NullTexture if texture is NULL

	CurrentTexture[stage] = texture;//?texture:NullTexture;

	return true;
}

//! sets a material
void VkDriver::setMaterial(const SMaterial& material)
{
	Material = material;
	if (Material.AutoCullSwithInMirror && curPassType == IrrPassType_Mirror) {
		switch (Material.MaterialType)
		{ 
		case EMT_TRANSPARENT_ALPHA_CHANNEL: Material.MaterialType = EMT_TRANSPARENT_ALPHA_CHANNEL_BACKFACE; break;
		case EMT_TRANSPARENT_ALPHA_CHANNEL_BACKFACE: Material.MaterialType = EMT_TRANSPARENT_ALPHA_CHANNEL; break;
		}
	}
	OverrideMaterial.apply(Material);

	// set textures
	for (u16 i = 0; i < MATERIAL_MAX_TEXTURES; i++)
	{
		ITexture* texture = Material.getTexture(i);

		if (i == 0 && !texture && material.MaterialType < EMT_LAST)   // TexArray (particle) no need 
			texture = NullTexture;

		setActiveTexture(i, texture);
#if USE_TEX_MATRIX
		setTransform((E_TRANSFORMATION_STATE)(ETS_TEXTURE_0 + i), material.getTextureMatrix(i));
#endif
	}
}


VK_EXTERNAL_DEVICE_INFO* VkDriver::getExtDevInfo()
{
	return g_pExtDevInfo;
}

void VkDriver::frameViewReset()
{
	if (g_pExtDevInfo)
		g_pExtDevInfo->viewId = 0;
	dsd.noUpdateOnlyDraw = false;
}

bool VkDriver::frameViewNext()
{
	if (g_pExtDevInfo && g_pExtDevInfo->viewId == 0 && g_pExtDevInfo->viewCount == 2)
	{
		g_pExtDevInfo->viewId = 1;
		dsd.noUpdateOnlyDraw = true;
		return true;
	}
	return false;
}

void VkDriver::flush()
{
	if (_currentRT)
		setRenderTarget(nullptr, false, false, 0);
	else
	{
		//may be  not need flush 
		assert(0);
	}
}

ITexture* VkDriver::getRenderTarget()
{
	return _currentRT;
}

bool VkDriver::setRenderTarget(video::ITexture* texture,
	bool clearBackBuffer, bool clearZBuffer, SColor color,bool multiThreadRender)
{
	//DP(("RT ==> %p",texture));
	if (texture && texture->getDriverType() != EDT_VK) { IRRLOG(("Fatal Error: Tried to set a texture not owned by this driver.", ELL_ERROR)); return false; }
	if (texture && !texture->isRenderTarget()) { IRRLOG(("Fatal Error: Tried to set a non render target texture as render target.", ELL_ERROR));	return false; }
#ifdef _DEBUG
	if (!isExtDev) {
		//DRIVER_THREAD_CHECK;
	}
#endif
	VkTexture* tex = static_cast<VkTexture*>(texture);

	if (_currentRT != tex)
	{
		if (_currentRT)
		{
			//DP(("END PASS+ of RT %p of %p", _currentRT, OffScreen ?  this : nullptr));
			_currentRT->getVRP()->cmdEnd();
			//DP(("END PASS- of RT %p of %p", _currentRT, OffScreen ? this : nullptr));
		}
		else
		{
			//endRenderPass();
		}
		if (tex == nullptr)
		{

		}
	}

	if (tex)
	{

		CurrentRendertargetSize = tex->getSize();
		tex->getVRP()->cmdBegin(clearBackBuffer, clearZBuffer, color, multiThreadRender);

	}
	else
	{
		//beginRenderPass();
		CurrentRendertargetSize = core::dimension2d<u32>(0, 0);//screen
	}


	_currentRT = tex;

	// clear depth
	if (clearZBuffer)
		this->clearZBuffer();

	// don't forget to set viewport
	//setViewPort(core::rect<s32>(0, 0, getCurrentRenderTargetSize().Width, getCurrentRenderTargetSize().Height));
	//DP(("setRT %p %d,%d ",texture,getCurrentRenderTargetSize().Width,getCurrentRenderTargetSize().Height));
	return true;
}

bool VkDriver::setRenderTarget(const core::array<video::IRenderTarget>& targets,
	bool clearBackBuffer, bool clearZBuffer, SColor color)
{
	throw "not support";
#if 0
	u32 i;
	// If no targets, set default render target
	if (targets.size() == 0)
	{
		// set default render target
		return setRenderTarget(0, clearBackBuffer, clearZBuffer, color);
	}

	// max number of render targets is 8 for DX11 feature level
	u32 maxMultipleRTTs = core::min_(8u, targets.size());

	// validation
	for (i = 0; i < maxMultipleRTTs; ++i)
	{
		if (targets[i].TargetType != ERT_RENDER_TEXTURE || !targets[i].RenderTexture)
		{
			maxMultipleRTTs = i;
			IRRLOG(("Missing texture for MRT.", ELL_WARNING));
			break;
		}

		// check for right driver type
		if (targets[i].RenderTexture->getDriverType() != EDT_VK)
		{
			maxMultipleRTTs = i;
			IRRLOG(("Tried to set a texture not owned by this driver.", ELL_WARNING));
			break;
		}

		// check for valid render target
		if (!targets[i].RenderTexture->isRenderTarget())
		{
			maxMultipleRTTs = i;
			IRRLOG(("Tried to set a non render target texture as render target.", ELL_WARNING));
			break;
		}

		// check for valid size
		if (targets[0].RenderTexture->getSize() != targets[i].RenderTexture->getSize())
		{
			maxMultipleRTTs = i;
			IRRLOG(("Render target texture has wrong size.", ELL_WARNING));
			break;
		}
	}
	if (maxMultipleRTTs == 0)
	{
		IRRLOG(("Fatal Error: No valid MRT found.", ELL_ERROR));
		return false;
	}

	VkTexture* tex = static_cast<VkTexture*>(targets[0].RenderTexture);

	ID3D11RenderTargetView* RTViews[D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT];

	// parse clear color
	SColorf fCol(color);
	// swizzle clear color is texture is passed
	// i.e.: is is default render target, don't swizzle
	FLOAT c[4] = { tex == 0 ? fCol.r : fCol.b,
					fCol.g,
					tex == 0 ? fCol.b : fCol.r,
					fCol.a };

	// zero blend description
	::ZeroMemory(&BlendDesc, sizeof(BlendDesc));
	BlendDesc.IndependentBlendEnable = TRUE;

	// set blend based on render target configuration
	// set source blend
	for (i = 0; i < maxMultipleRTTs; i++)
	{
		E_BLEND_FACTOR blendFac = targets[i].BlendFuncSrc;

		BlendDesc.RenderTarget[i].SrcBlend = BlendDesc.RenderTarget[i].SrcBlendAlpha =
			blendFac == EBF_ZERO ? D3D11_BLEND_ZERO :
			blendFac == EBF_ONE ? D3D11_BLEND_ONE :
			blendFac == EBF_DST_COLOR ? D3D11_BLEND_DEST_COLOR :
			blendFac == EBF_ONE_MINUS_DST_COLOR ? D3D11_BLEND_INV_DEST_COLOR :
			blendFac == EBF_SRC_COLOR ? D3D11_BLEND_SRC_COLOR :
			blendFac == EBF_ONE_MINUS_SRC_COLOR ? D3D11_BLEND_INV_SRC_COLOR :
			blendFac == EBF_SRC_ALPHA ? D3D11_BLEND_SRC_ALPHA :
			blendFac == EBF_ONE_MINUS_SRC_ALPHA ? D3D11_BLEND_INV_SRC_ALPHA :
			blendFac == EBF_DST_ALPHA ? D3D11_BLEND_DEST_ALPHA :
			blendFac == EBF_ONE_MINUS_DST_ALPHA ? D3D11_BLEND_INV_DEST_ALPHA :
			D3D11_BLEND_SRC_ALPHA_SAT;

		// set destination blend
		blendFac = targets[i].BlendFuncDst;
		BlendDesc.RenderTarget[i].DestBlend = BlendDesc.RenderTarget[i].DestBlendAlpha =
			blendFac == EBF_ZERO ? D3D11_BLEND_ZERO :
			blendFac == EBF_ONE ? D3D11_BLEND_ONE :
			blendFac == EBF_DST_COLOR ? D3D11_BLEND_DEST_COLOR :
			blendFac == EBF_ONE_MINUS_DST_COLOR ? D3D11_BLEND_INV_DEST_COLOR :
			blendFac == EBF_SRC_COLOR ? D3D11_BLEND_SRC_COLOR :
			blendFac == EBF_ONE_MINUS_SRC_COLOR ? D3D11_BLEND_INV_SRC_COLOR :
			blendFac == EBF_SRC_ALPHA ? D3D11_BLEND_SRC_ALPHA :
			blendFac == EBF_ONE_MINUS_SRC_ALPHA ? D3D11_BLEND_INV_SRC_ALPHA :
			blendFac == EBF_DST_ALPHA ? D3D11_BLEND_DEST_ALPHA :
			blendFac == EBF_ONE_MINUS_DST_ALPHA ? D3D11_BLEND_INV_DEST_ALPHA :
			D3D11_BLEND_SRC_ALPHA_SAT;

		// set blend operation
		BlendDesc.RenderTarget[i].BlendOp = D3D11_BLEND_OP_ADD;
		BlendDesc.RenderTarget[i].BlendOpAlpha = D3D11_BLEND_OP_ADD;
		BlendDesc.RenderTarget[i].BlendEnable = targets[i].BlendOp != EBO_NONE;

		// set blend based on render target information
		if (targets[i].BlendOp == EBO_NONE)
		{
			// set color mask
			BlendDesc.RenderTarget[i].RenderTargetWriteMask = getColorWriteEnable(targets[i].ColorMask);
		}

		// store render target view in array
		RTViews[i] = static_cast<VkTexture*>(targets[i].RenderTexture)->getRenderTargetView();

		// clear render target view
		if (clearBackBuffer)
			ImmediateContext->ClearRenderTargetView(RTViews[i], c);
	}

	// set depth buffer
	CurrentDepthBuffer = tex->DepthSurface->Surface;
	if (clearZBuffer)
		this->clearZBuffer();

	// set targets
	ImmediateContext->OMSetRenderTargets(maxMultipleRTTs, RTViews, CurrentDepthBuffer);

	// don't forget to set viewport
	CurrentRendertargetSize = tex->getSize();
	setViewPort(core::rect<s32>(0, 0, getCurrentRenderTargetSize().Width, getCurrentRenderTargetSize().Height));

#endif
	return true;
}

void VkDriver::setViewPort(const core::rect<s32>& area)
{
	throw;
	VkCommandBuffer cmdbuf = 0;
	core::rect<s32> vp = area;
	core::rect<s32> rendert(0, 0, getCurrentRenderTargetSize().Width, getCurrentRenderTargetSize().Height);
	vp.clipAgainst(rendert);

	// Update dynamic viewport state
	//for (int32_t i = 0; i < drawCmdBuffers.size(); ++i)
	{
		VkViewport viewport = {};
		viewport.width = (float)vp.getWidth();
		viewport.height = -(float)vp.getHeight();
		viewport.minDepth = (float)0.0f;
		viewport.maxDepth = (float)1.0f;
		vkCmdSetViewport(cmdbuf, 0, 1, &viewport);
	}
	//Diligent::Viewport viewPort;
	//viewPort.TopLeftX = (FLOAT)vp.UpperLeftCorner.X;
	//viewPort.TopLeftY = (FLOAT)vp.UpperLeftCorner.Y;
	//viewPort.Width = (FLOAT)vp.getWidth();
	//viewPort.Height = (FLOAT)vp.getHeight();
	//viewPort.MinDepth = 0.0f;
	//viewPort.MaxDepth = 1.0f;
	//ImmediateContext->SetViewports(1, &viewPort, 0, 0);

	this->ViewPort = vp;
}

const core::rect<s32>& VkDriver::getViewPort() const
{
	return ViewPort;
}




































































bool VkDriver::updateVertexHardwareBuffer(SHWBufferLink_VK* hwBuffer)
{
	if (!hwBuffer)
		return false;


	const scene::IMeshBuffer* mb = hwBuffer->MeshBuffer;
	const void* vertices = mb->getVertices();
	const u32 vertexCount = mb->getVertexCount();
	const E_VERTEX_TYPE vType = mb->getVertexType();
	const u32 vertexSize = getVertexPitchFromType(vType);
	u32 bufSize = vertexSize * vertexCount;
	// if vertex buffer doesn't exists OR 
	// number of vertex to update is bigger than current
	if (!hwBuffer->vertexBuffer || bufSize > hwBuffer->vertexBufferSize)
	{
		// Release buffer if need to expand
		if (bufSize > hwBuffer->vertexBufferSize)
		{
			delete  hwBuffer->vertexBuffer;
		}

		testHWBUFVtx = hwBuffer->vertexBuffer = (VkHardwareBuffer*)createHardwareBuffer(EHBT_VERTEX, EHBA_DEFAULT, bufSize, 0, vertices);

		if (!hwBuffer->vertexBuffer)
			throw;

		hwBuffer->vertexBufferSize = bufSize;
	}
	else // just update
	{
		void* pv = hwBuffer->vertexBuffer->lock();
		memcpy(pv, vertices, bufSize);
		hwBuffer->vertexBuffer->unlock();
	}

	return true;
}


bool VkDriver::updateIndexHardwareBuffer(SHWBufferLink_VK* hwBuffer)
{
	if (!hwBuffer)
		return false;

	const scene::IMeshBuffer* mb = hwBuffer->MeshBuffer;
	const u16* indices = mb->getIndices();
	const u32 indexCount = mb->getIndexCount();
	const u32 indexSize = mb->getIndexType() == EIT_16BIT ? 2 : 4;
	u32 bufSize = indexSize * indexCount;
	if (bufSize>0)
	if (!hwBuffer->indexBuffer || bufSize > hwBuffer->indexBufferSize)
	{
		// Release buffer if need to expand
		if (bufSize > hwBuffer->indexBufferSize)
		{
			delete hwBuffer->indexBuffer;
		}

		testHWBUFIdx = hwBuffer->indexBuffer = (VkHardwareBuffer*)createHardwareBuffer(EHBT_INDEX, EHBA_DEFAULT, bufSize, 0, indices);
		//      delete hwBuffer->indexBuffer;
				//hwBuffer->indexBuffer = new VkHardwareBuffer(this, EHBT_INDEX, EHBA_DEFAULT, bufSize, 0, indices);
		//      delete hwBuffer->indexBuffer;
				//hwBuffer->indexBuffer = new VkHardwareBuffer(this, EHBT_INDEX, EHBA_DEFAULT, bufSize, 0, indices);
		if (!hwBuffer->indexBuffer)
			throw;

		hwBuffer->indexBufferSize = bufSize;
	}
	else // just update 
	{
		void* pv = hwBuffer->indexBuffer->lock();
		memcpy(pv, indices, bufSize);
		hwBuffer->indexBuffer->unlock();
	}

	return true;
}

bool VkDriver::updateHardwareBuffer(SHWBufferLink* hwBuffer)
{
	if (!hwBuffer)
		return false;

	SHWBufferLink_VK* dx11Link = static_cast<SHWBufferLink_VK*>(hwBuffer);

	// update vertex buffer
	if (dx11Link->ChangedID_Vertex != dx11Link->MeshBuffer->getChangedID_Vertex() ||
		!dx11Link->vertexBuffer)
	{
		dx11Link->ChangedID_Vertex = dx11Link->MeshBuffer->getChangedID_Vertex();

		if (!updateVertexHardwareBuffer(dx11Link))
			return false;
	}

	// update index buffer
	if (dx11Link->ChangedID_Index != dx11Link->MeshBuffer->getChangedID_Index() ||
		!dx11Link->indexBuffer)
	{
		dx11Link->ChangedID_Index = dx11Link->MeshBuffer->getChangedID_Index();

		if (!updateIndexHardwareBuffer(dx11Link))
			return false;
	}

	return true;
}

VkDriver::SHWBufferLink* VkDriver::createHardwareBuffer(const scene::IMeshBuffer* mb)
{
	// DirectX 11 ALWAYS uses hardware buffer
	SHWBufferLink_VK* hwBuffer = new SHWBufferLink_VK(mb);

	//add to map
	HWBufferMap.insert(hwBuffer->MeshBuffer, hwBuffer);

	hwBuffer->ChangedID_Vertex = hwBuffer->MeshBuffer->getChangedID_Vertex();
	hwBuffer->ChangedID_Index = hwBuffer->MeshBuffer->getChangedID_Index();
	hwBuffer->Mapped_Vertex = mb->getHardwareMappingHint_Vertex();
	hwBuffer->Mapped_Index = mb->getHardwareMappingHint_Index();
	hwBuffer->LastUsed = 0;

	if (!updateHardwareBuffer(hwBuffer))
	{
		deleteHardwareBuffer(hwBuffer);
		return 0;
	}

	return hwBuffer;
}

void VkDriver::deleteHardwareBuffer(SHWBufferLink* _HWBuffer)
{
	if (!_HWBuffer) return;

	SHWBufferLink_VK* HWBuffer = static_cast<SHWBufferLink_VK*>(_HWBuffer);

	delete HWBuffer->indexBuffer;
	delete HWBuffer->vertexBuffer;
	//delete HWBuffer->vertexBuffer2;
	HWBuffer->indexBuffer = 0;
	HWBuffer->vertexBuffer = 0;
	//HWBuffer->vertexBuffer2 = 0;

	CNullDriver::deleteHardwareBuffer(_HWBuffer);
}

void VkDriver::drawHardwareBuffer(SHWBufferLink* _HWBuffer)
{
	if (!_HWBuffer)
		return;


	SHWBufferLink_VK* HWBuffer = static_cast<SHWBufferLink_VK*>(_HWBuffer);

	updateHardwareBuffer(HWBuffer);		//check if update is needed

	HWBuffer->LastUsed = 0;				//reset count

	const scene::IMeshBuffer* mb = HWBuffer->MeshBuffer;
	const E_VERTEX_TYPE vType = mb->getVertexType();
	const u32 stride = getVertexPitchFromType(vType);
	UINT offset = 0;


	// Draw indexed triangle

	//drawHardwareBuffer(HWBuffer->vertexBuffer, HWBuffer->indexBuffer, mb->getVertexType(), scene::EPT_TRIANGLES, mb->getIndexType());
	//return;

	// Bind triangle vertex buffer (contains position and colors)
	VkDeviceSize offsets[1] = { 0 };
	VkBuffer buf[1] = { HWBuffer->vertexBuffer->getBufferResource() };
	vkCmdBindVertexBuffers(currentCmdBuffer(), 0, 1, buf, offsets);
	
	// Bind triangle index buffer
	if (HWBuffer->indexBuffer) vkCmdBindIndexBuffer(currentCmdBuffer(), HWBuffer->indexBuffer->getBufferResource(), 0, (mb->getIndexType() == video::EIT_16BIT) ? VK_INDEX_TYPE_UINT16 : VK_INDEX_TYPE_UINT32);


	drawVertexPrimitiveList(0, mb->getVertexCount(), 0, mb->getIndexCount() / 3, mb->getVertexType(), scene::EPT_TRIANGLES, mb->getIndexType());

	// unset buffers
	//buffers[0] = NULL;
	//ImmediateContext->IASetVertexBuffers( 0, 1, buffers, &stride, &offset );
	//ImmediateContext->IASetIndexBuffer( NULL, DXGI_FORMAT_R16_UINT, 0 );

}

void VkDriver::drawHardwareBufferParallel(int paraId,
	IHardwareBuffer* vertices,	IHardwareBuffer* indices, E_VERTEX_TYPE vType,
	scene::E_PRIMITIVE_TYPE pType, E_INDEX_TYPE iType, u32 numInstances, u32 numDraw, u32 firstIdx
)
{
	CNullDriver::drawHardwareBuffer(vertices, indices, vType, pType, iType, numInstances, numDraw, firstIdx);
	drawHbInternal(vertices, indices, vType, iType, numInstances, numDraw, firstIdx,paraId);
}

void VkDriver::drawInstancedHardwareBuffer(IHardwareBuffer* vertices, IHardwareBuffer* indices, IHardwareBuffer* instanceBuffer, E_VERTEX_TYPE vType, scene::E_PRIMITIVE_TYPE primType, E_INDEX_TYPE iType, u32 instanceCount, u32 indexCount, u32 startIndex)
{
	CNullDriver::drawHardwareBuffer(vertices, indices, vType, primType, iType, instanceCount, indexCount, startIndex);
	DeclarationNode declNode = declarationMap.find(vType);
	if (!declNode)
	{
		IRRLOG(("Error, vertex type not registered", ELL_ERROR));
		return;
	}

	// Set render states
	if (!setRenderStates3DMode(-1, vType))
		return;

	if (!vertices || !indices || !instanceBuffer)
		return;

	auto commandBuffer = currentCmdBuffer();

	// Bind vertex and instance buffers
	VkBuffer vertexBuffers[2] = {
		static_cast<VkHardwareBuffer*>(vertices)->getBufferResource(),
		static_cast<VkHardwareBuffer*>(instanceBuffer)->getBufferResource()
	};
	VkDeviceSize offsets[2] = { 0, 0 };
	vkCmdBindVertexBuffers(commandBuffer, 0, 2, vertexBuffers, offsets);

	// Bind index buffer
	vkCmdBindIndexBuffer(commandBuffer, static_cast<VkHardwareBuffer*>(indices)->getBufferResource(), 0,
		iType == EIT_32BIT ? VK_INDEX_TYPE_UINT32 : VK_INDEX_TYPE_UINT16);

	// Draw instanced
	vkCmdDrawIndexed(commandBuffer, indexCount, instanceCount, startIndex, 0, 0);
}


void VkDriver::drawHardwareBuffer(
IHardwareBuffer* vertices, IHardwareBuffer* indices, E_VERTEX_TYPE vType,
scene::E_PRIMITIVE_TYPE pType, E_INDEX_TYPE iType, u32 numInstances, u32 numDraw, u32 firstIdx 
)
{
	CNullDriver::drawHardwareBuffer(vertices, indices, vType, pType, iType, numInstances, numDraw, firstIdx);
	drawHbInternal(vertices, indices, vType, iType, numInstances, numDraw, firstIdx,-1);
}

void VkDriver::drawHbInternal(const IHardwareBuffer *vertices, const IHardwareBuffer *indices,
								  E_VERTEX_TYPE &vType, E_INDEX_TYPE &iType, u32 numInstances,
								  u32 numDraw, u32 firstIdx,  int paraId)

{// Get vertex declaration
	auto cmb = paraId >= 0 ? threadData[paraId % threadPool.getThreadCount()].commandBuffer[0] : currentCmdBuffer();
	DeclarationNode declNode = declarationMap.find(vType);
	if (!declNode)
	{
		IRRLOG(("Error, vertex type not registered", ELL_ERROR));
		return;
	}

	// Set render states
	if (!setRenderStates3DMode(paraId,vType))
		return;
	const VkHardwareBuffer* vertexBuffer = static_cast<const VkHardwareBuffer*>(vertices);

	if (vertices)
	{
		UINT offset = 0;
		//throw;//to check
		VkDeviceSize offsets[1] = { 0 };
		VkBuffer buf[1] = { vertexBuffer->getBufferResource() };
		vkCmdBindVertexBuffers(cmb, 0, 1, buf, offsets);
	}


	// finally, draw
	bool drew = false;
	if (indices)
	{
		const VkHardwareBuffer* indexBuffer = static_cast<const VkHardwareBuffer*>(indices);
		vkCmdBindIndexBuffer(cmb, indexBuffer->getBufferResource(), 0,
			(iType == EIT_16BIT) ? VK_INDEX_TYPE_UINT16 : VK_INDEX_TYPE_UINT32);

		if (numDraw == 0)
		{
			const u32 idxSize = getIndexSize(iType);
			const u32 indexCount = indexBuffer->size() / idxSize - firstIdx;
			vkCmdDrawIndexed(cmb,
				indexCount,
				numInstances > 1 ? numInstances : 1, firstIdx, 0, 0);
			drew = true;
		}
	}

	if (!drew) if (numDraw > 0)  // binded on first submesh
	{
		vkCmdDrawIndexed(cmb,
			numDraw,
			numInstances > 1 ? numInstances : 1, firstIdx, 0, 0);
	}
	else  // auto draw all vertex 
	{
		const u32 stride = declNode->getValue()->getVertexPitch();
		const u32 vertexCount = vertexBuffer?vertexBuffer->size() / stride:0;
		vkCmdDraw(cmb, numDraw == 0 ? vertexCount : std::min(numDraw, vertexCount), 1, 0, 0);

		//DP(("Draw PT , check OK?"));
	}

	//// unset buffers
	//buffers[0] = 0;
	//ImmediateContext->IASetVertexBuffers( 0, 1, buffers, &stride, &offset );
	//ImmediateContext->IASetIndexBuffer( 0, DXGI_FORMAT_R16_UINT, 0 );
	//ImmediateContext->SOSetTargets( 1, buffers, &offset );
}

void VkDriver::drawHardwareBufferIndirect(IHardwareBuffer* vertices, IHardwareBuffer* indices, IHardwareBuffer* paramBuf, u32 paramBufOffset,
	u32 paramBufStride, E_VERTEX_TYPE vType, scene::E_PRIMITIVE_TYPE pType, E_INDEX_TYPE iType)
{
	CNullDriver::drawHardwareBuffer(vertices, indices, vType, pType, iType);

	// Get vertex declaration
	DeclarationNode declNode = declarationMap.find(vType);
	if (!declNode)
	{
		IRRLOG(("Error, vertex type not registered", ELL_ERROR));
		return;
	}

	// Set render states
	if (!setRenderStates3DMode(-1,vType))
		return;
	VkHardwareBuffer* vertexBuffer = static_cast<VkHardwareBuffer*>(vertices);

	if (vertices)
	{


		UINT offset = 0;
		//throw;//to check

		VkDeviceSize offsets[1] = { 0 };
		VkBuffer buf[1] = { vertexBuffer->getBufferResource() };
		vkCmdBindVertexBuffers(currentCmdBuffer(), 0, 1, buf, offsets);
	}
	VkHardwareBuffer* paramVkBuf = static_cast<VkHardwareBuffer*>(paramBuf);

	// finally, draw
	if (indices)
	{
		// Using indices
		VkHardwareBuffer* indexBuffer = static_cast<VkHardwareBuffer*>(indices);
		//const DXGI_FORMAT idxType = getIndexType(iType);
		const u32 idxSize = getIndexSize(iType);
		const u32 indexCount = indexBuffer->size() / idxSize;
		// set index buffer


		vkCmdBindIndexBuffer(currentCmdBuffer(), indexBuffer->getBufferResource(), 0,
			(iType == video::EIT_16BIT) ? VK_INDEX_TYPE_UINT16 : VK_INDEX_TYPE_UINT32);

		vkCmdDrawIndexedIndirect(currentCmdBuffer(), paramVkBuf->getBufferResource(), paramBufOffset, 1, paramBufStride);
	}
	else
	{
		if (vertices) {
			throw "not impl";
		}
		//const u32 stride = declNode->getValue()->getVertexPitch();
		//const u32 vertexCount = vertexBuffer->size() / stride;
		//vkCmdDraw(currentCmdBuffer(), numDraw == 0 ? vertexCount : std::min(numDraw, vertexCount), 1, 0, 0);
		vkCmdDrawIndirect(currentCmdBuffer(), paramVkBuf->getBufferResource(), paramBufOffset, 1, paramBufStride);

		//DP(("Draw PT , check OK?"));
	}

	//// unset buffers
	//buffers[0] = 0;
	//ImmediateContext->IASetVertexBuffers( 0, 1, buffers, &stride, &offset );
	//ImmediateContext->IASetIndexBuffer( 0, DXGI_FORMAT_R16_UINT, 0 );
	//ImmediateContext->SOSetTargets( 1, buffers, &offset );

}

void VkDriver::drawVertexPrimitiveList(const void* vertices, u32 vertexCount,
	const void* indexList, u32 primitiveCount,
	E_VERTEX_TYPE vType, scene::E_PRIMITIVE_TYPE pType,
	E_INDEX_TYPE iType)
{
#ifndef _IRR_COMPILE_WITH_DIRECT3D_11_
	if (!checkPrimitiveCount(primitiveCount))
		return;
#endif
	CNullDriver::drawVertexPrimitiveList(vertices, vertexCount, indexList, primitiveCount, vType, pType, iType);

	if (vertexCount || primitiveCount)
		draw2D3DVertexPrimitiveList(vertices, vertexCount, indexList, primitiveCount,
			vType, pType, iType, true);
}



void VkDriver::drawAuto(IHardwareBuffer* vertices, E_VERTEX_TYPE vType, scene::E_PRIMITIVE_TYPE pType, u32 ofs)
{
	throw;
	CNullDriver::drawAuto(vertices, vType, pType);


}
#if 0//VK_USE_2D_FX_RENDERER
void VkDriver::drawHardwareBuffer2D(IHardwareBuffer* vertices,
	IHardwareBuffer* indices, E_VERTEX_TYPE vType,
	scene::E_PRIMITIVE_TYPE pType, E_INDEX_TYPE iType, u32 numInstances, u32 ofs)
{
	CNullDriver::drawHardwareBuffer(vertices, indices, vType, pType, iType);

	// Get vertex declaration
	DeclarationNode declNode = declarationMap.find(vType);

	VkHardwareBuffer* vertexBuffer = static_cast<VkHardwareBuffer*>(vertices);
	const u32 stride = declNode->getValue()->getVertexPitch();
	const u32 vertexCount = vertexBuffer->size() / stride;
	UINT offset = ofs;

	// set vertex buffer
	ID3D11Buffer* buffers[1] = { vertexBuffer->getBufferResource() };
	ImmediateContext->IASetVertexBuffers(0, 1, buffers, &stride, &offset);


	if (indices)
	{
		// Using indices
		VkHardwareBuffer* indexBuffer = static_cast<VkHardwareBuffer*>(indices);
		const DXGI_FORMAT idxType = getIndexType(iType);
		const u32 idxSize = getIndexSize(iType);
		const u32 indexCount = indexBuffer->size() / idxSize;

		ImmediateContext->IASetIndexBuffer(indexBuffer->getBufferResource(), idxType, 0);


	}
	//DP(("hb %d",vertexCount));
	draw2DVertexPrimitiveListFast(0, vertexCount, 0, vertexCount / 4 * 2, vType, pType, iType, false);
	//DP(("hb- %d",vertexCount));
}




#endif

void VkDriver::makesureDuDv(int viewId, ECOLOR_FORMAT cf, irr::core::dimension2du rtSize) {
	int i = viewId;
	{
		if (!texMrRT[i] || texMrRT[i]->getSize() != rtSize)
		{
			if (texMrRT[i]) freeTexture(texMrRT[i]);
			if (texDuDv[i]) freeTexture(texDuDv[i]);
			texMrRT[i] = addRenderTargetTexture(rtSize, "ptcrtmr", cf);
			texDuDv[i] = addRenderTargetTexture(rtSize, "ptcrtdd", cf);
		}
	}
	//if (!texMrRT[0] || texMrRT[0]->getSize() != rtSize)
	//{
	//	if (texMrRT[0]) freeTexture(texMrRT[0]);
	//	if (texDuDv[0]) freeTexture(texDuDv[0]);
	//	texMrRT[0] = addRenderTargetTexture(rtSize, "ptcrtmr", cf);
	//	texDuDv[0] = addRenderTargetTexture(rtSize, "ptcrtdd", cf);
	//}
}

bool VkDriver::makesureCubeSphere() {
	if (!texCube[0]  )
	{
		for (int i = 0; i < 6; i++) {
			if (texCube[i]) freeTexture(texCube[i]);	
			texCube[i] = addRenderTargetTexture({1024,1024}, "ptcrtmr", ECF_A8R8G8B8);
		}
		return true;
	}
	return false;
}

void VkDriver::draw2DVertexPrimitiveListFast(const void* vertices, u32 vertexCount,
	const void* indexList, u32 primitiveCount,
	E_VERTEX_TYPE vType, scene::E_PRIMITIVE_TYPE pType,
	E_INDEX_TYPE iType, bool isSimpleImage)
{

}
#if VK_USE_2D_FX_RENDERER

inline E_MATERIAL_TYPE VkDriver::add2DMaterialType(Fx2DIdEnum fid, const c8* name)
{
	VkMr2D* mr1 = new VkMr2D(this, fid);
	mr1->InitMaterialRenderer();
	E_MATERIAL_TYPE mt = (E_MATERIAL_TYPE)addMaterialRenderer(mr1, name);
	mr1->drop();
	return mt;
}
void VkDriver::draw2DPointLists(const void* vertices, int n)
{
	// 	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();
	// 	core::rect<s32> ovp=getViewPort();
	// 	core::rect<s32> destRect(0,0,renderTargetSize.Width,renderTargetSize.Height);
	// 	setViewPort(destRect);
	CurrentTexture[0] = NullTexture;
	CurrentTexture[1] = 0;
	Material.MaterialType = mt2DVertexColor;
	draw2DVertexPrimitiveListFast(vertices, n, 0, 0, EVT_STANDARD, scene::EPT_POINTS, EIT_16BIT);
	//setViewPort(ovp);
}

void VkDriver::draw2DImageKaleido(const video::ITexture* texture,
	const core::position2d<s32>& pos, float rad, float scale
)
{
	if (!texture)
		return;


	core::position2d<s32> targetPos = pos;



	core::rect<f32> tcoords;
	tcoords.UpperLeftCorner.X = 0;
	tcoords.UpperLeftCorner.Y = 0;
	tcoords.LowerRightCorner.X = 1;
	tcoords.LowerRightCorner.Y = 1;

	// ok, we've clipped everything.
	// now draw it.
	CurrentTexture[0] = texture;
	CurrentTexture[1] = 0;

	const core::dimension2d<u32>& texSize = texture->getSize();
	const core::dimension2d<u32>& rtSize = getCurrentRenderTargetSize();
	core::rect<f32> rcPos(
		targetPos.X,
		targetPos.Y,
		targetPos.X + texSize.Width,
		targetPos.Y + texSize.Height);

	static bool isFirstDraw = true;
	static S3DVertex vtx[36]; // clock wise
	static s16 indices[54] = { 0,1,2,0,2,3 };
	using namespace core;


	if (isFirstDraw)
	{
		isFirstDraw = false;
		int vi = 0;
		auto rcOld = rcPos;
		vtx[0] = S3DVertex(rcPos.UpperLeftCorner.X, rcPos.UpperLeftCorner.Y, 0.0f,
			rtSize.Width, rtSize.Height, 0.0f, 0xFFFFFFFF,
			tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
		vtx[1] = S3DVertex(rcPos.LowerRightCorner.X, rcPos.UpperLeftCorner.Y, 0.0f,
			rtSize.Width, rtSize.Height, 0.0f, 0xFFFFFFFF,
			tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
		vtx[2] = S3DVertex(rcPos.LowerRightCorner.X, rcPos.LowerRightCorner.Y, 0.0f,
			rtSize.Width, rtSize.Height, 0.0f, 0xFFFFFFFF,
			tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
		vtx[3] = S3DVertex(rcPos.UpperLeftCorner.X, rcPos.LowerRightCorner.Y, 0.0f,
			rtSize.Width, rtSize.Height, 0.0f, 0xFFFFFFFF,
			tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);

		for (int i = 1; i < 9; i++) for (int j = 0; j < 6; j++)
		{
			indices[i * 6 + j] = indices[j] + i * 4;
		}
	}
	else
	{
		vtx[0].Pos.set(rcPos.UpperLeftCorner.X, rcPos.UpperLeftCorner.Y, 0.0f);
		vtx[1].Pos.set(rcPos.LowerRightCorner.X, rcPos.UpperLeftCorner.Y, 0.0f);
		vtx[2].Pos.set(rcPos.LowerRightCorner.X, rcPos.LowerRightCorner.Y, 0.0f);
		vtx[3].Pos.set(rcPos.UpperLeftCorner.X, rcPos.LowerRightCorner.Y, 0.0f);
	}

	int vi = 0;
	vector3df dxy;
#define FILLKLDVTX		\
vi+=4;\
vtx[vi+0] = vtx[0];		vtx[vi+1] = vtx[1];		vtx[vi+2] = vtx[2];		vtx[vi+3] = vtx[3];\
vtx[vi+0].Pos+=dxy;		vtx[vi+1].Pos+=dxy;		vtx[vi+2].Pos+=dxy;		vtx[vi+3].Pos+=dxy;

	dxy = vector3df(0, -(float)rtSize.Height, 0.f);	FILLKLDVTX;
	dxy = vector3df((float)rtSize.Width, -(float)rtSize.Height, 0.f);	FILLKLDVTX;
	dxy = vector3df((float)rtSize.Width, 0.f, 0.f); FILLKLDVTX;
	dxy = vector3df((float)rtSize.Width, (float)rtSize.Height, 0.f); FILLKLDVTX;
	dxy = vector3df(0, (float)rtSize.Height, 0.f); FILLKLDVTX;
	dxy = vector3df(-(float)rtSize.Width, (float)rtSize.Height, 0.f); FILLKLDVTX;
	dxy = vector3df(-(float)rtSize.Width, 0.f, 0.f); FILLKLDVTX;
	dxy = vector3df(-(float)rtSize.Width, -(float)rtSize.Height, 0.f); FILLKLDVTX;



	//Material = InitMaterial2D;
	Material.MaterialType = mt2DVertexAlpha;
	Material.TextureLayer[0].TrilinearFilter = false;
	Material.setFlag(EMF_TEXTURE_WRAP, ETC_CLAMP);
	//Material.PreMultipliedAlpha= getMaterial2D().PreMultipliedAlpha;
	draw2DVertexPrimitiveListFast(vtx, 4 * 9, indices, 2 * 9, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, true);
}



#endif
void VkDriver::draw2DImage(const video::ITexture* texture,
	const core::position2d<s32>& pos,
	const core::rect<s32>& sourceRect,
	const core::rect<s32>* clipRect,
	SColor color,
	bool useAlphaChannelOfTexture, int rotateDegree )
{

#if VK_USE_2D_FX_RENDERER
	if (!texture)
		return;
#ifdef _IRR_COMPILE_WITH_GUI_
	if (!_guiTexture)
		_guiTexture = findTexture("|guiRT|ngc");
	if (_guiTexture && texture == _guiTexture)
	{
		drawGuiLayer(texture);
		return;
}
#endif
	if (!sourceRect.isValid())
		return;

	core::position2d<s32> targetPos = pos;
	core::position2d<s32> sourcePos = sourceRect.UpperLeftCorner;
	// This needs to be signed as it may go negative.
	core::dimension2d<s32> sourceSize(sourceRect.getSize());

	if (clipRect)
	{
		if (targetPos.X < clipRect->UpperLeftCorner.X)
		{
			sourceSize.Width += targetPos.X - clipRect->UpperLeftCorner.X;
			if (sourceSize.Width <= 0)
				return;

			sourcePos.X -= targetPos.X - clipRect->UpperLeftCorner.X;
			targetPos.X = clipRect->UpperLeftCorner.X;
		}

		if (targetPos.X + (s32)sourceSize.Width > clipRect->LowerRightCorner.X)
		{
			sourceSize.Width -= (targetPos.X + sourceSize.Width) - clipRect->LowerRightCorner.X;
			if (sourceSize.Width <= 0)
				return;
		}

		if (targetPos.Y < clipRect->UpperLeftCorner.Y)
		{
			sourceSize.Height += targetPos.Y - clipRect->UpperLeftCorner.Y;
			if (sourceSize.Height <= 0)
				return;

			sourcePos.Y -= targetPos.Y - clipRect->UpperLeftCorner.Y;
			targetPos.Y = clipRect->UpperLeftCorner.Y;
		}

		if (targetPos.Y + (s32)sourceSize.Height > clipRect->LowerRightCorner.Y)
		{
			sourceSize.Height -= (targetPos.Y + sourceSize.Height) - clipRect->LowerRightCorner.Y;
			if (sourceSize.Height <= 0)
				return;
		}
	}

	// clip these coordinates

	if (targetPos.X < 0)
	{
		sourceSize.Width += targetPos.X;
		if (sourceSize.Width <= 0)
			return;

		sourcePos.X -= targetPos.X;
		targetPos.X = 0;
	}

	const core::dimension2d<u32>& ss = texture->getOriginalSize();
	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();

	if (targetPos.X + sourceSize.Width > (s32)renderTargetSize.Width)
	{
		sourceSize.Width -= (targetPos.X + sourceSize.Width) - renderTargetSize.Width;
		if (sourceSize.Width <= 0)
			return;
	}

	if (targetPos.Y < 0)
	{
		sourceSize.Height += targetPos.Y;
		if (sourceSize.Height <= 0)
			return;

		sourcePos.Y -= targetPos.Y;
		targetPos.Y = 0;
	}

	if (targetPos.Y + sourceSize.Height > (s32)renderTargetSize.Height)
	{
		sourceSize.Height -= (targetPos.Y + sourceSize.Height) - renderTargetSize.Height;
		if (sourceSize.Height <= 0)
			return;
	}

	core::rect<f32> tcoords;
	tcoords.UpperLeftCorner.X = (((f32)sourcePos.X)) / ss.Width;
	tcoords.UpperLeftCorner.Y = (((f32)sourcePos.Y)) / ss.Height;
	tcoords.LowerRightCorner.X = tcoords.UpperLeftCorner.X + ((f32)(sourceSize.Width) / ss.Width);
	tcoords.LowerRightCorner.Y = tcoords.UpperLeftCorner.Y + ((f32)(sourceSize.Height) / ss.Height);

	// ok, we've clipped everything.
	// now draw it.
	CurrentTexture[0] = texture;
	CurrentTexture[1] = 0;



#if 1
	core::rect<f32> rcPos(
		targetPos.X,
		targetPos.Y,
		targetPos.X + sourceSize.Width,
		targetPos.Y + sourceSize.Height);

	S3DVertex vtx[4]; // clock wise
	
	vtx[0] = S3DVertex(rcPos.UpperLeftCorner.X, rcPos.UpperLeftCorner.Y, 0.0f,
		renderTargetSize.Width, renderTargetSize.Height, 0.0f, color,
		tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[1] = S3DVertex(rcPos.LowerRightCorner.X, rcPos.UpperLeftCorner.Y, 0.0f,
		renderTargetSize.Width, renderTargetSize.Height, 0.0f, color,
		tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[2] = S3DVertex(rcPos.LowerRightCorner.X, rcPos.LowerRightCorner.Y, 0.0f,
		renderTargetSize.Width, renderTargetSize.Height, 0.0f, color,
		tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
	vtx[3] = S3DVertex(rcPos.UpperLeftCorner.X, rcPos.LowerRightCorner.Y, 0.0f,
		renderTargetSize.Width, renderTargetSize.Height, 0.0f, color,
		tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);

	s16 indices[6] = { 0,1,2,0,2,3 };

	//Material = InitMaterial2D;
	Material.MaterialType = useAlphaChannelOfTexture ? mt2DVertexAlpha : mt2DVertexColor;
	Material.TextureLayer[0].TrilinearFilter = false;
	Material.TextureLayer[0].BilinearFilter = false;
	Material.setFlag(EMF_TEXTURE_WRAP, ETC_CLAMP);
	//Material.PreMultipliedAlpha= getMaterial2D().PreMultipliedAlpha;
	draw2D3DVertexPrimitiveList(vtx, 4, indices, 2, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, true);


#else
	const core::rect<s32> poss(targetPos, sourceSize);
	Material = InitMaterial2D;

	if (useAlphaChannelOfTexture)
	{
		Material.MaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL;
		Material.setTexture(0, const_cast<video::ITexture*>(texture));
	}


	S3DVertex vtx[4];
	vtx[0] = S3DVertex((f32)poss.UpperLeftCorner.X, (f32)poss.UpperLeftCorner.Y, 0.0f,
		0.0f, 0.0f, 0.0f, color,
		tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[1] = S3DVertex((f32)poss.LowerRightCorner.X, (f32)poss.UpperLeftCorner.Y, 0.0f,
		0.0f, 0.0f, 0.0f, color,
		tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[2] = S3DVertex((f32)poss.LowerRightCorner.X, (f32)poss.LowerRightCorner.Y, 0.0f,
		0.0f, 0.0f, 0.0f, color,
		tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
	vtx[3] = S3DVertex((f32)poss.UpperLeftCorner.X, (f32)poss.LowerRightCorner.Y, 0.0f,
		0.0f, 0.0f, 0.0f, color,
		tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);

	s32 verticesSize = sizeof(vtx) / sizeof(vtx[0]);

	s16 indices[6] = { 0,1,2,0,2,3 };
	s32 indicesSize = sizeof(indices) / sizeof(indices[0]);

	// Set world to identity
	setTransform(ETS_WORLD, IdentityMatrix11);

	// Set view to translate a little forward
	core::matrix4 m;
	m.setTranslation(core::vector3df(-0.5f, -0.5f, 0));
	setTransform(ETS_VIEW, m);

	// Adjust projection
	//const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();
	m.buildProjectionMatrixOrthoLH(f32(renderTargetSize.Width), f32(-(s32)(renderTargetSize.Height)), -1.0, 1.0);
	m.setTranslation(core::vector3df(-1, 1, 0));
	setTransform(ETS_PROJECTION, m);

	// DX 11 needs a renderer, so configure solid
	MaterialRenderers[Material.MaterialType].Renderer->OnSetMaterial(Material, LastMaterial, ResetRenderStates, this);
	bool shaderOK = MaterialRenderers[Material.MaterialType].Renderer->OnRender(this, video::EVT_STANDARD);

	// Draw
	draw2DVertexPrimitiveListFast(vtx, verticesSize, indices, indicesSize / 3, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, true);
#endif

#endif
}


void VkDriver::draw2DImageRect(const video::ITexture* texture,
	const core::rect<s32>& destRect,
	const core::rect<s32>& sourceRect,
	const core::rect<s32>* clipRect,
	const video::SColor* const colors,
	bool useAlphaChannelOfTexture, int rotateDegree )
{
	if (!texture)
		return;

	const core::dimension2d<u32>& ss = texture->getOriginalSize();
	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();

	// clip source and destination rects to prevent draw pixels outside draw area.
	core::rect<f32> tcoords;
	tcoords.UpperLeftCorner.X = (f32)sourceRect.UpperLeftCorner.X / (f32)ss.Width;
	tcoords.UpperLeftCorner.Y = (f32)sourceRect.UpperLeftCorner.Y / (f32)ss.Height;
	tcoords.LowerRightCorner.X = (f32)sourceRect.LowerRightCorner.X / (f32)ss.Width;
	tcoords.LowerRightCorner.Y = (f32)sourceRect.LowerRightCorner.Y / (f32)ss.Height;
 
	static const video::SColor temp[4] =
	{
		0xFFFFFFFF,
		0xFFFFFFFF,
		0xFFFFFFFF,
		0xFFFFFFFF
	};

	const video::SColor* const useColor = colors ? colors : temp;
#if CPU_2D_POS
	core::rect<f32> rcPos(
		-1.0f + 2.f * destRect.UpperLeftCorner.X / renderTargetSize.Width,
		1.0f - 2.f * destRect.UpperLeftCorner.Y / renderTargetSize.Height,
		-1.0f + 2.f * (destRect.LowerRightCorner.X) / renderTargetSize.Width,
		1.0f - 2.f * (destRect.LowerRightCorner.Y) / renderTargetSize.Height);


	//TODO: static 
	S3DVertex vtx[4]; // clock wise
	vtx[0] = S3DVertex(rcPos.UpperLeftCorner.X, rcPos.UpperLeftCorner.Y, 0.0f,
		0.0f, 0.0f, 0.0f, useColor[0],
		tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[1] = S3DVertex(rcPos.LowerRightCorner.X, rcPos.UpperLeftCorner.Y, 0.0f,
		0.0f, 0.0f, 0.0f, useColor[1],
		tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[2] = S3DVertex(rcPos.LowerRightCorner.X, rcPos.LowerRightCorner.Y, 0.0f,
		0.0f, 0.0f, 0.0f, useColor[2],
		tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
	vtx[3] = S3DVertex(rcPos.UpperLeftCorner.X, rcPos.LowerRightCorner.Y, 0.0f,
		0.0f, 0.0f, 0.0f, useColor[3],
		tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);
#else
	//TODO: static 
	S3DVertex vtx[4]; // clock wise
#if 0
	if (rotateDegree == 90) {
		vtx[0] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[0],
			tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);
		vtx[1] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[1],
			tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
		vtx[2] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[2],
			tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
		vtx[3] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[3],
			tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
	}
	else if (rotateDegree == 270) {
		vtx[0] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[0],
			tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
		vtx[1] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[1],
			tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
		vtx[2] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[2],
			tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);
		vtx[3] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[3],
			tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
	}
	else
#endif
	{
		vtx[0] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[0],
			tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
		vtx[1] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[1],
			tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
		vtx[2] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[2],
			tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
		vtx[3] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
			(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[3],
			tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);
		if (rotateDegree != 0) {
			core::vector3df ctr = (vtx[0].Pos + vtx[2].Pos) / 2;
			vtx[0].Pos.rotateXYBy(-rotateDegree, ctr);
			vtx[1].Pos.rotateXYBy(-rotateDegree, ctr);
			vtx[2].Pos.rotateXYBy(-rotateDegree, ctr);
			vtx[3].Pos.rotateXYBy(-rotateDegree, ctr);
		}
	}


	//auto t = vtx[0].TCoords;	vtx[0].TCoords = vtx[3].TCoords; vtx[3].TCoords = vtx[2].TCoords; vtx[2].TCoords = vtx[1].TCoords; vtx[1].TCoords = t;
#endif

	s16 indices[6] = { 0,1,2,0,2,3 };

	//Material = InitMaterial2D;
	Material.MaterialType = useAlphaChannelOfTexture ? mt2DVertexAlpha : mt2DVertexColor;
	Material.TextureLayer[0] = getMaterial2D().TextureLayer[0];
	Material.setFlag(EMF_TEXTURE_WRAP, ETC_CLAMP);
	CurrentTexture[0] = texture;
	Material.AntiAliasing = EAAM_SIMPLE;
	CurrentTexture[1] = 0;
	//Material.PreMultipliedAlpha= getMaterial2D().PreMultipliedAlpha;
	draw2D3DVertexPrimitiveList(vtx, 4, indices, 2, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, true);
	}


#if VK_USE_2D_FX_RENDERER
void VkDriver::draw2DImageMr(const SMaterial& mr,
	const core::rect<s32>& destRect,
	const core::rect<s32>& sourceRect,
	const core::rect<s32>* clipRect,
	const video::SColor* const colors, bool invY 
)
{
	ITexture* texture = mr.TextureLayer[0].Texture;
	//if (!texture)	return;
	core::dimension2d<u32> ss;
	if (!texture)
		ss = core::dimension2d<u32>(sourceRect.LowerRightCorner.X - sourceRect.UpperLeftCorner.X, sourceRect.LowerRightCorner.Y - sourceRect.UpperLeftCorner.Y);
	else if (texture == DepthProxyTexture)
		ss = ScreenSize;
	else
		ss = texture->getSize();
	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();

	video::VkMr2D* mr2d = (video::VkMr2D*)getMaterialRenderer(mr.MaterialType);
	irr::video::CbMr2D& cb = mr2d->cbDraw;
	cb.res = float2(renderTargetSize.Width, renderTargetSize.Height);
	cb.resWdH = float(renderTargetSize.Width) / renderTargetSize.Height;
	cb.resHdW = float(renderTargetSize.Height) / renderTargetSize.Width;
	// clip source and destination rects to prevent draw pixels outside draw area.
	core::rect<f32> tcoords;
	tcoords.UpperLeftCorner.X = (f32)sourceRect.UpperLeftCorner.X / (f32)ss.Width;

	tcoords.LowerRightCorner.X = (f32)sourceRect.LowerRightCorner.X / (f32)ss.Width;
	if (invY) {
		tcoords.UpperLeftCorner.Y = 1.f-(f32)sourceRect.UpperLeftCorner.Y / (f32)ss.Height;
		tcoords.LowerRightCorner.Y = 1.f-(f32)sourceRect.LowerRightCorner.Y / (f32)ss.Height;
	}
	else {
		tcoords.UpperLeftCorner.Y = (f32)sourceRect.UpperLeftCorner.Y / (f32)ss.Height;
		tcoords.LowerRightCorner.Y = (f32)sourceRect.LowerRightCorner.Y / (f32)ss.Height;
	}
	const video::SColor temp[4] =
	{
		0xFFFFFFFF,
		0xFFFFFFFF,
		0xFFFFFFFF,
		0xFFFFFFFF
	};

	const video::SColor* const useColor = colors ? colors : temp;

	S3DVertex vtx[4]; // clock wise
	vtx[0] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[0],
		tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[1] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[1],
		tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[2] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[2],
		tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
	vtx[3] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[3],
		tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);


	s16 indices[6] = { 0,1,2,0,2,3 };

	Material = mr;
	CurrentTexture[0] = texture;
	CurrentTexture[1] = mr.TextureLayer[1].Texture;;
	CurrentTexture[2] = 0;
	draw2D3DVertexPrimitiveList(vtx, 4, indices, 2, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, true);

}
void VkDriver::draw2DSubPassMr(const SMaterial& mr)
{

	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();
	const core::dimension2d<u32>& ss = renderTargetSize;
	video::VkMr2D* mr2d = (video::VkMr2D*)getMaterialRenderer(mr.MaterialType);
	irr::video::CbMr2D& cb = mr2d->cbDraw;
	cb.res = float2(renderTargetSize.Width, renderTargetSize.Height);
	cb.resWdH = float(renderTargetSize.Width) / renderTargetSize.Height;
	cb.resHdW = float(renderTargetSize.Height) / renderTargetSize.Width;
	// clip source and destination rects to prevent draw pixels outside draw area.
	core::rect<f32> tcoords;
	tcoords.UpperLeftCorner.X = (f32)0;
	tcoords.UpperLeftCorner.Y = (f32)0;
	tcoords.LowerRightCorner.X = (f32)1;
	tcoords.LowerRightCorner.Y = (f32)1;


	const video::SColor temp[4] =
	{
		0xFFFFFFFF,
		0xFFFFFFFF,
		0xFFFFFFFF,
		0xFFFFFFFF
	};

	const video::SColor* const useColor = temp;

	S3DVertex vtx[4]; // clock wise
	vtx[0] = S3DVertex((float)0, (float)0, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[0],
		tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[1] = S3DVertex((float)renderTargetSize.Width, (float)0, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[1],
		tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[2] = S3DVertex((float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[2],
		tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
	vtx[3] = S3DVertex((float)0, (float)renderTargetSize.Height, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[3],
		tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);


	s16 indices[6] = { 0,1,2,0,2,3 };

	Material = mr;
	CurrentTexture[0] = mr.TextureLayer[0].Texture;
	CurrentTexture[1] = 0;
	CurrentTexture[2] = 0;
	draw2D3DVertexPrimitiveList(vtx, 4, indices, 2, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, true);

}
#if 0
void VkDriver::draw2DImageBatchMr(const SMaterial& mr,
	const core::array<core::position2d<s32> >& positions,
	const core::rect<s32>& sourceRects,
	const core::rect<s32>* clipRect,
	SColor color, bool useAlphaChannelOfTexture)
{ //TODO: to test

	const video::ITexture* texture = mr.TextureLayer[0].Texture;
	if (!texture)
		return;

	setActiveTexture(1, 0);		// disable second texture
	if (!setActiveTexture(0, const_cast<video::ITexture*>(texture)))
		return;

	const irr::u32 drawCount = positions.size();
	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();

	core::array<S3DVertex> vtx(drawCount * 4);
	core::array<u16> indices(drawCount * 6);
	vtx.clear();
	indices.clear();


	core::position2d<s32> sourcePos = sourceRects.UpperLeftCorner;
	// This needs to be signed as it may go negative.
	core::dimension2d<s32> sourceSize(sourceRects.getSize());


	// ok, we've clipped everything.
	// now draw it.

	core::rect<f32> tcoords;
	tcoords.UpperLeftCorner.X = (((f32)sourcePos.X)) / texture->getOriginalSize().Width;
	tcoords.UpperLeftCorner.Y = (((f32)sourcePos.Y)) / texture->getOriginalSize().Height;
	tcoords.LowerRightCorner.X = tcoords.UpperLeftCorner.X + ((f32)(sourceSize.Width) / texture->getOriginalSize().Width);
	tcoords.LowerRightCorner.Y = tcoords.UpperLeftCorner.Y + ((f32)(sourceSize.Height) / texture->getOriginalSize().Height);



	for (u32 i = 0; i < drawCount; i++)
	{
		core::position2d<s32> targetPos = positions[i];
		const core::rect<s32> poss(targetPos, sourceSize);

		vtx.push_back(S3DVertex((f32)poss.UpperLeftCorner.X, (f32)poss.UpperLeftCorner.Y, 0.0f,
			renderTargetSize.Width, renderTargetSize.Height, 0.0f, color,
			tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y));
		vtx.push_back(S3DVertex((f32)poss.LowerRightCorner.X, (f32)poss.UpperLeftCorner.Y, 0.0f,
			renderTargetSize.Width, renderTargetSize.Height, 0.0f, color,
			tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y));
		vtx.push_back(S3DVertex((f32)poss.LowerRightCorner.X, (f32)poss.LowerRightCorner.Y, 0.0f,
			renderTargetSize.Width, renderTargetSize.Height, 0.0f, color,
			tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y));
		vtx.push_back(S3DVertex((f32)poss.UpperLeftCorner.X, (f32)poss.LowerRightCorner.Y, 0.0f,
			renderTargetSize.Width, renderTargetSize.Height, 0.0f, color,
			tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y));

		const u32 curPos = vtx.size() - 4;
		int s0 = indices.size();
		indices.push_back(0 + curPos);
		indices.push_back(1 + curPos);
		indices.push_back(2 + curPos);

		indices.push_back(0 + curPos);
		indices.push_back(2 + curPos);
		indices.push_back(3 + curPos);
		int s1 = indices.size() - s0;

	}

	if (vtx.size())
	{

		Material = mr;
		CurrentTexture[0] = texture;
		CurrentTexture[1] = mr.TextureLayer[1].Texture;;
		CurrentTexture[2] = 0;
		// 		// Draw
		// 		draw2DVertexPrimitiveList(vtx.pointer(), vtx.size(), indices.pointer(), indices.size()/3, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT);
		draw2DVertexPrimitiveList(vtx.pointer(), vtx.size(), indices.pointer(), indices.size() / 3, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, true);
}
}
#endif
void VkDriver::DrawMrTexToTexture(ITexture* tgtTex, const SMaterial& mt, bool clearBuffer, bool clearZBuffer)
{
	assert(mt.TextureLayer[0].Texture);
	if (!mt.TextureLayer[0].Texture) return;
	const core::dimension2d<u32>& ts = tgtTex->getSize();
	core::rect<s32> destRect(0, 0, ts.Width, ts.Height);
	const core::dimension2d<u32>& ss = mt.TextureLayer[0].Texture->getSize();
	core::rect<s32> srcRect(0, 0, ss.Width, ss.Height);

	ITexture* rt = getRenderTarget();
	if (rt != tgtTex)	setRenderTarget(tgtTex, clearBuffer, clearZBuffer);
	draw2DImageMr(mt, destRect, srcRect, NULL, NULL,false);
	if (rt != tgtTex) setRenderTarget(rt, 0, 0);

}


void VkDriver::draw2DImage2Tex(const video::ITexture* texture, const video::ITexture* texture2, E_MATERIAL_TYPE mt,
	const core::rect<s32>& destRect,
	const core::rect<s32>* sourceRect,
	const core::rect<s32>* clipRect,
	const video::SColor* const colors,
	bool useAlphaChannelOfTexture)
{
	if (!texture || texture->getDriverType() != EDT_VK)
		return;

	const core::dimension2d<u32>& ss = texture->getOriginalSize();
	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();
	core::rect<f32> tcoords(0.f, 0.f, 1.f, 1.f);
	if (sourceRect)
	{
		tcoords.UpperLeftCorner.X = (f32)sourceRect->UpperLeftCorner.X / (f32)ss.Width;
		tcoords.UpperLeftCorner.Y = (f32)sourceRect->UpperLeftCorner.Y / (f32)ss.Height;
		tcoords.LowerRightCorner.X = (f32)sourceRect->LowerRightCorner.X / (f32)ss.Width;
		tcoords.LowerRightCorner.Y = (f32)sourceRect->LowerRightCorner.Y / (f32)ss.Height;
	}

	const video::SColor temp[4] = { 0xFFFFFFFF,		0xFFFFFFFF,		0xFFFFFFFF,		0xFFFFFFFF };
	const video::SColor* const useColor = colors ? colors : temp;

	S3DVertex vtx[4]; // clock wise
	vtx[0] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[0],
		tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[1] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.UpperLeftCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[1],
		tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y);
	vtx[2] = S3DVertex((float)destRect.LowerRightCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[2],
		tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y);
	vtx[3] = S3DVertex((float)destRect.UpperLeftCorner.X, (float)destRect.LowerRightCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, useColor[3],
		tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y);

	s32 verticesSize = sizeof(vtx) / sizeof(vtx[0]);

	s16 indices[6] = { 0,1,2,0,2,3 };
	s32 indicesSize = sizeof(indices) / sizeof(indices[0]);



	CurrentTexture[0] = texture;
	CurrentTexture[1] = texture2;
	CurrentTexture[2] = 0;

	Material = InitMaterial2D;
	Material.MaterialType = mt;


	//Material.MaterialType=EMT_SOLID; MaterialRenderers[EMT_SOLID].Renderer->OnRender(this, video::EVT_STANDARD);



	// Draw
	//core::rect<s32> ovp=getViewPort();
	//setViewPort(destRect);
	draw2DVertexPrimitiveListFast(vtx, 4, indices, 2, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, true);


	//setViewPort(ovp);
	//MaterialRenderers[Material.MaterialType].Renderer->OnUnsetMaterial();
}

#endif
void VkDriver::draw2DImageBatch(const video::ITexture* texture,
	const core::array<core::position2d<s32> >& positions,
	const core::array<core::rect<s32> >& sourceRects,
	const core::rect<s32>* clipRect,
	SColor color, bool useAlphaChannelOfTexture)
{
#if 0
	if (!texture)
		return;

	setActiveTexture(1, 0);		// disable second texture
	if (!setActiveTexture(0, const_cast<video::ITexture*>(texture)))
		return;

	const irr::u32 drawCount = core::min_<u32>(positions.size(), sourceRects.size());
	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();

	core::array<S3DVertex> vtx(drawCount * 4);
	core::array<u16> indices(drawCount * 6);
	vtx.clear();
	indices.clear();
	for (u32 i = 0; i < drawCount; i++)
	{
		core::position2d<s32> targetPos = positions[i];
		core::position2d<s32> sourcePos = sourceRects[i].UpperLeftCorner;
		// This needs to be signed as it may go negative.
		core::dimension2d<s32> sourceSize(sourceRects[i].getSize());

		if (clipRect)
		{
			if (targetPos.X < clipRect->UpperLeftCorner.X)
			{
				sourceSize.Width += targetPos.X - clipRect->UpperLeftCorner.X;
				if (sourceSize.Width <= 0)
					continue;

				sourcePos.X -= targetPos.X - clipRect->UpperLeftCorner.X;
				targetPos.X = clipRect->UpperLeftCorner.X;
			}

			if (targetPos.X + (s32)sourceSize.Width > clipRect->LowerRightCorner.X)
			{
				sourceSize.Width -= (targetPos.X + sourceSize.Width) - clipRect->LowerRightCorner.X;
				if (sourceSize.Width <= 0)
					continue;
			}

			if (targetPos.Y < clipRect->UpperLeftCorner.Y)
			{
				sourceSize.Height += targetPos.Y - clipRect->UpperLeftCorner.Y;
				if (sourceSize.Height <= 0)
					continue;

				sourcePos.Y -= targetPos.Y - clipRect->UpperLeftCorner.Y;
				targetPos.Y = clipRect->UpperLeftCorner.Y;
			}

			if (targetPos.Y + (s32)sourceSize.Height > clipRect->LowerRightCorner.Y)
			{
				sourceSize.Height -= (targetPos.Y + sourceSize.Height) - clipRect->LowerRightCorner.Y;
				if (sourceSize.Height <= 0)
					continue;
			}
		}

		// clip these coordinates

		if (targetPos.X < 0)
		{
			sourceSize.Width += targetPos.X;
			if (sourceSize.Width <= 0)
				continue;

			sourcePos.X -= targetPos.X;
			targetPos.X = 0;
		}

		if (targetPos.X + sourceSize.Width > (s32)renderTargetSize.Width)
		{
			sourceSize.Width -= (targetPos.X + sourceSize.Width) - renderTargetSize.Width;
			if (sourceSize.Width <= 0)
				continue;
		}

		if (targetPos.Y < 0)
		{
			sourceSize.Height += targetPos.Y;
			if (sourceSize.Height <= 0)
				continue;

			sourcePos.Y -= targetPos.Y;
			targetPos.Y = 0;
		}

		if (targetPos.Y + sourceSize.Height > (s32)renderTargetSize.Height)
		{
			sourceSize.Height -= (targetPos.Y + sourceSize.Height) - renderTargetSize.Height;
			if (sourceSize.Height <= 0)
				continue;
		}

		// ok, we've clipped everything.
		// now draw it.

		core::rect<f32> tcoords;
		tcoords.UpperLeftCorner.X = (((f32)sourcePos.X)) / texture->getOriginalSize().Width;
		tcoords.UpperLeftCorner.Y = (((f32)sourcePos.Y)) / texture->getOriginalSize().Height;
		tcoords.LowerRightCorner.X = tcoords.UpperLeftCorner.X + ((f32)(sourceSize.Width) / texture->getOriginalSize().Width);
		tcoords.LowerRightCorner.Y = tcoords.UpperLeftCorner.Y + ((f32)(sourceSize.Height) / texture->getOriginalSize().Height);

		const core::rect<s32> poss(targetPos, sourceSize);


		vtx.push_back(S3DVertex((f32)poss.UpperLeftCorner.X, (f32)poss.UpperLeftCorner.Y, 0.0f,
			0.0f, 0.0f, 0.0f, color,
			tcoords.UpperLeftCorner.X, tcoords.UpperLeftCorner.Y));
		vtx.push_back(S3DVertex((f32)poss.LowerRightCorner.X, (f32)poss.UpperLeftCorner.Y, 0.0f,
			0.0f, 0.0f, 0.0f, color,
			tcoords.LowerRightCorner.X, tcoords.UpperLeftCorner.Y));
		vtx.push_back(S3DVertex((f32)poss.LowerRightCorner.X, (f32)poss.LowerRightCorner.Y, 0.0f,
			0.0f, 0.0f, 0.0f, color,
			tcoords.LowerRightCorner.X, tcoords.LowerRightCorner.Y));
		vtx.push_back(S3DVertex((f32)poss.UpperLeftCorner.X, (f32)poss.LowerRightCorner.Y, 0.0f,
			0.0f, 0.0f, 0.0f, color,
			tcoords.UpperLeftCorner.X, tcoords.LowerRightCorner.Y));

		const u32 curPos = vtx.size() - 4;
		int s0 = indices.size();
		indices.push_back(0 + curPos);
		indices.push_back(1 + curPos);
		indices.push_back(2 + curPos);

		indices.push_back(0 + curPos);
		indices.push_back(2 + curPos);
		indices.push_back(3 + curPos);
		int s1 = indices.size() - s0;

	}

	Material = InitMaterial2D;
	if (vtx.size())
	{
		if (useAlphaChannelOfTexture)
		{
			Material.MaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL;
			Material.setTexture(0, const_cast<video::ITexture*>(texture));
		}

		// Set world to identity
		setTransform(ETS_WORLD, IdentityMatrix11);

		// Set view to translate a little forwar
		core::matrix4 m;
		m.setTranslation(core::vector3df(-0.5f, -0.5f, 0));
		setTransform(ETS_VIEW, m);

		// Adjust projection
		//const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();
		m.buildProjectionMatrixOrthoLH(f32(renderTargetSize.Width), f32(-(s32)(renderTargetSize.Height)), -1.0, 1.0);
		m.setTranslation(core::vector3df(-1, 1, 0));
		setTransform(ETS_PROJECTION, m);


		if (LastMaterial.MaterialType != Material.MaterialType &&
			LastMaterial.MaterialType >= 0 && LastMaterial.MaterialType < (s32)MaterialRenderers.size())
		{
			MaterialRenderers[LastMaterial.MaterialType].Renderer->OnUnsetMaterial();
			// 			MaterialRenderers[Material.MaterialType].Renderer->OnSetMaterial(Material, LastMaterial, ResetRenderStates, this);
			// 			MaterialRenderers[Material.MaterialType].Renderer->OnRender(this, vType);
		}

		MaterialRenderers[Material.MaterialType].Renderer->OnSetMaterial(Material, LastMaterial, ResetRenderStates, this);


		if (Material.MaterialType == EMT_ONETEXTURE_BLEND)
		{
			E_BLEND_FACTOR srcFact, dstFact; 			E_MODULATE_FUNC modulo;			u32 alphaSource;
			unpack_textureBlendFunc(srcFact, dstFact, modulo, alphaSource, Material.MaterialTypeParam);
			setRenderStates2DMode(alphaSource & video::EAS_VERTEX_COLOR, (Material.getTexture(0) != 0), (alphaSource & video::EAS_TEXTURE) != 0);
		}
		else if (Material.MaterialType < EMT_LAST)
			setRenderStates2DMode(Material.MaterialType == EMT_TRANSPARENT_VERTEX_ALPHA,
				(Material.getTexture(0) != 0),
				Material.MaterialType == EMT_TRANSPARENT_ALPHA_CHANNEL);

		bool shaderOK = MaterialRenderers[Material.MaterialType].Renderer->OnRender(this, video::EVT_STANDARD);

		// 		// Draw
		// 		draw2DVertexPrimitiveList(vtx.pointer(), vtx.size(), indices.pointer(), indices.size()/3, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT);
		draw2D3DVertexPrimitiveList(vtx.pointer(), vtx.size(), indices.pointer(), indices.size() / 3, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, false);
}
#endif
}

void VkDriver::draw2DRectangle(const core::rect<s32>& position,
	SColor colorLeftUp, SColor colorRightUp, SColor colorLeftDown, SColor colorRightDown,
	const core::rect<s32>* clip)
{

	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();
	core::vector2df  rtDiv(2.f / renderTargetSize.Width, 2.f / renderTargetSize.Height);

	core::rect<s32> pos(position);
	//if (!pos.isValid())	return;
	if (clip)	pos.clipAgainst(*clip);


	S3DVertex vtx[4];
	vtx[0] = S3DVertex((f32)pos.UpperLeftCorner.X, (f32)pos.UpperLeftCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, colorLeftUp, 0.0f, 0.0f);
	vtx[1] = S3DVertex((f32)pos.LowerRightCorner.X, (f32)pos.UpperLeftCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, colorRightUp, 0.0f, 1.0f);
	vtx[2] = S3DVertex((f32)pos.LowerRightCorner.X, (f32)pos.LowerRightCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, colorRightDown, 1.0f, 0.0f);
	vtx[3] = S3DVertex((f32)pos.UpperLeftCorner.X, (f32)pos.LowerRightCorner.Y, 0.0f,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, colorLeftDown, 1.0f, 1.0f);

	s16 indices[6] = { 0,1,2,0,2,3 };


	//Set Material
	//Material = InitMaterial2D;
// 	if (	colorLeftUp.getAlpha() < 255 ||
// 		colorRightUp.getAlpha() < 255 ||
// 		colorLeftDown.getAlpha() < 255 ||
// 		colorRightDown.getAlpha() < 255) 

	Material.MaterialType = mt2DVertexColor;

	CurrentTexture[0] = 0;
	//CurrentTexture[1] =  0;

	// Draw
	draw2DVertexPrimitiveListFast(vtx, 4, indices, 2, video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT, true);
	//draw2DVertexPrimitiveList(vtx, sizeof(vtx)/sizeof(vtx[0]), indices, sizeof(indices)/sizeof(indices[0]), video::EVT_STANDARD, scene::EPT_TRIANGLES, video::EIT_16BIT);
}

void VkDriver::draw2DLine(const core::position2d<s32>& start,
	const core::position2d<s32>& end, SColor color)
{

	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();



	S3DVertex vtx[2];
	vtx[0] = S3DVertex((float)start.X, (float)start.Y, 0,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, color, 0.0f, 0.0f);
	vtx[1] = S3DVertex((float)end.X, (float)end.Y, 0,
		(float)renderTargetSize.Width, (float)renderTargetSize.Height, 0.0f, color, 0.0f, 0.0f);



	CurrentTexture[0] = 0;
	s16 indices[2] = { 0,1 };

	Material.MaterialType = mt2DVertexColor;
	draw2DVertexPrimitiveListFast(vtx, 2, indices, 2, EVT_STANDARD, scene::EPT_LINES, EIT_16BIT);

}

void VkDriver::drawPixel(u32 x, u32 y, const SColor& color)
{
	const core::dimension2d<u32>& renderTargetSize = getCurrentRenderTargetSize();

	S3DVertex vtx[1];
	vtx[0] = S3DVertex(x, y, 0,
		0.0f, 0.0f, 0.0f, color, 0.0f, 0.0f);

	core::rect<s32> ovp = getViewPort();
	core::rect<s32> destRect(0, 0, renderTargetSize.Width, renderTargetSize.Height);
	setViewPort(destRect);
	CurrentTexture[0] = NullTexture; CurrentTexture[1] = 0;
	draw2D3DVertexPrimitiveList(vtx, 1, 0, 1,
		EVT_STANDARD, scene::EPT_POINTS, EIT_16BIT, true);
	setViewPort(ovp);
}

void VkDriver::draw3DLine(const core::vector3df& start,
	const core::vector3df& end, SColor color)
{
	S3DVertex vtx[2];
	vtx[0] = S3DVertex(start.X, start.Y, start.Z,
		0.0f, 0.0f, 0.0f, color, 0.0f, 0.0f);
	vtx[1] = S3DVertex(end.X, end.Y, end.Z,
		0.0f, 0.0f, 0.0f, color, 0.0f, 1.0f);
	CurrentTexture[0] = NullTexture; CurrentTexture[1] = 0;
	s16 indices[6] = { 0,1 };
	draw2D3DVertexPrimitiveList(vtx, 2, indices, 2,
		EVT_STANDARD, scene::EPT_LINES, EIT_16BIT, true);
}

const wchar_t* VkDriver::getName() const
{
	return L"Vulkan";
}

void VkDriver::deleteAllDynamicLights()
{
	RequestedLights.clear();

	CNullDriver::deleteAllDynamicLights();
}

s32 VkDriver::addDynamicLight(const SLight& light)
{
	CNullDriver::addDynamicLight(light);

	RequestedLights.push_back(RequestedLight(light));

	return (s32)(RequestedLights.size() - 1);
}

void VkDriver::turnLightOn(s32 lightIndex, bool turnOn)
{
	if (lightIndex < 0 || lightIndex >= (s32)RequestedLights.size())
		return;

	RequestedLight& requestedLight = RequestedLights[lightIndex];
	requestedLight.DesireToBeOn = turnOn;
}

u32 VkDriver::getMaximalDynamicLightAmount() const
{
	return MaxActiveLights;
}

void VkDriver::setAmbientLight(const SColorf& color)
{
	AmbientLight = color;
}

void VkDriver::drawStencilShadowVolume(const core::vector3df* triangles, s32 count, bool zfail)
{

}

void VkDriver::drawStencilShadow(bool clearStencilBuffer,
	video::SColor leftUpEdge, video::SColor rightUpEdge,
	video::SColor leftDownEdge, video::SColor rightDownEdge)
{

}

u32 VkDriver::getMaximalPrimitiveCount() const
{
	return 0x7fffffff;
}


bool VkDriver::setTextureCreationFlag(E_TEXTURE_CREATION_FLAG flag, bool enabled)
{
	if (flag == video::ETCF_CREATE_MIP_MAPS && !queryFeature(EVDF_MIP_MAP))
		enabled = false;


	return CNullDriver::setTextureCreationFlag(flag, enabled);
}


void VkDriver::setFog(SColor color, E_FOG_TYPE fogType, f32 start,
	f32 end, f32 density, bool pixelFog, bool rangeFog)
{
	// just forward method, configurations will be applyed in the material renderer
	CNullDriver::setFog(color, fogType, start, end, density, pixelFog, rangeFog);
}

void VkDriver::OnResetWindow(void* hwnd)
{
	SwapChain.cleanup();
	initSwapchain(hwnd);
	resetSwapchain();
	OnResize(ScreenSize);

}

void VkDriver::OnResize(const core::dimension2d<u32>& size)
{
	if (!Device || !SwapChain.swapChain)
		return;
	DP(("Driver OnResize+ %d %d", size.Width, size.Height));

	if (isExtDev)
	{

#ifdef _IRR_COMPILE_WITH_WINDOWS_DEVICE_
		MessageBoxA(0, "TODO OnResize External", "ERR", MB_OK);
#endif
		throw;// to check
	}


	//CNullDriver::OnResize(size);
	if (ScreenSize != size)
	{
		ScreenSize = size;


		if (!g_pExtDevInfo)
			resetSwapchain();
		DP(("Driver OnResize- %d %d", ScreenSize.Width, ScreenSize.Height));

		if (dsd.oitOn || pTexOitCount) {
			//assert(pTexOitCount);
			recreateOitScrBuf();
		}
	}
	CNullDriver::OnResize(ScreenSize);
	
}
int  VkDriver::toggleMultiPass()
{
	if (useTexRT)
	return dsd.hdrOn = !dsd.hdrOn;
 

	MultiPassMode = (MultiPassMode + 1) % 2;
	resetSwapchain();
	for (int i = 0; i < MaterialRenderers.size(); i++)
	{
		MaterialRenderers[i].Renderer->recreatePipeline();
	}
	return MultiPassMode;
}
void VkDriver::resetSwapchain()
{
	IRRLOG(("Resetting  device.", ELL_INFORMATION));


	if (g_pExtDevInfo)
	{
		throw "TODO Reset External";
	}

	if (!prepared)
	{
		return;
	}
	prepared = false;

	// Ensure all operations on the device have been finished before destroying resources

	vkDeviceWaitIdle(Device);

	// Recreate swap chain


	SwapChain.create(&ScreenSize.Width, &ScreenSize.Height, VSync);
	DP(("ScreenSize reset  %d,%d ", ScreenSize.Width, ScreenSize.Height));

	// Recreate the frame buffers
	vkDestroyImageView(Device, depthStencil.view, nullptr);
	vkDestroyImage(Device, depthStencil.image, nullptr);
	vkFreeMemory(Device, depthStencil.mem, nullptr);
	setupDepthStencil();
	for (uint32_t i = 0; i < frameBuffers.size(); i++) {
		vkDestroyFramebuffer(Device, frameBuffers[i], nullptr);
	}
	setupFrameBuffer();
	if (texFrameCopy)
		createTexCopy();
	// Command buffers need to be recreated as they may store
	// references to the recreated frame buffer
	destroyCommandBuffers();
	createCmdBufs();
	//buildCommandBuffers();

	vkDeviceWaitIdle(Device);

	if ((ScreenSize.Width > 0.0f) && (ScreenSize.Height > 0.0f)) {
		//camera.updateAspectRatio((float)ScreenSize.Width / (float)ScreenSize.Height);
	}

	// Notify derived class
	//windowResized();
	//viewChanged();

	prepared = true;

}

void VkDriver::recreateOitScrBuf() {
	if (pTexOitCount) { removeTexture(pTexOitCount); }
	pTexOitCount = (VkTexture*)addRenderTargetTexture(getScreenSize(), "oit_count<STO>", ECF_R32UI);
 
	mTexDescOitCountImg.imageView = pTexOitCount->getShaderResourceView();
	mTexDescOitCountImg.sampler = 0;
	mTexDescOitCountImg.imageLayout = VK_IMAGE_LAYOUT_GENERAL;
 
	ccTexOitCount++;
	//((VkTexture*)(m_pcb->pTexOitCount))->setVkImageLayout(VK_IMAGE_LAYOUT_GENERAL);
	if (!mCbOitCount)
	{
		
		mCbOitCount = (VkHardwareBuffer*)createHardwareBuffer(video::EHBT_STORAGE, video::EHBA_DEFAULT_RW, sizeof(OIT_CB)  , 0, 0);
		mCbOitData = (VkHardwareBuffer*)createHardwareBuffer(video::EHBT_STORAGE, video::EHBA_DEFAULT, OIT_BufSize * 32, 0, nullptr);
	}
	DP(("Recreated pTexOitCount %p",pTexOitCount->getTextureResource() ));
}


//! sets the needed renderstates
bool VkDriver::setRenderStates3DMode(int paraId,E_VERTEX_TYPE vType)
{
	if (!Device)
		return false;
	if (paraId >= 0)
	{
		
		auto mtr = getMaterialPara(paraId);
		assert(mtr.MaterialType == EMT_MMD || mtr.MaterialType == EMT_MMD_OIT);// only EMT support multi thread
		bool shaderOK = MaterialRenderers[mtr.MaterialType].Renderer->OnRender(this, LastVertexType, paraId);

		return shaderOK;
	}
	LastVertexType = vType;

	if (curPassType == IrrPassType_PickPoint) {
		auto p2 = Material.PickColor;
		if (Material.MaterialType == EMT_MMD_PICK) {
			//setMaterial(EMT_MMD_EDGE);
		}
		else {
			setMaterial(Material.MaterialType == EMT_POINT_CLOUD? MrPickPointDrawPt: MrPickPoint);
			Material.PickColor = p2;

		}
	}


	if (CurrentRenderMode != ERM_3D)
	{
		ResetRenderStates = true;
	}

	bSettedMaterial = false;
	if (ResetRenderStates ||
		//LastMaterial.MaterialType != Material.MaterialType// error
		LastMaterial != Material
		)
	{
		// unset old material
		if (CurrentRenderMode == ERM_3D &&
			LastMaterial.MaterialType != Material.MaterialType &&
			LastMaterial.MaterialType >= 0 && LastMaterial.MaterialType < (s32)MaterialRenderers.size())
			MaterialRenderers[LastMaterial.MaterialType].Renderer->OnUnsetMaterial();

		// set new material.
		if (Material.MaterialType >= 0 && Material.MaterialType < (s32)MaterialRenderers.size())
		{
			VkMaterialRenderer* mr = (VkMaterialRenderer * )MaterialRenderers[Material.MaterialType].Renderer;
			if (!mr->inited)
				mr->InitMaterialRenderer();
			mr->OnSetMaterial(Material, LastMaterial, ResetRenderStates, this);
			bSettedMaterial = true;
		}
	}



	bool shaderOK = true;
	if (Material.MaterialType >= 0 && Material.MaterialType < (s32)MaterialRenderers.size())
		shaderOK = MaterialRenderers[Material.MaterialType].Renderer->OnRender(this, LastVertexType,paraId);

	LastMaterial = Material;

	ResetRenderStates = false;

	CurrentRenderMode = ERM_3D;

	return shaderOK;
}



void VkDriver::setBasicRenderStates(const SMaterial& material, const SMaterial& lastMaterial,
	bool resetAllRenderstates)
{

}


E_DRIVER_TYPE VkDriver::getDriverType() const
{
	return EDT_VK;
}


const core::matrix4& VkDriver::getTransform(E_TRANSFORMATION_STATE state) const
{
	return Matrices[state];
}

ITexture* VkDriver::addRenderTargetTexture(const core::dimension2d<u32>& size,
	const io::path& name, const ECOLOR_FORMAT format,
	u32 sampleCount, u32 sampleQuality, u32 arraySlices)
{

#if 1
	ITexture* tex = new VkTexture(this, size, name, format, arraySlices, sampleCount, sampleQuality);
	if (tex)
	{
		checkDepthBuffer(tex);
		addTexture(tex);
		tex->drop();
	}
	return tex;
#endif
}

video::ITexture* VkDriver::createDeviceDependentTexture(IImage* surface, const io::path& name, void* mipmapData)
{
#if WIN81UP
	//ckadd: resize too large picture to fit max textrue size
	using namespace irr::core;
	u32 maxW = 2048; getMaxTextureSize().Width;
	if (surface->getDimension().Width > maxW || surface->getDimension().Height > maxW)
	{
		using namespace DirectX;
		dimension2du dim;
		if (surface->getDimension().Width >= surface->getDimension().Height)
			dim.set(maxW, maxW * surface->getDimension().Height / surface->getDimension().Width);
		else
			dim.set(maxW * surface->getDimension().Width / surface->getDimension().Height, maxW);

		auto pb = surface->lock();
		size_t rowPitch, slicePitch;
		ComputePitch(VK_FORMAT_B8G8R8A8_UNORM, surface->getDimension().Width, surface->getDimension().Height, rowPitch, slicePitch, 0);
		DirectX::Image dxImg = { surface->getDimension().Width,surface->getDimension().Height,VK_FORMAT_B8G8R8A8_UNORM,rowPitch,slicePitch,(uint8_t*)pb }, dxImgOut;
		ScratchImage si;
		DirectX::Resize(dxImg, dim.Width, dim.Height, TEX_FILTER_FANT, si);
		surface->unlock();
		//image->drop();
		dxImgOut = *si.GetImage(0, 0, 0);
		IImage* img = createImageFromData(video::ECOLOR_FORMAT::ECF_A8R8G8B8, core::dimension2du(dxImgOut.width, dxImgOut.height), dxImgOut.pixels, true, false);
		video::ITexture* newTex = new VkTexture(img, this, TextureCreationFlags, name, 1, mipmapData);
		img->drop();
		return newTex;
}
#endif
	return new VkTexture(surface, this, TextureCreationFlags, name, 1, mipmapData);
}

video::ITexture* VkDriver::createTextureArray(const std::vector<IImage*>& images, const io::path& name)
{
	return new VkTexture(images, this, TextureCreationFlags, name);

}



IHardwareBuffer* VkDriver::createHardwareBuffer(E_HARDWARE_BUFFER_TYPE type,
	E_HARDWARE_BUFFER_ACCESS accessType,
	u32 size, u32 flags, const void* initialData)
{
	return new VkHardwareBuffer(this, type, accessType, size, flags, initialData);
}

E_VERTEX_TYPE VkDriver::registerVertexType(core::array<SVertexElement>& elements)
{
	VkVertexDeclaration* decl = new VkVertexDeclaration(this, elements, (E_VERTEX_TYPE)declarationMap.size());
	declarationMap.insert(decl->getType(), decl);

	return decl->getType();
}

u32 VkDriver::queryMultisampleLevels(ECOLOR_FORMAT format, u32 numSamples) const
{
	UINT quality = 0;
#if TODO_MULTISAMPLE
	if (SUCCEEDED(Device->CheckMultisampleQualityLevels(this->getDeviceFormatFromColorFormat(format),
		4, &quality)))
	{
		return quality;
	}
#endif
	return 0;
}

void VkDriver::clearZBuffer()
{
	//if( CurrentDepthBuffer )		ImmediateContext->ClearDepthStencil( CurrentDepthBuffer, CLEAR_DEPTH_FLAG | CLEAR_STENCIL_FLAG, 1.f, 0, RESOURCE_STATE_TRANSITION_MODE_TRANSITION);

}

IImage* VkDriver::createScreenShot(video::ECOLOR_FORMAT format, video::E_RENDER_TARGET target)
{
	// Source for the copy is the last rendered swapchain image
	VkImage srcImage = SwapChain.images[currentBuffer];
	core::dimension2d<u32> scrSize = ScreenSize; 


#if 0
	bool supportsBlit = true;

	// Check blit support for source and destination
	VkFormatProperties formatProps;

	// Check if the device supports blitting from optimal images (the swapchain images are in optimal format)
	vkGetPhysicalDeviceFormatProperties(physicalDevice, SwapChain.colorFormat, &formatProps);
	if (!(formatProps.optimalTilingFeatures & VK_FORMAT_FEATURE_BLIT_SRC_BIT)) {
		//IRRLOG(("Device does not support blitting from optimal tiled images, using copy instead of blit!" ,ELL_ERROR));
		supportsBlit = false;
	}
	// Check if the device supports blitting to linear images 
	vkGetPhysicalDeviceFormatProperties(physicalDevice, IRR_SC_BUFFER_DXGI_FORMAT, &formatProps);
	if (!(formatProps.linearTilingFeatures & VK_FORMAT_FEATURE_BLIT_DST_BIT)) {
		//IRRLOG(("Device does not support blitting to linear tiled images, using copy instead of blit!", ELL_ERROR));
		supportsBlit = false;
	}
#endif
	if (ssWidth != scrSize.Width || ssHeight != scrSize.Height) {
		if (ssWidth != 0) {
			vkFreeMemory(Device, ssDstImgMem, nullptr);
			vkDestroyImage(Device, ssDstImg, nullptr);
		}
		ssWidth = scrSize.Width;
		ssHeight = scrSize.Height;
		// Create the linear tiled destination image to copy to and to read the memory from
		VkImageCreateInfo imageCreateCI(vks::initializers::imageCreateInfo());
		imageCreateCI.imageType = VK_IMAGE_TYPE_2D;
		// Note that vkCmdBlitImage (if supported) will also do format conversions if the swapchain color format would differ
		imageCreateCI.format = SwapChain.colorFormat;
		imageCreateCI.extent.width = ssWidth;
		imageCreateCI.extent.height = ssHeight;
		imageCreateCI.extent.depth = 1;
		imageCreateCI.arrayLayers = 1;
		imageCreateCI.mipLevels = 1;
		imageCreateCI.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
		imageCreateCI.samples = VK_SAMPLE_COUNT_1_BIT;
		imageCreateCI.tiling = VK_IMAGE_TILING_LINEAR;
		imageCreateCI.usage = VK_IMAGE_USAGE_TRANSFER_DST_BIT;
		// Create the image

		VK_CHECK_RESULT(vkCreateImage(Device, &imageCreateCI, nullptr, &ssDstImg));
		DP(("crtex ss %p", ssDstImg));
		// Create memory to back up the image
		VkMemoryRequirements memRequirements;
		VkMemoryAllocateInfo memAllocInfo(vks::initializers::memoryAllocateInfo());

		vkGetImageMemoryRequirements(Device, ssDstImg, &memRequirements);
		memAllocInfo.allocationSize = memRequirements.size;
		// Memory must be host visible to copy from
		memAllocInfo.memoryTypeIndex = mDevice->getMemoryType(memRequirements.memoryTypeBits, VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT
#ifdef _WIN32
			| VK_MEMORY_PROPERTY_HOST_CACHED_BIT
#endif
		);
		VK_CHECK_RESULT(vkAllocateMemory(Device, &memAllocInfo, nullptr, &ssDstImgMem));
		VK_CHECK_RESULT(vkBindImageMemory(Device, ssDstImg, ssDstImgMem, 0));
	}
		copyVkImage(scrSize.Width, scrSize.Height, srcImage, VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL, VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL,
			ssDstImg, VK_IMAGE_LAYOUT_UNDEFINED, VK_IMAGE_LAYOUT_GENERAL);
	
	// Get layout of the image (including row pitch)
	VkImageSubresource subResource{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 0 };
	VkSubresourceLayout subResourceLayout;
	vkGetImageSubresourceLayout(Device, ssDstImg, &subResource, &subResourceLayout);

	// Map image memory so we can start copying from it
	const char* dataTex, * data;
	vkMapMemory(Device, ssDstImgMem, 0, VK_WHOLE_SIZE, 0, (void**)&dataTex);
	data = dataTex;
	data += subResourceLayout.offset;


#if 0


	// If source is BGR (destination is always RGB) and we can't use blit (which does automatic conversion), we'll have to manually swizzle color components
	bool colorSwizzle = false;
	// Check if source is BGR 
	// Note: Not complete, only contains most common and basic BGR surface formats for demonstation purposes
	if (!supportsBlit)
	{
		std::vector<VkFormat> formatsBGR = { VK_FORMAT_B8G8R8A8_SRGB, VK_FORMAT_B8G8R8A8_UNORM, VK_FORMAT_B8G8R8A8_SNORM };
		colorSwizzle = (std::find(formatsBGR.begin(), formatsBGR.end(), SwapChain.colorFormat) != formatsBGR.end());
	}

	std::ofstream file("h:/test/debugImg.ppm", std::ios::out | std::ios::binary);
	// ppm header
	file << "P6\n" << width << "\n" << height << "\n" << 255 << "\n";
	// ppm binary pixel data
	for (uint32_t y = 0; y < height; y++)
	{
		unsigned int* row = (unsigned int*)data;

		for (uint32_t x = 0; x < width; x++)
		{
			if (colorSwizzle)
			{
				file.write((char*)row + 2, 1);
				file.write((char*)row + 1, 1);
				file.write((char*)row, 1);
			}
			else
			{
				file.write((char*)row, 3);
			}
			row++;
		}
		data += subResourceLayout.rowPitch;
	}
	file.close();
	data = dataTex;
	data += subResourceLayout.offset;
#endif
	int RowPitch = subResourceLayout.rowPitch;

	// Process data in place, handling alpha channel
#if 0
	for (u32 y = 0; y < height; ++y)
	{
		u8* ptr = (u8*)data + y * RowPitch + 3;
		for (u32 x = 0; x < width; ++x)
		{
			*ptr = 0xFF;
			ptr += 4;
}
}
#endif

	auto img = createImage(ColorFormat, irr::core::dimension2du(ssWidth, ssHeight));
	// Copy data to image 
	int colorPixelSize = getVkFormatBitsPerPixel(SwapChain.colorFormat) / 8;
	const u32 bwidth = ssWidth * colorPixelSize; //ckadd
	if (RowPitch != bwidth)
	{
		assert(RowPitch > bwidth);
		u8* tgtpos = (u8*)img->lock();
		u8* srcpos = (u8*)data;

		for (u32 y = 0; y < ssHeight; ++y)
		{
			// copy scanline
			memcpy(tgtpos, srcpos, bwidth);

			tgtpos += bwidth;
			srcpos += RowPitch;
		}
	}
	else
	{
		auto pb = img->lock();
		memcpy(pb, data, ssWidth * ssHeight * colorPixelSize);
	}
	// Unlock image and texture
	img->unlock();


	// Clean up resources
	vkUnmapMemory(Device, ssDstImgMem);



	// Return image
	img->isRGBA = isSystemRGBA;
	return img;

}



ITexture* VkDriver::createFrameBufCopy()
{
	VkImage srcImage = fbaHdr.image;
	VkTexture* texBuf = (VkTexture*)addRenderTargetTexture(ScreenSize, "texFrameCopy", ECF_A16B16G16R16F);
	VkImage dstImage = texBuf->getTextureResource();
	copyVkImage(ScreenSize.Width, ScreenSize.Height, srcImage, VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL, VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL,
		dstImage, VK_IMAGE_LAYOUT_UNDEFINED, VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL);
	return texBuf;
}

void VkDriver::frameBufCopyFrom(ITexture* tex)
{
	VkImage dstImage = fbaHdr.image;
	VkTexture* src = (VkTexture*)tex;
	copyVkImage(ScreenSize.Width, ScreenSize.Height,
		src->getTextureResource(), VK_IMAGE_LAYOUT_GENERAL, VK_IMAGE_LAYOUT_GENERAL,
		dstImage, VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL, VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL);
}

bool VkDriver::setClipPlane(u32 index, const core::plane3df& plane, bool enable)
{
#if USE_UNWORK
	if (index > 3)
		return false;
	ClipPlanes[index] = plane;
	enableClipPlane(index, enable);
	return true;
#else
	assert(0);
	return false;
#endif
}

void VkDriver::enableClipPlane(u32 index, bool enable)
{
#if USE_UNWORK
	ClipPlaneEnabled[index] = enable;
#else
	assert(0);
#endif
}

void VkDriver::getClipPlane(u32 index, core::plane3df& plane, bool& enable)
{
#if USE_UNWORK
	plane = ClipPlanes[index];
	enable = ClipPlaneEnabled[index];
#else
	assert(0);
#endif
}

void VkDriver::enableMaterial2D(bool enable)
{
	CNullDriver::enableMaterial2D(enable);
}

ECOLOR_FORMAT VkDriver::getColorFormat() const
{
	return ColorFormat;
}

VkFormat VkDriver::getVkScColorFormat() const
{
	return D3DColorFormat;
}

core::dimension2du VkDriver::getMaxTextureSize() const
{

	return core::dimension2du(4096, 4096);  // atleast, as spec


	return core::dimension2du(16384, 16384);
}

VkFormat VkDriver::getDeviceFormatFromColorFormat(ECOLOR_FORMAT format) const
{
	switch (format)
	{
	case ECF_A1R5G5B5:
		return VK_FORMAT_A1R5G5B5_UNORM_PACK16;

	case ECF_R5G6B5:
		return VK_FORMAT_R5G6B5_UNORM_PACK16;
	case ECF_R8:					return VK_FORMAT_R8_UNORM; break;
	case ECF_R8G8:					return VK_FORMAT_R8G8_UNORM; break;
	case ECF_R8G8B8:
		return IRR_SC_BUFFER_DXGI_FORMAT;

	case ECF_A8R8G8B8:
		return IRR_SC_BUFFER_DXGI_FORMAT;

	case ECF_R16F:
		return VK_FORMAT_R16_SFLOAT;

	case ECF_G16R16F:
		return VK_FORMAT_R16G16_SFLOAT;

	case ECF_A16B16G16R16F:
		return VK_FORMAT_R16G16B16A16_SFLOAT;
	case ECF_A16B16G16R16UN:
		return VK_FORMAT_R16G16B16A16_UNORM;

	case ECF_R32F:
		return VK_FORMAT_R32_SFLOAT;

	case ECF_R32UI:
		return VK_FORMAT_R32_UINT;
	case ECF_RGBA32UI:
		return VK_FORMAT_R32G32B32A32_UINT;
	case ECF_G32R32F:
		return VK_FORMAT_R32G32_SFLOAT;

	case ECF_A32B32G32R32F:
		return VK_FORMAT_R32G32B32A32_SFLOAT;
	case ECF_A2B10G10R10UN:	
		return VK_FORMAT_A2B10G10R10_UNORM_PACK32;
		
	}
	throw;
	return VK_FORMAT_UNDEFINED;
}
ECOLOR_FORMAT VkDriver::getColorFormatFromDeviceFormat(VkFormat format) const
{
	switch (format)
	{
	case VK_FORMAT_R8G8B8A8_UNORM: case VK_FORMAT_B8G8R8A8_UNORM:
		return ECF_A8R8G8B8;
	case VK_FORMAT_R8_UNORM:
		return ECF_R8;
	case VK_FORMAT_R8G8_UNORM:
		return ECF_R8G8;
	case VK_FORMAT_R32G32B32A32_SFLOAT:
		return ECF_A32B32G32R32F;

	case VK_FORMAT_R16G16B16A16_SFLOAT:
		return ECF_A16B16G16R16F;
	case VK_FORMAT_R16G16B16A16_UNORM:
		return ECF_A16B16G16R16UN;
	case VK_FORMAT_R32_UINT:
		return ECF_R32UI;
	case VK_FORMAT_R32G32B32A32_UINT:
		return ECF_RGBA32UI;
	case VK_FORMAT_A2B10G10R10_UNORM_PACK32:
		return ECF_A2B10G10R10UN;
	case VK_FORMAT_R8G8B8_UNORM :
		return ECF_R8G8B8;
		//case ECF_A1R5G5B5:
		//	return VK_FORMAT_A1R5G5B5_UNORM_PACK16;

		//case ECF_R5G6B5:
		//	return VK_FORMAT_R5G6B5_UNORM_PACK16;



		//case ECF_A8R8G8B8:
		//	return IRR_SC_BUFFER_DXGI_FORMAT;

		//case ECF_R16F:
		//	return VK_FORMAT_R16_SFLOAT;

		//case ECF_G16R16F:
		//	return VK_FORMAT_R16G16_SFLOAT;



		//case ECF_R32F:
		//	return VK_FORMAT_R32_SFLOAT;

		//case ECF_G32R32F:
		//	return VK_FORMAT_R32G32_SFLOAT;

		//case ECF_A32B32G32R32F:
		//	return VK_FORMAT_R32G32B32A32_SFLOAT;
	}
	throw;
	return ECF_UNKNOWN;
}
VkPrimitiveTopology VkDriver::getTopology(scene::E_PRIMITIVE_TYPE primType) const
{
	switch (primType)
	{
	case scene::EPT_POINT_SPRITES:
	case scene::EPT_POINTS:
		return VK_PRIMITIVE_TOPOLOGY_POINT_LIST;

	case scene::EPT_LINE_STRIP:
		return  VK_PRIMITIVE_TOPOLOGY_LINE_STRIP;

	case scene::EPT_LINE_LOOP:
	case scene::EPT_LINES:
		return VK_PRIMITIVE_TOPOLOGY_LINE_LIST;

	case scene::EPT_TRIANGLE_STRIP:
		return VK_PRIMITIVE_TOPOLOGY_TRIANGLE_STRIP;

	case scene::EPT_TRIANGLES:
		return VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;

	case scene::EPT_TRIANGLE_FAN:
		return VK_PRIMITIVE_TOPOLOGY_TRIANGLE_FAN;
	default:
		throw;
		return VK_PRIMITIVE_TOPOLOGY_POINT_LIST;
	}
}


u32 VkDriver::getIndexAmount(scene::E_PRIMITIVE_TYPE primType, u32 primitiveCount) const
{
	switch (primType)
	{
	case scene::EPT_LINE_STRIP:
		return primitiveCount * 2;

	case scene::EPT_LINE_LOOP:
	case scene::EPT_LINES:
	case scene::EPT_UI_QUARD:
		return primitiveCount;

	case scene::EPT_TRIANGLE_STRIP:
		return primitiveCount + 2;

	case scene::EPT_TRIANGLES:
		return primitiveCount * 3;

	default:
		return 0;
	}
}

VkFormat VkDriver::getIndexType(E_INDEX_TYPE iType) const
{
	return (iType == video::EIT_16BIT) ? VK_FORMAT_R16_UINT : VK_FORMAT_R32_UINT;
}

VkVertexDeclaration* VkDriver::getVertexDeclaration(E_VERTEX_TYPE vType) const
{
	DeclarationNode declNode = declarationMap.find(vType);
	if (declNode)
		return declNode->getValue();

	return 0;
}


void VkDriver::checkDepthBuffer(ITexture* tex)
{
	//if (!tex)
	return;

	const core::dimension2du optSize = tex->getSize().getOptimalSize(
		!queryFeature(EVDF_TEXTURE_NPOT),
		!queryFeature(EVDF_TEXTURE_NSQUARE), true);
	SDepthSurfaceVk* depth = 0;
	core::dimension2du destSize(0x7fffffff, 0x7fffffff);
	for (u32 i = 0; i < DepthBuffers.size(); ++i)
	{
		if ((DepthBuffers[i]->Size.Width == optSize.Width) &&
			(DepthBuffers[i]->Size.Height == optSize.Height))
		{
			if ((DepthBuffers[i]->Size.Width < destSize.Width) &&
				(DepthBuffers[i]->Size.Height < destSize.Height))
			{
				depth = DepthBuffers[i];
				destSize = DepthBuffers[i]->Size;
			}
		}
	}

	// Create a depth buffer for this texture
	if (!depth)
	{
		throw;
#if 0//TODO
		// create depth buffer
		DepthBuffers.push_back(new SDepthSurfaceVk(Device));
		DepthBuffers.getLast()->Surface = createDepthStencilView(optSize);
		if (DepthBuffers.getLast()->Surface)
		{
			depth = DepthBuffers.getLast();
			depth->Size.set(optSize.Width, optSize.Height);
		}
		else
		{
			char buffer[128];
			sprintf(buffer, "Could not create DepthBuffer of %ix%i", optSize.Width, optSize.Height);
			IRRLOG((buffer, ELL_ERROR));
			DepthBuffers.erase(DepthBuffers.size() - 1);
}
#endif
}
	else
		depth->grab();

	static_cast<VkTexture*>(tex)->DepthSurface = depth;
}

void VkDriver::removeDepthSurface(SDepthSurfaceVk* depth)
{
	for (u32 i = 0; i < DepthBuffers.size(); ++i)
	{
		if (DepthBuffers[i] == depth)
		{
			DepthBuffers.erase(i);
			return;
		}
	}
}

bool VkDriver::uploadVertexData(const void* vertices, u32 vertexCount,
	const void* indexList, u32 indexCount,
	E_VERTEX_TYPE vType, E_INDEX_TYPE iType)
{
	// Parse information about buffers
	const u32 vertexStride = getVertexPitchFromType(vType);
	const u32 indexStride = iType == video::EIT_16BIT ? 2 : 4;
	//const DXGI_FORMAT indexFormat = iType == video::EIT_16BIT ? DXGI_FORMAT_R16_UINT : DXGI_FORMAT_R32_UINT;
	UINT offset = 0;

	// reallocated if needed



	if (!vertices && !indexList)
		throw;
	const uu::CVtxIdxCache::SDrawData& vid = mVICache.AppendVtxIdx(
		vertices, vertices ? vertexCount * vertexStride : 0,
		indexList, indexList ? indexCount * indexStride : 0);
	//DP(("\n+V=%8d (%3d) +I=%8d (%3d)\nBV=%8d (%3d) BI=%8d (%3d)", vertices ? vertexCount * vertexStride : 0, vertexCount,
	//	indexList ? indexCount * indexStride : 0, indexCount, mVICache.mbV.curDataSize(),vid.ofsV, mVICache.mbI.curDataSize(),vid.ofsI));
	if (!reallocateDynamicBuffers(mVICache.mbV.curDataSize(), mVICache.mbI.curDataSize()))
		throw;



	//for (int32_t i = 0; i < drawCmdBuffers.size(); ++i)
	{
		// Bind triangle vertex buffer (contains position and colors)
		VkDeviceSize offsets[1] = { vid.ofsV };
		VkBuffer buf[1] = { DynVertexBuffer->getBufferResource() };
		if (vertices) vkCmdBindVertexBuffers(currentCmdBuffer(), 0, 1, buf, offsets);

		// Bind triangle index buffer
		VkDeviceSize ofsIndex = vid.ofsI;
		if (indexList) vkCmdBindIndexBuffer(currentCmdBuffer(), DynIndexBuffer->getBufferResource(), ofsIndex,
			(iType == video::EIT_16BIT) ? VK_INDEX_TYPE_UINT16 : VK_INDEX_TYPE_UINT32);


	}
	return true;
}

bool VkDriver::reallocateDynamicBuffers(u32 vertexBufferSize, u32 indexBufferSize)
{

	if (!DynVertexBuffer || DynVertexBuffer->Size < vertexBufferSize)
	{
		waitAddedFences();
		// Release old buffer if small
		if (!DynVertexBuffer)
			DynVertexBuffer = new VkHardwareBuffer(this, EHBT_VERTEX, EHBA_DEFAULT, vertexBufferSize, 0, 0);
		else if (DynVertexBuffer->Size < vertexBufferSize)
			DynVertexBuffer->resize(std::max(vertexBufferSize, (u32(DynVertexBuffer->Size * 1.2) / 256 + 1) * 256));

		if (!DynVertexBuffer)
			throw;
	}

	if (!DynIndexBuffer || DynIndexBuffer->Size < indexBufferSize)
	{
		waitAddedFences();
		// Release old buffer if small
		if (!DynIndexBuffer)
			DynIndexBuffer = new VkHardwareBuffer(this, EHBT_INDEX, EHBA_DEFAULT, indexBufferSize, 0, 0);
		else if (DynIndexBuffer->Size < indexBufferSize)
			DynIndexBuffer->resize(std::max(indexBufferSize, (u32(DynIndexBuffer->Size * 1.2) / 256 + 1) * 256));

		if (!DynIndexBuffer)
			throw;
	}

#if 0//VK_USE_2D_FX_RENDERER
	//2D 
	if (!_vb2D)
	{
		desc.ByteWidth = 8192 * 2;
		desc.BindFlags = D3D11_BIND_VERTEX_BUFFER;
		ThrowIfFailed(Device->CreateBuffer(&desc, NULL, &_vb2D));
	}

	if (!_ib2D)
	{
		desc.ByteWidth = 1024 * 2;
		desc.BindFlags = D3D11_BIND_INDEX_BUFFER;
		ThrowIfFailed(hr = Device->CreateBuffer(&desc, NULL, &_ib2D));
	}
#endif

	return true;
}


// returns the current size of the screen or rendertarget
const core::dimension2d<u32>& VkDriver::getCurrentRenderTargetSize() const
{
	if (CurrentRendertargetSize.Width == 0)
		return ScreenSize;
	else
		return CurrentRendertargetSize;
}
#if 0
void VkDriver::ShaderSetTextures(u32 ShaderFlag, ITexture** ppTex, u32 start, u32 size)
{
	//ID3D11SamplerState* samplers[SHADER_MAX_TEXTURE_NUM];
	ID3D11ShaderResourceView* views[SHADER_MAX_TEXTURE_NUM];
	u32 cmt = 0;
	for (u32 i = 0; i < min(size, SHADER_MAX_TEXTURE_NUM); i++)
	{
		if (CurrentTexture[i])
		{
			views[i] = ((VkTexture*)ppTex[i])->getShaderResourceView();
			cmt++;
		}
		else
		{
			views[i] = 0;
			break;
		}
	}
	if (cmt) {
		//if (ShaderFlag & SHADER_FLAG_VS)	ImmediateContext->PSSetShaderResources( start, cmt, views );

		if (ShaderFlag & SHADER_FLAG_PS)	ImmediateContext->PSSetShaderResources(start, cmt, views);
		if (ShaderFlag & SHADER_FLAG_GS)	ImmediateContext->GSSetShaderResources(start, cmt, views);		// Test for recirculate

	}
}


#endif


////////////// IGPUProgrammingServices methods start ////////////////////////////////////////////

////////////// IGPUProgrammingServices methods end ////////////////////////////////////////////

////////////// IMaterialRenderer methods start ////////////////////////////////////////////

void VkDriver::setVertexShaderConstant(const f32* data, s32 startRegister, s32 constantAmount)
{
	IRRLOG(("\"setVertexShaderConstant\" with offset is not supported", ELL_ERROR));
}

void VkDriver::setPixelShaderConstant(const f32* data, s32 startRegister, s32 constantAmount)
{
	IRRLOG(("\"setPixelShaderConstant\" with offset is not supported", ELL_ERROR));
}

bool VkDriver::setVertexShaderConstant(const c8* name, const f32* floats, int count)
{
	static_cast<VkMaterialRenderer*>(MaterialRenderers[Material.MaterialType].Renderer)->setVariable(name, floats, count);
	return true;
}

bool VkDriver::setPixelShaderConstant(const c8* name, const f32* floats, int count)
{
	static_cast<VkMaterialRenderer*>(MaterialRenderers[Material.MaterialType].Renderer)->setVariable(name, floats, count);
	return true;
}

bool VkDriver::setStreamOutputBuffer(IHardwareBuffer* buffer, u32 ofs)
{
#if TODO_STREAM_OUT
	ID3D11Buffer* buffers = 0;
	UINT offset = ofs;

	// If buffer is null, remove from pipeline
	if (!buffer)
	{
		ImmediateContext->SOSetTargets(1, &buffers, &offset);
		return true;
	}

	// Validate buffer
	if (buffer->getDriverType() != EDT_VK)
	{
		IRRLOG(("Fatal Error: Tried to set a buffer not owned by this driver.", ELL_ERROR));
		return false;
	}
	if (buffer->getType() != EHBT_STREAM_OUTPUT)
	{
		IRRLOG(("Fatal Error: Tried to set a buffer that is not for stream output.", ELL_ERROR));
		return false;
	}

	// Set stream output buffer
	buffers = static_cast<VkHardwareBuffer*>(buffer)->getBufferResource();
	ImmediateContext->SOSetTargets(1, &buffers, &offset);
#endif
	return true;
}

IVideoDriver* VkDriver::getVideoDriver()
{
	return this;
}




void VkDriver::UpdateDataPerView()
{
	_bUpdatePerFrame = true;
	if (LastMaterial.MaterialType >= 0 && LastMaterial.MaterialType < (s32)MaterialRenderers.size())
		MaterialRenderers[LastMaterial.MaterialType].Renderer->OnUnsetMaterial();
	LastMaterial.MaterialType = EMT_FORCE_32BIT;
}


void VkDriver::createTexCopy()
{
	if (texFrameCopy) removeTexture(texFrameCopy);
	texFrameCopy = (VkTexture*)addRenderTargetTexture(ScreenSize, "texFrameCopy", this->getColorFormat());

}

void VkDriver::copyLastFrame(irr::video::ITexture* texTgt)
{
	//waitDriverIdle(2);
	if (!SwapChain.imagesRendered[currentBuffer])
	{
		return;
	}
	if (!texTgt) {
		if (!texFrameCopy)
			createTexCopy();
		texTgt = texFrameCopy;
	}
	auto tgtVkTex = static_cast<VkTexture*>(texTgt);
	VkImage srcImage = SwapChain.images[currentBuffer];
	VkImage dstImage = tgtVkTex->getTextureResource();

	VkDeviceMemory dstImageMemory;
	u32 width = ScreenSize.Width;
	u32 height = ScreenSize.Height;
	copyVkImage(ScreenSize.Width, ScreenSize.Height, srcImage, VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL, VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL,
		dstImage, VK_IMAGE_LAYOUT_UNDEFINED, VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL);
	tgtVkTex->ImageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
	//saveTexture(tgtVkTex, "data/tmp.png");
}
void VkDriver::beginOIT()
{
	if (!UseOIT || !dsd.oitOn) return;


	assert(pTexOitCount->getSize() == ScreenSize);
	//if (Driver->curPassType == IrrPassType_Mirror)
	{
		pTexOitCount->ClearBackBuffer(0);

		OIT_CB* oitcb = (OIT_CB*)mCbOitCount->lock(true);
		//DP(("OIT %d", *puCbOitCount));
		oitcb->uCbOitCount = 0;
		oitcb->uMaxCount = std::clamp(dsd.OitCb.uMaxCount-1,0u,63u);
		oitcb->minAlpha = std::clamp(dsd.OitCb.minAlpha,0.0f,1.0f);
		mCbOitCount->unlock();
	}

}
void VkDriver::endOIT()
{
	if (!UseOIT || !dsd.oitOn) return;


#if 0 //to check
	VkMemoryBarrier memoryBarrier = vks::initializers::memoryBarrier();
	vkCmdPipelineBarrier( currentCmdBuffer(), VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT, VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT, 0, 0, nullptr, 0, nullptr, 0, nullptr);
	memoryBarrier.srcAccessMask = VK_ACCESS_SHADER_READ_BIT | VK_ACCESS_SHADER_WRITE_BIT;
	memoryBarrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT | VK_ACCESS_SHADER_WRITE_BIT;
	vkCmdPipelineBarrier(currentCmdBuffer(), VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT, VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT, 0, 1, &memoryBarrier, 0, nullptr, 0, nullptr);
#endif 

	//should after fw particle,use its DS
	vkCmdBindPipeline(currentCmdBuffer(), VK_PIPELINE_BIND_POINT_GRAPHICS, mPlOitR);
	vkCmdDraw(currentCmdBuffer(), 3, 1, 0, 0);  
	
}

//! creates a video driver
IVideoDriver* createVkDriver(const irr::SIrrlichtCreationParameters& params, io::IFileSystem* io, void* platformDevice)

{
	VkDriver* drv = new VkDriver(params, io);
	if (!drv->initDriver(params.WindowSize, (HWND)params.WindowId, params.Bits, params.Fullscreen, false, params.HighPrecisionFPU, params.Vsync, params.AntiAlias))
	{
		drv->drop();
		drv = 0;
	}

	return drv;
}

irr::video::E_MATERIAL_TYPE irr::video::VkDriver::getMT(irr::video::Fx2DIdEnum fx)
{
	video::E_MATERIAL_TYPE mt;
	auto it = mMtMap.find(fx);
	if (it == mMtMap.end())
	{
		char cst[] = "mt2D_0_"; cst[5] = '0' + fx;
		mt = add2DMaterialType(fx, cst);
		mMtMap[fx] = mt;
	}
	else
		mt = mMtMap[fx];
	return mt;
}

irr::video::CbMr2D* irr::video::VkDriver::getCB(irr::video::Fx2DIdEnum fx)
{
	auto it = mMtMap.find(fx);
	if (it == mMtMap.end())
		return nullptr;
	video::VkMr2D* mr = (video::VkMr2D*)getMaterialRenderer(it->second);
	return &(mr->cbDraw);
}

irr::video::VkMr2D* irr::video::VkDriver::getRenderer(irr::video::Fx2DIdEnum fx)
{
	auto it = mMtMap.find(fx);
	if (it == mMtMap.end())
		return nullptr;
	video::VkMr2D* mr = (video::VkMr2D*)getMaterialRenderer(it->second);
	return mr;
}



}

// end namespace video
} // end namespace irr


VK_EXTERNAL_DEVICE_INFO* IrrGetExternalDeviceInfo()
{
	return &(irr::video::g_edi);
}

void IrrUpdateExternalVulkanDeviceInfo(VK_EXTERNAL_DEVICE_INFO* pi)
{

	irr::video::g_edi = *pi;
	irr::video::g_pExtDevInfo = IrrGetExternalDeviceInfo();
	if (irr::g_irrDrv11)
	irr::g_irrDrv11->UpdateExternalDeviceInfo();
}


#endif