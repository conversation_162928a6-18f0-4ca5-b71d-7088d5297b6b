﻿  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
  Finished searching libraries
  
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
  
  Finished searching libraries
     Creating library D:\AProj\VkUpApp\x64\Debug\TextFirework.lib and object D:\AProj\VkUpApp\x64\Debug\TextFirework.exp
  
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
  
  Finished searching libraries
  
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
  
  Finished searching libraries
  
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
  
  Finished searching libraries
  
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
  
  Finished searching libraries
  
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
  
  Finished searching libraries
LINK : warning LNK4217: symbol 'createDeviceEx' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(UaLibMain.obj)' in function '"public: virtual void __cdecl ualib::UaLibMain::LibInitialize(struct ualib::LibCreateionParam *)" (?LibInitialize@UaLibMain@ualib@@UEAAXPEAULibCreateionParam@2@@Z)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnGuQin.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CInstancedMeshSceneNode.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnPhyFluid.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CLabelSceneNode.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnPiano.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnPhyCloth.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnPhyInflatable.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CMidiPlateSceneNode.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CLineGridSceneNode.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnWater.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CVoxelMeshSceneNode.obj)'
LINK : warning LNK4217: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnCsParticle.obj)' in function '"public: virtual class irr::video::SMaterial & __cdecl irr::scene::ISceneNode::getMaterial(unsigned int)" (?getMaterial@ISceneNode@scene@irr@@UEAAAEAVSMaterial@video@3@I@Z)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(irrSaba.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnArItem.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CCubeGridSceneNode.obj)'
  TextFirework.vcxproj -> D:\AProj\VkUpApp\x64\Debug\TextFirework.exe
