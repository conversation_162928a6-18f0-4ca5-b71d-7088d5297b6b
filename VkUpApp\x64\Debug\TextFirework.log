﻿  stdafx.cpp
D:\AProj\VkUpApp\stdafx.h(12,9): warning C4005: 'NOMINMAX': macro redefinition
  (compiling source file 'stdafx.cpp')
      D:\AProj\VkUpApp\stdafx.h(12,9):
      'NOMINMAX' previously declared on the command line
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'stdafx.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.h(41,45): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'stdafx.cpp')
  
D:\AProj\CommonStaticLib\MidiMan.h(91,31): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'stdafx.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.h(400,63): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'stdafx.cpp')
  
  CPlayer.cpp
D:\AProj\VkUpApp\CPlayer.cpp(274,20): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/CPlayer.cpp')
  
D:\AProj\VkUpApp\CPlayer.cpp(311,20): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/CPlayer.cpp')
  
D:\AProj\VkUpApp\CPlayer.cpp(315,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/CPlayer.cpp')
  
  FFMpegTest.cpp
D:\SDK\ffmpeg-dev\include\libavutil\rational.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/FFMpegTest.cpp')
  
D:\AProj\VkUpApp\FFMpegTest.cpp(47,29): warning C4101: 'codec_name': unreferenced local variable
  (compiling source file '/FFMpegTest.cpp')
  
  TTSMan.cpp
  VkUpApp.cpp
D:\AProj\AppMainLib\app\MusicFirework\AppMain.h(6,9): warning C4005: 'HAS_FW': macro redefinition
  (compiling source file '/VkUpApp.cpp')
      D:\AProj\VkUpApp\VkUpAppStage.h(6,9):
      see previous definition of 'HAS_FW'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/VkUpApp.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/VkUpApp.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/VkUpApp.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\VkUpApp\VkUpApp.cpp(195,46): warning C4267: '=': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file '/VkUpApp.cpp')
  
D:\AProj\VkUpApp\VkUpApp.cpp(514,77): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file '/VkUpApp.cpp')
  
D:\AProj\VkUpApp\VkUpApp.cpp(806,14): warning C4244: '=': conversion from 'LONG' to 'irr::f32', possible loss of data
  (compiling source file '/VkUpApp.cpp')
  
D:\AProj\VkUpApp\VkUpApp.cpp(807,14): warning C4244: '=': conversion from 'LONG' to 'irr::f32', possible loss of data
  (compiling source file '/VkUpApp.cpp')
  
D:\AProj\VkUpApp\VkUpApp.cpp(821,36): warning C4244: '=': conversion from 'double' to 'irr::f32', possible loss of data
  (compiling source file '/VkUpApp.cpp')
  
D:\AProj\VkUpApp\VkUpApp.cpp(1389,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/VkUpApp.cpp')
  
D:\AProj\VkUpApp\VkUpApp.cpp(1390,10): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/VkUpApp.cpp')
  
D:\AProj\VkUpApp\VkUpApp.cpp(1399,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/VkUpApp.cpp')
  
D:\AProj\VkUpApp\VkUpApp.cpp(1483,17): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/VkUpApp.cpp')
  
  Generating Code...
  TextBmp.cpp
D:\AProj\VkUpApp\TextBmp.cpp(66,15): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
D:\AProj\VkUpApp\TextBmp.cpp(81,34): warning C4244: 'argument': conversion from 'int' to 'Gdiplus::REAL', possible loss of data
D:\AProj\VkUpApp\TextBmp.cpp(119,83): warning C4244: 'argument': conversion from 'int' to 'Gdiplus::REAL', possible loss of data
D:\AProj\VkUpApp\TextBmp.cpp(119,81): warning C4244: 'argument': conversion from 'int' to 'Gdiplus::REAL', possible loss of data
D:\AProj\VkUpApp\TextBmp.cpp(119,49): warning C4267: 'argument': conversion from 'size_t' to 'INT', possible loss of data
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
  Finished searching libraries
     Creating library D:\AProj\VkUpApp\x64\Debug\TextFirework.lib and object D:\AProj\VkUpApp\x64\Debug\TextFirework.exp
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
  Finished searching libraries
  Searching libraries
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMTD.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdiplus.lib:
      Searching D:\SDK\VulkanSDK\Lib\vulkan-1.lib:
      Searching D:\SDK\freetype\objs\x64\Debug Static\freetype.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletCollision_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletDynamics_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\LinearMath_Debug.lib:
      Searching D:\SDK\bullet3Build\lib\Debug\BulletSoftBody_Debug.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\propsys.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\comsuppw.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64\atls.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avcodec.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avdevice.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avfilter.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avformat.lib:
      Searching D:\SDK\ffmpeg-dev\lib\avutil.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swresample.lib:
      Searching D:\SDK\ffmpeg-dev\lib\swscale.lib:
      Searching D:\SDK\LeapSDK\lib\x64\LeapC.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCooking_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetUtils_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PVDRuntime_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SnippetRender_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\SceneQuery_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXExtensions_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXPvdSDK_static_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysX_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXCommon_64.lib:
      Searching D:\SDK\PhysX\physx\bin\win.x86_64.vc143.mt\debug\PhysXFoundation_64.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\Strmiids.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\XAudio2.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\imm32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dwmapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\xinput.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dinput8.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxguid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winmm.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntimed.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\libucrtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplat.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mf.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfreadwrite.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfuuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shlwapi.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib:
      Searching D:\AProj\VkUpApp\packages\OpenXR.Loader.1.0.10.2\build\native\..\..\native\x64\release\lib\openxr_loader.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\AppMainLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\IrrlichtUP.lib:
      Searching D:\AProj\VkUpApp\x64\Debug\MFT_AudioDelay.lib:
      Searching C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcpmtd.lib:
      Searching C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\mfplay.lib:
  Finished searching libraries
LINK : warning LNK4217: symbol 'createDeviceEx' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(UaLibMain.obj)' in function '"public: virtual void __cdecl ualib::UaLibMain::LibInitialize(struct ualib::LibCreateionParam *)" (?LibInitialize@UaLibMain@ualib@@UEAAXPEAULibCreateionParam@2@@Z)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnGuQin.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CInstancedMeshSceneNode.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CLabelSceneNode.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnPiano.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnPhyCloth.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnPhyInflatable.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CMidiPlateSceneNode.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CLineGridSceneNode.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnWater.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CVoxelMeshSceneNode.obj)'
LINK : warning LNK4217: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnCsParticle.obj)' in function '"public: virtual class irr::video::SMaterial & __cdecl irr::scene::ISceneNode::getMaterial(unsigned int)" (?getMaterial@ISceneNode@scene@irr@@UEAAAEAVSMaterial@video@3@I@Z)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(irrSaba.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(SnArItem.obj)'
LINK : warning LNK4286: symbol '?IdentityMaterial@video@irr@@3VSMaterial@12@A (class irr::video::SMaterial irr::video::IdentityMaterial)' defined in 'IrrlichtUP.lib(Irrlicht.obj)' is imported by 'AppMainLib.lib(CCubeGridSceneNode.obj)'
  TextFirework.vcxproj -> D:\AProj\VkUpApp\x64\Debug\TextFirework.exe
