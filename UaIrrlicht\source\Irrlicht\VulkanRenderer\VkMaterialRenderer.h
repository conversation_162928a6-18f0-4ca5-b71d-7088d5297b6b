
#ifndef __C_DG_MATERIAL_RENDERER_H_INCLUDED__
#define __C_DG_MATERIAL_RENDERER_H_INCLUDED__

#include "IrrCompileConfig.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_

#define MR_DECLARE_PCB(T,V,node) T *V=(T *)node->getMaterial(0).getCB(sizeof(T));
#include "VkHeader.h"
#include "IMaterialRenderer.h"
#include "VkDriver.h"
#include <map>
#include <string>
namespace irr
{

namespace video
{
	struct ShaderToyCb {
		float3 iResolution;
		float iTime;

		float2 pad01;
		float iTimeDelta;
		int iFrame;

		float4 iMouse;
		float4 iDate;
		float4 fv;
		float4 fv1;
		int4 iv;
		float3 camPos;
		float fovDZ;
		float3 camUp;
		float camRoll;
		float3 tgtPos;
		float tgtDis;
		float freqs[4];
		float vol;

		bool is_debugdraw;
		bool is_pause;
		bool main_image_srgb;
        bool has_depth_texture;


	};

class VkMaterialRenderer : public IMaterialRenderer
{
public:
	VkMaterialRenderer(IVideoDriver* driver)
	: Driver(static_cast<VkDriver*>(driver))
	, BaseRenderer(0)
	{
		Device = Driver->Device;
	}

	virtual ~VkMaterialRenderer()
	{
		if (BaseRenderer)
			BaseRenderer->drop();


	}

	//! sets a variable in the shader.
	//! \param name: Name of the variable
	//! \param floats: Pointer to array of floats
	//! \param count: Amount of floats in array.
	virtual bool setVariable(const c8* name, const f32* floats, int count)
	{
		return false;
	}

	virtual bool OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId = -1) override
	{
		return false;
	}


	virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial, bool resetAllRenderstates, IMaterialRendererServices* services) override
	{
		CurrentMaterial = material;
		services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
	}
	virtual void OnUnsetMaterial() { }


	virtual bool isTransparent() const { return true; }

	//! get shader signature
	virtual const void* getShaderByteCode() const = 0;

	//! get shader signature size
	virtual u32 getShaderByteCodeSize() const = 0;
#if 1
	inline float4 colorToD3DXVECTOR4(video::SColor& clr)
	{
		u32 color = clr.color;
		return float4(
			float((color >> 16) & 0xFF) / 255.0f,
			float((color >> 8) & 0xFF) / 255.0f,
			float(color & 0xFF) / 255.0f,
			float((color >> 24) & 0xFF) / 255.0f
		);
	}

	inline float4 colorToD3DXVECTOR4(const video::SColor& clr)
	{
		u32 color = clr.color;
		return float4(
			// R
			float((color >> 16) & 0xFF) / 255.0f,
			float((color >> 8) & 0xFF) / 255.0f,
			float(color & 0xFF) / 255.0f,
			float((color >> 24) & 0xFF) / 255.0f
		);
	}
#else
	inline float4 colorToD3DXVECTOR4(video::SColor& clr)
	{
		const float cf255 = 255.0f;
		const float r = (float)clr.getRed() / cf255;
		const float g = (float)clr.getGreen() / cf255;
		const float b = (float)clr.getBlue() / cf255;
		const float a = (float)clr.getAlpha() / cf255;
		return float4(r,g,b,a);
	}

	inline float4 colorToD3DXVECTOR4(const video::SColor& clr)
	{
		const float cf255 = 255.0f;
		const float r = (float)clr.getRed() / cf255;
		const float g = (float)clr.getGreen() / cf255;
		const float b = (float)clr.getBlue() / cf255;
		const float a = (float)clr.getAlpha() / cf255;
		return float4(r,g,b,a);
	}
#endif

	virtual void InitMaterialRenderer()=0; //should inplement in sub class
	VkPipelineRasterizationStateCreateInfo rasterizationState{}, rsPoint{}, rsLine{}, rasterizationStateNonSolid{}, rasterizationStateCullNone{}, rasterizationStateCullFront{}, rasterizationStateCounterClockWise{}, rasterizationStateCullNoneCounterClockWise{};
	bool inited = false;
	E_VERTEX_TYPE vertexType = E_VERTEX_TYPE::EVT_STANDARD;
protected:

	void SetPipelineCommonInfo();



	// Irrlicht objects
	VkDriver* Driver=nullptr;
	// Descriptor set pool

	VkDevice Device;


	VkMaterialRenderer* BaseRenderer;
	//VkGraphicsPipelineCreateInfo mPlDesc{};
	VkGraphicsPipelineCreateInfo PipelineCI{}, PipelineCI2{}, PipelineCIPass1{};
	VkPipelineInputAssemblyStateCreateInfo inputAssemblyState{}, inputAssemblyStatePointList{};
	VkPipelineDepthStencilStateCreateInfo depthStencilState{}, depthStencilStateNoZ{}, depthStencilStateNoZWrite{};
	VkPipelineColorBlendStateCreateInfo colorBlendState{};
	VkPipelineViewportStateCreateInfo viewportState{};
	VkPipelineMultisampleStateCreateInfo multisampleState{};
	VkPipelineDynamicStateCreateInfo dynamicState{};
	VkPipelineColorBlendAttachmentState blendAttachmentState{};
	std::vector<VkDynamicState> dynamicStateEnables = {
	VK_DYNAMIC_STATE_VIEWPORT,
	VK_DYNAMIC_STATE_SCISSOR,
//#if VK_USE_DYNAMIC_STATE_EXT
//	VK_DYNAMIC_STATE_CULL_MODE,
//	VK_DYNAMIC_STATE_FRONT_FACE,
//#endif

	};

	VkPipeline mCurPipeLine {};
	//Diligent::RefCntAutoPtr<Diligent::ITextureView> m_TextureSRV[_IRR_MATERIAL_MAX_TEXTURES_];
	//Diligent::IShaderResourceBinding *mCurSrb = nullptr;
	SMaterial CurrentMaterial;


};


inline void IrrMatrix_to_VkMatrix(const irr::core::matrix4 &m, float4x4 &md) {
	//memcpy(&md, m.pointer(), sizeof(float4x4));
	md = *(float4x4*)m.pointer();
}
}
}

#endif
#endif


